<!--
/**
 * 折旧历史 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-07-04 17:13:20
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
	<title th:text="${lang.translate('折旧历史')}">折旧历史</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="display:none">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->



        <div class="layui-row form-row" id="random-1330-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column" >


                    <div class="layui-form-item" >
                        <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('折旧方式')}">折旧方式</div><div class="layui-required">*</div></div>
                        <div class="layui-input-block layui-input-block-c1">
                            <div id="depreciationMethod" input-type="select" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetDepreciationMethodEnum')}" extraParam="{}"></div>
                        </div>
                    </div>


                                    <div class="layui-form-item" >
                        <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('业务编号')}">业务编号</div></div>
                        <div class="layui-input-block layui-input-block-c1">
                            <input lay-filter="businessCode" id="businessCode" name="businessCode" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('业务编号') }" type="text" class="layui-input"  />
                        </div>
                    </div>


                    <div class="layui-form-item" >
                        <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('折旧额')}">折旧额</div></div>
                        <div class="layui-input-block layui-input-block-c1">
                            <input lay-filter="depreciationPrice" id="depreciationPrice" name="depreciationPrice" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('折旧额') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />
                        </div>
                    </div>



                    <div class="layui-form-item" >
                        <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('折旧前价格')}">折旧前价格</div></div>
                        <div class="layui-input-block layui-input-block-c1">
                            <input lay-filter="beforePrice" id="beforePrice" name="beforePrice" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('折旧前价格') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />
                        </div>
                    </div>



                    <div class="layui-form-item" >
                        <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('折旧后价格')}">折旧后价格</div></div>
                        <div class="layui-input-block layui-input-block-c1">
                            <input lay-filter="afterPrice" id="afterPrice" name="afterPrice" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('折旧后价格') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />
                        </div>
                    </div>



                    <div class="layui-form-item" >
                        <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('资产原值')}">资产原值</div></div>
                        <div class="layui-input-block layui-input-block-c1">
                            <input lay-filter="originalUnitPrice" id="originalUnitPrice" name="originalUnitPrice" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('资产原值') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />
                        </div>
                    </div>



                    <div class="layui-form-item" >
                        <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('记录时间')}">记录时间</div></div>
                        <div class="layui-input-block layui-input-block-c1">
                            <input input-type="date" lay-filter="recordTime" id="recordTime" name="recordTime"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择') +''+ lang.translate('记录时间') }" type="text" class="layui-input"   />
                        </div>
                    </div>

                                    <div class="layui-form-item" >
                        <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('备注')}">备注</div></div>
                        <div class="layui-input-block layui-input-block-c1">
                            <input lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('备注') }" type="text" class="layui-input"  />
                        </div>
                    </div>

                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 20px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消')}"  style="margin-right: 15px" >取消</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var SELECT_DEPRECIATIONMETHOD_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetDepreciationMethodEnum')}]];
    var VALIDATE_CONFIG={"depreciationMethod":{"labelInForm":"折旧方式","inputType":"select_box","required":true}};
    var AUTH_PREFIX="eam_asset_depreciation_history";


</script>



<script th:src="'/business/eam/asset_depreciation_history/asset_depreciation_history_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset_depreciation_history/asset_depreciation_history_form.js?'+${cacheKey}"></script>

</body>
</html>
