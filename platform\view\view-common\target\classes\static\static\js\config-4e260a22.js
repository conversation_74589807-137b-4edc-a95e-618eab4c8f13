var m=Object.defineProperty;var n=(t,o,i)=>o in t?m(t,o,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[o]=i;var r=(t,o,i)=>(n(t,typeof o!="symbol"?o+"":o,i),i);import{a8 as e}from"./index-bb2cbf17.js";import{d as p}from"./chartEditStore-55fbe93c.js";import{J as a}from"./index-0ec04aee.js";import"./plugin-3ef0fcec.js";import"./icon-f36697ff.js";import"./SettingItem-7fe1cfec.js";/* empty css                                                                      */import"./SettingItemBox-500aaf18.js";import"./CollapseItem.vue_vue_type_script_setup_true_lang-9b6d8958.js";import"./index.esm.min-f1367f57.js";import"./http-36f53bd1.js";import"./lodash-d17632fd.js";import"./fileTypeEnum-21359a08.js";const s=[["行1列1","行1列2","行1列3"],["行2列1","行2列2","行2列3"],["行3列1","行3列2","行3列3"],["行4列1","行4列2","行4列3"],["行5列1","行5列2","行5列3"],["行6列1","行6列2","行6列3"],["行7列1","行7列2","行7列3"],["行8列1","行8列2","行8列3"],["行9列1","行9列2","行9列3"],["行10列1","行10列2","行10列3"]],d={header:["列1","列2","列3"],dataset:s,index:!0,columnWidth:[30,100,100],align:["center","right","right","right"],rowNum:5,waitTime:2,headerHeight:35,carousel:"single",headerBGC:"#00BAFF",oddRowBGC:"#003B51",evenRowBGC:"#0A2732"};class A extends p{constructor(){super(...arguments);r(this,"key",a.key);r(this,"chartConfig",e(a));r(this,"option",e(d))}}export{A as default,d as option};
