<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no" />
    <title></title>
    <link rel="stylesheet" href="/extmodule/element-ui/2.15.7.themechalk.index.min.css" th:href="'/extmodule/element-ui/2.15.7.themechalk.index.min.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="vFormRender.css?t=20210730"> <!-- 根据Web服务器或CDN路径修改 -->
    <style type="text/css">
    </style>
</head>
<body>

<div id="app">
<!--    <el-button @click="test">测试</el-button>-->
    <v-form-render :form-json="formJson" :form-data="formData" :option-data="optionData" ref="vFormRef">
    </v-form-render>

</div>

<script type="text/javascript">
    if (!!window.ActiveXObject || "ActiveXObject" in window) { //IE load polyfill.js for Promise
        var scriptEle = document.createElement("script");
        scriptEle.type = "text/javascript";
        scriptEle.src = "https://cdn.bootcss.com/babel-polyfill/6.23.0/polyfill.min.js"
        document.body.appendChild(scriptEle)
    }

</script>

<script th:src="'/extmodule/axios/axios.min.js?'+${cacheKey}"></script>
<script th:src="'/extmodule/vue/vue.2.6.14.min.js?'+${cacheKey}"></script>
<script th:src="'/extmodule/element-ui/2.15.7.index.min.js?'+${cacheKey}"></script>
<script th:src="'/business/common/form/vFormRender.umd.min.js?'+${cacheKey}"></script>

<script th:inline="javascript">

    var FORM_JSON = [[${formJson}]];
    var FORM_DATA = [[${formData}]];
    var PAGE_TYPE = [[${pageType}]];
    var APPROVAL_STATUS = [[${approvalStatus}]];


    var ID = [[${id}]];
    var ACTION = [[${action}]];
    var vueApp=new Vue({
        el: '#app',
        data: {
            approvalStatus:APPROVAL_STATUS,
            pageType:PAGE_TYPE,
            formDataResult:{},
            formJson: FORM_JSON,
            formData: FORM_DATA,
            optionData: {}
        },
        mounted(){
            console.log("this.pageType,",this.pageType);
            console.log("this.approvalStatus,",this.approvalStatus);
            var disableForm=false;
            if(this.pageType&&this.pageType=="approve"){
                disableForm=true;
            }
            if(this.approvalStatus&&this.approvalStatus=="drafting"){
                console.log("drafting")
                disableForm=false;
            }else{
                disableForm=true;
                console.log("disableForm")
            }
            if(disableForm){
                this.$refs.vFormRef.disableForm()
            }
        },
        methods: {
            updateData:async function(){
                 var res=await  this.updateFormData();
                 console.log("res:",this.formDataResult);
                 return this.formDataResult;
             },
            updateFormData: function(){
                 this.formDataResult.sucess=false;
                 this.formDataResult.id=ID;
                 var _this=this;
                 this.$refs.vFormRef.getFormData().then(function(data){
                    _this.formDataResult.sucess=true;
                    _this.formDataResult.data=JSON.stringify(data);
                    axios.post('/service-common/sys-form-info/save-form-data',_this.formDataResult).
                        then( function(res) {
                            _this.formDataResult.sucess=true;
                        }).catch(err=>{
                            _this.formDataResult.sucess=true;
                            console.log(err);
                        })
                }).catch( function(error) {
                    // Form Validation Failed
                    alert(error)
                    _this.formDataResult.sucess=false;
                    _this.formDataResult.message=error;
                })
            },
            setDisableForm:function(){
                this.$refs.vFormRef.disableForm()
            },
            submitForm: function() {
            }
        }
    });

    function updateData(){
        return vueApp.updateData();
    }

    function disableData(){
        return vueApp.setDisableForm();
    }
</script>
</body>
</html>

