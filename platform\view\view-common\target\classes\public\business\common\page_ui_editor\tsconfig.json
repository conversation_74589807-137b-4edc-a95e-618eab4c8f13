{"compilerOptions": {"module": "esnext", "target": "esnext", "lib": ["es6", "dom"], "sourceMap": true, "jsx": "react", "moduleResolution": "node", "rootDir": "./src", "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": true, "strictNullChecks": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowSyntheticDefaultImports": true, "typeRoots": ["./node_modules/@types", "./typings"]}, "include": ["**/*"], "exclude": ["node_modules"], "types": ["typePatches"]}