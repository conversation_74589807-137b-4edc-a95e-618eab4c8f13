<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('组件')}">组件</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <link rel="stylesheet" href="/business/oa/portal/portal.css" th:href="'/business/oa/portal/portal.css?'+${cacheKey}"/>
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>
<body>
<div class="portal-container">
    <div class="layui-row">
            <div class="layui-card" id="card">
                <div class="layui-card-header">
                    <div class="oa-portal-card-item-none">
                        <a class="oa-portal-card-item-title">内部公告</a>
                        <span class="oa-portal-card-item-more"><a href="javascript:openMore('public')">更多</a></span>
                    </div>
                </div>
                <div class="layui-card-body" style="background-color: white;" >
                    <div class="oa-portal-card-list" id="public-data">
                    </div>
                </div>
            </div>
    </div>
</div>
<script>

    function getUrlParam(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }
    var parHeight=350;
    if(getUrlParam("height")){
        parHeight=getUrlParam("height");
    }
    $("#card").css("height",parHeight+"px");
    window.openMore=function(type) {
        window.open("/business/oa/notice/notice_list_public.html?type="+type,"","left=100,top=100,width=800,height=600")
    }
    window.openNotice=function(id) {
        console.log(id);
        var url="/business/oa/notice/notice_view.html?id="+id;
        let win = window.open(url, "_blank");
    }


    function fillData(data){
        fillNPData(data,'public-data')
    }
    function fillNPData(data,type){
        var noticeC=$("#"+type)
        if(data.length>0){
            var html=""
            for(var i=0;i<data.length;i++){
                var e=data[i];
                var zd="";
                if(e.iftop=="Y"){
                    zd="<image style=\"width:11px;height: 11px;margin-right:5px;margin-bottom: 2px;\" src='./zd.png'></image>"
                }
                var title="";
                if(e.title.length>36){
                    title=e.title.slice(0,35)+"...";
                }else{
                    title=e.title;
                }
                html=html+"    <div class=\"oa-portal-card-item\">\n" +
                    "                            <a class=\"oa-portal-card-item-title\" href=\"javascript:openNotice('"+data[i].id+"')\" > "+zd+title+"</a>\n" +
                    "                            <span class=\"oa-portal-card-item-desc\">"+data[i].createTime+"</span>\n" +
                    "                        </div>"
            }
            noticeC.html(html);
        }else{
            noticeC.html("无数据")
        }
    }
    $.ajax({
        url : "/service-oa/oa-notice/query-paged-data",
        type : "post",
        async : true,//此处需要注意的是要想获取ajax返回的值这个async属性必须设置成同步的，否则获取不到返回值
        data : {pageSize:5,pageIndex:1,status:"enable",type:"public"},
        dataType : "json",
        success : function(r) {
            if (r.success) {
                fillData(r.data.list);
            }
        }
    });
</script>
</body>
</html>
