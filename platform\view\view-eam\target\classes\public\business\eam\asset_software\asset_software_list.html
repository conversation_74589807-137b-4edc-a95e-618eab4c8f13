<!--
/**
 * 软件资产 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-05-05 06:04:12
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <title th:text="${lang.translate('软件资产')}">软件资产</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 流程 , procId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 办理状态 , status ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('办理状态')}" class="search-label status-label">办理状态</span><span class="search-colon">:</span></div>
                        <div id="status" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}" style="width:150px" extraParam="{}"></div>
                    </div>
                    <!-- 软件分类 , categoryId ,typeName=select_box, isHideInSearch=true -->
                    <!-- 软件编号 , code ,typeName=text_input, isHideInSearch=true -->
                    <!-- 软件版本 , softwareVersion ,typeName=text_input, isHideInSearch=true -->
                    <!-- 成本 , costPrice ,typeName=number_input, isHideInSearch=true -->
                    <!-- 所属公司 , ownCompanyId ,typeName=button, isHideInSearch=true -->
                    <!-- 使用公司 , useOrganizationId ,typeName=button, isHideInSearch=true -->
                    <!-- 管理人 , managerId ,typeName=button, isHideInSearch=true -->
                    <!-- 来源明细 , sourceDetail ,typeName=text_input, isHideInSearch=true -->
                    <!-- 授权信息 , authorizationInfo ,typeName=text_area, isHideInSearch=true -->
                    <!-- 授权数量 , authorizedNumber ,typeName=number_input, isHideInSearch=true -->
                    <!-- 可用数量 , authorizedAvailableNumber ,typeName=number_input, isHideInSearch=true -->
                    <!-- 无限授权 , authorizedNumberUnlimit ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 授权码 , authorizationCode ,typeName=text_area, isHideInSearch=true -->
                    <!-- 授权到期时间 , authorizationExpirationDate ,typeName=date_input, isHideInSearch=true -->
                    <!-- 永久授权 , authorizationExpirationUnlimit ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 是否维保 , needMaintenance ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 维保开始时间 , maintenanceStartDate ,typeName=date_input, isHideInSearch=true -->
                    <!-- 维保到期时间 , maintenanceEndDate ,typeName=date_input, isHideInSearch=true -->
                    <!-- 登记时间 , registerDate ,typeName=date_input, isHideInSearch=true -->
                    <!-- 软件说明 , content ,typeName=text_area, isHideInSearch=true -->
                    <!-- 备注 , notes ,typeName=text_area, isHideInSearch=true -->
                    <!-- 附件 , attachId ,typeName=upload, isHideInSearch=true -->
                    <!-- 标签1 , label1 ,typeName=text_input, isHideInSearch=true -->
                    <!-- 标签2 , label2 ,typeName=text_input, isHideInSearch=true -->
                    <!-- 标签3 , label3 ,typeName=text_area, isHideInSearch=true -->
                    <!-- 标签4 , label4 ,typeName=text_area, isHideInSearch=true -->
                    <!-- 制单人 , originatorId ,typeName=button, isHideInSearch=true -->
                    <!-- 变更类型 , chsType ,typeName=text_input, isHideInSearch=true -->
                    <!-- 变更状态 , chsStatus ,typeName=text_input, isHideInSearch=true -->
                    <!-- 变更版本号 , chsVersion ,typeName=text_input, isHideInSearch=true -->
                    <!-- 变更ID , changeInstanceId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 流程概要 , summary ,typeName=text_input, isHideInSearch=true -->
                    <!-- 最后审批人账户ID , latestApproverId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 最后审批人姓名 , latestApproverName ,typeName=text_input, isHideInSearch=true -->
                    <!-- 下一节点审批人 , nextApproverIds ,typeName=text_input, isHideInSearch=true -->
                    <!-- 下一个审批节点审批人姓名 , nextApproverNames ,typeName=text_input, isHideInSearch=true -->
                    <!-- 审批意见 , approvalOpinion ,typeName=text_input, isHideInSearch=true -->
                    <!-- 业务编号 , businessCode ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('业务编号')}" class="search-label businessCode-label">业务编号</span><span class="search-colon">:</span></div>
                        <input id="businessCode" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>
                    <!-- 软件名称 , name ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('软件名称')}" class="search-label name-label">软件名称</span><span class="search-colon">:</span></div>
                        <input id="name" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>
                    <!-- 版权类型 , copyrightType ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('版权类型')}" class="search-label copyrightType-label">版权类型</span><span class="search-colon">:</span></div>
                        <div id="copyrightType" th:data="${'/service-system/sys-dict-item/query-list?dictCode=asset_software_copyright_type'}" style="width:150px" extraParam="{}"></div>
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 许可模式 , licenseMode ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('许可模式')}" class="search-label licenseMode-label">许可模式</span><span class="search-colon">:</span></div>
                        <div id="licenseMode" th:data="${'/service-system/sys-dict-item/query-list?dictCode=asset_software_license_mode'}" style="width:150px" extraParam="{}"></div>
                    </div>
                    <!-- 供应商 , supplierId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('供应商')}" class="search-label supplierId-label">供应商</span><span class="search-colon">:</span></div>
                        <div id="supplierId" th:data="${'/service-eam/eam-supplier/query-paged-list'}" style="width:150px" extraParam="{}"></div>
                    </div>
                    <!-- 来源 , sourceId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('来源')}" class="search-label sourceId-label">来源</span><span class="search-colon">:</span></div>
                        <div id="sourceId" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_source'}" style="width:150px" extraParam="{}"></div>
                    </div>
                    <!-- 详细位置 , positionDetail ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('详细位置')}" class="search-label positionDetail-label">详细位置</span><span class="search-colon">:</span></div>
                        <input id="positionDetail" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">

                    <!-- 维保商 , maintainerId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('维保商')}" class="search-label maintainerId-label">维保商</span><span class="search-colon">:</span></div>
                        <div id="maintainerId" th:data="${'/service-eam/eam-maintainer/query-paged-list'}" style="width:150px" extraParam="{}"></div>
                    </div>


                    <!-- 购置日期 , purchaseDate ,typeName=date_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('购置日期')}" class="search-label purchaseDate-label">购置日期</span><span class="search-colon">:</span></div>
                            <input type="text" id="purchaseDate-begin" style="width: 150px" lay-verify="date" th:placeholder="${lang.translate('开始日期')}" autocomplete="off" class="layui-input search-input search-date-input"  readonly >
                            <span class="search-dash">-</span>
                            <input type="text" id="purchaseDate-end"  style="width: 150px"  lay-verify="date" th:placeholder="${lang.translate('结束日期')}" autocomplete="off" class="layui-input search-input search-date-input" readonly>
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索')}">搜索</span></button>
                <button id="search-button-advance" class="layui-btn layui-btn-primary icon-btn search-button-advance"><i class="layui-icon">&#xe671;</i><span th:text="${lang.translate('更多')}">更多</span></button>
            </div>
        </div>

        <div style="margin-top: 42px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>

<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${perm.checkAuth('eam_asset_software:create')}" id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建')}">新建</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${perm.checkAuth('eam_asset_software:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view"  data-id="{{d.id}}"> <span th:text="${lang.translate('查看')}">查看</span></button>
    <button th:if="${perm.checkAnyAuth('eam_asset_software:update','eam_asset_software:save')}" class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit"data-id="{{d.id}}"><span th:text="${lang.translate('修改')}">修改</span></button>
    <button th:if="${perm.checkAuth('eam_asset_software:delete')}" class="layui-btn layui-btn-xs layui-btn-danger ops-delete-button " lay-event="del" data-id="{{d.id}}"><span th:text="${lang.translate('删除')}">删除</span></button>
    <button th:if="${perm.checkAuth('eam_asset_software:for-approval')}"class="layui-btn layui-btn-xs  for-approval-button " lay-event="for-approval" data-id="{{d.id}}"><span th:text="${lang.translate('送审')}">送审</span></button>
    <button th:if="${perm.checkAuth('eam_asset_software:confirm')}"class="layui-btn layui-btn-xs  confirm-data-button " lay-event="confirm-data" data-id="{{d.id}}"><span th:text="${lang.translate('确认')}">确认</span></button>
    <button th:if="${perm.checkAuth('eam_asset_software:revoke')}"class="layui-btn layui-btn-xs  revoke-data-button " lay-event="revoke-data" data-id="{{d.id}}"><span th:text="${lang.translate('撤销')}">撤销</span></button>
    <button th:if="${perm.checkAuth('eam_asset_software:bill')}"class="layui-btn layui-btn-xs  download-bill-button " lay-event="download-bill" data-id="{{d.id}}"><span th:text="${lang.translate('单据')}">单据</span></button>

</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}]];
    var RADIO_AUTHORIZEDNUMBERUNLIMIT_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusYNEnum')}]];
    var RADIO_AUTHORIZATIONEXPIRATIONUNLIMIT_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusYNEnum')}]];
    var RADIO_NEEDMAINTENANCE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusYNEnum')}]];
    var AUTH_PREFIX="eam_asset_software";

    // APPROVAL_REQUIRED
    var APPROVAL_REQUIRED = [[${approvalRequired}]] ;

</script>

<script th:src="'/business/eam/asset_software/asset_software_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset_software/asset_software_list.js?'+${cacheKey}"></script>

</body>
</html>
