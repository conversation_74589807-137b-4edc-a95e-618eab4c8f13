var fe=Object.defineProperty,ge=Object.defineProperties;var ve=Object.getOwnPropertyDescriptors;var le=Object.getOwnPropertySymbols;var he=Object.prototype.hasOwnProperty,ye=Object.prototype.propertyIsEnumerable;var se=(l,c,s)=>c in l?fe(l,c,{enumerable:!0,configurable:!0,writable:!0,value:s}):l[c]=s,Q=(l,c)=>{for(var s in c||(c={}))he.call(c,s)&&se(l,s,c[s]);if(le)for(var s of le(c))ye.call(c,s)&&se(l,s,c[s]);return l},W=(l,c)=>ge(l,ve(c));var de=(l,c,s)=>new Promise((g,P)=>{var x=d=>{try{p(s.next(d))}catch(m){P(m)}},f=d=>{try{p(s.throw(d))}catch(m){P(m)}},p=d=>d.done?g(d.value):Promise.resolve(d.value).then(x,f);p((s=s.apply(l,c)).next())});import{d as L,l as O,L as K,r as o,o as u,E as U,w as t,e,f as y,c as C,q as _,u as a,j as V,b8 as _e,m as pe,Z as ee,a0 as G,a5 as ae,F as J,s as ne,ao as re,t as F,A as be,B as qe,p as xe,cS as A,aJ as X,cR as ce,aK as M,a9 as we,M as Pe,aG as ue,a7 as Z,cr as De,cs as Ie,bh as Ce}from"./index-bb2cbf17.js";import{i as te}from"./icon-f36697ff.js";import{c as ke}from"./http-36f53bd1.js";/* empty css                                                                      */import{S as z}from"./SettingItemBox-500aaf18.js";import{r as Se}from"./chartEditStore-55fbe93c.js";import{n as Re}from"./noData-9e194391.js";import{u as oe}from"./useTargetData.hook-a16b3b4d.js";import{g as me}from"./plugin-3ef0fcec.js";import"./index-819682d7.js";import{R as $e,a as Ee}from"./pondIndex.vue_vue_type_style_index_0_scoped_de860d6d_lang-a2489be1.js";import"./EditorWorker-54a88558.js";import"./editorWorker-43a98755.js";import{S as j}from"./SettingItem-7fe1cfec.js";import{l as Oe}from"./lodash-d17632fd.js";import{C as He}from"./index-922b8da1.js";import"./index-56351f34.js";import"./fileTypeEnum-21359a08.js";const Te={class:"go-pr-3"},Ue={key:1},ze=L({__name:"pondIndex",props:{modelShow:Boolean,targetDataRequest:Object},emits:["update:modelShow","editSaveHandle"],setup(l,{emit:c}){const s=l,g=c;O();const P=O(),x=O(!1);K(()=>s.modelShow,d=>{x.value=d});const f=()=>{g("update:modelShow",!1)},p=()=>{var d,m;if(!((d=s.targetDataRequest)!=null&&d.dataPondName)){window.$message.warning("请在左下角输入名称！"),(m=P.value)==null||m.focus();return}me({message:"保存内容将同步修改所有使用此接口的组件, 是否继续?",isMaskClosable:!0,transformOrigin:"center",onPositiveCallback:()=>{g("update:modelShow",!1),g("editSaveHandle",s.targetDataRequest)}})};return(d,m)=>{const D=o("n-space"),i=o("n-scrollbar"),k=o("n-tag"),w=o("n-input"),b=o("n-button"),H=o("n-card"),$=o("n-modal");return u(),U($,{class:"go-chart-data-request",show:x.value,"onUpdate:show":m[1]||(m[1]=v=>x.value=v),"mask-closable":!1,closeOnEsc:!1},{default:t(()=>[e(H,{bordered:!1,role:"dialog",size:"small","aria-modal":"true",style:{width:"1000px",height:"800px"}},{header:t(()=>[]),"header-extra":t(()=>[]),action:t(()=>[e(D,{justify:"space-between"},{default:t(()=>[l.targetDataRequest?(u(),U(D,{key:0},{default:t(()=>[e(k,{bordered:!1,type:"primary"},{default:t(()=>[y("名称：")]),_:1}),e(w,{value:l.targetDataRequest.dataPondName,"onUpdate:value":m[0]||(m[0]=v=>l.targetDataRequest.dataPondName=v),ref_key:"inputInstRef",ref:P,type:"text",size:"small",autofocus:!0,clearable:!0,minlength:1,maxlength:16,placeholder:"请输入名称"},null,8,["value"])]),_:1})):(u(),C("span",Ue)),e(D,null,{default:t(()=>[e(b,{onClick:f},{default:t(()=>[y("取消")]),_:1}),e(b,{type:"primary",onClick:p},{default:t(()=>[y("保存")]),_:1})]),_:1})]),_:1})]),default:t(()=>[e(i,{style:{"max-height":"718px"}},{default:t(()=>[_("div",Te,[e(D,{vertical:""},{default:t(()=>{var v;return[e(a($e)),e(a(Ee),{"target-data-request":(v=l.targetDataRequest)==null?void 0:v.dataPondRequestConfig},null,8,["target-data-request"])]}),_:1})])]),_:1})]),_:1})]),_:1},8,["show"])}}}),Ne=V(ze,[["__scopeId","data-v-de860d6d"]]),Be=l=>(be("data-v-43dfb7fe"),l=l(),qe(),l),Ae={class:"go-chart-data-pond-list"},Fe={class:"pond-item-box"},je=Be(()=>_("span",null," 创建 ",-1)),Le=["onClick"],Me={class:"item-content"},Ge={class:"item-content-body"},Ve=["onClick"],Je=L({__name:"index",emits:["createPond","deletePond"],setup(l,{emit:c}){_e(b=>({"1013e1a5":D.value}));const s=c,{DuplicateOutlineIcon:g,TrashIcon:P}=te.ionicons5,x=pe(),{chartEditStore:f,targetData:p}=oe(),{requestDataPond:d}=ee(f.getRequestGlobalConfig),m=G(()=>p.value.request.requestDataPondId),D=G(()=>x.getAppTheme),i=()=>{s("createPond",!0)},k=(b,H)=>{b.stopPropagation(),b.preventDefault(),s("deletePond",H)},w=b=>{p.value.request.requestDataPondId=b.dataPondId};return(b,H)=>{const $=o("n-timeline-item"),v=o("n-timeline"),N=o("n-icon"),B=o("n-button"),I=o("n-divider"),n=o("n-a"),r=o("n-text"),q=o("n-space"),S=o("n-tag"),T=o("n-ellipsis"),h=o("n-scrollbar");return u(),C("div",Ae,[e(v,{class:"pond-item-timeline",style:{width:"20px"}},{default:t(()=>[e($,{type:"info"}),e($,{type:"success"})]),_:1}),_("div",Fe,[e(B,{class:"create-btn go-py-4",ghost:"",onClick:i},{icon:t(()=>[e(N,null,{default:t(()=>[e(a(g))]),_:1})]),default:t(()=>[je]),_:1}),e(I,{style:{margin:"10px 0"}}),a(d).length?ae("",!0):(u(),U(q,{key:0,justify:"center"},{default:t(()=>[e(r,{class:"not-layer-text",depth:3},{default:t(()=>[y(" 暂无数据内容， "),e(n,{onClick:i},{default:t(()=>[y("立即创建")]),_:1})]),_:1})]),_:1})),e(h,{style:{"max-height":"490px"}},{default:t(()=>[(u(!0),C(J,null,ne(a(d),R=>(u(),C("div",{key:R.dataPondId,class:re([{select:R.dataPondId===m.value},"pond-item"]),onClick:E=>w(R)},[_("div",Me,[_("div",Ge,[_("div",null,[e(S,{class:"go-mr-1",type:R.dataPondId===m.value?"warning":"",bordered:!1},{default:t(()=>[y(" 名称 ")]),_:2},1032,["type"]),e(T,{style:{"max-width":"180px"}},{default:t(()=>[y(F(R.dataPondName||"暂无"),1)]),_:2},1024)]),_("div",null,[e(S,{class:"go-mr-1",type:R.dataPondId===m.value?"warning":"",bordered:!1},{default:t(()=>[y(" 地址 ")]),_:2},1032,["type"]),e(T,{style:{"max-width":"180px"}},{default:t(()=>[y(F(R.dataPondRequestConfig.requestUrl||"暂无"),1)]),_:2},1024)])]),_("div",{class:"item-content-icon go-transition-quick",onClick:E=>k(E,R)},[e(N,null,{default:t(()=>[e(a(P))]),_:1})],8,Ve)]),_("div",{class:re({"select-modal":R.dataPondId===m.value})},null,2)],10,Le))),128))]),_:1})])])}}});const Ye=V(Je,[["__scopeId","data-v-43dfb7fe"]]),Qe=_("thead",null,[_("tr",null,[_("th",null,"key"),_("th",null,"value")])],-1),ie=L({__name:"displayTable",props:{target:Object},setup(l){const c=l,s={key:"",value:""},g=xe({content:[]});return K(()=>c.target,P=>{g.content=[];for(const x in P)g.content.push({key:x,value:P[x]});g.content.length||(g.content=[JSON.parse(JSON.stringify(s))])},{immediate:!0,deep:!0}),(P,x)=>{const f=o("n-table");return u(),U(f,{bordered:!1,"single-line":!1,size:"small",style:{"border-bottom-right-radius":"7px","border-bottom-left-radius":"7px"}},{default:t(()=>[Qe,_("tbody",null,[(u(!0),C(J,null,ne(g.content,(p,d)=>(u(),C("tr",{key:d},[_("td",null,F(p.key||"暂无"),1),_("td",null,F(p.value||"暂无"),1)]))),128))])]),_:1})}}}),We={class:"go-chart-data-display"},Xe={class:"go-mr-3"},Ke={key:0},Ze={class:"go-mt-3"},et={key:0},tt={key:1},at={key:1},nt=L({__name:"index",props:{globalData:Object,targetData:Object},setup(l){const c=l,{HelpOutlineIcon:s,FlashIcon:g,PulseIcon:P}=te.ionicons5,{requestUrl:x,requestInterval:f,requestHttpType:p,requestContentType:d,requestSQLContent:m,requestParams:D,requestParamsBodyType:i,requestIntervalUnit:k}=ee(c.targetData.dataPondRequestConfig);A.HEADER;const w={[X.DEFAULT]:"普通请求",[X.SQL]:"SQL 请求"},b=O(A.PARAMS);return(H,$)=>{const v=o("n-input"),N=o("n-icon"),B=o("n-divider"),I=o("n-tab"),n=o("n-tabs"),r=o("n-text"),q=o("n-card"),S=o("n-code"),T=o("n-scrollbar");return u(),C("div",We,[e(T,{style:{"max-height":"570px"}},{default:t(()=>[_("div",Xe,[_("div",null,[e(a(z),{name:"主体信息"},{default:t(()=>[e(a(j),{name:"接口名称"},{default:t(()=>{var h;return[e(v,{size:"small",placeholder:((h=l.targetData)==null?void 0:h.dataPondName)||"暂无",disabled:!0},null,8,["placeholder"])]}),_:1}),e(a(j),{name:"接口类型"},{default:t(()=>[e(v,{size:"small",placeholder:a(p)||"暂无",disabled:!0},null,8,["placeholder"])]),_:1})]),_:1}),e(a(z),null,{default:t(()=>[e(a(j),{name:"组件间隔"},{default:t(()=>[e(v,{size:"small",placeholder:`${a(f)||"暂无"}`,disabled:!0},{suffix:t(()=>[y(F(l.targetData&&a(ce)[a(k)]),1)]),_:1},8,["placeholder"])]),_:1}),e(a(j),{name:"全局间隔（默认）"},{default:t(()=>{var h;return[e(v,{size:"small",placeholder:`${((h=l.globalData)==null?void 0:h.requestInterval)||"暂无"}`,disabled:!0},{suffix:t(()=>[y(F(l.globalData&&a(ce)[l.globalData.requestIntervalUnit]),1)]),_:1},8,["placeholder"])]}),_:1})]),_:1}),e(a(z),{name:"源地址",alone:!0},{default:t(()=>{var h;return[e(v,{size:"small",placeholder:((h=l.globalData)==null?void 0:h.requestOriginUrl)||"暂无",disabled:!0},{prefix:t(()=>[e(N,{component:a(P)},null,8,["component"])]),_:1},8,["placeholder"])]}),_:1}),e(a(z),{name:"接口地址",alone:!0},{default:t(()=>[e(v,{size:"small",placeholder:a(x)||"暂无",disabled:!0},{prefix:t(()=>[e(N,{component:a(g)},null,8,["component"])]),_:1},8,["placeholder"])]),_:1})]),e(B),e(a(z),{name:"类型"},{default:t(()=>[e(a(j),{name:"配置类型"},{default:t(()=>[e(v,{size:"small",placeholder:l.targetData&&w[a(d)],disabled:!0},null,8,["placeholder"])]),_:1}),a(d)===a(X).DEFAULT?(u(),U(a(j),{key:0,name:"body 类型"},{default:t(()=>[e(v,{size:"small",placeholder:l.targetData&&a(i),disabled:!0},null,8,["placeholder"])]),_:1})):ae("",!0)]),_:1}),a(d)===a(X).DEFAULT?(u(),C("div",Ke,[e(n,{type:"line",animated:"",value:b.value,"onUpdate:value":$[0]||($[0]=h=>b.value=h)},{default:t(()=>[(u(!0),C(J,null,ne(a(A),h=>(u(),U(I,{key:h,name:h,tab:h},{default:t(()=>[y(F(h),1)]),_:2},1032,["name","tab"]))),128))]),_:1},8,["value"]),_("div",Ze,[b.value!==a(A).BODY?(u(),C("div",et,[e(ie,{class:"go-my-3",target:a(D)[b.value]},null,8,["target"])])):(u(),C("div",tt,[a(i)===a(M).NONE?(u(),U(q,{key:0,class:"go-mt-3 go-pb-3"},{default:t(()=>[e(r,{depth:"3"},{default:t(()=>[y("该接口没有 Body 体")]),_:1})]),_:1})):a(i)===a(M).FORM_DATA||a(i)===a(M).X_WWW_FORM_URLENCODED?(u(),U(ie,{key:1,class:"go-my-3",target:a(D)[a(A).BODY][a(i)]},null,8,["target"])):a(i)===a(M).JSON?(u(),U(q,{key:2,size:"small",style:{"padding-bottom":"7px"}},{default:t(()=>[e(S,{code:a(D)[a(A).BODY][a(i)]||"暂无内容",language:"json"},null,8,["code"])]),_:1})):a(i)===a(M).XML?(u(),U(S,{key:3,code:a(D)[a(A).BODY][a(i)]||"",language:"html"},null,8,["code"])):ae("",!0)]))])])):(u(),C("div",at,[e(a(z),{name:"键名"},{default:t(()=>[e(r,null,{default:t(()=>[y("sql")]),_:1})]),_:1}),e(a(z),{name:"键值"},{default:t(()=>[e(S,{code:a(m).sql||"",language:"sql"},null,8,["code"])]),_:1})]))])]),_:1})])}}});const ot=V(nt,[["__scopeId","data-v-cc877db2"]]),lt={class:"pond-content"},st={key:1,class:"no-data go-flex-center"},dt=["src"],rt=L({__name:"index",props:{modelShow:Boolean},emits:["update:modelShow","sendHandle"],setup(l,{emit:c}){const s=l,g=c,{PencilIcon:P}=te.ionicons5,{chartEditStore:x,targetData:f}=oe(),{requestDataPond:p}=ee(x.getRequestGlobalConfig),d=O(!1),m=O(!1),D=O(!1),i=O(!1),k=O(),w=G(()=>{var q,S;const n=(S=(q=f==null?void 0:f.value)==null?void 0:q.request)==null?void 0:S.requestDataPondId;return n?p.value.filter(T=>n===T.dataPondId)[0]:void 0});K(()=>s.modelShow,n=>{m.value=n}),K(()=>w.value,n=>{D.value=!0,k.value=n,we(()=>{D.value=!1})},{immediate:!0});const b=(n=!1)=>{i.value=!!n,d.value=!0},H=()=>{const n=Pe();k.value={dataPondId:n,dataPondName:n,dataPondRequestConfig:Oe.cloneDeep(W(Q({},Se),{requestDataType:ue.Pond}))},b()},$=n=>{i.value?v(n):N(n),i.value=!1,d.value=!1},v=n=>{try{const r=p.value.findIndex(q=>q.dataPondId===n.dataPondId);r!==-1?(p.value.splice(r,1,n),x.getComponentList.forEach(q=>{q.request.requestDataType===ue.Pond&&q.request.requestDataPondId===n.dataPondId&&(q.request=W(Q({},Z(n.dataPondRequestConfig)),{requestDataPondId:n.dataPondId}))}),window.$message.success("保存成功！")):window.$message.error("编辑失败，请稍后重试！")}catch(r){window.$message.error("编辑失败，请稍后重试！")}},N=n=>{try{k.value?(p.value.unshift(n),window.$message.success("创建成功!")):window.$message.error("创建失败，请稍后重试!")}catch(r){window.$message.error("创建失败，请稍后重试!")}},B=n=>{me({message:"删除数据后，需手动处理使用改接口的组件，是否继续？",isMaskClosable:!0,transformOrigin:"center",onPositiveCallback:()=>{const r=p.value.findIndex(q=>q.dataPondId===n.dataPondId);r!==-1?(p.value.splice(r,1),window.$message.success("删除成功!")):window.$message.error("删除失败，请稍后重试！")}})},I=()=>{w.value&&(f.value.request=W(Q({},Z(w.value.dataPondRequestConfig)),{requestDataPondId:w.value.dataPondId})),g("update:modelShow",!1),g("sendHandle")};return(n,r)=>{const q=o("n-text"),S=o("n-icon"),T=o("n-button"),h=o("n-space"),R=o("n-card"),E=o("n-modal");return u(),C(J,null,[e(E,{class:"go-chart-data-pond-control",show:m.value,"onUpdate:show":r[1]||(r[1]=Y=>m.value=Y),"mask-closable":!1},{default:t(()=>[e(R,{bordered:!1,role:"dialog",size:"small","aria-modal":"true",style:{width:"900px",height:"650px"}},{header:t(()=>[]),"header-extra":t(()=>[]),action:t(()=>[e(h,{justify:"space-between"},{default:t(()=>[e(T,{type:"info",secondary:"",disabled:!w.value,onClick:r[0]||(r[0]=Y=>b(!0))},{icon:t(()=>[e(S,null,{default:t(()=>[e(a(P))]),_:1})]),default:t(()=>[y(" 编辑内容 ")]),_:1},8,["disabled"]),e(T,{type:"primary",onClick:I},{default:t(()=>[y("保存 & 发送请求")]),_:1})]),_:1})]),default:t(()=>[_("div",lt,[w.value&&!D.value?(u(),U(a(ot),{key:0,targetData:w.value,globalData:a(x).getRequestGlobalConfig},null,8,["targetData","globalData"])):(u(),C("div",st,[_("img",{src:a(Re),alt:"暂无数据"},null,8,dt),e(q,{depth:3},{default:t(()=>[y("暂未选择公共接口")]),_:1})])),e(a(Ye),{onCreatePond:H,onDeletePond:B})])]),_:1})]),_:1},8,["show"]),e(a(Ne),{modelShow:d.value,"onUpdate:modelShow":r[2]||(r[2]=Y=>d.value=Y),targetDataRequest:k.value,isEdit:i.value,onEditSaveHandle:$},null,8,["modelShow","targetDataRequest","isEdit"])],64)}}});const ct=V(rt,[["__scopeId","data-v-f251a5df"]]),ut={class:"go-chart-data-pond"},it={class:"go-absolute-center"},_t=L({__name:"index",setup(l){_e(I=>({"5189590e":v.value}));const c=pe(),{HelpOutlineIcon:s,FlashIcon:g,PulseIcon:P,FishIcon:x}=te.ionicons5,{targetData:f,chartEditStore:p}=oe(),{requestDataPond:d,requestInterval:m,requestIntervalUnit:D}=ee(p.getRequestGlobalConfig),i=O(!1),k=O(!1),w=O(!1);let b=0,H;const $=G(()=>{const I=f.value.request.requestDataPondId;return I?d.value.filter(r=>I===r.dataPondId)[0]:void 0}),v=G(()=>c.getAppTheme),N=()=>{k.value=!0},B=()=>de(this,null,function*(){var I;if(!((I=f.value)!=null&&I.request)){window.$message.warning("请选择一个公共接口！");return}i.value=!0;try{const n=yield ke(Z(f.value.request),Z(p.getRequestGlobalConfig));if(i.value=!1,n){if(!(n!=null&&n.data)&&!f.value.filter){window.$message.warning("您的数据不符合默认格式，请配置过滤器！"),w.value=!0;return}f.value.option.dataset=Ce(n==null?void 0:n.data,n,f.value.filter),w.value=!0;return}window.$message.warning("没有拿到返回值，请检查接口！")}catch(n){console.error(n),i.value=!1,window.$message.warning("数据异常，请检查参数！")}});return De(()=>{var n;const I=(n=f.value)==null?void 0:n.filter;H!==I&&b&&(H=I,B()),b++}),Ie(()=>{H=null}),(I,n)=>{const r=o("n-icon"),q=o("n-input"),S=o("n-button"),T=o("n-card"),h=o("n-tooltip"),R=o("go-skeleton");return u(),C(J,null,[_("div",ut,[e(T,{class:"n-card-shallow"},{default:t(()=>[e(a(z),{name:"请求名称",alone:!0},{default:t(()=>{var E;return[e(q,{size:"small",placeholder:((E=$.value)==null?void 0:E.dataPondName)||"暂无",disabled:!0},{prefix:t(()=>[e(r,{component:a(x)},null,8,["component"])]),_:1},8,["placeholder"])]}),_:1}),e(a(z),{name:"接口地址",alone:!0},{default:t(()=>{var E;return[e(q,{size:"small",placeholder:((E=$.value)==null?void 0:E.dataPondRequestConfig.requestUrl)||"暂无",disabled:!0},{prefix:t(()=>[e(r,{component:a(g)},null,8,["component"])]),_:1},8,["placeholder"])]}),_:1}),_("div",{class:"edit-text",onClick:N},[_("div",it,[e(S,{type:"primary",secondary:""},{default:t(()=>[y("编辑配置")]),_:1})])])]),_:1})]),e(a(z),{alone:!0},{name:t(()=>[y(" 测试 "),e(h,{trigger:"hover"},{trigger:t(()=>[e(r,{size:"21",depth:3},{default:t(()=>[e(a(s))]),_:1})]),default:t(()=>[y(" 默认赋值给 dataset 字段 ")]),_:1})]),default:t(()=>[e(S,{type:"primary",ghost:"",onClick:B},{icon:t(()=>[e(r,null,{default:t(()=>[e(a(g))]),_:1})]),default:t(()=>[y(" 发送请求 ")]),_:1})]),_:1}),e(a(He),{show:w.value&&!i.value,ajax:!0},null,8,["show"]),e(R,{load:i.value,repeat:3},null,8,["load"]),e(a(ct),{modelShow:k.value,"onUpdate:modelShow":n[0]||(n[0]=E=>k.value=E),onSendHandle:B},null,8,["modelShow"])],64)}}});const Et=V(_t,[["__scopeId","data-v-19fefd48"]]);export{Et as default};
