<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('预览')}">预览</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link href="/assets/bootstrapk/css/bootstrap.min.css" rel="stylesheet">
    <link href="/assets/textview/highlight.min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
    <style>
        * {
            margin: 0;
            padding: 0;
        }
        html, body {
            height: 100%;
            width: 100%;
        }
    </style>
</head>
<body>
<div style="height: 50px;"></div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-heading">
            <h4 class="panel-title">
                <span data-toggle="collapse" data-parent="#accordion" href="#collapseOne">
                   文本内容
                </span>
            </h4>
        </div>
        <div class="panel-body">
            <div id="fileText" class='code'></div>
        </div>
    </div>
</div>

</body>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var ID = [[${id}]];
    var TYPE = [[${type}]];
    var AUTH_PREFIX="oa_netdisk_view_txt";
</script>
<script th:src="'/assets/bootstrapk/js/bootstrap.min.js?'+${cacheKey}"></script>
<!--<script th:src="'/assets/txtview/base64.min.js?'+${cacheKey}"></script>-->
<script th:src="'/assets/textview/highlight.min.js?'+${cacheKey}"></script>
<script th:src="'/business/oa/netdisk_file/view_txt_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/oa/netdisk_file/view_txt.js?'+${cacheKey}"></script>
<script>
    var url="/service-oa/oa-netdisk-virtual-fd/download-by-id?id="+ID+"&fdType=file";
    var jsContainer = document.getElementById('fileText');
    function readTxt() {
        var xhr = new XMLHttpRequest();
        xhr.open('get', url, true);
        xhr.send();
        xhr.onreadystatechange = function () {
            console.log(xhr);
            if (xhr.readyState == 4 && xhr.status == 200) {
                //alert("请求服务器数据成功且返回数据成功！");
                jsContainer.innerText = xhr.responseText;
            }
        };
    }
    readTxt();
</script>
</body>
</html>