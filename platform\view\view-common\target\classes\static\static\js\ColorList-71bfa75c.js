import{d as m,r as p,o as a,c,F as C,s as y,e as n,w as o,q as s,an as r,t,f as i,u as B,j as R}from"./index-bb2cbf17.js";const G=[{CMYK:[62,0,21,16],RGB:[81,214,169],hex:"#51d6a9",name:"碧空绿",pinyin:"bikonlv"},{CMYK:[73,17,20,1],RGB:[102,169,201],hex:"#66a9c9",name:"涧石蓝",pinyin:"jianshilan"},{CMYK:[4,13,67,0],RGB:[248,223,114],hex:"#f8df72",name:"茉莉黄",pinyin:"molihuang"},{CMYK:[76,51,0,0],RGB:[60,126,255],hex:"#3c7eff",name:"深海蓝",pinyin:"shenh<PERSON>an"}],$={class:"content-left"},b=["onClick"],S={class:"Pinyin-upper"},q=["onClick"],K={class:"Pinyin-upper"},L=m({__name:"ColorList",props:{designColor:{type:Object,required:!0}},emits:["colorSelectHandle"],setup(v,{emit:x}){const g=x,u=f=>{g("colorSelectHandle",f)};return(f,M)=>{const l=p("n-space"),d=p("n-divider"),h=p("n-text");return a(),c("div",$,[(a(!0),c(C,null,y(B(G),(e,_)=>(a(),c("div",{class:"content-left-item go-transition-quick go-mb-0",span:"12 1000:6 1400:4 1800:4 2200:2",key:_,onClick:k=>u(e)},[n(l,null,{default:o(()=>[s("div",{class:"content-left-item-color",style:r({backgroundColor:e.hex})},null,4),n(l,{vertical:""},{default:o(()=>[n(l,null,{default:o(()=>[s("span",{style:r({color:e.hex})},t(e.name),5),s("span",S,t(e.pinyin.toUpperCase()),1)]),_:2},1024),n(h,null,{default:o(()=>[i(t(e.hex)+" ",1),n(d,{vertical:""}),i(" "+t(`rgb(${e.RGB[0]}, ${e.RGB[1]}, ${e.RGB[2]})`),1)]),_:2},1024)]),_:2},1024)]),_:2},1024)],8,b))),128)),n(d),(a(!0),c(C,null,y(v.designColor,(e,_)=>(a(),c("div",{class:"content-left-item go-transition-quick",span:"12 1000:6 1400:4 1800:4 2200:2",key:_,onClick:k=>u(e)},[n(l,null,{default:o(()=>[s("div",{class:"content-left-item-color",style:r({backgroundColor:e.hex})},null,4),n(l,{vertical:""},{default:o(()=>[n(l,null,{default:o(()=>[s("span",{style:r({color:e.hex})},t(e.name),5),s("span",K,t(e.pinyin.toUpperCase()),1)]),_:2},1024),n(h,null,{default:o(()=>[i(t(e.hex)+" ",1),n(d,{vertical:""}),i(" "+t(`rgb(${e.RGB[0]}, ${e.RGB[1]}, ${e.RGB[2]})`),1)]),_:2},1024)]),_:2},1024)]),_:2},1024)],8,q))),128))])}}});const j=R(L,[["__scopeId","data-v-e8d4af24"]]);export{j as default};
