var n=Object.defineProperty,s=Object.defineProperties;var d=Object.getOwnPropertyDescriptors;var e=Object.getOwnPropertySymbols;var g=Object.prototype.hasOwnProperty,C=Object.prototype.propertyIsEnumerable;var t=(r,p,i)=>p in r?n(r,p,{enumerable:!0,configurable:!0,writable:!0,value:i}):r[p]=i,f=(r,p)=>{for(var i in p||(p={}))g.call(p,i)&&t(r,i,p[i]);if(e)for(var i of e(p))C.call(p,i)&&t(r,i,p[i]);return r},l=(r,p)=>s(r,d(p));var o=(r,p,i)=>(t(r,typeof p!="symbol"?p+"":p,i),i);import{aM as h,a8 as a}from"./index-bb2cbf17.js";import{d as c}from"./chartEditStore-55fbe93c.js";import{F as m}from"./index-0ec04aee.js";import"./plugin-3ef0fcec.js";import"./icon-f36697ff.js";import"./SettingItem-7fe1cfec.js";/* empty css                                                                      */import"./SettingItemBox-500aaf18.js";import"./CollapseItem.vue_vue_type_script_setup_true_lang-9b6d8958.js";import"./index.esm.min-f1367f57.js";import"./http-36f53bd1.js";import"./lodash-d17632fd.js";import"./fileTypeEnum-21359a08.js";const F={dataset:3234,flipperLength:6,flipperBgColor:"#16293E",flipperTextColor:"#4A9EF8FF",flipperWidth:30,flipperHeight:50,flipperRadius:5,flipperGap:10,flipperType:"down",flipperSpeed:450,flipperBorderWidth:0};class G extends c{constructor(){super(...arguments);o(this,"key",m.key);o(this,"attr",l(f({},h),{w:300,h:100,zIndex:-1}));o(this,"chartConfig",a(m));o(this,"option",a(F))}}export{G as default,F as option};
