import{d as X,l as Y,r as u,o as m,c as g,Q as I,R as N,q as V,e as t,w as a,f,t as j,u as o,F as p,s as q,E as y,ao as E,an as L,a5 as S,j as M}from"./index-bb2cbf17.js";import{B as T}from"./chartEditStore-55fbe93c.js";import{S as s}from"./SettingItem-7fe1cfec.js";import{S as i}from"./SettingItemBox-500aaf18.js";import{_ as Z}from"./CollapseItem.vue_vue_type_script_setup_true_lang-9b6d8958.js";import{i as D}from"./icon-f36697ff.js";import{l as G}from"./logo-aa8b8747.js";/* empty css                                                                      */const O={key:0,class:"preset-filter"},H=X({__name:"StylesSetting",props:{isGroup:{type:Boolean,required:!1},isCanvas:{type:Boolean,default:!1},chartStyles:{type:Object,required:!0}},setup(e){const{HelpOutlineIcon:k}=D.ionicons5,v=r=>`${(parseFloat(r)*100).toFixed(0)}%`,F=r=>`${r}deg`,h=Y([]);for(let r=1;r<=12;r++)h.value.push({index:r,src:G,hueRotate:r*30});return(r,n)=>{const C=u("n-divider"),x=u("n-tag"),b=u("n-switch"),d=u("n-slider"),U=u("n-image"),w=u("n-text"),z=u("n-icon"),R=u("n-tooltip"),B=u("n-select"),c=u("n-input-number");return m(),g(p,null,[I(V("div",null,[t(C,{"n-divider":"",style:{margin:"10px 0"}}),t(x,{type:"warning"},{default:a(()=>[f(" 解散分组「 "+j(e.isCanvas?"滤镜":"滤镜 / 变换")+" 」也将消失!",1)]),_:1})],512),[[N,e.isGroup]]),t(o(Z),{name:e.isCanvas?"滤镜":"滤镜 / 变换"},{header:a(()=>[t(b,{value:e.chartStyles.filterShow,"onUpdate:value":n[0]||(n[0]=l=>e.chartStyles.filterShow=l),size:"small"},null,8,["value"])]),default:a(()=>[t(o(i),{name:"色相",alone:!0},{default:a(()=>[t(o(s),{name:`值：${e.chartStyles.hueRotate}deg`},{default:a(()=>[t(d,{value:e.chartStyles.hueRotate,"onUpdate:value":n[1]||(n[1]=l=>e.chartStyles.hueRotate=l),step:3,min:0,max:360,"format-tooltip":F},null,8,["value"])]),_:1},8,["name"])]),_:1}),t(o(i),{name:"饱和度",alone:!0},{default:a(()=>[t(o(s),{name:`值：${(parseFloat(String(e.chartStyles.saturate))*100).toFixed(0)}%`},{default:a(()=>[t(d,{value:e.chartStyles.saturate,"onUpdate:value":n[2]||(n[2]=l=>e.chartStyles.saturate=l),step:.1,min:0,max:2,"format-tooltip":v},null,8,["value"])]),_:1},8,["name"])]),_:1}),t(o(i),{name:"对比度",alone:!0},{default:a(()=>[t(o(s),{name:`值：${(parseFloat(String(e.chartStyles.contrast))*100).toFixed(0)}%`},{default:a(()=>[t(d,{value:e.chartStyles.contrast,"onUpdate:value":n[3]||(n[3]=l=>e.chartStyles.contrast=l),step:.1,min:0,max:2,"format-tooltip":v},null,8,["value"])]),_:1},8,["name"])]),_:1}),t(o(i),{name:"亮度",alone:!0},{default:a(()=>[t(o(s),{name:`值：${(parseFloat(String(e.chartStyles.brightness))*100).toFixed(0)}%`},{default:a(()=>[t(d,{value:e.chartStyles.brightness,"onUpdate:value":n[4]||(n[4]=l=>e.chartStyles.brightness=l),step:.1,min:0,max:2,"format-tooltip":v},null,8,["value"])]),_:1},8,["name"])]),_:1}),t(o(i),{name:"透明度",alone:!0},{default:a(()=>[t(o(s),{name:`值：${(parseFloat(String(e.chartStyles.opacity))*100).toFixed(0)}%`},{default:a(()=>[t(d,{value:e.chartStyles.opacity,"onUpdate:value":n[5]||(n[5]=l=>e.chartStyles.opacity=l),step:.1,min:0,max:1,"format-tooltip":v},null,8,["value"])]),_:1},8,["name"])]),_:1}),h.value.length?(m(),g("div",O,[(m(!0),g(p,null,q(h.value,(l,$)=>(m(),y(U,{class:E(["preset-img",{"active-preset":l.hueRotate===e.chartStyles.hueRotate}]),width:"46","preview-disabled":"","object-fit":"scale-down",key:$,style:L({filter:`hue-rotate(${l.hueRotate}deg)`}),src:l.src,onClick:()=>e.chartStyles.hueRotate=l.hueRotate},null,8,["class","style","src","onClick"]))),128))])):S("",!0),e.isCanvas?S("",!0):(m(),y(o(i),{key:1,alone:!0},{name:a(()=>[t(w,null,{default:a(()=>[f("混合")]),_:1}),t(R,{trigger:"hover"},{trigger:a(()=>[t(z,{size:"21",depth:3},{default:a(()=>[t(o(k))]),_:1})]),default:a(()=>[t(w,null,{default:a(()=>[f("视频组件需要底色透明一般选中滤色")]),_:1})]),_:1})]),default:a(()=>[t(o(s),null,{default:a(()=>[t(B,{value:e.chartStyles.blendMode,"onUpdate:value":n[6]||(n[6]=l=>e.chartStyles.blendMode=l),size:"small",filterable:"",options:o(T)},null,8,["value","options"])]),_:1})]),_:1})),e.isCanvas?S("",!0):(m(),y(o(i),{key:2,name:"旋转°"},{default:a(()=>[t(o(s),{name:"Z轴(平面) - 旋转"},{default:a(()=>[t(c,{value:e.chartStyles.rotateZ,"onUpdate:value":n[7]||(n[7]=l=>e.chartStyles.rotateZ=l),min:0,max:360,size:"small",placeholder:"角度"},null,8,["value"])]),_:1}),t(o(s),{name:"X轴 - 旋转"},{default:a(()=>[t(c,{value:e.chartStyles.rotateX,"onUpdate:value":n[8]||(n[8]=l=>e.chartStyles.rotateX=l),min:0,max:360,size:"small",placeholder:"角度"},null,8,["value"])]),_:1}),t(o(s),{name:"Y轴 - 旋转"},{default:a(()=>[t(c,{value:e.chartStyles.rotateY,"onUpdate:value":n[9]||(n[9]=l=>e.chartStyles.rotateY=l),min:0,max:360,size:"small",placeholder:"角度"},null,8,["value"])]),_:1})]),_:1})),e.isCanvas?S("",!0):(m(),y(o(i),{key:3,name:"倾斜°"},{default:a(()=>[t(o(s),{name:"X轴 - 倾斜"},{default:a(()=>[t(c,{value:e.chartStyles.skewX,"onUpdate:value":n[10]||(n[10]=l=>e.chartStyles.skewX=l),min:0,max:360,size:"small",placeholder:"角度"},null,8,["value"])]),_:1}),t(o(s),{name:"Y轴 - 倾斜"},{default:a(()=>[t(c,{value:e.chartStyles.skewY,"onUpdate:value":n[11]||(n[11]=l=>e.chartStyles.skewY=l),min:0,max:360,size:"small",placeholder:"角度"},null,8,["value"])]),_:1})]),_:1})),t(x,{type:"warning"},{default:a(()=>[f(" 若预览时大屏模糊，可以尝试关闭滤镜进行修复 ")]),_:1})]),_:1},8,["name"])],64)}}}),te=M(H,[["__scopeId","data-v-9b37593c"]]);export{te as S};
