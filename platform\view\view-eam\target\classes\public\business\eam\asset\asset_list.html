<!--
/**
 * 资产 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2024-06-17 08:45:53
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('资产')}">资产</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 资产分类 , categoryId ,typeName=select_box, isHideInSearch=true -->
                    <!-- 分类编码 , categoryCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 流程ID , procId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 办理状态 , status ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('办理状态')}" class="search-label status-label">办理状态</span><span class="search-colon">:</span></div>
                        <div id="status" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}" style="width:140px" extraParam="{}"></div>
                    </div>
                    <!-- 批次编码 , batchCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 归属 , ownerCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 是否显示 , display ,typeName=text_input, isHideInSearch=true -->
                    <!-- 是否清理 , cleanOut ,typeName=text_input, isHideInSearch=true -->
                    <!-- 清理时间 , cleanTime ,typeName=date_input, isHideInSearch=true -->
                    <!-- 物品档案 , goodsId ,typeName=select_box, isHideInSearch=true -->
                    <!-- 标准型号物品图片 , pictureId ,typeName=upload, isHideInSearch=true -->
                    <!-- 计量单位 , unit ,typeName=text_input, isHideInSearch=true -->
                    <!-- 使用期限 , serviceLife ,typeName=number_input, isHideInSearch=true -->
                    <!-- 安全等级 , safetyLevelCode ,typeName=select_box, isHideInSearch=true -->
                    <!-- 存放区域 , regionId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 详细位置 , positionDetail ,typeName=text_input, isHideInSearch=true -->
                    <!-- 仓库 , warehouseId ,typeName=select_box, isHideInSearch=true -->
                    <!-- 物品档案 , goodsStockId ,typeName=select_box, isHideInSearch=true -->
                    <!-- 资产数量 , assetNumber ,typeName=number_input, isHideInSearch=true -->
                    <!-- 剩余数量 , remainNumber ,typeName=number_input, isHideInSearch=true -->
                    <!-- 生产日期 , productionDate ,typeName=date_input, isHideInSearch=true -->
                    <!-- 入账日期 , registerDate ,typeName=date_input, isHideInSearch=true -->
                    <!-- RFID标签 , rfid ,typeName=text_input, isHideInSearch=true -->
                    <!-- 附件 , attach ,typeName=upload, isHideInSearch=true -->
                    <!-- 最近核对日期 , lastVerificationDate ,typeName=date_input, isHideInSearch=true -->
                    <!-- 用途 , purpose ,typeName=text_input, isHideInSearch=true -->
                    <!-- 维保厂商 , maintainerName ,typeName=text_input, isHideInSearch=true -->
                    <!-- 维保状态 , maintenanceStatus ,typeName=select_box, isHideInSearch=true -->
                    <!-- 维保价格 , maintenancePrice ,typeName=number_input, isHideInSearch=true -->
                    <!-- 维保方式 , maintenanceMethod ,typeName=select_box, isHideInSearch=true -->
                    <!-- 建议维保方式 , suggestMaintenanceMethod ,typeName=select_box, isHideInSearch=true -->
                    <!-- 联系人 , contacts ,typeName=text_input, isHideInSearch=true -->
                    <!-- 联系方式 , contactInformation ,typeName=text_input, isHideInSearch=true -->
                    <!-- 负责人 , director ,typeName=text_input, isHideInSearch=true -->
                    <!-- 维保开始时间 , maintenanceStartDate ,typeName=date_input, isHideInSearch=true -->
                    <!-- 维保到期时间 , maintenanceEndDate ,typeName=date_input, isHideInSearch=true -->
                    <!-- 维保备注 , maintenanceNotes ,typeName=text_area, isHideInSearch=true -->
                    <!-- 财务分类 , financialCategoryId ,typeName=select_box, isHideInSearch=true -->
                    <!-- 财务编号 , financialCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 财务选项 , financialOption ,typeName=select_box, isHideInSearch=true -->
                    <!-- 费用项目 , expenseItem ,typeName=select_box, isHideInSearch=true -->
                    <!-- 供应商 , supplierId ,typeName=select_box, isHideInSearch=true -->
                    <!-- 客户信息 , customerInfo ,typeName=text_input, isHideInSearch=true -->
                    <!-- 含税总值 , taxAmountPrice ,typeName=number_input, isHideInSearch=true -->
                    <!-- 未税总值 , totalAmountPrice ,typeName=number_input, isHideInSearch=true -->
                    <!-- 含税单价 , purchaseUnitPrice ,typeName=number_input, isHideInSearch=true -->
                    <!-- 资产原值 , originalUnitPrice ,typeName=number_input, isHideInSearch=true -->
                    <!-- 资产净值 , navPrice ,typeName=number_input, isHideInSearch=true -->
                    <!-- 已用期限 , assetUsedServiceLife ,typeName=number_input, isHideInSearch=true -->
                    <!-- 最后折旧 , depreciationId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 最后折旧时间 , depreciationOperTime ,typeName=date_input, isHideInSearch=true -->
                    <!-- 残值率 , residualsRate ,typeName=number_input, isHideInSearch=true -->
                    <!-- 残值 , residualsPrice ,typeName=number_input, isHideInSearch=true -->
                    <!-- 税额 , taxAmountRate ,typeName=number_input, isHideInSearch=true -->
                    <!-- 本年折旧 , currentYearDepreciation ,typeName=number_input, isHideInSearch=true -->
                    <!-- 折旧年份 , depreciationYear ,typeName=number_input, isHideInSearch=true -->
                    <!-- 累计折旧 , accumulatedDepreciation ,typeName=number_input, isHideInSearch=true -->
                    <!-- 月折金额 , monthDepreciationPrice ,typeName=number_input, isHideInSearch=true -->
                    <!-- 登记时间 , entryTime ,typeName=date_input, isHideInSearch=true -->
                    <!-- 财务备注 , financialNotes ,typeName=text_area, isHideInSearch=true -->
                    <!-- 设备编号 , equipmentCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 启停状态 , equipmentStatus ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 设备CPU , equipmentCpu ,typeName=text_input, isHideInSearch=true -->
                    <!-- 设备内存 , equipmentMemory ,typeName=text_input, isHideInSearch=true -->
                    <!-- 设备配置 , equipmentConf ,typeName=text_area, isHideInSearch=true -->
                    <!-- 设备序列号 , equipmentSerialNumber ,typeName=text_input, isHideInSearch=true -->
                    <!-- 机柜 , rackId ,typeName=select_box, isHideInSearch=true -->
                    <!-- 设备机柜上位置 , rackUpNumber ,typeName=number_input, isHideInSearch=true -->
                    <!-- 设备机柜下位置 , rackDownNumber ,typeName=number_input, isHideInSearch=true -->
                    <!-- 短标签1 , label ,typeName=text_input, isHideInSearch=true -->
                    <!-- 短标签3 , label3 ,typeName=text_area, isHideInSearch=true -->
                    <!-- 短标签5 , label5 ,typeName=text_input, isHideInSearch=true -->
                    <!-- 长标签4 , label4 ,typeName=text_area, isHideInSearch=true -->
                    <!-- 长标签2 , label2 ,typeName=text_area, isHideInSearch=true -->
                    <!-- 单据 , billId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 巡检时间 , lastInspectTime ,typeName=date_input, isHideInSearch=true -->
                    <!-- 经度数据 , longitude ,typeName=number_input, isHideInSearch=true -->
                    <!-- 维度数据 , dimension ,typeName=number_input, isHideInSearch=true -->
                    <!-- 内部控制标签 , internalControlLabel ,typeName=text_input, isHideInSearch=true -->
                    <!-- 领用ID , collectionId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 借用ID , borrowId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 报废ID , scrapId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 处置ID , handleId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 修改人ID , updateBy ,typeName=text_input, isHideInSearch=true -->
                    <!-- 制单人 , originatorId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 变更类型 , chsType ,typeName=text_input, isHideInSearch=true -->
                    <!-- 变更状态 , chsStatus ,typeName=text_input, isHideInSearch=true -->
                    <!-- 变更版本号 , chsVersion ,typeName=text_input, isHideInSearch=true -->
                    <!-- 变更ID , changeInstanceId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 流程概要 , summary ,typeName=text_input, isHideInSearch=true -->
                    <!-- 最后审批人账户ID , latestApproverId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 最后审批人姓名 , latestApproverName ,typeName=text_input, isHideInSearch=true -->
                    <!-- 下一节点审批人 , nextApproverIds ,typeName=text_input, isHideInSearch=true -->
                    <!-- 下一个审批节点审批人姓名 , nextApproverNames ,typeName=text_input, isHideInSearch=true -->
                    <!-- 审批意见 , approvalOpinion ,typeName=text_input, isHideInSearch=true -->
                    <!-- 选择 , assetSelectedData ,typeName=text_input, isHideInSearch=true -->
                    <!-- catalogCodeValue , catalogCodeValue ,typeName=text_input, isHideInSearch=true -->
                    <!-- 资产状态 , assetStatus ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('资产状态')}" class="search-label assetStatus-label">资产状态</span><span class="search-colon">:</span></div>
                        <div id="assetStatus" th:data="${'/service-eam/eam-asset-status/query-list'}" style="width:140px" extraParam="{}"></div>
                    </div>
                    <!-- 资产编号 , assetCode ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('资产编号')}" class="search-label assetCode-label">资产编号</span><span class="search-colon">:</span></div>
                        <input id="assetCode" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>
                    <!-- 来源 , sourceId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('来源')}" class="search-label sourceId-label">来源</span><span class="search-colon">:</span></div>
                        <div id="sourceId" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_source'}" style="width:140px" extraParam="{}"></div>
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 名称 , name ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('名称')}" class="search-label name-label">名称</span><span class="search-colon">:</span></div>
                        <input id="name" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>
                    <!-- 规格型号 , model ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('规格型号')}" class="search-label model-label">规格型号</span><span class="search-colon">:</span></div>
                        <input id="model" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>
                    <!-- 序列号 , serialNumber ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('序列号')}" class="search-label serialNumber-label">序列号</span><span class="search-colon">:</span></div>
                        <input id="serialNumber" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>
                    <!-- 资产备注 , assetNotes ,typeName=text_area, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('资产备注')}" class="search-label assetNotes-label">资产备注</span><span class="search-colon">:</span></div>
                        <input id="assetNotes" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 所属公司 , ownCompanyId ,typeName=button, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('所属公司')}" class="search-label ownCompanyId-label">所属公司</span><span class="search-colon">:</span></div>
                            <input lay-filter="ownCompanyId" id="ownCompanyId" name="ownCompanyId"  type="hidden" class="layui-input"   />
                            <button id="ownCompanyId-button" type="button" action-type="org-dialog" class="layui-btn layui-btn-primary   " style="width: 140px"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择公司')}" th:default-label="${lang.translate('请选择公司')}">按钮文本</span></button>
                    </div>
                    <!-- 使用公司/部门 , useOrganizationId ,typeName=button, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('使用公司/部门')}" class="search-label useOrganizationId-label">使用公司/部门</span><span class="search-colon">:</span></div>
                            <input lay-filter="useOrganizationId" id="useOrganizationId" name="useOrganizationId"  type="hidden" class="layui-input"   />
                            <button id="useOrganizationId-button" type="button" action-type="org-dialog" class="layui-btn layui-btn-primary   " style="width: 140px"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择组织节点')}" th:default-label="${lang.translate('请选择组织节点')}">按钮文本</span></button>
                    </div>
                    <!-- 管理人员 , managerId ,typeName=button, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('管理人员')}" class="search-label managerId-label">管理人员</span><span class="search-colon">:</span></div>
                            <input lay-filter="managerId" id="managerId" name="managerId"  type="hidden" class="layui-input"   />
                            <button id="managerId-button" type="button" action-type="emp-dialog" class="layui-btn layui-btn-primary   " style="width: 140px"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择人员')}" th:default-label="${lang.translate('请选择人员')}">按钮文本</span></button>
                    </div>
                    <!-- 使用人员 , useUserId ,typeName=button, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('使用人员')}" class="search-label useUserId-label">使用人员</span><span class="search-colon">:</span></div>
                            <input lay-filter="useUserId" id="useUserId" name="useUserId"  type="hidden" class="layui-input"   />
                            <button id="useUserId-button" type="button" action-type="emp-dialog" class="layui-btn layui-btn-primary   " style="width: 140px"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择人员')}" th:default-label="${lang.translate('请选择人员')}">按钮文本</span></button>
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 位置 , positionId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('位置')}" class="search-label positionId-label">位置</span><span class="search-colon">:</span></div>
                        <div id="positionId" th:data="${'/service-eam/eam-position/query-paged-list'}" style="width:140px" extraParam="{}"></div>
                    </div>
                    <!-- 业务编号 , businessCode ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('业务编号')}" class="search-label businessCode-label">业务编号</span><span class="search-colon">:</span></div>
                        <input id="businessCode" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>
                    <!-- 厂商 , manufacturerId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('厂商')}" class="search-label manufacturerId-label">厂商</span><span class="search-colon">:</span></div>
                        <div id="manufacturerId" th:data="${'/service-eam/eam-manufacturer/query-list'}" style="width:140px" extraParam="{}"></div>
                    </div>
                    <!-- 维保商 , maintainerId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('维保商')}" class="search-label maintainerId-label">维保商</span><span class="search-colon">:</span></div>
                        <div id="maintainerId" th:data="${'/service-eam/eam-maintainer/query-list'}" style="width:140px" extraParam="{}"></div>
                    </div>
                    <!-- 采购日期 , purchaseDate ,typeName=date_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('采购日期')}" class="search-label purchaseDate-label">采购日期</span><span class="search-colon">:</span></div>
                            <input type="text" id="purchaseDate-begin" style="width: 140px" lay-verify="date" th:placeholder="${lang.translate('开始日期')}" autocomplete="off" class="layui-input search-input search-date-input"  readonly >
                            <span class="search-dash">-</span>
                            <input type="text" id="purchaseDate-end"  style="width: 140px"  lay-verify="date" th:placeholder="${lang.translate('结束日期')}" autocomplete="off" class="layui-input search-input search-date-input" readonly>
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 运行环境 , equipmentEnvironmentCode ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('运行环境')}" class="search-label equipmentEnvironmentCode-label">运行环境</span><span class="search-colon">:</span></div>
                        <div id="equipmentEnvironmentCode" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_equipment_environment'}" style="width:140px" extraParam="{}"></div>
                    </div>
                    <!-- 管理IP , manageIp ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('管理IP')}" class="search-label manageIp-label">管理IP</span><span class="search-colon">:</span></div>
                        <input id="manageIp" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>
                    <!-- 设备IP , equipmentIp ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('设备IP')}" class="search-label equipmentIp-label">设备IP</span><span class="search-colon">:</span></div>
                        <input id="equipmentIp" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>
                    <!-- 设备标签 , equipmentLabel ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('设备标签')}" class="search-label equipmentLabel-label">设备标签</span><span class="search-colon">:</span></div>
                        <input id="equipmentLabel" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 清理类型 , cleanOutType ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('清理类型')}" class="search-label cleanOutType-label">清理类型</span><span class="search-colon">:</span></div>
                        <div id="cleanOutType" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetOperateCleanOutEnum')}" style="width:140px" extraParam="{}"></div>
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索','','cmp:table.search')}">搜索</span></button>
                <button id="search-button-advance" class="layui-btn layui-btn-primary icon-btn search-button-advance"><i class="layui-icon">&#xe671;</i><span th:text="${lang.translate('更多','','cmp:table.search')}">更多</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 42px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${perm.checkAuth('eam_asset:create')}" id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建','','cmp:table.button')}">新建</span></button>
        <button th:if="${perm.checkAuth('eam_asset:delete-by-ids')}" id="delete-button" class="layui-btn icon-btn layui-btn-danger layui-btn-sm batch-delete-button " lay-event="batch-del"><i class="layui-icon">&#xe67e;</i><span th:text="${lang.translate('删除','','cmp:table.button')}">删除</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${perm.checkAuth('eam_asset:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view"  data-id="{{d.id}}"> <span th:text="${lang.translate('查看','','cmp:table.ops')}">查看</span></button>
    <button th:if="${perm.checkAnyAuth('eam_asset:update','eam_asset:save')}" class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit"data-id="{{d.id}}"><span th:text="${lang.translate('修改','','cmp:table.ops')}">修改</span></button>


    <button th:if="${perm.checkAuth('eam_asset:delete')}" class="layui-btn layui-btn-xs layui-btn-danger ops-delete-button " lay-event="del" data-id="{{d.id}}"><span th:text="${lang.translate('删除','','cmp:table.ops')}">删除</span></button>

    <button class="layui-btn layui-btn-xs " lay-event="asset-data-change" data-id="{{d.id}}"><span th:text="${lang.translate('变更','','cmp:table.ops')}">变更</span></button>

    <button class="layui-btn layui-btn-xs layui-btn-primary layui-border-blue" lay-event="ops-more" data-id="{{d.id}}"><span th:text="${lang.translate('更多','','cmp:table.ops')}">更多</span><i class="layui-icon layui-icon-down"></i></button>
</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}]];
    var SELECT_CLEANOUTTYPE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetOperateCleanOutEnum')}]];
    var RADIO_EQUIPMENTSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetEquipmentStatusEnum')}]];
    var AUTH_PREFIX="eam_asset";


</script>

<script th:src="'/business/eam/asset/asset_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset/asset_list.js?'+${cacheKey}"></script>

</body>
</html>