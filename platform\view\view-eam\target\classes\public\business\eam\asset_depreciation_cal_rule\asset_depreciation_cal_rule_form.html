<!--
/**
 * 计算方法 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2023-10-18 06:50:19
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
	<title th:text="${lang.translate('计算方法')}">计算方法</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="opacity:0">


         <!--开始：group 循环-->


        <div class="layui-row form-row" id="default-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column" >

                <!-- text_input : 主键 ,  id -->
                <div class="layui-form-item" style="display: none"inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('主键')}">主键</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="id" id="id" name="id" th:placeholder="${ lang.translate('请输入'+'主键') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- select_box : 折旧方案 ,  depreciationId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('折旧方案')}">折旧方案</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <div id="depreciationId" input-type="select" th:data="${'/service-eam/eam-asset-depreciation/query-paged-list'}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- radio_box : 状态 ,  status  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('状态')}">状态</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input input-type="radio" type="radio" name="status" lay-filter="status" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==-1)}">
                    </div>
                </div>
            
                <!-- radio_box : 动作 ,  actionCode  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('动作')}">动作</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input input-type="radio" type="radio" name="actionCode" lay-filter="actionCode" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.eam.AssetDepreciationRuleActionCodeEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==-1)}">
                    </div>
                </div>
            
                <!-- number_input : 规则编号 ,  ruleNumber  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('规则编号')}">规则编号</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="ruleNumber" id="ruleNumber" name="ruleNumber" th:placeholder="${ lang.translate('请输入'+'规则编号') }" type="text" class="layui-input"    lay-verify="|required"   autocomplete="off" input-type="number_input" integer="true" decimal="false" allow-negative="true" step="1.0"   scale="0" />
                    </div>
                </div>
            
                <!-- text_input : 字段值 ,  columnValue -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('字段值')}">字段值</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="columnValue" id="columnValue" name="columnValue" th:placeholder="${ lang.translate('请输入'+'字段值') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            
                <!-- text_input : 字段名称 ,  columnName -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('字段名称')}">字段名称</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="columnName" id="columnName" name="columnName" th:placeholder="${ lang.translate('请输入'+'字段名称') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            
                <!-- radio_box : 计算类型 ,  calculationType  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('计算类型')}">计算类型</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input input-type="radio" type="radio" name="calculationType" lay-filter="calculationType" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.eam.AssetDepreciationCalculationMethodTypeEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==-1)}">
                    </div>
                </div>
            
                <!-- text_area : 计算方法 ,  methodContent  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('计算方法')}">计算方法</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <textarea lay-filter="methodContent" id="methodContent" name="methodContent" th:placeholder="${ lang.translate('请输入'+'计算方法') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>
            
                <!-- text_input : 方法描述 ,  methodContentInfo -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('方法描述')}">方法描述</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="methodContentInfo" id="methodContentInfo" name="methodContentInfo" th:placeholder="${ lang.translate('请输入'+'方法描述') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- radio_box : 返回类型 ,  returnType  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('返回类型')}">返回类型</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input input-type="radio" type="radio" name="returnType" lay-filter="returnType" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.eam.AssetDepreciationCalculationReturnTypeEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==-1)}">
                    </div>
                </div>
            
                <!-- text_input : 备注 ,  notes -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入'+'备注') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 修改人ID ,  updateBy -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('修改人ID')}">修改人ID</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="updateBy" id="updateBy" name="updateBy" th:placeholder="${ lang.translate('请输入'+'修改人ID') }" type="text" class="layui-input"  />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 20px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_asset_depreciation_cal_rule:create','eam_asset_depreciation_cal_rule:update','eam_asset_depreciation_cal_rule:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var RADIO_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_ACTIONCODE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetDepreciationRuleActionCodeEnum')}]];
    var RADIO_CALCULATIONTYPE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetDepreciationCalculationMethodTypeEnum')}]];
    var RADIO_RETURNTYPE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetDepreciationCalculationReturnTypeEnum')}]];
    var VALIDATE_CONFIG={"calculationType":{"labelInForm":"计算类型","inputType":"radio_box","required":true},"ruleNumber":{"labelInForm":"规则编号","inputType":"number_input","required":true},"actionCode":{"labelInForm":"动作","inputType":"radio_box","required":true},"columnValue":{"labelInForm":"字段值","inputType":"text_input","required":true},"returnType":{"labelInForm":"返回类型","inputType":"radio_box","required":true},"status":{"labelInForm":"状态","inputType":"radio_box","required":true},"columnName":{"labelInForm":"字段名称","inputType":"text_input","required":true}};
    var AUTH_PREFIX="eam_asset_depreciation_cal_rule";


</script>



<script th:src="'/business/eam/asset_depreciation_cal_rule/asset_depreciation_cal_rule_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset_depreciation_cal_rule/asset_depreciation_cal_rule_form.js?'+${cacheKey}"></script>

</body>
</html>