var f=Object.defineProperty,c=Object.defineProperties;var l=Object.getOwnPropertyDescriptors;var a=Object.getOwnPropertySymbols;var C=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable;var p=(i,o,t)=>o in i?f(i,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[o]=t,m=(i,o)=>{for(var t in o||(o={}))C.call(o,t)&&p(i,t,o[t]);if(a)for(var t of a(o))d.call(o,t)&&p(i,t,o[t]);return i},e=(i,o)=>c(i,l(o));var r=(i,o,t)=>(p(i,typeof o!="symbol"?o+"":o,t),t);import{aM as g,a8 as n}from"./index-bb2cbf17.js";import{d as h}from"./chartEditStore-55fbe93c.js";import{j as s}from"./index-0ec04aee.js";import"./plugin-3ef0fcec.js";import"./icon-f36697ff.js";import"./SettingItem-7fe1cfec.js";/* empty css                                                                      */import"./SettingItemBox-500aaf18.js";import"./CollapseItem.vue_vue_type_script_setup_true_lang-9b6d8958.js";import"./index.esm.min-f1367f57.js";import"./http-36f53bd1.js";import"./lodash-d17632fd.js";import"./fileTypeEnum-21359a08.js";const u={dataset:"uim:apple",color:"#03A9F4",size:64,rotate:0};class v extends h{constructor(){super(...arguments);r(this,"key",s.key);r(this,"attr",e(m({},g),{w:64,h:64,zIndex:1}));r(this,"chartConfig",n(s));r(this,"option",n(u))}}export{v as default,u as option};
