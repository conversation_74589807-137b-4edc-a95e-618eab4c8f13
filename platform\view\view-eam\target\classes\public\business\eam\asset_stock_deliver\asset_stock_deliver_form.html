<!--
/**
 * 库存出库 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-04-17 19:24:30
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
	<title th:text="${lang.translate('库存出库')}">库存出库</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="display:none">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->



        <div class="layui-row form-row" id="random-6702-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('业务名称')}">业务名称</div><div class="layui-required">*</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="name" id="name" name="name" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('业务名称') }" type="text" class="layui-input"    lay-verify="|required"  />
                        </div>
                    </div>

                                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('出货人')}">出货人</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="shipperName" id="shipperName" name="shipperName" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('出货人') }" type="text" class="layui-input"  />
                        </div>
                    </div>


                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('出货日期')}">出货日期</div></div>
                        <div class="layui-input-block ">
                            <input input-type="date" lay-filter="deliverDate" id="deliverDate" name="deliverDate"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择') +''+ lang.translate('出货日期') }" type="text" class="layui-input"   />
                        </div>
                    </div>

                <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('领用公司')}">领用公司</div><div class="layui-required">*</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="receivingCompanyId" id="receivingCompanyId" name="receivingCompanyId"  type="hidden" class="layui-input"    lay-verify="|required"   />
                            <button id="receivingCompanyId-button" type="button" action-type="org-dialog" class="layui-btn   " style="width: 100%"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择公司')}" th:default-label="${lang.translate('请选择公司')}">按钮文本</span></button>
                        </div>
                    </div>

                                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('领用部门')}">领用部门</div><div class="layui-required">*</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="receivingOrgId" id="receivingOrgId" name="receivingOrgId"  type="hidden" class="layui-input"    lay-verify="|required"   />
                            <button id="receivingOrgId-button" type="button" action-type="org-dialog" class="layui-btn   " style="width: 100%"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择组织节点')}" th:default-label="${lang.translate('请选择组织节点')}">按钮文本</span></button>
                        </div>
                    </div>

                                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('领用人')}">领用人</div><div class="layui-required">*</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="receiverId" id="receiverId" name="receiverId"  type="hidden" class="layui-input"    lay-verify="|required"   />
                            <button id="receiverId-button" type="button" action-type="emp-dialog" class="layui-btn   " style="width: 100%"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择人员')}" th:default-label="${lang.translate('请选择人员')}">按钮文本</span></button>
                        </div>
                    </div>

                <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >


                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('出库类型')}">出库类型</div><div class="layui-required">*</div></div>
                        <div class="layui-input-block ">
                            <div id="deliverType" input-type="select" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetDeliverTypeEnum')}" extraParam="{}"></div>
                        </div>
                    </div>


                                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('详细位置')}">详细位置</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="positionDetail" id="positionDetail" name="positionDetail" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('详细位置') }" type="text" class="layui-input"  />
                        </div>
                    </div>

                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->



        <div class="layui-row form-row" id="random-8802-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >

                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('出库说明')}">出库说明</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="content" id="content" name="content" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('出库说明') }" type="text" class="layui-input"  />
                        </div>
                    </div>

                                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('备注')}">备注</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('备注') }" type="text" class="layui-input"  />
                        </div>
                    </div>


                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('附件')}">附件</div></div>
                    <div class="layui-upload layui-input-block ">
                        <input input-type="upload" id="attach"  name="attach" lay-filter="attach" style="display: none">
                        <button type="button" class="layui-btn" id="attach-button" th:text="${lang.translate('选择附件')}">选择附件</button>
                        <div class="layui-upload-list" id="attach-file-list"></div>
                    </div>
                </div>



                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->

        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-4403-fieldset">
       <legend th:text="${lang.translate('资产列表')}" >资产列表</legend>
        </fieldset>
        <div class="layui-row form-row" style="text-align: center;" id="random-4403-content">
            <div style="display: inline-block;padding-right: 8px;padding-left: 8px" class="layui-col-xs12">
            <iframe js-fn="assetSelectList" class="form-iframe" frameborder="0" style="width: 100%"></iframe>
            </div>
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 250px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_asset_stock_deliver:create','eam_asset_stock_deliver:update','eam_asset_stock_deliver:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>

<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}]];
    var SELECT_DELIVERSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetDeliverStatusEnum')}]];
    var SELECT_DELIVERTYPE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetDeliverTypeEnum')}]];
    var VALIDATE_CONFIG={"receiverId":{"labelInForm":"领用人","inputType":"button","required":true},"receivingCompanyId":{"labelInForm":"领用公司","inputType":"button","required":true},"receivingOrgId":{"labelInForm":"领用部门","inputType":"button","required":true},"name":{"labelInForm":"业务名称","inputType":"text_input","required":true},"deliverType":{"labelInForm":"出库类型","inputType":"select_box","required":true}};
    var AUTH_PREFIX="eam_asset_stock_deliver";

    // 单据ID
    var BILL_ID = [[${billId}]] ;

    // 单据类型
    var BILL_TYPE = [[${billType}]] ;

    var OWNER_CODE = [[${ownerCode}]] ;



</script>



<script th:src="'/business/eam/asset_stock_deliver/asset_stock_deliver_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset_stock_deliver/asset_stock_deliver_form.js?'+${cacheKey}"></script>

</body>
</html>
