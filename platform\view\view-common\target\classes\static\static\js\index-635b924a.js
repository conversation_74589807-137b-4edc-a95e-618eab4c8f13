import{d as C,a0 as c,r as h,o,c as l,F as x,s as z,be as t,ao as d,bf as B,e as w,w as I,E as R,G as q,j as F}from"./index-bb2cbf17.js";import{i as g}from"./icon-f36697ff.js";const L={class:"go-apple-control-btn"},M=["onClick"],E=C({__name:"index",props:{mini:{request:!1,type:Boolean,default:!1},disabled:{request:!1,type:Boolean,default:!1},hidden:{request:!1,type:Array,default(){return[]}},narrow:{request:!1,type:Boolean,default:!1}},emits:["close","remove","resize","fullResize"],setup(s,{emit:u}){const f=u,a=s,{CloseIcon:m,RemoveIcon:_,ResizeIcon:p}=g.ionicons5,v=c(()=>k.filter(r=>a.hidden.findIndex(i=>r.key==i)===-1)),y=c(()=>a.narrow&&t(!0)),k=[{title:"关闭",key:"close",icon:m},{title:"缩小",key:"remove",icon:_},{title:y.value?"缩小":"放大",key:a.narrow?"fullResize":"resize",icon:p}],b=e=>{e==="fullResize"&&t(),e==="remove"&&t(!0)&&t(),f(e)};return(e,r)=>{const i=h("n-icon");return o(),l("div",L,[(o(!0),l(x,null,z(v.value,n=>(o(),l("div",{key:n.key,class:d(["btn",[n.key,s.disabled&&"disabled",s.mini&&"mini"]]),onClick:B(j=>b(n.key),["stop"])},[w(i,{size:"10",class:d(["icon-base",{hover:!s.disabled}])},{default:I(()=>[(o(),R(q(n.icon)))]),_:2},1032,["class"])],10,M))),128))])}}});const G=F(E,[["__scopeId","data-v-281e8a22"]]);export{G as M};
