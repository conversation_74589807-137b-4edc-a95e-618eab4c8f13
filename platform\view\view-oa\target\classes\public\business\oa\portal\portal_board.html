<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('门户')}">门户</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <link rel="stylesheet" href="/business/oa/portal/portal.css" th:href="'/business/oa/portal/portal.css?'+${cacheKey}"/>
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>

    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
<!--    <link rel="stylesheet" href="/console/pear/component/pear/css/pear-support.css?20230508224346443c1">-->
</head>

<body style="">
<div class="portal-container">


    <div class="layui-row">
        <div class="layui-col-md6" style="padding:5px;">
            <div class="layui-carousel" id="carousel" >
                <div carousel-item>
                    <div th:each="item,stat:${bannerList}"><image style="height:280px;width:100%" th:src="'/service-storage/sys-file/download?id='+${item.pictureId}"></image></div>
                </div>
            </div>
        </div>
        <div class="layui-col-md6" style="padding:5px;">
            <div class="layui-card">
                <div class="layui-card-header">
                    <div class="oa-portal-card-item-none">
                        <a class="oa-portal-card-item-title">新闻公告</a>
                        <!--                        public-->
                        <span class="oa-portal-card-item-more"><a href="javascript:openMore('notice')">更多</a></span>
                    </div>
                </div>
                <div class="layui-card-body" style="background-color: white;height: 202px;" >
                    <div class="oa-portal-card-list" id="notice-data">
                    </div>
                </div>
            </div>

        </div>
    </div>




    <div class="layui-row">
        <div class="layui-col-md6" style="padding:5px;" >
            <div class="layui-card">
                <div class="layui-card-header">
                    <div class="oa-portal-card-item-none">
                        <a class="oa-portal-card-item-title">我的流程</a>
                    </div>
                </div>
                <div class="layui-card-body" style="background-color: white;height: 300px;" >
                    <div class="system-div">
                        <div class="column process-instance-column">
                            <div class="layui-tab layui-tab-brief" lay-filter="process-instances-tab">
                                <ul class="layui-tab-title">
                                    <li class="layui-this" catalog="approving" id="tab-approving">待我处理</li>
                                    <li catalog="drafted"  id="tab-drafted">我的申请</li>
                                    <li catalog="approved" id="tab-approved">我已处理</li>
                                    <li catalog="drafting" id="tab-drafting">我的草稿</li>

                                </ul>
                                <div class="layui-tab-content">
                                    <div class="layui-tab-item layui-show">
                                        <div class="process-instances-list process-instances-list-approving">
                                        </div>
                                    </div>
                                    <div class="layui-tab-item">
                                        <div class="process-instances-list process-instances-list-drafted">
                                        </div>
                                    </div>
                                    <div class="layui-tab-item">
                                        <div class="process-instances-list process-instances-list-approved">
                                        </div>
                                    </div>
                                    <div class="layui-tab-item">
                                        <div class="process-instances-list process-instances-list-drafting">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>


        </div>




        <div class="layui-col-md6" style="padding:5px;">
            <div class="layui-card">
                <div class="layui-card-header">
                    <div class="oa-portal-card-item-none">
                        <a class="oa-portal-card-item-title">内部通知</a>
                        <span class="oa-portal-card-item-more"><a href="javascript:openMore('public')">更多</a></span>
                    </div>
                </div>
                <div class="layui-card-body" style="background-color: white;height: 300px;" >
                    <div class="oa-portal-card-list" id="public-data">
                    </div>
                </div>
            </div>

        </div>
    </div>



    <!-- 流程分类 -->
    <div class="catalog-row">
        <div class="catalog-unit" >
            <div class="catalog-title" >
                <img src="/assets/images/bpm-portal/fav.png" style="width: 28px;float: left;"><div>工作流程</div>
            </div>
            <div class="process-definition-items commonly-used">
            </div>

        </div>
    </div>


    <div class="layui-row">
        <div class="layui-col-md6" style="padding:5px;" >
            <div class="layui-card">
                <div class="layui-card-header">
                    <div class="oa-portal-card-item-none">
                        <a class="oa-portal-card-item-title">常用下载</a>
                        <span class="oa-portal-card-item-more"><a href="javascript:openDownFileMore()">更多</a></span>
                    </div>
                </div>
                <div class="layui-card-body" style="background-color: white;height: 300px;" >
                    <div class="oa-portal-card-list" id="downfile-data">
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md6" style="padding:5px;">


                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="oa-portal-card-item-none">
                            <a class="oa-portal-card-item-title">关联系统</a>
                        </div>
                    </div>
                    <div class="layui-card-body" style="background-color: white;height: 300px;" >
                        <div class="system-div" id="system-data">
                            <!--                        <div class="item-div">-->
                            <!--                            <image style="height:25px;width:25px" src="/business/oa/portal/banner.png"></image>-->
                            <!--                            <span class="item-info"><a class="item-info-a">百度系统</a></span>-->
                            <!--                        </div>-->
                            <!--                     -->
                        </div>
                    </div>
                </div>



        </div>
    </div>

</div>


<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>

<script>
    var LAYUI_TABLE_WIDTH_CONFIG = null;
    var SELECT_TYPE_DATA = [{"code":"L01","text":"\u4E8B\u5047"},{"code":"L02","text":"\u5E74\u4F11\u5047"},{"code":"L03","text":"\u8C03\u4F11"}];
    var VALIDATE_CONFIG={"reason":{"labelInForm":"请假事由","inputType":"text_input","required":true},"beginTime":{"date":true,"labelInForm":"开始时间","inputType":"date_input","required":true},"endTime":{"date":true,"labelInForm":"结束时间","inputType":"date_input","required":true},"type":{"labelInForm":"请假类型","inputType":"select_box","required":true}};
    var AUTH_PREFIX="oa_portal";

</script>


<script th:src="'/business/oa/portal/portal.js?'+${cacheKey}"></script>

</body>
</html>
