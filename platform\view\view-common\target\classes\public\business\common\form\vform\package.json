{"name": "variant-form", "version": "2.2.9", "private": false, "scripts": {"serve": "vue-cli-service serve --open src/main.js", "build": "vue-cli-service build --report --dest dist/build", "lib": "vue-cli-service build --report --target lib --dest dist/lib --name VFormDesigner install.js", "lib-render": "vue-cli-service build --report --target lib --dest dist/lib-render --name VFormRender install-render.js", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.21.1", "clipboard": "^2.0.8", "core-js": "^3.6.5", "element-ui": "^2.15.1", "file-saver": "^2.0.5", "vue": "^2.6.11", "vue2-editor": "^2.10.2", "vuedraggable": "^2.24.3"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "ace-builds": "^1.4.12", "babel-eslint": "^10.1.0", "babel-polyfill": "^6.26.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "mvdir": "^1.0.21", "sass": "^1.45.1", "sass-loader": "^8.0.2", "svg-sprite-loader": "^5.2.1", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}