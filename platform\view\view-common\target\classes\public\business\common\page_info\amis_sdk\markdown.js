;/*!node_modules/markdown-it/lib/common/entities.js*/
amis.define("3a8a690",(function(r,e,a,t){"use strict";a.exports={Aacute:"Á",aacute:"á",Abreve:"Ă",abreve:"ă",ac:"∾",acd:"∿",acE:"∾̳",Acirc:"Â",acirc:"â",acute:"´",Acy:"А",acy:"а",AElig:"Æ",aelig:"æ",af:"⁡",Afr:"𝔄",afr:"𝔞",Agrave:"À",agrave:"à",alefsym:"ℵ",aleph:"ℵ",Alpha:"Α",alpha:"α",Amacr:"Ā",amacr:"ā",amalg:"⨿",amp:"&",AMP:"&",andand:"⩕",And:"⩓",and:"∧",andd:"⩜",andslope:"⩘",andv:"⩚",ang:"∠",ange:"⦤",angle:"∠",angmsdaa:"⦨",angmsdab:"⦩",angmsdac:"⦪",angmsdad:"⦫",angmsdae:"⦬",angmsdaf:"⦭",angmsdag:"⦮",angmsdah:"⦯",angmsd:"∡",angrt:"∟",angrtvb:"⊾",angrtvbd:"⦝",angsph:"∢",angst:"Å",angzarr:"⍼",Aogon:"Ą",aogon:"ą",Aopf:"𝔸",aopf:"𝕒",apacir:"⩯",ap:"≈",apE:"⩰",ape:"≊",apid:"≋",apos:"'",ApplyFunction:"⁡",approx:"≈",approxeq:"≊",Aring:"Å",aring:"å",Ascr:"𝒜",ascr:"𝒶",Assign:"≔",ast:"*",asymp:"≈",asympeq:"≍",Atilde:"Ã",atilde:"ã",Auml:"Ä",auml:"ä",awconint:"∳",awint:"⨑",backcong:"≌",backepsilon:"϶",backprime:"‵",backsim:"∽",backsimeq:"⋍",Backslash:"∖",Barv:"⫧",barvee:"⊽",barwed:"⌅",Barwed:"⌆",barwedge:"⌅",bbrk:"⎵",bbrktbrk:"⎶",bcong:"≌",Bcy:"Б",bcy:"б",bdquo:"„",becaus:"∵",because:"∵",Because:"∵",bemptyv:"⦰",bepsi:"϶",bernou:"ℬ",Bernoullis:"ℬ",Beta:"Β",beta:"β",beth:"ℶ",between:"≬",Bfr:"𝔅",bfr:"𝔟",bigcap:"⋂",bigcirc:"◯",bigcup:"⋃",bigodot:"⨀",bigoplus:"⨁",bigotimes:"⨂",bigsqcup:"⨆",bigstar:"★",bigtriangledown:"▽",bigtriangleup:"△",biguplus:"⨄",bigvee:"⋁",bigwedge:"⋀",bkarow:"⤍",blacklozenge:"⧫",blacksquare:"▪",blacktriangle:"▴",blacktriangledown:"▾",blacktriangleleft:"◂",blacktriangleright:"▸",blank:"␣",blk12:"▒",blk14:"░",blk34:"▓",block:"█",bne:"=⃥",bnequiv:"≡⃥",bNot:"⫭",bnot:"⌐",Bopf:"𝔹",bopf:"𝕓",bot:"⊥",bottom:"⊥",bowtie:"⋈",boxbox:"⧉",boxdl:"┐",boxdL:"╕",boxDl:"╖",boxDL:"╗",boxdr:"┌",boxdR:"╒",boxDr:"╓",boxDR:"╔",boxh:"─",boxH:"═",boxhd:"┬",boxHd:"╤",boxhD:"╥",boxHD:"╦",boxhu:"┴",boxHu:"╧",boxhU:"╨",boxHU:"╩",boxminus:"⊟",boxplus:"⊞",boxtimes:"⊠",boxul:"┘",boxuL:"╛",boxUl:"╜",boxUL:"╝",boxur:"└",boxuR:"╘",boxUr:"╙",boxUR:"╚",boxv:"│",boxV:"║",boxvh:"┼",boxvH:"╪",boxVh:"╫",boxVH:"╬",boxvl:"┤",boxvL:"╡",boxVl:"╢",boxVL:"╣",boxvr:"├",boxvR:"╞",boxVr:"╟",boxVR:"╠",bprime:"‵",breve:"˘",Breve:"˘",brvbar:"¦",bscr:"𝒷",Bscr:"ℬ",bsemi:"⁏",bsim:"∽",bsime:"⋍",bsolb:"⧅",bsol:"\\",bsolhsub:"⟈",bull:"•",bullet:"•",bump:"≎",bumpE:"⪮",bumpe:"≏",Bumpeq:"≎",bumpeq:"≏",Cacute:"Ć",cacute:"ć",capand:"⩄",capbrcup:"⩉",capcap:"⩋",cap:"∩",Cap:"⋒",capcup:"⩇",capdot:"⩀",CapitalDifferentialD:"ⅅ",caps:"∩︀",caret:"⁁",caron:"ˇ",Cayleys:"ℭ",ccaps:"⩍",Ccaron:"Č",ccaron:"č",Ccedil:"Ç",ccedil:"ç",Ccirc:"Ĉ",ccirc:"ĉ",Cconint:"∰",ccups:"⩌",ccupssm:"⩐",Cdot:"Ċ",cdot:"ċ",cedil:"¸",Cedilla:"¸",cemptyv:"⦲",cent:"¢",centerdot:"·",CenterDot:"·",cfr:"𝔠",Cfr:"ℭ",CHcy:"Ч",chcy:"ч",check:"✓",checkmark:"✓",Chi:"Χ",chi:"χ",circ:"ˆ",circeq:"≗",circlearrowleft:"↺",circlearrowright:"↻",circledast:"⊛",circledcirc:"⊚",circleddash:"⊝",CircleDot:"⊙",circledR:"®",circledS:"Ⓢ",CircleMinus:"⊖",CirclePlus:"⊕",CircleTimes:"⊗",cir:"○",cirE:"⧃",cire:"≗",cirfnint:"⨐",cirmid:"⫯",cirscir:"⧂",ClockwiseContourIntegral:"∲",CloseCurlyDoubleQuote:"”",CloseCurlyQuote:"’",clubs:"♣",clubsuit:"♣",colon:":",Colon:"∷",Colone:"⩴",colone:"≔",coloneq:"≔",comma:",",commat:"@",comp:"∁",compfn:"∘",complement:"∁",complexes:"ℂ",cong:"≅",congdot:"⩭",Congruent:"≡",conint:"∮",Conint:"∯",ContourIntegral:"∮",copf:"𝕔",Copf:"ℂ",coprod:"∐",Coproduct:"∐",copy:"©",COPY:"©",copysr:"℗",CounterClockwiseContourIntegral:"∳",crarr:"↵",cross:"✗",Cross:"⨯",Cscr:"𝒞",cscr:"𝒸",csub:"⫏",csube:"⫑",csup:"⫐",csupe:"⫒",ctdot:"⋯",cudarrl:"⤸",cudarrr:"⤵",cuepr:"⋞",cuesc:"⋟",cularr:"↶",cularrp:"⤽",cupbrcap:"⩈",cupcap:"⩆",CupCap:"≍",cup:"∪",Cup:"⋓",cupcup:"⩊",cupdot:"⊍",cupor:"⩅",cups:"∪︀",curarr:"↷",curarrm:"⤼",curlyeqprec:"⋞",curlyeqsucc:"⋟",curlyvee:"⋎",curlywedge:"⋏",curren:"¤",curvearrowleft:"↶",curvearrowright:"↷",cuvee:"⋎",cuwed:"⋏",cwconint:"∲",cwint:"∱",cylcty:"⌭",dagger:"†",Dagger:"‡",daleth:"ℸ",darr:"↓",Darr:"↡",dArr:"⇓",dash:"‐",Dashv:"⫤",dashv:"⊣",dbkarow:"⤏",dblac:"˝",Dcaron:"Ď",dcaron:"ď",Dcy:"Д",dcy:"д",ddagger:"‡",ddarr:"⇊",DD:"ⅅ",dd:"ⅆ",DDotrahd:"⤑",ddotseq:"⩷",deg:"°",Del:"∇",Delta:"Δ",delta:"δ",demptyv:"⦱",dfisht:"⥿",Dfr:"𝔇",dfr:"𝔡",dHar:"⥥",dharl:"⇃",dharr:"⇂",DiacriticalAcute:"´",DiacriticalDot:"˙",DiacriticalDoubleAcute:"˝",DiacriticalGrave:"`",DiacriticalTilde:"˜",diam:"⋄",diamond:"⋄",Diamond:"⋄",diamondsuit:"♦",diams:"♦",die:"¨",DifferentialD:"ⅆ",digamma:"ϝ",disin:"⋲",div:"÷",divide:"÷",divideontimes:"⋇",divonx:"⋇",DJcy:"Ђ",djcy:"ђ",dlcorn:"⌞",dlcrop:"⌍",dollar:"$",Dopf:"𝔻",dopf:"𝕕",Dot:"¨",dot:"˙",DotDot:"⃜",doteq:"≐",doteqdot:"≑",DotEqual:"≐",dotminus:"∸",dotplus:"∔",dotsquare:"⊡",doublebarwedge:"⌆",DoubleContourIntegral:"∯",DoubleDot:"¨",DoubleDownArrow:"⇓",DoubleLeftArrow:"⇐",DoubleLeftRightArrow:"⇔",DoubleLeftTee:"⫤",DoubleLongLeftArrow:"⟸",DoubleLongLeftRightArrow:"⟺",DoubleLongRightArrow:"⟹",DoubleRightArrow:"⇒",DoubleRightTee:"⊨",DoubleUpArrow:"⇑",DoubleUpDownArrow:"⇕",DoubleVerticalBar:"∥",DownArrowBar:"⤓",downarrow:"↓",DownArrow:"↓",Downarrow:"⇓",DownArrowUpArrow:"⇵",DownBreve:"̑",downdownarrows:"⇊",downharpoonleft:"⇃",downharpoonright:"⇂",DownLeftRightVector:"⥐",DownLeftTeeVector:"⥞",DownLeftVectorBar:"⥖",DownLeftVector:"↽",DownRightTeeVector:"⥟",DownRightVectorBar:"⥗",DownRightVector:"⇁",DownTeeArrow:"↧",DownTee:"⊤",drbkarow:"⤐",drcorn:"⌟",drcrop:"⌌",Dscr:"𝒟",dscr:"𝒹",DScy:"Ѕ",dscy:"ѕ",dsol:"⧶",Dstrok:"Đ",dstrok:"đ",dtdot:"⋱",dtri:"▿",dtrif:"▾",duarr:"⇵",duhar:"⥯",dwangle:"⦦",DZcy:"Џ",dzcy:"џ",dzigrarr:"⟿",Eacute:"É",eacute:"é",easter:"⩮",Ecaron:"Ě",ecaron:"ě",Ecirc:"Ê",ecirc:"ê",ecir:"≖",ecolon:"≕",Ecy:"Э",ecy:"э",eDDot:"⩷",Edot:"Ė",edot:"ė",eDot:"≑",ee:"ⅇ",efDot:"≒",Efr:"𝔈",efr:"𝔢",eg:"⪚",Egrave:"È",egrave:"è",egs:"⪖",egsdot:"⪘",el:"⪙",Element:"∈",elinters:"⏧",ell:"ℓ",els:"⪕",elsdot:"⪗",Emacr:"Ē",emacr:"ē",empty:"∅",emptyset:"∅",EmptySmallSquare:"◻",emptyv:"∅",EmptyVerySmallSquare:"▫",emsp13:" ",emsp14:" ",emsp:" ",ENG:"Ŋ",eng:"ŋ",ensp:" ",Eogon:"Ę",eogon:"ę",Eopf:"𝔼",eopf:"𝕖",epar:"⋕",eparsl:"⧣",eplus:"⩱",epsi:"ε",Epsilon:"Ε",epsilon:"ε",epsiv:"ϵ",eqcirc:"≖",eqcolon:"≕",eqsim:"≂",eqslantgtr:"⪖",eqslantless:"⪕",Equal:"⩵",equals:"=",EqualTilde:"≂",equest:"≟",Equilibrium:"⇌",equiv:"≡",equivDD:"⩸",eqvparsl:"⧥",erarr:"⥱",erDot:"≓",escr:"ℯ",Escr:"ℰ",esdot:"≐",Esim:"⩳",esim:"≂",Eta:"Η",eta:"η",ETH:"Ð",eth:"ð",Euml:"Ë",euml:"ë",euro:"€",excl:"!",exist:"∃",Exists:"∃",expectation:"ℰ",exponentiale:"ⅇ",ExponentialE:"ⅇ",fallingdotseq:"≒",Fcy:"Ф",fcy:"ф",female:"♀",ffilig:"ﬃ",fflig:"ﬀ",ffllig:"ﬄ",Ffr:"𝔉",ffr:"𝔣",filig:"ﬁ",FilledSmallSquare:"◼",FilledVerySmallSquare:"▪",fjlig:"fj",flat:"♭",fllig:"ﬂ",fltns:"▱",fnof:"ƒ",Fopf:"𝔽",fopf:"𝕗",forall:"∀",ForAll:"∀",fork:"⋔",forkv:"⫙",Fouriertrf:"ℱ",fpartint:"⨍",frac12:"½",frac13:"⅓",frac14:"¼",frac15:"⅕",frac16:"⅙",frac18:"⅛",frac23:"⅔",frac25:"⅖",frac34:"¾",frac35:"⅗",frac38:"⅜",frac45:"⅘",frac56:"⅚",frac58:"⅝",frac78:"⅞",frasl:"⁄",frown:"⌢",fscr:"𝒻",Fscr:"ℱ",gacute:"ǵ",Gamma:"Γ",gamma:"γ",Gammad:"Ϝ",gammad:"ϝ",gap:"⪆",Gbreve:"Ğ",gbreve:"ğ",Gcedil:"Ģ",Gcirc:"Ĝ",gcirc:"ĝ",Gcy:"Г",gcy:"г",Gdot:"Ġ",gdot:"ġ",ge:"≥",gE:"≧",gEl:"⪌",gel:"⋛",geq:"≥",geqq:"≧",geqslant:"⩾",gescc:"⪩",ges:"⩾",gesdot:"⪀",gesdoto:"⪂",gesdotol:"⪄",gesl:"⋛︀",gesles:"⪔",Gfr:"𝔊",gfr:"𝔤",gg:"≫",Gg:"⋙",ggg:"⋙",gimel:"ℷ",GJcy:"Ѓ",gjcy:"ѓ",gla:"⪥",gl:"≷",glE:"⪒",glj:"⪤",gnap:"⪊",gnapprox:"⪊",gne:"⪈",gnE:"≩",gneq:"⪈",gneqq:"≩",gnsim:"⋧",Gopf:"𝔾",gopf:"𝕘",grave:"`",GreaterEqual:"≥",GreaterEqualLess:"⋛",GreaterFullEqual:"≧",GreaterGreater:"⪢",GreaterLess:"≷",GreaterSlantEqual:"⩾",GreaterTilde:"≳",Gscr:"𝒢",gscr:"ℊ",gsim:"≳",gsime:"⪎",gsiml:"⪐",gtcc:"⪧",gtcir:"⩺",gt:">",GT:">",Gt:"≫",gtdot:"⋗",gtlPar:"⦕",gtquest:"⩼",gtrapprox:"⪆",gtrarr:"⥸",gtrdot:"⋗",gtreqless:"⋛",gtreqqless:"⪌",gtrless:"≷",gtrsim:"≳",gvertneqq:"≩︀",gvnE:"≩︀",Hacek:"ˇ",hairsp:" ",half:"½",hamilt:"ℋ",HARDcy:"Ъ",hardcy:"ъ",harrcir:"⥈",harr:"↔",hArr:"⇔",harrw:"↭",Hat:"^",hbar:"ℏ",Hcirc:"Ĥ",hcirc:"ĥ",hearts:"♥",heartsuit:"♥",hellip:"…",hercon:"⊹",hfr:"𝔥",Hfr:"ℌ",HilbertSpace:"ℋ",hksearow:"⤥",hkswarow:"⤦",hoarr:"⇿",homtht:"∻",hookleftarrow:"↩",hookrightarrow:"↪",hopf:"𝕙",Hopf:"ℍ",horbar:"―",HorizontalLine:"─",hscr:"𝒽",Hscr:"ℋ",hslash:"ℏ",Hstrok:"Ħ",hstrok:"ħ",HumpDownHump:"≎",HumpEqual:"≏",hybull:"⁃",hyphen:"‐",Iacute:"Í",iacute:"í",ic:"⁣",Icirc:"Î",icirc:"î",Icy:"И",icy:"и",Idot:"İ",IEcy:"Е",iecy:"е",iexcl:"¡",iff:"⇔",ifr:"𝔦",Ifr:"ℑ",Igrave:"Ì",igrave:"ì",ii:"ⅈ",iiiint:"⨌",iiint:"∭",iinfin:"⧜",iiota:"℩",IJlig:"Ĳ",ijlig:"ĳ",Imacr:"Ī",imacr:"ī",image:"ℑ",ImaginaryI:"ⅈ",imagline:"ℐ",imagpart:"ℑ",imath:"ı",Im:"ℑ",imof:"⊷",imped:"Ƶ",Implies:"⇒",incare:"℅",in:"∈",infin:"∞",infintie:"⧝",inodot:"ı",intcal:"⊺",int:"∫",Int:"∬",integers:"ℤ",Integral:"∫",intercal:"⊺",Intersection:"⋂",intlarhk:"⨗",intprod:"⨼",InvisibleComma:"⁣",InvisibleTimes:"⁢",IOcy:"Ё",iocy:"ё",Iogon:"Į",iogon:"į",Iopf:"𝕀",iopf:"𝕚",Iota:"Ι",iota:"ι",iprod:"⨼",iquest:"¿",iscr:"𝒾",Iscr:"ℐ",isin:"∈",isindot:"⋵",isinE:"⋹",isins:"⋴",isinsv:"⋳",isinv:"∈",it:"⁢",Itilde:"Ĩ",itilde:"ĩ",Iukcy:"І",iukcy:"і",Iuml:"Ï",iuml:"ï",Jcirc:"Ĵ",jcirc:"ĵ",Jcy:"Й",jcy:"й",Jfr:"𝔍",jfr:"𝔧",jmath:"ȷ",Jopf:"𝕁",jopf:"𝕛",Jscr:"𝒥",jscr:"𝒿",Jsercy:"Ј",jsercy:"ј",Jukcy:"Є",jukcy:"є",Kappa:"Κ",kappa:"κ",kappav:"ϰ",Kcedil:"Ķ",kcedil:"ķ",Kcy:"К",kcy:"к",Kfr:"𝔎",kfr:"𝔨",kgreen:"ĸ",KHcy:"Х",khcy:"х",KJcy:"Ќ",kjcy:"ќ",Kopf:"𝕂",kopf:"𝕜",Kscr:"𝒦",kscr:"𝓀",lAarr:"⇚",Lacute:"Ĺ",lacute:"ĺ",laemptyv:"⦴",lagran:"ℒ",Lambda:"Λ",lambda:"λ",lang:"⟨",Lang:"⟪",langd:"⦑",langle:"⟨",lap:"⪅",Laplacetrf:"ℒ",laquo:"«",larrb:"⇤",larrbfs:"⤟",larr:"←",Larr:"↞",lArr:"⇐",larrfs:"⤝",larrhk:"↩",larrlp:"↫",larrpl:"⤹",larrsim:"⥳",larrtl:"↢",latail:"⤙",lAtail:"⤛",lat:"⪫",late:"⪭",lates:"⪭︀",lbarr:"⤌",lBarr:"⤎",lbbrk:"❲",lbrace:"{",lbrack:"[",lbrke:"⦋",lbrksld:"⦏",lbrkslu:"⦍",Lcaron:"Ľ",lcaron:"ľ",Lcedil:"Ļ",lcedil:"ļ",lceil:"⌈",lcub:"{",Lcy:"Л",lcy:"л",ldca:"⤶",ldquo:"“",ldquor:"„",ldrdhar:"⥧",ldrushar:"⥋",ldsh:"↲",le:"≤",lE:"≦",LeftAngleBracket:"⟨",LeftArrowBar:"⇤",leftarrow:"←",LeftArrow:"←",Leftarrow:"⇐",LeftArrowRightArrow:"⇆",leftarrowtail:"↢",LeftCeiling:"⌈",LeftDoubleBracket:"⟦",LeftDownTeeVector:"⥡",LeftDownVectorBar:"⥙",LeftDownVector:"⇃",LeftFloor:"⌊",leftharpoondown:"↽",leftharpoonup:"↼",leftleftarrows:"⇇",leftrightarrow:"↔",LeftRightArrow:"↔",Leftrightarrow:"⇔",leftrightarrows:"⇆",leftrightharpoons:"⇋",leftrightsquigarrow:"↭",LeftRightVector:"⥎",LeftTeeArrow:"↤",LeftTee:"⊣",LeftTeeVector:"⥚",leftthreetimes:"⋋",LeftTriangleBar:"⧏",LeftTriangle:"⊲",LeftTriangleEqual:"⊴",LeftUpDownVector:"⥑",LeftUpTeeVector:"⥠",LeftUpVectorBar:"⥘",LeftUpVector:"↿",LeftVectorBar:"⥒",LeftVector:"↼",lEg:"⪋",leg:"⋚",leq:"≤",leqq:"≦",leqslant:"⩽",lescc:"⪨",les:"⩽",lesdot:"⩿",lesdoto:"⪁",lesdotor:"⪃",lesg:"⋚︀",lesges:"⪓",lessapprox:"⪅",lessdot:"⋖",lesseqgtr:"⋚",lesseqqgtr:"⪋",LessEqualGreater:"⋚",LessFullEqual:"≦",LessGreater:"≶",lessgtr:"≶",LessLess:"⪡",lesssim:"≲",LessSlantEqual:"⩽",LessTilde:"≲",lfisht:"⥼",lfloor:"⌊",Lfr:"𝔏",lfr:"𝔩",lg:"≶",lgE:"⪑",lHar:"⥢",lhard:"↽",lharu:"↼",lharul:"⥪",lhblk:"▄",LJcy:"Љ",ljcy:"љ",llarr:"⇇",ll:"≪",Ll:"⋘",llcorner:"⌞",Lleftarrow:"⇚",llhard:"⥫",lltri:"◺",Lmidot:"Ŀ",lmidot:"ŀ",lmoustache:"⎰",lmoust:"⎰",lnap:"⪉",lnapprox:"⪉",lne:"⪇",lnE:"≨",lneq:"⪇",lneqq:"≨",lnsim:"⋦",loang:"⟬",loarr:"⇽",lobrk:"⟦",longleftarrow:"⟵",LongLeftArrow:"⟵",Longleftarrow:"⟸",longleftrightarrow:"⟷",LongLeftRightArrow:"⟷",Longleftrightarrow:"⟺",longmapsto:"⟼",longrightarrow:"⟶",LongRightArrow:"⟶",Longrightarrow:"⟹",looparrowleft:"↫",looparrowright:"↬",lopar:"⦅",Lopf:"𝕃",lopf:"𝕝",loplus:"⨭",lotimes:"⨴",lowast:"∗",lowbar:"_",LowerLeftArrow:"↙",LowerRightArrow:"↘",loz:"◊",lozenge:"◊",lozf:"⧫",lpar:"(",lparlt:"⦓",lrarr:"⇆",lrcorner:"⌟",lrhar:"⇋",lrhard:"⥭",lrm:"‎",lrtri:"⊿",lsaquo:"‹",lscr:"𝓁",Lscr:"ℒ",lsh:"↰",Lsh:"↰",lsim:"≲",lsime:"⪍",lsimg:"⪏",lsqb:"[",lsquo:"‘",lsquor:"‚",Lstrok:"Ł",lstrok:"ł",ltcc:"⪦",ltcir:"⩹",lt:"<",LT:"<",Lt:"≪",ltdot:"⋖",lthree:"⋋",ltimes:"⋉",ltlarr:"⥶",ltquest:"⩻",ltri:"◃",ltrie:"⊴",ltrif:"◂",ltrPar:"⦖",lurdshar:"⥊",luruhar:"⥦",lvertneqq:"≨︀",lvnE:"≨︀",macr:"¯",male:"♂",malt:"✠",maltese:"✠",Map:"⤅",map:"↦",mapsto:"↦",mapstodown:"↧",mapstoleft:"↤",mapstoup:"↥",marker:"▮",mcomma:"⨩",Mcy:"М",mcy:"м",mdash:"—",mDDot:"∺",measuredangle:"∡",MediumSpace:" ",Mellintrf:"ℳ",Mfr:"𝔐",mfr:"𝔪",mho:"℧",micro:"µ",midast:"*",midcir:"⫰",mid:"∣",middot:"·",minusb:"⊟",minus:"−",minusd:"∸",minusdu:"⨪",MinusPlus:"∓",mlcp:"⫛",mldr:"…",mnplus:"∓",models:"⊧",Mopf:"𝕄",mopf:"𝕞",mp:"∓",mscr:"𝓂",Mscr:"ℳ",mstpos:"∾",Mu:"Μ",mu:"μ",multimap:"⊸",mumap:"⊸",nabla:"∇",Nacute:"Ń",nacute:"ń",nang:"∠⃒",nap:"≉",napE:"⩰̸",napid:"≋̸",napos:"ŉ",napprox:"≉",natural:"♮",naturals:"ℕ",natur:"♮",nbsp:" ",nbump:"≎̸",nbumpe:"≏̸",ncap:"⩃",Ncaron:"Ň",ncaron:"ň",Ncedil:"Ņ",ncedil:"ņ",ncong:"≇",ncongdot:"⩭̸",ncup:"⩂",Ncy:"Н",ncy:"н",ndash:"–",nearhk:"⤤",nearr:"↗",neArr:"⇗",nearrow:"↗",ne:"≠",nedot:"≐̸",NegativeMediumSpace:"​",NegativeThickSpace:"​",NegativeThinSpace:"​",NegativeVeryThinSpace:"​",nequiv:"≢",nesear:"⤨",nesim:"≂̸",NestedGreaterGreater:"≫",NestedLessLess:"≪",NewLine:"\n",nexist:"∄",nexists:"∄",Nfr:"𝔑",nfr:"𝔫",ngE:"≧̸",nge:"≱",ngeq:"≱",ngeqq:"≧̸",ngeqslant:"⩾̸",nges:"⩾̸",nGg:"⋙̸",ngsim:"≵",nGt:"≫⃒",ngt:"≯",ngtr:"≯",nGtv:"≫̸",nharr:"↮",nhArr:"⇎",nhpar:"⫲",ni:"∋",nis:"⋼",nisd:"⋺",niv:"∋",NJcy:"Њ",njcy:"њ",nlarr:"↚",nlArr:"⇍",nldr:"‥",nlE:"≦̸",nle:"≰",nleftarrow:"↚",nLeftarrow:"⇍",nleftrightarrow:"↮",nLeftrightarrow:"⇎",nleq:"≰",nleqq:"≦̸",nleqslant:"⩽̸",nles:"⩽̸",nless:"≮",nLl:"⋘̸",nlsim:"≴",nLt:"≪⃒",nlt:"≮",nltri:"⋪",nltrie:"⋬",nLtv:"≪̸",nmid:"∤",NoBreak:"⁠",NonBreakingSpace:" ",nopf:"𝕟",Nopf:"ℕ",Not:"⫬",not:"¬",NotCongruent:"≢",NotCupCap:"≭",NotDoubleVerticalBar:"∦",NotElement:"∉",NotEqual:"≠",NotEqualTilde:"≂̸",NotExists:"∄",NotGreater:"≯",NotGreaterEqual:"≱",NotGreaterFullEqual:"≧̸",NotGreaterGreater:"≫̸",NotGreaterLess:"≹",NotGreaterSlantEqual:"⩾̸",NotGreaterTilde:"≵",NotHumpDownHump:"≎̸",NotHumpEqual:"≏̸",notin:"∉",notindot:"⋵̸",notinE:"⋹̸",notinva:"∉",notinvb:"⋷",notinvc:"⋶",NotLeftTriangleBar:"⧏̸",NotLeftTriangle:"⋪",NotLeftTriangleEqual:"⋬",NotLess:"≮",NotLessEqual:"≰",NotLessGreater:"≸",NotLessLess:"≪̸",NotLessSlantEqual:"⩽̸",NotLessTilde:"≴",NotNestedGreaterGreater:"⪢̸",NotNestedLessLess:"⪡̸",notni:"∌",notniva:"∌",notnivb:"⋾",notnivc:"⋽",NotPrecedes:"⊀",NotPrecedesEqual:"⪯̸",NotPrecedesSlantEqual:"⋠",NotReverseElement:"∌",NotRightTriangleBar:"⧐̸",NotRightTriangle:"⋫",NotRightTriangleEqual:"⋭",NotSquareSubset:"⊏̸",NotSquareSubsetEqual:"⋢",NotSquareSuperset:"⊐̸",NotSquareSupersetEqual:"⋣",NotSubset:"⊂⃒",NotSubsetEqual:"⊈",NotSucceeds:"⊁",NotSucceedsEqual:"⪰̸",NotSucceedsSlantEqual:"⋡",NotSucceedsTilde:"≿̸",NotSuperset:"⊃⃒",NotSupersetEqual:"⊉",NotTilde:"≁",NotTildeEqual:"≄",NotTildeFullEqual:"≇",NotTildeTilde:"≉",NotVerticalBar:"∤",nparallel:"∦",npar:"∦",nparsl:"⫽⃥",npart:"∂̸",npolint:"⨔",npr:"⊀",nprcue:"⋠",nprec:"⊀",npreceq:"⪯̸",npre:"⪯̸",nrarrc:"⤳̸",nrarr:"↛",nrArr:"⇏",nrarrw:"↝̸",nrightarrow:"↛",nRightarrow:"⇏",nrtri:"⋫",nrtrie:"⋭",nsc:"⊁",nsccue:"⋡",nsce:"⪰̸",Nscr:"𝒩",nscr:"𝓃",nshortmid:"∤",nshortparallel:"∦",nsim:"≁",nsime:"≄",nsimeq:"≄",nsmid:"∤",nspar:"∦",nsqsube:"⋢",nsqsupe:"⋣",nsub:"⊄",nsubE:"⫅̸",nsube:"⊈",nsubset:"⊂⃒",nsubseteq:"⊈",nsubseteqq:"⫅̸",nsucc:"⊁",nsucceq:"⪰̸",nsup:"⊅",nsupE:"⫆̸",nsupe:"⊉",nsupset:"⊃⃒",nsupseteq:"⊉",nsupseteqq:"⫆̸",ntgl:"≹",Ntilde:"Ñ",ntilde:"ñ",ntlg:"≸",ntriangleleft:"⋪",ntrianglelefteq:"⋬",ntriangleright:"⋫",ntrianglerighteq:"⋭",Nu:"Ν",nu:"ν",num:"#",numero:"№",numsp:" ",nvap:"≍⃒",nvdash:"⊬",nvDash:"⊭",nVdash:"⊮",nVDash:"⊯",nvge:"≥⃒",nvgt:">⃒",nvHarr:"⤄",nvinfin:"⧞",nvlArr:"⤂",nvle:"≤⃒",nvlt:"<⃒",nvltrie:"⊴⃒",nvrArr:"⤃",nvrtrie:"⊵⃒",nvsim:"∼⃒",nwarhk:"⤣",nwarr:"↖",nwArr:"⇖",nwarrow:"↖",nwnear:"⤧",Oacute:"Ó",oacute:"ó",oast:"⊛",Ocirc:"Ô",ocirc:"ô",ocir:"⊚",Ocy:"О",ocy:"о",odash:"⊝",Odblac:"Ő",odblac:"ő",odiv:"⨸",odot:"⊙",odsold:"⦼",OElig:"Œ",oelig:"œ",ofcir:"⦿",Ofr:"𝔒",ofr:"𝔬",ogon:"˛",Ograve:"Ò",ograve:"ò",ogt:"⧁",ohbar:"⦵",ohm:"Ω",oint:"∮",olarr:"↺",olcir:"⦾",olcross:"⦻",oline:"‾",olt:"⧀",Omacr:"Ō",omacr:"ō",Omega:"Ω",omega:"ω",Omicron:"Ο",omicron:"ο",omid:"⦶",ominus:"⊖",Oopf:"𝕆",oopf:"𝕠",opar:"⦷",OpenCurlyDoubleQuote:"“",OpenCurlyQuote:"‘",operp:"⦹",oplus:"⊕",orarr:"↻",Or:"⩔",or:"∨",ord:"⩝",order:"ℴ",orderof:"ℴ",ordf:"ª",ordm:"º",origof:"⊶",oror:"⩖",orslope:"⩗",orv:"⩛",oS:"Ⓢ",Oscr:"𝒪",oscr:"ℴ",Oslash:"Ø",oslash:"ø",osol:"⊘",Otilde:"Õ",otilde:"õ",otimesas:"⨶",Otimes:"⨷",otimes:"⊗",Ouml:"Ö",ouml:"ö",ovbar:"⌽",OverBar:"‾",OverBrace:"⏞",OverBracket:"⎴",OverParenthesis:"⏜",para:"¶",parallel:"∥",par:"∥",parsim:"⫳",parsl:"⫽",part:"∂",PartialD:"∂",Pcy:"П",pcy:"п",percnt:"%",period:".",permil:"‰",perp:"⊥",pertenk:"‱",Pfr:"𝔓",pfr:"𝔭",Phi:"Φ",phi:"φ",phiv:"ϕ",phmmat:"ℳ",phone:"☎",Pi:"Π",pi:"π",pitchfork:"⋔",piv:"ϖ",planck:"ℏ",planckh:"ℎ",plankv:"ℏ",plusacir:"⨣",plusb:"⊞",pluscir:"⨢",plus:"+",plusdo:"∔",plusdu:"⨥",pluse:"⩲",PlusMinus:"±",plusmn:"±",plussim:"⨦",plustwo:"⨧",pm:"±",Poincareplane:"ℌ",pointint:"⨕",popf:"𝕡",Popf:"ℙ",pound:"£",prap:"⪷",Pr:"⪻",pr:"≺",prcue:"≼",precapprox:"⪷",prec:"≺",preccurlyeq:"≼",Precedes:"≺",PrecedesEqual:"⪯",PrecedesSlantEqual:"≼",PrecedesTilde:"≾",preceq:"⪯",precnapprox:"⪹",precneqq:"⪵",precnsim:"⋨",pre:"⪯",prE:"⪳",precsim:"≾",prime:"′",Prime:"″",primes:"ℙ",prnap:"⪹",prnE:"⪵",prnsim:"⋨",prod:"∏",Product:"∏",profalar:"⌮",profline:"⌒",profsurf:"⌓",prop:"∝",Proportional:"∝",Proportion:"∷",propto:"∝",prsim:"≾",prurel:"⊰",Pscr:"𝒫",pscr:"𝓅",Psi:"Ψ",psi:"ψ",puncsp:" ",Qfr:"𝔔",qfr:"𝔮",qint:"⨌",qopf:"𝕢",Qopf:"ℚ",qprime:"⁗",Qscr:"𝒬",qscr:"𝓆",quaternions:"ℍ",quatint:"⨖",quest:"?",questeq:"≟",quot:'"',QUOT:'"',rAarr:"⇛",race:"∽̱",Racute:"Ŕ",racute:"ŕ",radic:"√",raemptyv:"⦳",rang:"⟩",Rang:"⟫",rangd:"⦒",range:"⦥",rangle:"⟩",raquo:"»",rarrap:"⥵",rarrb:"⇥",rarrbfs:"⤠",rarrc:"⤳",rarr:"→",Rarr:"↠",rArr:"⇒",rarrfs:"⤞",rarrhk:"↪",rarrlp:"↬",rarrpl:"⥅",rarrsim:"⥴",Rarrtl:"⤖",rarrtl:"↣",rarrw:"↝",ratail:"⤚",rAtail:"⤜",ratio:"∶",rationals:"ℚ",rbarr:"⤍",rBarr:"⤏",RBarr:"⤐",rbbrk:"❳",rbrace:"}",rbrack:"]",rbrke:"⦌",rbrksld:"⦎",rbrkslu:"⦐",Rcaron:"Ř",rcaron:"ř",Rcedil:"Ŗ",rcedil:"ŗ",rceil:"⌉",rcub:"}",Rcy:"Р",rcy:"р",rdca:"⤷",rdldhar:"⥩",rdquo:"”",rdquor:"”",rdsh:"↳",real:"ℜ",realine:"ℛ",realpart:"ℜ",reals:"ℝ",Re:"ℜ",rect:"▭",reg:"®",REG:"®",ReverseElement:"∋",ReverseEquilibrium:"⇋",ReverseUpEquilibrium:"⥯",rfisht:"⥽",rfloor:"⌋",rfr:"𝔯",Rfr:"ℜ",rHar:"⥤",rhard:"⇁",rharu:"⇀",rharul:"⥬",Rho:"Ρ",rho:"ρ",rhov:"ϱ",RightAngleBracket:"⟩",RightArrowBar:"⇥",rightarrow:"→",RightArrow:"→",Rightarrow:"⇒",RightArrowLeftArrow:"⇄",rightarrowtail:"↣",RightCeiling:"⌉",RightDoubleBracket:"⟧",RightDownTeeVector:"⥝",RightDownVectorBar:"⥕",RightDownVector:"⇂",RightFloor:"⌋",rightharpoondown:"⇁",rightharpoonup:"⇀",rightleftarrows:"⇄",rightleftharpoons:"⇌",rightrightarrows:"⇉",rightsquigarrow:"↝",RightTeeArrow:"↦",RightTee:"⊢",RightTeeVector:"⥛",rightthreetimes:"⋌",RightTriangleBar:"⧐",RightTriangle:"⊳",RightTriangleEqual:"⊵",RightUpDownVector:"⥏",RightUpTeeVector:"⥜",RightUpVectorBar:"⥔",RightUpVector:"↾",RightVectorBar:"⥓",RightVector:"⇀",ring:"˚",risingdotseq:"≓",rlarr:"⇄",rlhar:"⇌",rlm:"‏",rmoustache:"⎱",rmoust:"⎱",rnmid:"⫮",roang:"⟭",roarr:"⇾",robrk:"⟧",ropar:"⦆",ropf:"𝕣",Ropf:"ℝ",roplus:"⨮",rotimes:"⨵",RoundImplies:"⥰",rpar:")",rpargt:"⦔",rppolint:"⨒",rrarr:"⇉",Rrightarrow:"⇛",rsaquo:"›",rscr:"𝓇",Rscr:"ℛ",rsh:"↱",Rsh:"↱",rsqb:"]",rsquo:"’",rsquor:"’",rthree:"⋌",rtimes:"⋊",rtri:"▹",rtrie:"⊵",rtrif:"▸",rtriltri:"⧎",RuleDelayed:"⧴",ruluhar:"⥨",rx:"℞",Sacute:"Ś",sacute:"ś",sbquo:"‚",scap:"⪸",Scaron:"Š",scaron:"š",Sc:"⪼",sc:"≻",sccue:"≽",sce:"⪰",scE:"⪴",Scedil:"Ş",scedil:"ş",Scirc:"Ŝ",scirc:"ŝ",scnap:"⪺",scnE:"⪶",scnsim:"⋩",scpolint:"⨓",scsim:"≿",Scy:"С",scy:"с",sdotb:"⊡",sdot:"⋅",sdote:"⩦",searhk:"⤥",searr:"↘",seArr:"⇘",searrow:"↘",sect:"§",semi:";",seswar:"⤩",setminus:"∖",setmn:"∖",sext:"✶",Sfr:"𝔖",sfr:"𝔰",sfrown:"⌢",sharp:"♯",SHCHcy:"Щ",shchcy:"щ",SHcy:"Ш",shcy:"ш",ShortDownArrow:"↓",ShortLeftArrow:"←",shortmid:"∣",shortparallel:"∥",ShortRightArrow:"→",ShortUpArrow:"↑",shy:"­",Sigma:"Σ",sigma:"σ",sigmaf:"ς",sigmav:"ς",sim:"∼",simdot:"⩪",sime:"≃",simeq:"≃",simg:"⪞",simgE:"⪠",siml:"⪝",simlE:"⪟",simne:"≆",simplus:"⨤",simrarr:"⥲",slarr:"←",SmallCircle:"∘",smallsetminus:"∖",smashp:"⨳",smeparsl:"⧤",smid:"∣",smile:"⌣",smt:"⪪",smte:"⪬",smtes:"⪬︀",SOFTcy:"Ь",softcy:"ь",solbar:"⌿",solb:"⧄",sol:"/",Sopf:"𝕊",sopf:"𝕤",spades:"♠",spadesuit:"♠",spar:"∥",sqcap:"⊓",sqcaps:"⊓︀",sqcup:"⊔",sqcups:"⊔︀",Sqrt:"√",sqsub:"⊏",sqsube:"⊑",sqsubset:"⊏",sqsubseteq:"⊑",sqsup:"⊐",sqsupe:"⊒",sqsupset:"⊐",sqsupseteq:"⊒",square:"□",Square:"□",SquareIntersection:"⊓",SquareSubset:"⊏",SquareSubsetEqual:"⊑",SquareSuperset:"⊐",SquareSupersetEqual:"⊒",SquareUnion:"⊔",squarf:"▪",squ:"□",squf:"▪",srarr:"→",Sscr:"𝒮",sscr:"𝓈",ssetmn:"∖",ssmile:"⌣",sstarf:"⋆",Star:"⋆",star:"☆",starf:"★",straightepsilon:"ϵ",straightphi:"ϕ",strns:"¯",sub:"⊂",Sub:"⋐",subdot:"⪽",subE:"⫅",sube:"⊆",subedot:"⫃",submult:"⫁",subnE:"⫋",subne:"⊊",subplus:"⪿",subrarr:"⥹",subset:"⊂",Subset:"⋐",subseteq:"⊆",subseteqq:"⫅",SubsetEqual:"⊆",subsetneq:"⊊",subsetneqq:"⫋",subsim:"⫇",subsub:"⫕",subsup:"⫓",succapprox:"⪸",succ:"≻",succcurlyeq:"≽",Succeeds:"≻",SucceedsEqual:"⪰",SucceedsSlantEqual:"≽",SucceedsTilde:"≿",succeq:"⪰",succnapprox:"⪺",succneqq:"⪶",succnsim:"⋩",succsim:"≿",SuchThat:"∋",sum:"∑",Sum:"∑",sung:"♪",sup1:"¹",sup2:"²",sup3:"³",sup:"⊃",Sup:"⋑",supdot:"⪾",supdsub:"⫘",supE:"⫆",supe:"⊇",supedot:"⫄",Superset:"⊃",SupersetEqual:"⊇",suphsol:"⟉",suphsub:"⫗",suplarr:"⥻",supmult:"⫂",supnE:"⫌",supne:"⊋",supplus:"⫀",supset:"⊃",Supset:"⋑",supseteq:"⊇",supseteqq:"⫆",supsetneq:"⊋",supsetneqq:"⫌",supsim:"⫈",supsub:"⫔",supsup:"⫖",swarhk:"⤦",swarr:"↙",swArr:"⇙",swarrow:"↙",swnwar:"⤪",szlig:"ß",Tab:"\t",target:"⌖",Tau:"Τ",tau:"τ",tbrk:"⎴",Tcaron:"Ť",tcaron:"ť",Tcedil:"Ţ",tcedil:"ţ",Tcy:"Т",tcy:"т",tdot:"⃛",telrec:"⌕",Tfr:"𝔗",tfr:"𝔱",there4:"∴",therefore:"∴",Therefore:"∴",Theta:"Θ",theta:"θ",thetasym:"ϑ",thetav:"ϑ",thickapprox:"≈",thicksim:"∼",ThickSpace:"  ",ThinSpace:" ",thinsp:" ",thkap:"≈",thksim:"∼",THORN:"Þ",thorn:"þ",tilde:"˜",Tilde:"∼",TildeEqual:"≃",TildeFullEqual:"≅",TildeTilde:"≈",timesbar:"⨱",timesb:"⊠",times:"×",timesd:"⨰",tint:"∭",toea:"⤨",topbot:"⌶",topcir:"⫱",top:"⊤",Topf:"𝕋",topf:"𝕥",topfork:"⫚",tosa:"⤩",tprime:"‴",trade:"™",TRADE:"™",triangle:"▵",triangledown:"▿",triangleleft:"◃",trianglelefteq:"⊴",triangleq:"≜",triangleright:"▹",trianglerighteq:"⊵",tridot:"◬",trie:"≜",triminus:"⨺",TripleDot:"⃛",triplus:"⨹",trisb:"⧍",tritime:"⨻",trpezium:"⏢",Tscr:"𝒯",tscr:"𝓉",TScy:"Ц",tscy:"ц",TSHcy:"Ћ",tshcy:"ћ",Tstrok:"Ŧ",tstrok:"ŧ",twixt:"≬",twoheadleftarrow:"↞",twoheadrightarrow:"↠",Uacute:"Ú",uacute:"ú",uarr:"↑",Uarr:"↟",uArr:"⇑",Uarrocir:"⥉",Ubrcy:"Ў",ubrcy:"ў",Ubreve:"Ŭ",ubreve:"ŭ",Ucirc:"Û",ucirc:"û",Ucy:"У",ucy:"у",udarr:"⇅",Udblac:"Ű",udblac:"ű",udhar:"⥮",ufisht:"⥾",Ufr:"𝔘",ufr:"𝔲",Ugrave:"Ù",ugrave:"ù",uHar:"⥣",uharl:"↿",uharr:"↾",uhblk:"▀",ulcorn:"⌜",ulcorner:"⌜",ulcrop:"⌏",ultri:"◸",Umacr:"Ū",umacr:"ū",uml:"¨",UnderBar:"_",UnderBrace:"⏟",UnderBracket:"⎵",UnderParenthesis:"⏝",Union:"⋃",UnionPlus:"⊎",Uogon:"Ų",uogon:"ų",Uopf:"𝕌",uopf:"𝕦",UpArrowBar:"⤒",uparrow:"↑",UpArrow:"↑",Uparrow:"⇑",UpArrowDownArrow:"⇅",updownarrow:"↕",UpDownArrow:"↕",Updownarrow:"⇕",UpEquilibrium:"⥮",upharpoonleft:"↿",upharpoonright:"↾",uplus:"⊎",UpperLeftArrow:"↖",UpperRightArrow:"↗",upsi:"υ",Upsi:"ϒ",upsih:"ϒ",Upsilon:"Υ",upsilon:"υ",UpTeeArrow:"↥",UpTee:"⊥",upuparrows:"⇈",urcorn:"⌝",urcorner:"⌝",urcrop:"⌎",Uring:"Ů",uring:"ů",urtri:"◹",Uscr:"𝒰",uscr:"𝓊",utdot:"⋰",Utilde:"Ũ",utilde:"ũ",utri:"▵",utrif:"▴",uuarr:"⇈",Uuml:"Ü",uuml:"ü",uwangle:"⦧",vangrt:"⦜",varepsilon:"ϵ",varkappa:"ϰ",varnothing:"∅",varphi:"ϕ",varpi:"ϖ",varpropto:"∝",varr:"↕",vArr:"⇕",varrho:"ϱ",varsigma:"ς",varsubsetneq:"⊊︀",varsubsetneqq:"⫋︀",varsupsetneq:"⊋︀",varsupsetneqq:"⫌︀",vartheta:"ϑ",vartriangleleft:"⊲",vartriangleright:"⊳",vBar:"⫨",Vbar:"⫫",vBarv:"⫩",Vcy:"В",vcy:"в",vdash:"⊢",vDash:"⊨",Vdash:"⊩",VDash:"⊫",Vdashl:"⫦",veebar:"⊻",vee:"∨",Vee:"⋁",veeeq:"≚",vellip:"⋮",verbar:"|",Verbar:"‖",vert:"|",Vert:"‖",VerticalBar:"∣",VerticalLine:"|",VerticalSeparator:"❘",VerticalTilde:"≀",VeryThinSpace:" ",Vfr:"𝔙",vfr:"𝔳",vltri:"⊲",vnsub:"⊂⃒",vnsup:"⊃⃒",Vopf:"𝕍",vopf:"𝕧",vprop:"∝",vrtri:"⊳",Vscr:"𝒱",vscr:"𝓋",vsubnE:"⫋︀",vsubne:"⊊︀",vsupnE:"⫌︀",vsupne:"⊋︀",Vvdash:"⊪",vzigzag:"⦚",Wcirc:"Ŵ",wcirc:"ŵ",wedbar:"⩟",wedge:"∧",Wedge:"⋀",wedgeq:"≙",weierp:"℘",Wfr:"𝔚",wfr:"𝔴",Wopf:"𝕎",wopf:"𝕨",wp:"℘",wr:"≀",wreath:"≀",Wscr:"𝒲",wscr:"𝓌",xcap:"⋂",xcirc:"◯",xcup:"⋃",xdtri:"▽",Xfr:"𝔛",xfr:"𝔵",xharr:"⟷",xhArr:"⟺",Xi:"Ξ",xi:"ξ",xlarr:"⟵",xlArr:"⟸",xmap:"⟼",xnis:"⋻",xodot:"⨀",Xopf:"𝕏",xopf:"𝕩",xoplus:"⨁",xotime:"⨂",xrarr:"⟶",xrArr:"⟹",Xscr:"𝒳",xscr:"𝓍",xsqcup:"⨆",xuplus:"⨄",xutri:"△",xvee:"⋁",xwedge:"⋀",Yacute:"Ý",yacute:"ý",YAcy:"Я",yacy:"я",Ycirc:"Ŷ",ycirc:"ŷ",Ycy:"Ы",ycy:"ы",yen:"¥",Yfr:"𝔜",yfr:"𝔶",YIcy:"Ї",yicy:"ї",Yopf:"𝕐",yopf:"𝕪",Yscr:"𝒴",yscr:"𝓎",YUcy:"Ю",yucy:"ю",yuml:"ÿ",Yuml:"Ÿ",Zacute:"Ź",zacute:"ź",Zcaron:"Ž",zcaron:"ž",Zcy:"З",zcy:"з",Zdot:"Ż",zdot:"ż",zeetrf:"ℨ",ZeroWidthSpace:"​",Zeta:"Ζ",zeta:"ζ",zfr:"𝔷",Zfr:"ℨ",ZHcy:"Ж",zhcy:"ж",zigrarr:"⇝",zopf:"𝕫",Zopf:"ℤ",Zscr:"𝒵",zscr:"𝓏",zwj:"‍",zwnj:"‌"}}));
;/*!node_modules/uc.micro/categories/P/regex.js*/
amis.define("c8815ce",(function(u,D,F,E){F.exports=/[!-#%-\*,-\/:;\?@\[-\]_\{\}\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061E\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166D\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4E\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]|\uD800[\uDD00-\uDD02\uDF9F\uDFD0]|\uD801\uDD6F|\uD802[\uDC57\uDD1F\uDD3F\uDE50-\uDE58\uDE7F\uDEF0-\uDEF6\uDF39-\uDF3F\uDF99-\uDF9C]|\uD803[\uDF55-\uDF59]|\uD804[\uDC47-\uDC4D\uDCBB\uDCBC\uDCBE-\uDCC1\uDD40-\uDD43\uDD74\uDD75\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDDF\uDE38-\uDE3D\uDEA9]|\uD805[\uDC4B-\uDC4F\uDC5B\uDC5D\uDCC6\uDDC1-\uDDD7\uDE41-\uDE43\uDE60-\uDE6C\uDF3C-\uDF3E]|\uD806[\uDC3B\uDE3F-\uDE46\uDE9A-\uDE9C\uDE9E-\uDEA2]|\uD807[\uDC41-\uDC45\uDC70\uDC71\uDEF7\uDEF8]|\uD809[\uDC70-\uDC74]|\uD81A[\uDE6E\uDE6F\uDEF5\uDF37-\uDF3B\uDF44]|\uD81B[\uDE97-\uDE9A]|\uD82F\uDC9F|\uD836[\uDE87-\uDE8B]|\uD83A[\uDD5E\uDD5F]/}));
;/*!node_modules/mdurl/encode.js*/
amis.define("8396db0",(function(e,t,r,n){"use strict";var o={};function i(e,t,r){var n,s,f,a,c,u="";for("string"!=typeof t&&(r=t,t=i.defaultChars),void 0===r&&(r=!0),c=function(e){var t,r,n=o[e];if(n)return n;for(n=o[e]=[],t=0;t<128;t++)r=String.fromCharCode(t),/^[0-9a-z]$/i.test(r)?n.push(r):n.push("%"+("0"+t.toString(16).toUpperCase()).slice(-2));for(t=0;t<e.length;t++)n[e.charCodeAt(t)]=e[t];return n}(t),n=0,s=e.length;n<s;n++)if(f=e.charCodeAt(n),r&&37===f&&n+2<s&&/^[0-9a-f]{2}$/i.test(e.slice(n+1,n+3)))u+=e.slice(n,n+3),n+=2;else if(f<128)u+=c[f];else if(f>=55296&&f<=57343){if(f>=55296&&f<=56319&&n+1<s&&(a=e.charCodeAt(n+1))>=56320&&a<=57343){u+=encodeURIComponent(e[n]+e[n+1]),n++;continue}u+="%EF%BF%BD"}else u+=encodeURIComponent(e[n]);return u}i.defaultChars=";/?:@&=+$,-_.!~*'()#",i.componentChars="-_.!~*'()",r.exports=i}));
;/*!node_modules/mdurl/decode.js*/
amis.define("58c4763",(function(r,e,t,n){"use strict";var a={};function s(r,e){var t;return"string"!=typeof e&&(e=s.defaultChars),t=function(r){var e,t,n=a[r];if(n)return n;for(n=a[r]=[],e=0;e<128;e++)t=String.fromCharCode(e),n.push(t);for(e=0;e<r.length;e++)n[t=r.charCodeAt(e)]="%"+("0"+t.toString(16).toUpperCase()).slice(-2);return n}(e),r.replace(/(%[a-f0-9]{2})+/gi,(function(r){var e,n,a,s,i,o,c,f="";for(e=0,n=r.length;e<n;e+=3)(a=parseInt(r.slice(e+1,e+3),16))<128?f+=t[a]:192==(224&a)&&e+3<n&&128==(192&(s=parseInt(r.slice(e+4,e+6),16)))?(f+=(c=a<<6&1984|63&s)<128?"��":String.fromCharCode(c),e+=3):224==(240&a)&&e+6<n&&(s=parseInt(r.slice(e+4,e+6),16),i=parseInt(r.slice(e+7,e+9),16),128==(192&s)&&128==(192&i))?(f+=(c=a<<12&61440|s<<6&4032|63&i)<2048||c>=55296&&c<=57343?"���":String.fromCharCode(c),e+=6):240==(248&a)&&e+9<n&&(s=parseInt(r.slice(e+4,e+6),16),i=parseInt(r.slice(e+7,e+9),16),o=parseInt(r.slice(e+10,e+12),16),128==(192&s)&&128==(192&i)&&128==(192&o))?((c=a<<18&1835008|s<<12&258048|i<<6&4032|63&o)<65536||c>1114111?f+="����":(c-=65536,f+=String.fromCharCode(55296+(c>>10),56320+(1023&c))),e+=9):f+="�";return f}))}s.defaultChars=";/?:@&=+$,#",s.componentChars="",t.exports=s}));
;/*!node_modules/mdurl/format.js*/
amis.define("65cffa5",(function(t,a,e,s){"use strict";e.exports=function(t){var a="";return a+=t.protocol||"",a+=t.slashes?"//":"",a+=t.auth?t.auth+"@":"",t.hostname&&-1!==t.hostname.indexOf(":")?a+="["+t.hostname+"]":a+=t.hostname||"",a+=t.port?":"+t.port:"",a+=t.pathname||"",a+=t.search||"",a+=t.hash||""}}));
;/*!node_modules/mdurl/parse.js*/
amis.define("e7b66f8",(function(t,s,h,e){"use strict";function i(){this.protocol=null,this.slashes=null,this.auth=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.pathname=null}var a=/^([a-z0-9.+-]+:)/i,n=/:[0-9]*$/,r=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,o=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r","\n","\t"]),l=["'"].concat(o),c=["%","/","?",";","#"].concat(l),p=["/","?","#"],f=/^[+a-z0-9A-Z_-]{0,63}$/,u=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,m={javascript:!0,"javascript:":!0},v={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0};i.prototype.parse=function(t,s){var h,e,i,n,o,l=t;if(l=l.trim(),!s&&1===t.split("#").length){var g=r.exec(l);if(g)return this.pathname=g[1],g[2]&&(this.search=g[2]),this}var x=a.exec(l);if(x&&(i=(x=x[0]).toLowerCase(),this.protocol=x,l=l.substr(x.length)),(s||x||l.match(/^\/\/[^@\/]+@[^@\/]+/))&&(!(o="//"===l.substr(0,2))||x&&m[x]||(l=l.substr(2),this.slashes=!0)),!m[x]&&(o||x&&!v[x])){var b,d,O=-1;for(h=0;h<p.length;h++)-1!==(n=l.indexOf(p[h]))&&(-1===O||n<O)&&(O=n);for(-1!==(d=-1===O?l.lastIndexOf("@"):l.lastIndexOf("@",O))&&(b=l.slice(0,d),l=l.slice(d+1),this.auth=b),O=-1,h=0;h<c.length;h++)-1!==(n=l.indexOf(c[h]))&&(-1===O||n<O)&&(O=n);-1===O&&(O=l.length),":"===l[O-1]&&O--;var j=l.slice(0,O);l=l.slice(O),this.parseHost(j),this.hostname=this.hostname||"";var $="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!$){var z=this.hostname.split(/\./);for(h=0,e=z.length;h<e;h++){var A=z[h];if(A&&!A.match(f)){for(var w="",y=0,C=A.length;y<C;y++)A.charCodeAt(y)>127?w+="x":w+=A[y];if(!w.match(f)){var H=z.slice(0,h),I=z.slice(h+1),Z=A.match(u);Z&&(H.push(Z[1]),I.unshift(Z[2])),I.length&&(l=I.join(".")+l),this.hostname=H.join(".");break}}}}this.hostname.length>255&&(this.hostname=""),$&&(this.hostname=this.hostname.substr(1,this.hostname.length-2))}var _=l.indexOf("#");-1!==_&&(this.hash=l.substr(_),l=l.slice(0,_));var k=l.indexOf("?");return-1!==k&&(this.search=l.substr(k),l=l.slice(0,k)),l&&(this.pathname=l),v[i]&&this.hostname&&!this.pathname&&(this.pathname=""),this},i.prototype.parseHost=function(t){var s=n.exec(t);s&&(":"!==(s=s[0])&&(this.port=s.substr(1)),t=t.substr(0,t.length-s.length)),t&&(this.hostname=t)},h.exports=function(t,s){if(t&&t instanceof i)return t;var h=new i;return h.parse(t,s),h}}));
;/*!node_modules/mdurl/index.js*/
amis.define("de5c5db",(function(e,o,s,t){"use strict";s.exports.encode=e("8396db0"),s.exports.decode=e("58c4763"),s.exports.format=e("65cffa5"),s.exports.parse=e("e7b66f8")}));
;/*!node_modules/uc.micro/properties/Any/regex.js*/
amis.define("9a1c1d9",(function(F,u,D,e){D.exports=/[\0-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/}));
;/*!node_modules/uc.micro/categories/Cc/regex.js*/
amis.define("143c403",(function(x,e,i,n){i.exports=/[\0-\x1F\x7F-\x9F]/}));
;/*!node_modules/uc.micro/categories/Cf/regex.js*/
amis.define("c1c6790",(function(u,D,F,C){F.exports=/[\xAD\u0600-\u0605\u061C\u06DD\u070F\u08E2\u180E\u200B-\u200F\u202A-\u202E\u2060-\u2064\u2066-\u206F\uFEFF\uFFF9-\uFFFB]|\uD804[\uDCBD\uDCCD]|\uD82F[\uDCA0-\uDCA3]|\uD834[\uDD73-\uDD7A]|\uDB40[\uDC01\uDC20-\uDC7F]/}));
;/*!node_modules/uc.micro/categories/Z/regex.js*/
amis.define("50a8187",(function(u,e,i,n){i.exports=/[ \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]/}));
;/*!node_modules/uc.micro/index.js*/
amis.define("72f4f43",(function(c,f,e,i){"use strict";f.Any=c("9a1c1d9"),f.Cc=c("143c403"),f.Cf=c("c1c6790"),f.P=c("c8815ce"),f.Z=c("50a8187")}));
;/*!node_modules/markdown-it/lib/common/utils.js*/
amis.define("19ebe76",(function(e,c,r,t){"use strict";var a=Object.prototype.hasOwnProperty;function n(e,c){return a.call(e,c)}function s(e){return!(e>=55296&&e<=57343)&&(!(e>=64976&&e<=65007)&&(65535!=(65535&e)&&65534!=(65535&e)&&(!(e>=0&&e<=8)&&(11!==e&&(!(e>=14&&e<=31)&&(!(e>=127&&e<=159)&&!(e>1114111)))))))}function i(e){if(e>65535){var c=55296+((e-=65536)>>10),r=56320+(1023&e);return String.fromCharCode(c,r)}return String.fromCharCode(e)}var o=/\\([!"#$%&'()*+,\-.\/:;<=>?@[\\\]^_`{|}~])/g,u=new RegExp(o.source+"|"+/&([a-z#][a-z0-9]{1,31});/gi.source,"gi"),f=/^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))/i,l=e("3a8a690");var p=/[&<>"]/,d=/[&<>"]/g,g={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};function h(e){return g[e]}var b=/[.?*+^$[\]\\(){}|-]/g;var C=e("c8815ce");c.lib={},c.lib.mdurl=e("de5c5db"),c.lib.ucmicro=e("72f4f43"),c.assign=function(e){var c=Array.prototype.slice.call(arguments,1);return c.forEach((function(c){if(c){if("object"!=typeof c)throw new TypeError(c+"must be object");Object.keys(c).forEach((function(r){e[r]=c[r]}))}})),e},c.isString=function(e){return"[object String]"===function(e){return Object.prototype.toString.call(e)}(e)},c.has=n,c.unescapeMd=function(e){return e.indexOf("\\")<0?e:e.replace(o,"$1")},c.unescapeAll=function(e){return e.indexOf("\\")<0&&e.indexOf("&")<0?e:e.replace(u,(function(e,c,r){return c||function(e,c){var r=0;return n(l,c)?l[c]:35===c.charCodeAt(0)&&f.test(c)&&s(r="x"===c[1].toLowerCase()?parseInt(c.slice(2),16):parseInt(c.slice(1),10))?i(r):e}(e,r)}))},c.isValidEntityCode=s,c.fromCodePoint=i,c.escapeHtml=function(e){return p.test(e)?e.replace(d,h):e},c.arrayReplaceAt=function(e,c,r){return[].concat(e.slice(0,c),r,e.slice(c+1))},c.isSpace=function(e){switch(e){case 9:case 32:return!0}return!1},c.isWhiteSpace=function(e){if(e>=8192&&e<=8202)return!0;switch(e){case 9:case 10:case 11:case 12:case 13:case 32:case 160:case 5760:case 8239:case 8287:case 12288:return!0}return!1},c.isMdAsciiPunct=function(e){switch(e){case 33:case 34:case 35:case 36:case 37:case 38:case 39:case 40:case 41:case 42:case 43:case 44:case 45:case 46:case 47:case 58:case 59:case 60:case 61:case 62:case 63:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 124:case 125:case 126:return!0;default:return!1}},c.isPunctChar=function(e){return C.test(e)},c.escapeRE=function(e){return e.replace(b,"\\$&")},c.normalizeReference=function(e){return e=e.trim().replace(/\s+/g," "),"Ṿ"==="ẞ".toLowerCase()&&(e=e.replace(/ẞ/g,"ß")),e.toLowerCase().toUpperCase()}}));
;/*!node_modules/markdown-it/lib/helpers/parse_link_label.js*/
amis.define("41f4502",(function(s,o,e,i){"use strict";e.exports=function(s,o,e){var i,p,r,n,f=-1,t=s.posMax,a=s.pos;for(s.pos=o+1,i=1;s.pos<t;){if(93===(r=s.src.charCodeAt(s.pos))&&0===--i){p=!0;break}if(n=s.pos,s.md.inline.skipToken(s),91===r)if(n===s.pos-1)i++;else if(e)return s.pos=a,-1}return p&&(f=s.pos),s.pos=a,f}}));
;/*!node_modules/markdown-it/lib/helpers/parse_link_destination.js*/
amis.define("25bca96",(function(r,e,i,t){"use strict";var s=r("19ebe76").unescapeAll;i.exports=function(r,e,i){var t,o,f=e,n={ok:!1,pos:0,lines:0,str:""};if(60===r.charCodeAt(e)){for(e++;e<i;){if(10===(t=r.charCodeAt(e)))return n;if(60===t)return n;if(62===t)return n.pos=e+1,n.str=s(r.slice(f+1,e)),n.ok=!0,n;92===t&&e+1<i?e+=2:e++}return n}for(o=0;e<i&&32!==(t=r.charCodeAt(e))&&!(t<32||127===t);)if(92===t&&e+1<i){if(32===r.charCodeAt(e+1))break;e+=2}else{if(40===t&&++o>32)return n;if(41===t){if(0===o)break;o--}e++}return f===e||0!==o||(n.str=s(r.slice(f,e)),n.lines=0,n.pos=e,n.ok=!0),n}}));
;/*!node_modules/markdown-it/lib/helpers/parse_link_title.js*/
amis.define("87b3365",(function(r,e,t,n){"use strict";var i=r("19ebe76").unescapeAll;t.exports=function(r,e,t){var n,s,o=0,u=e,c={ok:!1,pos:0,lines:0,str:""};if(e>=t)return c;if(34!==(s=r.charCodeAt(e))&&39!==s&&40!==s)return c;for(e++,40===s&&(s=41);e<t;){if((n=r.charCodeAt(e))===s)return c.pos=e+1,c.lines=o,c.str=i(r.slice(u+1,e)),c.ok=!0,c;if(40===n&&41===s)return c;10===n?o++:92===n&&e+1<t&&(e++,10===r.charCodeAt(e)&&o++),e++}return c}}));
;/*!node_modules/markdown-it/lib/helpers/index.js*/
amis.define("33cc776",(function(i,e,n,a){"use strict";e.parseLinkLabel=i("41f4502"),e.parseLinkDestination=i("25bca96"),e.parseLinkTitle=i("87b3365")}));
;/*!node_modules/markdown-it/lib/renderer.js*/
amis.define("6b95629",(function(e,n,t,r){"use strict";var i=e("19ebe76").assign,o=e("19ebe76").unescapeAll,s=e("19ebe76").escapeHtml,c={};function l(){this.rules=i({},c)}c.code_inline=function(e,n,t,r,i){var o=e[n];return"<code"+i.renderAttrs(o)+">"+s(e[n].content)+"</code>"},c.code_block=function(e,n,t,r,i){var o=e[n];return"<pre"+i.renderAttrs(o)+"><code>"+s(e[n].content)+"</code></pre>\n"},c.fence=function(e,n,t,r,i){var c,l,d,u,a,h=e[n],f=h.info?o(h.info).trim():"",p="",g="";return f&&(p=(d=f.split(/(\s+)/g))[0],g=d.slice(2).join("")),0===(c=t.highlight&&t.highlight(h.content,p,g)||s(h.content)).indexOf("<pre")?c+"\n":f?(l=h.attrIndex("class"),u=h.attrs?h.attrs.slice():[],l<0?u.push(["class",t.langPrefix+p]):(u[l]=u[l].slice(),u[l][1]+=" "+t.langPrefix+p),a={attrs:u},"<pre><code"+i.renderAttrs(a)+">"+c+"</code></pre>\n"):"<pre><code"+i.renderAttrs(h)+">"+c+"</code></pre>\n"},c.image=function(e,n,t,r,i){var o=e[n];return o.attrs[o.attrIndex("alt")][1]=i.renderInlineAsText(o.children,t,r),i.renderToken(e,n,t)},c.hardbreak=function(e,n,t){return t.xhtmlOut?"<br />\n":"<br>\n"},c.softbreak=function(e,n,t){return t.breaks?t.xhtmlOut?"<br />\n":"<br>\n":"\n"},c.text=function(e,n){return s(e[n].content)},c.html_block=function(e,n){return e[n].content},c.html_inline=function(e,n){return e[n].content},l.prototype.renderAttrs=function(e){var n,t,r;if(!e.attrs)return"";for(r="",n=0,t=e.attrs.length;n<t;n++)r+=" "+s(e.attrs[n][0])+'="'+s(e.attrs[n][1])+'"';return r},l.prototype.renderToken=function(e,n,t){var r,i="",o=!1,s=e[n];return s.hidden?"":(s.block&&-1!==s.nesting&&n&&e[n-1].hidden&&(i+="\n"),i+=(-1===s.nesting?"</":"<")+s.tag,i+=this.renderAttrs(s),0===s.nesting&&t.xhtmlOut&&(i+=" /"),s.block&&(o=!0,1===s.nesting&&n+1<e.length&&("inline"===(r=e[n+1]).type||r.hidden||-1===r.nesting&&r.tag===s.tag)&&(o=!1)),i+=o?">\n":">")},l.prototype.renderInline=function(e,n,t){for(var r,i="",o=this.rules,s=0,c=e.length;s<c;s++)void 0!==o[r=e[s].type]?i+=o[r](e,s,n,t,this):i+=this.renderToken(e,s,n);return i},l.prototype.renderInlineAsText=function(e,n,t){for(var r="",i=0,o=e.length;i<o;i++)"text"===e[i].type?r+=e[i].content:"image"===e[i].type?r+=this.renderInlineAsText(e[i].children,n,t):"softbreak"===e[i].type&&(r+="\n");return r},l.prototype.render=function(e,n,t){var r,i,o,s="",c=this.rules;for(r=0,i=e.length;r<i;r++)"inline"===(o=e[r].type)?s+=this.renderInline(e[r].children,n,t):void 0!==c[o]?s+=c[e[r].type](e,r,n,t,this):s+=this.renderToken(e,r,n,t);return s},t.exports=l}));
;/*!node_modules/markdown-it/lib/ruler.js*/
amis.define("8dbfc89",(function(_,e,r,n){"use strict";function t(){this.__rules__=[],this.__cache__=null}t.prototype.__find__=function(_){for(var e=0;e<this.__rules__.length;e++)if(this.__rules__[e].name===_)return e;return-1},t.prototype.__compile__=function(){var _=this,e=[""];_.__rules__.forEach((function(_){_.enabled&&_.alt.forEach((function(_){e.indexOf(_)<0&&e.push(_)}))})),_.__cache__={},e.forEach((function(e){_.__cache__[e]=[],_.__rules__.forEach((function(r){r.enabled&&(e&&r.alt.indexOf(e)<0||_.__cache__[e].push(r.fn))}))}))},t.prototype.at=function(_,e,r){var n=this.__find__(_),t=r||{};if(-1===n)throw new Error("Parser rule not found: "+_);this.__rules__[n].fn=e,this.__rules__[n].alt=t.alt||[],this.__cache__=null},t.prototype.before=function(_,e,r,n){var t=this.__find__(_),i=n||{};if(-1===t)throw new Error("Parser rule not found: "+_);this.__rules__.splice(t,0,{name:e,enabled:!0,fn:r,alt:i.alt||[]}),this.__cache__=null},t.prototype.after=function(_,e,r,n){var t=this.__find__(_),i=n||{};if(-1===t)throw new Error("Parser rule not found: "+_);this.__rules__.splice(t+1,0,{name:e,enabled:!0,fn:r,alt:i.alt||[]}),this.__cache__=null},t.prototype.push=function(_,e,r){var n=r||{};this.__rules__.push({name:_,enabled:!0,fn:e,alt:n.alt||[]}),this.__cache__=null},t.prototype.enable=function(_,e){Array.isArray(_)||(_=[_]);var r=[];return _.forEach((function(_){var n=this.__find__(_);if(n<0){if(e)return;throw new Error("Rules manager: invalid rule name "+_)}this.__rules__[n].enabled=!0,r.push(_)}),this),this.__cache__=null,r},t.prototype.enableOnly=function(_,e){Array.isArray(_)||(_=[_]),this.__rules__.forEach((function(_){_.enabled=!1})),this.enable(_,e)},t.prototype.disable=function(_,e){Array.isArray(_)||(_=[_]);var r=[];return _.forEach((function(_){var n=this.__find__(_);if(n<0){if(e)return;throw new Error("Rules manager: invalid rule name "+_)}this.__rules__[n].enabled=!1,r.push(_)}),this),this.__cache__=null,r},t.prototype.getRules=function(_){return null===this.__cache__&&this.__compile__(),this.__cache__[_]||[]},r.exports=t}));
;/*!node_modules/markdown-it/lib/rules_core/normalize.js*/
amis.define("8c2133e",(function(e,r,c,n){"use strict";var s=/\r\n?|\n/g,a=/\0/g;c.exports=function(e){var r;r=(r=e.src.replace(s,"\n")).replace(a,"�"),e.src=r}}));
;/*!node_modules/markdown-it/lib/rules_core/block.js*/
amis.define("2f3c20f",(function(n,e,i,s){"use strict";i.exports=function(n){var e;n.inlineMode?((e=new n.Token("inline","",0)).content=n.src,e.map=[0,1],e.children=[],n.tokens.push(e)):n.md.block.parse(n.src,n.md,n.env,n.tokens)}}));
;/*!node_modules/markdown-it/lib/rules_core/inline.js*/
amis.define("6e2305d",(function(n,e,i,t){"use strict";i.exports=function(n){var e,i,t,o=n.tokens;for(i=0,t=o.length;i<t;i++)"inline"===(e=o[i]).type&&n.md.inline.parse(e.content,n.md,n.env,e.children)}}));
;/*!node_modules/markdown-it/lib/rules_core/linkify.js*/
amis.define("2699c7e",(function(e,t,n,i){"use strict";var l=e("19ebe76").arrayReplaceAt;function o(e){return/^<\/a\s*>/i.test(e)}n.exports=function(e){var t,n,i,a,s,r,c,m,k,p,f,h,d,u,y,v,x,T,w=e.tokens;if(e.md.options.linkify)for(n=0,i=w.length;n<i;n++)if("inline"===w[n].type&&e.md.linkify.pretest(w[n].content))for(d=0,t=(a=w[n].children).length-1;t>=0;t--)if("link_close"!==(r=a[t]).type){if("html_inline"===r.type&&(T=r.content,/^<a[>\s]/i.test(T)&&d>0&&d--,o(r.content)&&d++),!(d>0)&&"text"===r.type&&e.md.linkify.test(r.content)){for(k=r.content,x=e.md.linkify.match(k),c=[],h=r.level,f=0,m=0;m<x.length;m++)u=x[m].url,y=e.md.normalizeLink(u),e.md.validateLink(y)&&(v=x[m].text,v=x[m].schema?"mailto:"!==x[m].schema||/^mailto:/i.test(v)?e.md.normalizeLinkText(v):e.md.normalizeLinkText("mailto:"+v).replace(/^mailto:/,""):e.md.normalizeLinkText("http://"+v).replace(/^http:\/\//,""),(p=x[m].index)>f&&((s=new e.Token("text","",0)).content=k.slice(f,p),s.level=h,c.push(s)),(s=new e.Token("link_open","a",1)).attrs=[["href",y]],s.level=h++,s.markup="linkify",s.info="auto",c.push(s),(s=new e.Token("text","",0)).content=v,s.level=h,c.push(s),(s=new e.Token("link_close","a",-1)).level=--h,s.markup="linkify",s.info="auto",c.push(s),f=x[m].lastIndex);f<k.length&&((s=new e.Token("text","",0)).content=k.slice(f),s.level=h,c.push(s)),w[n].children=a=l(a,t,c)}}else for(t--;a[t].level!==r.level&&"link_open"!==a[t].type;)t--}}));
;/*!node_modules/markdown-it/lib/rules_core/replacements.js*/
amis.define("a1053c9",(function(e,t,n,o){"use strict";var c=/\+-|\.\.|\?\?\?\?|!!!!|,,|--/,r=/\((c|tm|r|p)\)/i,p=/\((c|tm|r|p)\)/gi,i={c:"©",r:"®",p:"§",tm:"™"};function a(e,t){return i[t.toLowerCase()]}function l(e){var t,n,o=0;for(t=e.length-1;t>=0;t--)"text"!==(n=e[t]).type||o||(n.content=n.content.replace(p,a)),"link_open"===n.type&&"auto"===n.info&&o--,"link_close"===n.type&&"auto"===n.info&&o++}function s(e){var t,n,o=0;for(t=e.length-1;t>=0;t--)"text"!==(n=e[t]).type||o||c.test(n.content)&&(n.content=n.content.replace(/\+-/g,"±").replace(/\.{2,}/g,"…").replace(/([?!])…/g,"$1..").replace(/([?!]){4,}/g,"$1$1$1").replace(/,{2,}/g,",").replace(/(^|[^-])---(?=[^-]|$)/gm,"$1—").replace(/(^|\s)--(?=\s|$)/gm,"$1–").replace(/(^|[^-\s])--(?=[^-\s]|$)/gm,"$1–")),"link_open"===n.type&&"auto"===n.info&&o--,"link_close"===n.type&&"auto"===n.info&&o++}n.exports=function(e){var t;if(e.md.options.typographer)for(t=e.tokens.length-1;t>=0;t--)"inline"===e.tokens[t].type&&(r.test(e.tokens[t].content)&&l(e.tokens[t].children),c.test(e.tokens[t].content)&&s(e.tokens[t].children))}}));
;/*!node_modules/markdown-it/lib/rules_core/smartquotes.js*/
amis.define("4c2a18b",(function(e,t,n,o){"use strict";var i=e("19ebe76").isWhiteSpace,r=e("19ebe76").isPunctChar,s=e("19ebe76").isMdAsciiPunct,c=/['"]/,l=/['"]/g;function f(e,t,n){return e.substr(0,t)+n+e.substr(t+1)}function h(e,t){var n,o,c,h,d,a,g,p,u,k,b,x,C,m,v,y,A,q,S,P,I;for(S=[],n=0;n<e.length;n++){for(o=e[n],g=e[n].level,A=S.length-1;A>=0&&!(S[A].level<=g);A--);if(S.length=A+1,"text"===o.type){d=0,a=(c=o.content).length;e:for(;d<a&&(l.lastIndex=d,h=l.exec(c));){if(v=y=!0,d=h.index+1,q="'"===h[0],u=32,h.index-1>=0)u=c.charCodeAt(h.index-1);else for(A=n-1;A>=0&&("softbreak"!==e[A].type&&"hardbreak"!==e[A].type);A--)if(e[A].content){u=e[A].content.charCodeAt(e[A].content.length-1);break}if(k=32,d<a)k=c.charCodeAt(d);else for(A=n+1;A<e.length&&("softbreak"!==e[A].type&&"hardbreak"!==e[A].type);A++)if(e[A].content){k=e[A].content.charCodeAt(0);break}if(b=s(u)||r(String.fromCharCode(u)),x=s(k)||r(String.fromCharCode(k)),C=i(u),(m=i(k))?v=!1:x&&(C||b||(v=!1)),C?y=!1:b&&(m||x||(y=!1)),34===k&&'"'===h[0]&&u>=48&&u<=57&&(y=v=!1),v&&y&&(v=b,y=x),v||y){if(y)for(A=S.length-1;A>=0&&(p=S[A],!(S[A].level<g));A--)if(p.single===q&&S[A].level===g){p=S[A],q?(P=t.md.options.quotes[2],I=t.md.options.quotes[3]):(P=t.md.options.quotes[0],I=t.md.options.quotes[1]),o.content=f(o.content,h.index,I),e[p.token].content=f(e[p.token].content,p.pos,P),d+=I.length-1,p.token===n&&(d+=P.length-1),a=(c=o.content).length,S.length=A;continue e}v?S.push({token:n,pos:h.index,single:q,level:g}):y&&q&&(o.content=f(o.content,h.index,"’"))}else q&&(o.content=f(o.content,h.index,"’"))}}}}n.exports=function(e){var t;if(e.md.options.typographer)for(t=e.tokens.length-1;t>=0;t--)"inline"===e.tokens[t].type&&c.test(e.tokens[t].content)&&h(e.tokens[t].children,e)}}));
;/*!node_modules/markdown-it/lib/token.js*/
amis.define("8c6f9ed",(function(t,s,i,r){"use strict";function n(t,s,i){this.type=t,this.tag=s,this.attrs=null,this.map=null,this.nesting=i,this.level=0,this.children=null,this.content="",this.markup="",this.info="",this.meta=null,this.block=!1,this.hidden=!1}n.prototype.attrIndex=function(t){var s,i,r;if(!this.attrs)return-1;for(i=0,r=(s=this.attrs).length;i<r;i++)if(s[i][0]===t)return i;return-1},n.prototype.attrPush=function(t){this.attrs?this.attrs.push(t):this.attrs=[t]},n.prototype.attrSet=function(t,s){var i=this.attrIndex(t),r=[t,s];i<0?this.attrPush(r):this.attrs[i]=r},n.prototype.attrGet=function(t){var s=this.attrIndex(t),i=null;return s>=0&&(i=this.attrs[s][1]),i},n.prototype.attrJoin=function(t,s){var i=this.attrIndex(t);i<0?this.attrPush([t,s]):this.attrs[i][1]=this.attrs[i][1]+" "+s},i.exports=n}));
;/*!node_modules/markdown-it/lib/rules_core/state_core.js*/
amis.define("bec21f3",(function(t,e,i,s){"use strict";var n=t("8c6f9ed");function o(t,e,i){this.src=t,this.env=i,this.tokens=[],this.inlineMode=!1,this.md=e}o.prototype.Token=n,i.exports=o}));
;/*!node_modules/markdown-it/lib/parser_core.js*/
amis.define("24c6892",(function(e,t,r,c){"use strict";var n=e("8dbfc89"),i=[["normalize",e("8c2133e")],["block",e("2f3c20f")],["inline",e("6e2305d")],["linkify",e("2699c7e")],["replacements",e("a1053c9")],["smartquotes",e("4c2a18b")]];function o(){this.ruler=new n;for(var e=0;e<i.length;e++)this.ruler.push(i[e][0],i[e][1])}o.prototype.process=function(e){var t,r,c;for(t=0,r=(c=this.ruler.getRules("")).length;t<r;t++)c[t](e)},o.prototype.State=e("bec21f3"),r.exports=o}));
;/*!node_modules/markdown-it/lib/rules_block/table.js*/
amis.define("d4b8a3d",(function(t,e,r,n){"use strict";var s=t("19ebe76").isSpace;function h(t,e){var r=t.bMarks[e]+t.tShift[e],n=t.eMarks[e];return t.src.substr(r,n-r)}function u(t){var e,r=[],n=0,s=t.length,h=!1,u=0,i="";for(e=t.charCodeAt(n);n<s;)124===e&&(h?(i+=t.substring(u,n-1),u=n):(r.push(i+t.substring(u,n)),i="",u=n+1)),h=92===e,n++,e=t.charCodeAt(n);return r.push(i+t.substring(u)),r}r.exports=function(t,e,r,n){var i,o,a,l,p,f,c,d,b,g,k,_,C,m,y,A,M,I;if(e+2>r)return!1;if(f=e+1,t.sCount[f]<t.blkIndent)return!1;if(t.sCount[f]-t.blkIndent>=4)return!1;if((a=t.bMarks[f]+t.tShift[f])>=t.eMarks[f])return!1;if(124!==(M=t.src.charCodeAt(a++))&&45!==M&&58!==M)return!1;if(a>=t.eMarks[f])return!1;if(124!==(I=t.src.charCodeAt(a++))&&45!==I&&58!==I&&!s(I))return!1;if(45===M&&s(I))return!1;for(;a<t.eMarks[f];){if(124!==(i=t.src.charCodeAt(a))&&45!==i&&58!==i&&!s(i))return!1;a++}for(c=(o=h(t,e+1)).split("|"),g=[],l=0;l<c.length;l++){if(!(k=c[l].trim())){if(0===l||l===c.length-1)continue;return!1}if(!/^:?-+:?$/.test(k))return!1;58===k.charCodeAt(k.length-1)?g.push(58===k.charCodeAt(0)?"center":"right"):58===k.charCodeAt(0)?g.push("left"):g.push("")}if(-1===(o=h(t,e).trim()).indexOf("|"))return!1;if(t.sCount[e]-t.blkIndent>=4)return!1;if((c=u(o)).length&&""===c[0]&&c.shift(),c.length&&""===c[c.length-1]&&c.pop(),0===(d=c.length)||d!==g.length)return!1;if(n)return!0;for(m=t.parentType,t.parentType="table",A=t.md.block.ruler.getRules("blockquote"),(b=t.push("table_open","table",1)).map=_=[e,0],(b=t.push("thead_open","thead",1)).map=[e,e+1],(b=t.push("tr_open","tr",1)).map=[e,e+1],l=0;l<c.length;l++)b=t.push("th_open","th",1),g[l]&&(b.attrs=[["style","text-align:"+g[l]]]),(b=t.push("inline","",0)).content=c[l].trim(),b.children=[],b=t.push("th_close","th",-1);for(b=t.push("tr_close","tr",-1),b=t.push("thead_close","thead",-1),f=e+2;f<r&&!(t.sCount[f]<t.blkIndent);f++){for(y=!1,l=0,p=A.length;l<p;l++)if(A[l](t,f,r,!0)){y=!0;break}if(y)break;if(!(o=h(t,f).trim()))break;if(t.sCount[f]-t.blkIndent>=4)break;for((c=u(o)).length&&""===c[0]&&c.shift(),c.length&&""===c[c.length-1]&&c.pop(),f===e+2&&((b=t.push("tbody_open","tbody",1)).map=C=[e+2,0]),(b=t.push("tr_open","tr",1)).map=[f,f+1],l=0;l<d;l++)b=t.push("td_open","td",1),g[l]&&(b.attrs=[["style","text-align:"+g[l]]]),(b=t.push("inline","",0)).content=c[l]?c[l].trim():"",b.children=[],b=t.push("td_close","td",-1);b=t.push("tr_close","tr",-1)}return C&&(b=t.push("tbody_close","tbody",-1),C[1]=f),b=t.push("table_close","table",-1),_[1]=f,t.parentType=m,t.line=f,!0}}));
;/*!node_modules/markdown-it/lib/rules_block/code.js*/
amis.define("a61fc19",(function(n,e,t,i){"use strict";t.exports=function(n,e,t){var i,o,s;if(n.sCount[e]-n.blkIndent<4)return!1;for(o=i=e+1;i<t;)if(n.isEmpty(i))i++;else{if(!(n.sCount[i]-n.blkIndent>=4))break;o=++i}return n.line=o,(s=n.push("code_block","code",0)).content=n.getLines(e,o,4+n.blkIndent,!1)+"\n",s.map=[e,n.line],!0}}));
;/*!node_modules/markdown-it/lib/rules_block/fence.js*/
amis.define("ff9258f",(function(r,e,n,t){"use strict";n.exports=function(r,e,n,t){var s,i,f,u,a,c,o,k=!1,C=r.bMarks[e]+r.tShift[e],d=r.eMarks[e];if(r.sCount[e]-r.blkIndent>=4)return!1;if(C+3>d)return!1;if(126!==(s=r.src.charCodeAt(C))&&96!==s)return!1;if(a=C,(i=(C=r.skipChars(C,s))-a)<3)return!1;if(o=r.src.slice(a,C),f=r.src.slice(C,d),96===s&&f.indexOf(String.fromCharCode(s))>=0)return!1;if(t)return!0;for(u=e;!(++u>=n)&&!((C=a=r.bMarks[u]+r.tShift[u])<(d=r.eMarks[u])&&r.sCount[u]<r.blkIndent);)if(r.src.charCodeAt(C)===s&&!(r.sCount[u]-r.blkIndent>=4||(C=r.skipChars(C,s))-a<i||(C=r.skipSpaces(C))<d)){k=!0;break}return i=r.sCount[e],r.line=u+(k?1:0),(c=r.push("fence","code",0)).info=f,c.content=r.getLines(e+1,u,i,!0),c.markup=o,c.map=[e,r.line],!0}}));
;/*!node_modules/markdown-it/lib/rules_block/blockquote.js*/
amis.define("d256287",(function(t,s,n,o){"use strict";var e=t("19ebe76").isSpace;n.exports=function(t,s,n,o){var u,r,b,a,k,C,h,c,i,p,l,f,d,M,S,A,I,m,q,x,g=t.lineMax,y=t.bMarks[s]+t.tShift[s],T=t.eMarks[s];if(t.sCount[s]-t.blkIndent>=4)return!1;if(62!==t.src.charCodeAt(y++))return!1;if(o)return!0;for(a=i=t.sCount[s]+1,32===t.src.charCodeAt(y)?(y++,a++,i++,u=!1,A=!0):9===t.src.charCodeAt(y)?(A=!0,(t.bsCount[s]+i)%4==3?(y++,a++,i++,u=!1):u=!0):A=!1,p=[t.bMarks[s]],t.bMarks[s]=y;y<T&&(r=t.src.charCodeAt(y),e(r));)9===r?i+=4-(i+t.bsCount[s]+(u?1:0))%4:i++,y++;for(l=[t.bsCount[s]],t.bsCount[s]=t.sCount[s]+1+(A?1:0),C=y>=T,M=[t.sCount[s]],t.sCount[s]=i-a,S=[t.tShift[s]],t.tShift[s]=y-t.bMarks[s],m=t.md.block.ruler.getRules("blockquote"),d=t.parentType,t.parentType="blockquote",c=s+1;c<n&&(x=t.sCount[c]<t.blkIndent,!((y=t.bMarks[c]+t.tShift[c])>=(T=t.eMarks[c])));c++)if(62!==t.src.charCodeAt(y++)||x){if(C)break;for(I=!1,b=0,k=m.length;b<k;b++)if(m[b](t,c,n,!0)){I=!0;break}if(I){t.lineMax=c,0!==t.blkIndent&&(p.push(t.bMarks[c]),l.push(t.bsCount[c]),S.push(t.tShift[c]),M.push(t.sCount[c]),t.sCount[c]-=t.blkIndent);break}p.push(t.bMarks[c]),l.push(t.bsCount[c]),S.push(t.tShift[c]),M.push(t.sCount[c]),t.sCount[c]=-1}else{for(a=i=t.sCount[c]+1,32===t.src.charCodeAt(y)?(y++,a++,i++,u=!1,A=!0):9===t.src.charCodeAt(y)?(A=!0,(t.bsCount[c]+i)%4==3?(y++,a++,i++,u=!1):u=!0):A=!1,p.push(t.bMarks[c]),t.bMarks[c]=y;y<T&&(r=t.src.charCodeAt(y),e(r));)9===r?i+=4-(i+t.bsCount[c]+(u?1:0))%4:i++,y++;C=y>=T,l.push(t.bsCount[c]),t.bsCount[c]=t.sCount[c]+1+(A?1:0),M.push(t.sCount[c]),t.sCount[c]=i-a,S.push(t.tShift[c]),t.tShift[c]=y-t.bMarks[c]}for(f=t.blkIndent,t.blkIndent=0,(q=t.push("blockquote_open","blockquote",1)).markup=">",q.map=h=[s,0],t.md.block.tokenize(t,s,c),(q=t.push("blockquote_close","blockquote",-1)).markup=">",t.lineMax=g,t.parentType=d,h[1]=t.line,b=0;b<S.length;b++)t.bMarks[b+s]=p[b],t.tShift[b+s]=S[b],t.sCount[b+s]=M[b],t.bsCount[b+s]=l[b];return t.blkIndent=f,!0}}));
;/*!node_modules/markdown-it/lib/rules_block/hr.js*/
amis.define("ef006fa",(function(r,e,n,t){"use strict";var i=r("19ebe76").isSpace;n.exports=function(r,e,n,t){var a,f,s,o,u=r.bMarks[e]+r.tShift[e],c=r.eMarks[e];if(r.sCount[e]-r.blkIndent>=4)return!1;if(42!==(a=r.src.charCodeAt(u++))&&45!==a&&95!==a)return!1;for(f=1;u<c;){if((s=r.src.charCodeAt(u++))!==a&&!i(s))return!1;s===a&&f++}return!(f<3)&&(t||(r.line=e+1,(o=r.push("hr","hr",0)).map=[e,r.line],o.markup=Array(f+1).join(String.fromCharCode(a))),!0)}}));
;/*!node_modules/markdown-it/lib/rules_block/list.js*/
amis.define("b21a68f",(function(t,e,r,n){"use strict";var i=t("19ebe76").isSpace;function s(t,e){var r,n,s,a;return n=t.bMarks[e]+t.tShift[e],s=t.eMarks[e],42!==(r=t.src.charCodeAt(n++))&&45!==r&&43!==r||n<s&&(a=t.src.charCodeAt(n),!i(a))?-1:n}function a(t,e){var r,n=t.bMarks[e]+t.tShift[e],s=n,a=t.eMarks[e];if(s+1>=a)return-1;if((r=t.src.charCodeAt(s++))<48||r>57)return-1;for(;;){if(s>=a)return-1;if(!((r=t.src.charCodeAt(s++))>=48&&r<=57)){if(41===r||46===r)break;return-1}if(s-n>=10)return-1}return s<a&&(r=t.src.charCodeAt(s),!i(r))?-1:s}r.exports=function(t,e,r,n){var i,o,l,f,u,k,h,d,p,b,c,C,m,g,I,S,_,M,A,v,y,T,E,x,z,N,R,j,q=!1,w=!0;if(t.sCount[e]-t.blkIndent>=4)return!1;if(t.listIndent>=0&&t.sCount[e]-t.listIndent>=4&&t.sCount[e]<t.blkIndent)return!1;if(n&&"paragraph"===t.parentType&&t.sCount[e]>=t.blkIndent&&(q=!0),(E=a(t,e))>=0){if(h=!0,z=t.bMarks[e]+t.tShift[e],m=Number(t.src.slice(z,E-1)),q&&1!==m)return!1}else{if(!((E=s(t,e))>=0))return!1;h=!1}if(q&&t.skipSpaces(E)>=t.eMarks[e])return!1;if(C=t.src.charCodeAt(E-1),n)return!0;for(c=t.tokens.length,h?(j=t.push("ordered_list_open","ol",1),1!==m&&(j.attrs=[["start",m]])):j=t.push("bullet_list_open","ul",1),j.map=b=[e,0],j.markup=String.fromCharCode(C),I=e,x=!1,R=t.md.block.ruler.getRules("list"),M=t.parentType,t.parentType="list";I<r;){for(T=E,g=t.eMarks[I],k=S=t.sCount[I]+E-(t.bMarks[e]+t.tShift[e]);T<g;){if(9===(i=t.src.charCodeAt(T)))S+=4-(S+t.bsCount[I])%4;else{if(32!==i)break;S++}T++}if((u=(o=T)>=g?1:S-k)>4&&(u=1),f=k+u,(j=t.push("list_item_open","li",1)).markup=String.fromCharCode(C),j.map=d=[e,0],h&&(j.info=t.src.slice(z,E-1)),y=t.tight,v=t.tShift[e],A=t.sCount[e],_=t.listIndent,t.listIndent=t.blkIndent,t.blkIndent=f,t.tight=!0,t.tShift[e]=o-t.bMarks[e],t.sCount[e]=S,o>=g&&t.isEmpty(e+1)?t.line=Math.min(t.line+2,r):t.md.block.tokenize(t,e,r,!0),t.tight&&!x||(w=!1),x=t.line-e>1&&t.isEmpty(t.line-1),t.blkIndent=t.listIndent,t.listIndent=_,t.tShift[e]=v,t.sCount[e]=A,t.tight=y,(j=t.push("list_item_close","li",-1)).markup=String.fromCharCode(C),I=e=t.line,d[1]=I,o=t.bMarks[e],I>=r)break;if(t.sCount[I]<t.blkIndent)break;if(t.sCount[e]-t.blkIndent>=4)break;for(N=!1,l=0,p=R.length;l<p;l++)if(R[l](t,I,r,!0)){N=!0;break}if(N)break;if(h){if((E=a(t,I))<0)break;z=t.bMarks[I]+t.tShift[I]}else if((E=s(t,I))<0)break;if(C!==t.src.charCodeAt(E-1))break}return(j=h?t.push("ordered_list_close","ol",-1):t.push("bullet_list_close","ul",-1)).markup=String.fromCharCode(C),b[1]=I,t.line=I,t.parentType=M,w&&function(t,e){var r,n,i=t.level+2;for(r=e+2,n=t.tokens.length-2;r<n;r++)t.tokens[r].level===i&&"paragraph_open"===t.tokens[r].type&&(t.tokens[r+2].hidden=!0,t.tokens[r].hidden=!0,r+=2)}(t,c),!0}}));
;/*!node_modules/markdown-it/lib/rules_block/reference.js*/
amis.define("042cdda",(function(e,r,n,t){"use strict";var i=e("19ebe76").normalizeReference,f=e("19ebe76").isSpace;n.exports=function(e,r,n,t){var a,o,s,c,d,l,h,k,u,C,p,b,A,m,v,L,g=0,y=e.bMarks[r]+e.tShift[r],T=e.eMarks[r],I=r+1;if(e.sCount[r]-e.blkIndent>=4)return!1;if(91!==e.src.charCodeAt(y))return!1;for(;++y<T;)if(93===e.src.charCodeAt(y)&&92!==e.src.charCodeAt(y-1)){if(y+1===T)return!1;if(58!==e.src.charCodeAt(y+1))return!1;break}for(c=e.lineMax,v=e.md.block.ruler.getRules("reference"),C=e.parentType,e.parentType="reference";I<c&&!e.isEmpty(I);I++)if(!(e.sCount[I]-e.blkIndent>3||e.sCount[I]<0)){for(m=!1,l=0,h=v.length;l<h;l++)if(v[l](e,I,c,!0)){m=!0;break}if(m)break}for(T=(A=e.getLines(r,I,e.blkIndent,!1).trim()).length,y=1;y<T;y++){if(91===(a=A.charCodeAt(y)))return!1;if(93===a){u=y;break}(10===a||92===a&&++y<T&&10===A.charCodeAt(y))&&g++}if(u<0||58!==A.charCodeAt(u+1))return!1;for(y=u+2;y<T;y++)if(10===(a=A.charCodeAt(y)))g++;else if(!f(a))break;if(!(p=e.md.helpers.parseLinkDestination(A,y,T)).ok)return!1;if(d=e.md.normalizeLink(p.str),!e.md.validateLink(d))return!1;for(o=y=p.pos,s=g+=p.lines,b=y;y<T;y++)if(10===(a=A.charCodeAt(y)))g++;else if(!f(a))break;for(p=e.md.helpers.parseLinkTitle(A,y,T),y<T&&b!==y&&p.ok?(L=p.str,y=p.pos,g+=p.lines):(L="",y=o,g=s);y<T&&(a=A.charCodeAt(y),f(a));)y++;if(y<T&&10!==A.charCodeAt(y)&&L)for(L="",y=o,g=s;y<T&&(a=A.charCodeAt(y),f(a));)y++;return!(y<T&&10!==A.charCodeAt(y))&&(!!(k=i(A.slice(1,u)))&&(t||(void 0===e.env.references&&(e.env.references={}),void 0===e.env.references[k]&&(e.env.references[k]={title:L,href:d}),e.parentType=C,e.line=r+g+1),!0))}}));
;/*!node_modules/markdown-it/lib/common/html_blocks.js*/
amis.define("c46085f",(function(e,t,o,a){"use strict";o.exports=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","section","source","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"]}));
;/*!node_modules/markdown-it/lib/common/html_re.js*/
amis.define("a4ce055",(function(s,e,A,x){"use strict";var a="<[A-Za-z][A-Za-z0-9\\-]*(?:\\s+[a-zA-Z_:][a-zA-Z0-9:._-]*(?:\\s*=\\s*(?:[^\"'=<>`\\x00-\\x20]+|'[^']*'|\"[^\"]*\"))?)*\\s*\\/?>",_="<\\/[A-Za-z][A-Za-z0-9\\-]*\\s*>",Z=new RegExp("^(?:"+a+"|"+_+"|\x3c!----\x3e|\x3c!--(?:-?[^>-])(?:-?[^-])*--\x3e|<[?][\\s\\S]*?[?]>|<![A-Z]+\\s+[^>]*>|<!\\[CDATA\\[[\\s\\S]*?\\]\\]>)"),z=new RegExp("^(?:"+a+"|"+_+")");A.exports.HTML_TAG_RE=Z,A.exports.HTML_OPEN_CLOSE_TAG_RE=z}));
;/*!node_modules/markdown-it/lib/rules_block/html_block.js*/
amis.define("1b96fcd",(function(t,e,r,n){"use strict";var s=t("c46085f"),i=t("a4ce055").HTML_OPEN_CLOSE_TAG_RE,c=[[/^<(script|pre|style|textarea)(?=(\s|>|$))/i,/<\/(script|pre|style|textarea)>/i,!0],[/^<!--/,/-->/,!0],[/^<\?/,/\?>/,!0],[/^<![A-Z]/,/>/,!0],[/^<!\[CDATA\[/,/\]\]>/,!0],[new RegExp("^</?("+s.join("|")+")(?=(\\s|/?>|$))","i"),/^$/,!0],[new RegExp(i.source+"\\s*$"),/^$/,!1]];r.exports=function(t,e,r,n){var s,i,f,a,l=t.bMarks[e]+t.tShift[e],o=t.eMarks[e];if(t.sCount[e]-t.blkIndent>=4)return!1;if(!t.md.options.html)return!1;if(60!==t.src.charCodeAt(l))return!1;for(a=t.src.slice(l,o),s=0;s<c.length&&!c[s][0].test(a);s++);if(s===c.length)return!1;if(n)return c[s][2];if(i=e+1,!c[s][1].test(a))for(;i<r&&!(t.sCount[i]<t.blkIndent);i++)if(l=t.bMarks[i]+t.tShift[i],o=t.eMarks[i],a=t.src.slice(l,o),c[s][1].test(a)){0!==a.length&&i++;break}return t.line=i,(f=t.push("html_block","",0)).map=[e,i],f.content=t.getLines(e,i,t.blkIndent,!0),!0}}));
;/*!node_modules/markdown-it/lib/rules_block/heading.js*/
amis.define("eeb13fb",(function(e,r,i,n){"use strict";var s=e("19ebe76").isSpace;i.exports=function(e,r,i,n){var c,t,a,h,p=e.bMarks[r]+e.tShift[r],o=e.eMarks[r];if(e.sCount[r]-e.blkIndent>=4)return!1;if(35!==(c=e.src.charCodeAt(p))||p>=o)return!1;for(t=1,c=e.src.charCodeAt(++p);35===c&&p<o&&t<=6;)t++,c=e.src.charCodeAt(++p);return!(t>6||p<o&&!s(c))&&(n||(o=e.skipSpacesBack(o,p),(a=e.skipCharsBack(o,35,p))>p&&s(e.src.charCodeAt(a-1))&&(o=a),e.line=r+1,(h=e.push("heading_open","h"+String(t),1)).markup="########".slice(0,t),h.map=[r,e.line],(h=e.push("inline","",0)).content=e.src.slice(p,o).trim(),h.map=[r,e.line],h.children=[],(h=e.push("heading_close","h"+String(t),-1)).markup="########".slice(0,t)),!0)}}));
;/*!node_modules/markdown-it/lib/rules_block/lheading.js*/
amis.define("10ce769",(function(e,n,r,t){"use strict";r.exports=function(e,n,r){var t,i,a,p,s,o,h,u,f,k,l=n+1,d=e.md.block.ruler.getRules("paragraph");if(e.sCount[n]-e.blkIndent>=4)return!1;for(k=e.parentType,e.parentType="paragraph";l<r&&!e.isEmpty(l);l++)if(!(e.sCount[l]-e.blkIndent>3)){if(e.sCount[l]>=e.blkIndent&&(o=e.bMarks[l]+e.tShift[l])<(h=e.eMarks[l])&&(45===(f=e.src.charCodeAt(o))||61===f)&&(o=e.skipChars(o,f),(o=e.skipSpaces(o))>=h)){u=61===f?1:2;break}if(!(e.sCount[l]<0)){for(i=!1,a=0,p=d.length;a<p;a++)if(d[a](e,l,r,!0)){i=!0;break}if(i)break}}return!!u&&(t=e.getLines(n,l,e.blkIndent,!1).trim(),e.line=l+1,(s=e.push("heading_open","h"+String(u),1)).markup=String.fromCharCode(f),s.map=[n,e.line],(s=e.push("inline","",0)).content=t,s.map=[n,e.line-1],s.children=[],(s=e.push("heading_close","h"+String(u),-1)).markup=String.fromCharCode(f),e.parentType=k,!0)}}));
;/*!node_modules/markdown-it/lib/rules_block/paragraph.js*/
amis.define("12859fa",(function(e,n,p,r){"use strict";p.exports=function(e,n){var p,r,a,t,i,l,s=n+1,o=e.md.block.ruler.getRules("paragraph"),u=e.lineMax;for(l=e.parentType,e.parentType="paragraph";s<u&&!e.isEmpty(s);s++)if(!(e.sCount[s]-e.blkIndent>3||e.sCount[s]<0)){for(r=!1,a=0,t=o.length;a<t;a++)if(o[a](e,s,u,!0)){r=!0;break}if(r)break}return p=e.getLines(n,s,e.blkIndent,!1).trim(),e.line=s,(i=e.push("paragraph_open","p",1)).map=[n,e.line],(i=e.push("inline","",0)).content=p,i.map=[n,e.line],i.children=[],i=e.push("paragraph_close","p",-1),e.parentType=l,!0}}));
;/*!node_modules/markdown-it/lib/rules_block/state_block.js*/
amis.define("83a2ef5",(function(t,s,i,r){"use strict";var h=t("8c6f9ed"),e=t("19ebe76").isSpace;function n(t,s,i,r){var h,n,o,u,a,p,c,f;for(this.src=t,this.md=s,this.env=i,this.tokens=r,this.bMarks=[],this.eMarks=[],this.tShift=[],this.sCount=[],this.bsCount=[],this.blkIndent=0,this.line=0,this.lineMax=0,this.tight=!1,this.ddIndent=-1,this.listIndent=-1,this.parentType="root",this.level=0,this.result="",f=!1,o=u=p=c=0,a=(n=this.src).length;u<a;u++){if(h=n.charCodeAt(u),!f){if(e(h)){p++,9===h?c+=4-c%4:c++;continue}f=!0}10!==h&&u!==a-1||(10!==h&&u++,this.bMarks.push(o),this.eMarks.push(u),this.tShift.push(p),this.sCount.push(c),this.bsCount.push(0),f=!1,p=0,c=0,o=u+1)}this.bMarks.push(n.length),this.eMarks.push(n.length),this.tShift.push(0),this.sCount.push(0),this.bsCount.push(0),this.lineMax=this.bMarks.length-1}n.prototype.push=function(t,s,i){var r=new h(t,s,i);return r.block=!0,i<0&&this.level--,r.level=this.level,i>0&&this.level++,this.tokens.push(r),r},n.prototype.isEmpty=function(t){return this.bMarks[t]+this.tShift[t]>=this.eMarks[t]},n.prototype.skipEmptyLines=function(t){for(var s=this.lineMax;t<s&&!(this.bMarks[t]+this.tShift[t]<this.eMarks[t]);t++);return t},n.prototype.skipSpaces=function(t){for(var s,i=this.src.length;t<i&&(s=this.src.charCodeAt(t),e(s));t++);return t},n.prototype.skipSpacesBack=function(t,s){if(t<=s)return t;for(;t>s;)if(!e(this.src.charCodeAt(--t)))return t+1;return t},n.prototype.skipChars=function(t,s){for(var i=this.src.length;t<i&&this.src.charCodeAt(t)===s;t++);return t},n.prototype.skipCharsBack=function(t,s,i){if(t<=i)return t;for(;t>i;)if(s!==this.src.charCodeAt(--t))return t+1;return t},n.prototype.getLines=function(t,s,i,r){var h,n,o,u,a,p,c,f=t;if(t>=s)return"";for(p=new Array(s-t),h=0;f<s;f++,h++){for(n=0,c=u=this.bMarks[f],a=f+1<s||r?this.eMarks[f]+1:this.eMarks[f];u<a&&n<i;){if(o=this.src.charCodeAt(u),e(o))9===o?n+=4-(n+this.bsCount[f])%4:n++;else{if(!(u-c<this.tShift[f]))break;n++}u++}p[h]=n>i?new Array(n-i+1).join(" ")+this.src.slice(u,a):this.src.slice(u,a)}return p.join("")},n.prototype.Token=h,i.exports=n}));
;/*!node_modules/markdown-it/lib/parser_block.js*/
amis.define("6291969",(function(e,r,t,a){"use strict";var n=e("8dbfc89"),i=[["table",e("d4b8a3d"),["paragraph","reference"]],["code",e("a61fc19")],["fence",e("ff9258f"),["paragraph","reference","blockquote","list"]],["blockquote",e("d256287"),["paragraph","reference","blockquote","list"]],["hr",e("ef006fa"),["paragraph","reference","blockquote","list"]],["list",e("b21a68f"),["paragraph","reference","blockquote"]],["reference",e("042cdda")],["html_block",e("1b96fcd"),["paragraph","reference","blockquote"]],["heading",e("eeb13fb"),["paragraph","reference","blockquote"]],["lheading",e("10ce769")],["paragraph",e("12859fa")]];function o(){this.ruler=new n;for(var e=0;e<i.length;e++)this.ruler.push(i[e][0],i[e][1],{alt:(i[e][2]||[]).slice()})}o.prototype.tokenize=function(e,r,t){for(var a,n=this.ruler.getRules(""),i=n.length,o=r,l=!1,f=e.md.options.maxNesting;o<t&&(e.line=o=e.skipEmptyLines(o),!(o>=t))&&!(e.sCount[o]<e.blkIndent);){if(e.level>=f){e.line=t;break}for(a=0;a<i&&!n[a](e,o,t,!1);a++);e.tight=!l,e.isEmpty(e.line-1)&&(l=!0),(o=e.line)<t&&e.isEmpty(o)&&(l=!0,o++,e.line=o)}},o.prototype.parse=function(e,r,t,a){var n;e&&(n=new this.State(e,r,t,a),this.tokenize(n,n.line,n.lineMax))},o.prototype.State=e("83a2ef5"),t.exports=o}));
;/*!node_modules/markdown-it/lib/rules_inline/text.js*/
amis.define("9e3419b",(function(s,e,c,a){"use strict";function r(s){switch(s){case 10:case 33:case 35:case 36:case 37:case 38:case 42:case 43:case 45:case 58:case 60:case 61:case 62:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 125:case 126:return!0;default:return!1}}c.exports=function(s,e){for(var c=s.pos;c<s.posMax&&!r(s.src.charCodeAt(c));)c++;return c!==s.pos&&(e||(s.pending+=s.src.slice(s.pos,c)),s.pos=c,!0)}}));
;/*!node_modules/markdown-it/lib/rules_inline/newline.js*/
amis.define("51ad46e",(function(e,n,r,i){"use strict";var s=e("19ebe76").isSpace;r.exports=function(e,n){var r,i,d,p=e.pos;if(10!==e.src.charCodeAt(p))return!1;if(r=e.pending.length-1,i=e.posMax,!n)if(r>=0&&32===e.pending.charCodeAt(r))if(r>=1&&32===e.pending.charCodeAt(r-1)){for(d=r-1;d>=1&&32===e.pending.charCodeAt(d-1);)d--;e.pending=e.pending.slice(0,d),e.push("hardbreak","br",0)}else e.pending=e.pending.slice(0,-1),e.push("softbreak","br",0);else e.push("softbreak","br",0);for(p++;p<i&&s(e.src.charCodeAt(p));)p++;return e.pos=p,!0}}));
;/*!node_modules/markdown-it/lib/rules_inline/escape.js*/
amis.define("33730bf",(function(r,e,s,o){"use strict";for(var n=r("19ebe76").isSpace,t=[],c=0;c<256;c++)t.push(0);"\\!\"#$%&'()*+,./:;<=>?@[]^_`{|}~-".split("").forEach((function(r){t[r.charCodeAt(0)]=1})),s.exports=function(r,e){var s,o=r.pos,c=r.posMax;if(92!==r.src.charCodeAt(o))return!1;if(++o<c){if((s=r.src.charCodeAt(o))<256&&0!==t[s])return e||(r.pending+=r.src[o]),r.pos+=2,!0;if(10===s){for(e||r.push("hardbreak","br",0),o++;o<c&&(s=r.src.charCodeAt(o),n(s));)o++;return r.pos=o,!0}}return e||(r.pending+="\\"),r.pos++,!0}}));
;/*!node_modules/markdown-it/lib/rules_inline/backticks.js*/
amis.define("481bcdb",(function(c,e,r,n){"use strict";r.exports=function(c,e){var r,n,s,i,t,o,a,d,p=c.pos;if(96!==c.src.charCodeAt(p))return!1;for(r=p,p++,n=c.posMax;p<n&&96===c.src.charCodeAt(p);)p++;if(a=(s=c.src.slice(r,p)).length,c.backticksScanned&&(c.backticks[a]||0)<=r)return e||(c.pending+=s),c.pos+=a,!0;for(t=o=p;-1!==(t=c.src.indexOf("`",o));){for(o=t+1;o<n&&96===c.src.charCodeAt(o);)o++;if((d=o-t)===a)return e||((i=c.push("code_inline","code",0)).markup=s,i.content=c.src.slice(p,t).replace(/\n/g," ").replace(/^ (.+) $/,"$1")),c.pos=o,!0;c.backticks[d]=t}return c.backticksScanned=!0,e||(c.pending+=s),c.pos+=a,!0}}));
;/*!node_modules/markdown-it/lib/rules_inline/strikethrough.js*/
amis.define("882a8d4",(function(e,t,n,o){"use strict";function s(e,t){var n,o,s,r,k,i=[],p=t.length;for(n=0;n<p;n++)126===(s=t[n]).marker&&-1!==s.end&&(r=t[s.end],(k=e.tokens[s.token]).type="s_open",k.tag="s",k.nesting=1,k.markup="~~",k.content="",(k=e.tokens[r.token]).type="s_close",k.tag="s",k.nesting=-1,k.markup="~~",k.content="","text"===e.tokens[r.token-1].type&&"~"===e.tokens[r.token-1].content&&i.push(r.token-1));for(;i.length;){for(o=(n=i.pop())+1;o<e.tokens.length&&"s_close"===e.tokens[o].type;)o++;n!==--o&&(k=e.tokens[o],e.tokens[o]=e.tokens[n],e.tokens[n]=k)}}n.exports.tokenize=function(e,t){var n,o,s,r,k=e.pos,i=e.src.charCodeAt(k);if(t)return!1;if(126!==i)return!1;if(s=(o=e.scanDelims(e.pos,!0)).length,r=String.fromCharCode(i),s<2)return!1;for(s%2&&(e.push("text","",0).content=r,s--),n=0;n<s;n+=2)e.push("text","",0).content=r+r,e.delimiters.push({marker:i,length:0,token:e.tokens.length-1,end:-1,open:o.can_open,close:o.can_close});return e.pos+=o.length,!0},n.exports.postProcess=function(e){var t,n=e.tokens_meta,o=e.tokens_meta.length;for(s(e,e.delimiters),t=0;t<o;t++)n[t]&&n[t].delimiters&&s(e,n[t].delimiters)}}));
;/*!node_modules/markdown-it/lib/rules_inline/emphasis.js*/
amis.define("b14d1d1",(function(e,n,t,o){"use strict";function r(e,n){var t,o,r,s,k,i;for(t=n.length-1;t>=0;t--)95!==(o=n[t]).marker&&42!==o.marker||-1!==o.end&&(r=n[o.end],i=t>0&&n[t-1].end===o.end+1&&n[t-1].marker===o.marker&&n[t-1].token===o.token-1&&n[o.end+1].token===r.token+1,k=String.fromCharCode(o.marker),(s=e.tokens[o.token]).type=i?"strong_open":"em_open",s.tag=i?"strong":"em",s.nesting=1,s.markup=i?k+k:k,s.content="",(s=e.tokens[r.token]).type=i?"strong_close":"em_close",s.tag=i?"strong":"em",s.nesting=-1,s.markup=i?k+k:k,s.content="",i&&(e.tokens[n[t-1].token].content="",e.tokens[n[o.end+1].token].content="",t--))}t.exports.tokenize=function(e,n){var t,o,r=e.pos,s=e.src.charCodeAt(r);if(n)return!1;if(95!==s&&42!==s)return!1;for(o=e.scanDelims(e.pos,42===s),t=0;t<o.length;t++)e.push("text","",0).content=String.fromCharCode(s),e.delimiters.push({marker:s,length:o.length,token:e.tokens.length-1,end:-1,open:o.can_open,close:o.can_close});return e.pos+=o.length,!0},t.exports.postProcess=function(e){var n,t=e.tokens_meta,o=e.tokens_meta.length;for(r(e,e.delimiters),n=0;n<o;n++)t[n]&&t[n].delimiters&&r(e,t[n].delimiters)}}));
;/*!node_modules/markdown-it/lib/rules_inline/link.js*/
amis.define("736804c",(function(e,r,s,i){"use strict";var o=e("19ebe76").normalizeReference,n=e("19ebe76").isSpace;s.exports=function(e,r){var s,i,t,a,c,p,f,l,d="",h="",u=e.pos,k=e.posMax,m=e.pos,L=!0;if(91!==e.src.charCodeAt(e.pos))return!1;if(c=e.pos+1,(a=e.md.helpers.parseLinkLabel(e,e.pos,!0))<0)return!1;if((p=a+1)<k&&40===e.src.charCodeAt(p)){for(L=!1,p++;p<k&&(i=e.src.charCodeAt(p),n(i)||10===i);p++);if(p>=k)return!1;if(m=p,(f=e.md.helpers.parseLinkDestination(e.src,p,e.posMax)).ok){for(d=e.md.normalizeLink(f.str),e.md.validateLink(d)?p=f.pos:d="",m=p;p<k&&(i=e.src.charCodeAt(p),n(i)||10===i);p++);if(f=e.md.helpers.parseLinkTitle(e.src,p,e.posMax),p<k&&m!==p&&f.ok)for(h=f.str,p=f.pos;p<k&&(i=e.src.charCodeAt(p),n(i)||10===i);p++);}(p>=k||41!==e.src.charCodeAt(p))&&(L=!0),p++}if(L){if(void 0===e.env.references)return!1;if(p<k&&91===e.src.charCodeAt(p)?(m=p+1,(p=e.md.helpers.parseLinkLabel(e,p))>=0?t=e.src.slice(m,p++):p=a+1):p=a+1,t||(t=e.src.slice(c,a)),!(l=e.env.references[o(t)]))return e.pos=u,!1;d=l.href,h=l.title}return r||(e.pos=c,e.posMax=a,e.push("link_open","a",1).attrs=s=[["href",d]],h&&s.push(["title",h]),e.md.inline.tokenize(e),e.push("link_close","a",-1)),e.pos=p,e.posMax=k,!0}}));
;/*!node_modules/markdown-it/lib/rules_inline/image.js*/
amis.define("9712465",(function(e,r,s,i){"use strict";var t=e("19ebe76").normalizeReference,o=e("19ebe76").isSpace;s.exports=function(e,r){var s,i,c,n,a,p,d,l,f,h,m,u,k,A="",C=e.pos,L=e.posMax;if(33!==e.src.charCodeAt(e.pos))return!1;if(91!==e.src.charCodeAt(e.pos+1))return!1;if(p=e.pos+2,(a=e.md.helpers.parseLinkLabel(e,e.pos+1,!1))<0)return!1;if((d=a+1)<L&&40===e.src.charCodeAt(d)){for(d++;d<L&&(i=e.src.charCodeAt(d),o(i)||10===i);d++);if(d>=L)return!1;for(k=d,(f=e.md.helpers.parseLinkDestination(e.src,d,e.posMax)).ok&&(A=e.md.normalizeLink(f.str),e.md.validateLink(A)?d=f.pos:A=""),k=d;d<L&&(i=e.src.charCodeAt(d),o(i)||10===i);d++);if(f=e.md.helpers.parseLinkTitle(e.src,d,e.posMax),d<L&&k!==d&&f.ok)for(h=f.str,d=f.pos;d<L&&(i=e.src.charCodeAt(d),o(i)||10===i);d++);else h="";if(d>=L||41!==e.src.charCodeAt(d))return e.pos=C,!1;d++}else{if(void 0===e.env.references)return!1;if(d<L&&91===e.src.charCodeAt(d)?(k=d+1,(d=e.md.helpers.parseLinkLabel(e,d))>=0?n=e.src.slice(k,d++):d=a+1):d=a+1,n||(n=e.src.slice(p,a)),!(l=e.env.references[t(n)]))return e.pos=C,!1;A=l.href,h=l.title}return r||(c=e.src.slice(p,a),e.md.inline.parse(c,e.md,e.env,u=[]),(m=e.push("image","img",0)).attrs=s=[["src",A],["alt",""]],m.children=u,m.content=c,h&&s.push(["title",h])),e.pos=d,e.posMax=L,!0}}));
;/*!node_modules/markdown-it/lib/rules_inline/autolink.js*/
amis.define("b1169aa",(function(a,t,n,i){"use strict";var e=/^([a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)$/,o=/^([a-zA-Z][a-zA-Z0-9+.\-]{1,31}):([^<>\x00-\x20]*)$/;n.exports=function(a,t){var n,i,r,s,u,l,k=a.pos;if(60!==a.src.charCodeAt(k))return!1;for(u=a.pos,l=a.posMax;;){if(++k>=l)return!1;if(60===(s=a.src.charCodeAt(k)))return!1;if(62===s)break}return n=a.src.slice(u+1,k),o.test(n)?(i=a.md.normalizeLink(n),!!a.md.validateLink(i)&&(t||((r=a.push("link_open","a",1)).attrs=[["href",i]],r.markup="autolink",r.info="auto",(r=a.push("text","",0)).content=a.md.normalizeLinkText(n),(r=a.push("link_close","a",-1)).markup="autolink",r.info="auto"),a.pos+=n.length+2,!0)):!!e.test(n)&&(i=a.md.normalizeLink("mailto:"+n),!!a.md.validateLink(i)&&(t||((r=a.push("link_open","a",1)).attrs=[["href",i]],r.markup="autolink",r.info="auto",(r=a.push("text","",0)).content=a.md.normalizeLinkText(n),(r=a.push("link_close","a",-1)).markup="autolink",r.info="auto"),a.pos+=n.length+2,!0))}}));
;/*!node_modules/markdown-it/lib/rules_inline/html_inline.js*/
amis.define("3ba6564",(function(t,n,c,e){"use strict";var r=t("a4ce055").HTML_TAG_RE;c.exports=function(t,n){var c,e,s,o=t.pos;return!!t.md.options.html&&(s=t.posMax,!(60!==t.src.charCodeAt(o)||o+2>=s)&&(!(33!==(c=t.src.charCodeAt(o+1))&&63!==c&&47!==c&&!function(t){var n=32|t;return n>=97&&n<=122}(c))&&(!!(e=t.src.slice(o).match(r))&&(n||(t.push("html_inline","",0).content=t.src.slice(o,o+e[0].length)),t.pos+=e[0].length,!0))))}}));
;/*!node_modules/markdown-it/lib/rules_inline/entity.js*/
amis.define("1d97bf4",(function(e,i,r,s){"use strict";var n=e("3a8a690"),t=e("19ebe76").has,a=e("19ebe76").isValidEntityCode,o=e("19ebe76").fromCodePoint,c=/^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i,f=/^&([a-z][a-z0-9]{1,31});/i;r.exports=function(e,i){var r,s,p=e.pos,d=e.posMax;if(38!==e.src.charCodeAt(p))return!1;if(p+1<d)if(35===e.src.charCodeAt(p+1)){if(s=e.src.slice(p).match(c))return i||(r="x"===s[1][0].toLowerCase()?parseInt(s[1].slice(1),16):parseInt(s[1],10),e.pending+=a(r)?o(r):o(65533)),e.pos+=s[0].length,!0}else if((s=e.src.slice(p).match(f))&&t(n,s[1]))return i||(e.pending+=n[s[1]]),e.pos+=s[0].length,!0;return i||(e.pending+="&"),e.pos++,!0}}));
;/*!node_modules/markdown-it/lib/rules_inline/balance_pairs.js*/
amis.define("f599e3b",(function(e,n,r,t){"use strict";function o(e,n){var r,t,o,a,l,i,m,s,k={},h=n.length;if(h){var f=0,g=-2,p=[];for(r=0;r<h;r++)if(o=n[r],p.push(0),n[f].marker===o.marker&&g===o.token-1||(f=r),g=o.token,o.length=o.length||0,o.close){for(k.hasOwnProperty(o.marker)||(k[o.marker]=[-1,-1,-1,-1,-1,-1]),l=k[o.marker][(o.open?3:0)+o.length%3],i=t=f-p[f]-1;t>l;t-=p[t]+1)if((a=n[t]).marker===o.marker&&a.open&&a.end<0&&(m=!1,(a.close||o.open)&&(a.length+o.length)%3==0&&(a.length%3==0&&o.length%3==0||(m=!0)),!m)){s=t>0&&!n[t-1].open?p[t-1]+1:0,p[r]=r-t+s,p[t]=s,o.open=!1,a.end=r,a.close=!1,i=-1,g=-2;break}-1!==i&&(k[o.marker][(o.open?3:0)+(o.length||0)%3]=i)}}}r.exports=function(e){var n,r=e.tokens_meta,t=e.tokens_meta.length;for(o(0,e.delimiters),n=0;n<t;n++)r[n]&&r[n].delimiters&&o(0,r[n].delimiters)}}));
;/*!node_modules/markdown-it/lib/rules_inline/text_collapse.js*/
amis.define("5be24a0",(function(t,e,n,o){"use strict";n.exports=function(t){var e,n,o=0,s=t.tokens,i=t.tokens.length;for(e=n=0;e<i;e++)s[e].nesting<0&&o--,s[e].level=o,s[e].nesting>0&&o++,"text"===s[e].type&&e+1<i&&"text"===s[e+1].type?s[e+1].content=s[e].content+s[e+1].content:(e!==n&&(s[n]=s[e]),n++);e!==n&&(s.length=n)}}));
;/*!node_modules/markdown-it/lib/rules_inline/state_inline.js*/
amis.define("fd3b50a",(function(e,t,i,s){"use strict";var h=e("8c6f9ed"),n=e("19ebe76").isWhiteSpace,r=e("19ebe76").isPunctChar,o=e("19ebe76").isMdAsciiPunct;function c(e,t,i,s){this.src=e,this.env=i,this.md=t,this.tokens=s,this.tokens_meta=Array(s.length),this.pos=0,this.posMax=this.src.length,this.level=0,this.pending="",this.pendingLevel=0,this.cache={},this.delimiters=[],this._prev_delimiters=[],this.backticks={},this.backticksScanned=!1}c.prototype.pushPending=function(){var e=new h("text","",0);return e.content=this.pending,e.level=this.pendingLevel,this.tokens.push(e),this.pending="",e},c.prototype.push=function(e,t,i){this.pending&&this.pushPending();var s=new h(e,t,i),n=null;return i<0&&(this.level--,this.delimiters=this._prev_delimiters.pop()),s.level=this.level,i>0&&(this.level++,this._prev_delimiters.push(this.delimiters),this.delimiters=[],n={delimiters:this.delimiters}),this.pendingLevel=this.level,this.tokens.push(s),this.tokens_meta.push(n),s},c.prototype.scanDelims=function(e,t){var i,s,h,c,l,p,d,a,u,v=e,m=!0,g=!0,f=this.posMax,k=this.src.charCodeAt(e);for(i=e>0?this.src.charCodeAt(e-1):32;v<f&&this.src.charCodeAt(v)===k;)v++;return h=v-e,s=v<f?this.src.charCodeAt(v):32,d=o(i)||r(String.fromCharCode(i)),u=o(s)||r(String.fromCharCode(s)),p=n(i),(a=n(s))?m=!1:u&&(p||d||(m=!1)),p?g=!1:d&&(a||u||(g=!1)),t?(c=m,l=g):(c=m&&(!g||d),l=g&&(!m||u)),{can_open:c,can_close:l,length:h}},c.prototype.Token=h,i.exports=c}));
;/*!node_modules/markdown-it/lib/parser_inline.js*/
amis.define("eb747d0",(function(e,t,s,i){"use strict";var o=e("8dbfc89"),n=[["text",e("9e3419b")],["newline",e("51ad46e")],["escape",e("33730bf")],["backticks",e("481bcdb")],["strikethrough",e("882a8d4").tokenize],["emphasis",e("b14d1d1").tokenize],["link",e("736804c")],["image",e("9712465")],["autolink",e("b1169aa")],["html_inline",e("3ba6564")],["entity",e("1d97bf4")]],r=[["balance_pairs",e("f599e3b")],["strikethrough",e("882a8d4").postProcess],["emphasis",e("b14d1d1").postProcess],["text_collapse",e("5be24a0")]];function p(){var e;for(this.ruler=new o,e=0;e<n.length;e++)this.ruler.push(n[e][0],n[e][1]);for(this.ruler2=new o,e=0;e<r.length;e++)this.ruler2.push(r[e][0],r[e][1])}p.prototype.skipToken=function(e){var t,s,i=e.pos,o=this.ruler.getRules(""),n=o.length,r=e.md.options.maxNesting,p=e.cache;if(void 0===p[i]){if(e.level<r)for(s=0;s<n&&(e.level++,t=o[s](e,!0),e.level--,!t);s++);else e.pos=e.posMax;t||e.pos++,p[i]=e.pos}else e.pos=p[i]},p.prototype.tokenize=function(e){for(var t,s,i=this.ruler.getRules(""),o=i.length,n=e.posMax,r=e.md.options.maxNesting;e.pos<n;){if(e.level<r)for(s=0;s<o&&!(t=i[s](e,!1));s++);if(t){if(e.pos>=n)break}else e.pending+=e.src[e.pos++]}e.pending&&e.pushPending()},p.prototype.parse=function(e,t,s,i){var o,n,r,p=new this.State(e,t,s,i);for(this.tokenize(p),r=(n=this.ruler2.getRules("")).length,o=0;o<r;o++)n[o](p)},p.prototype.State=e("fd3b50a"),s.exports=p}));
;/*!node_modules/linkify-it/lib/re.js*/
amis.define("5dc32c0",(function(_,s,c,r){"use strict";c.exports=function(s){var c={};c.src_Any=_("9a1c1d9").source,c.src_Cc=_("143c403").source,c.src_Z=_("50a8187").source,c.src_P=_("c8815ce").source,c.src_ZPCc=[c.src_Z,c.src_P,c.src_Cc].join("|"),c.src_ZCc=[c.src_Z,c.src_Cc].join("|");return c.src_pseudo_letter="(?:(?![><｜]|"+c.src_ZPCc+")"+c.src_Any+")",c.src_ip4="(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)",c.src_auth="(?:(?:(?!"+c.src_ZCc+"|[@/\\[\\]()]).)+@)?",c.src_port="(?::(?:6(?:[0-4]\\d{3}|5(?:[0-4]\\d{2}|5(?:[0-2]\\d|3[0-5])))|[1-5]?\\d{1,4}))?",c.src_host_terminator="(?=$|[><｜]|"+c.src_ZPCc+")(?!-|_|:\\d|\\.-|\\.(?!$|"+c.src_ZPCc+"))",c.src_path="(?:[/?#](?:(?!"+c.src_ZCc+"|"+"[><｜]|[()[\\]{}.,\"'?!\\-;]).|\\[(?:(?!"+c.src_ZCc+"|\\]).)*\\]|\\((?:(?!"+c.src_ZCc+"|[)]).)*\\)|\\{(?:(?!"+c.src_ZCc+'|[}]).)*\\}|\\"(?:(?!'+c.src_ZCc+'|["]).)+\\"|\\\'(?:(?!'+c.src_ZCc+"|[']).)+\\'|\\'(?="+c.src_pseudo_letter+"|[-]).|\\.{2,}[a-zA-Z0-9%/&]|\\.(?!"+c.src_ZCc+"|[.]).|"+(s&&s["---"]?"\\-(?!--(?:[^-]|$))(?:-*)|":"\\-+|")+",(?!"+c.src_ZCc+").|;(?!"+c.src_ZCc+").|\\!+(?!"+c.src_ZCc+"|[!]).|\\?(?!"+c.src_ZCc+"|[?]).)+|\\/)?",c.src_email_name='[\\-;:&=\\+\\$,\\.a-zA-Z0-9_][\\-;:&=\\+\\$,\\"\\.a-zA-Z0-9_]*',c.src_xn="xn--[a-z0-9\\-]{1,59}",c.src_domain_root="(?:"+c.src_xn+"|"+c.src_pseudo_letter+"{1,63})",c.src_domain="(?:"+c.src_xn+"|(?:"+c.src_pseudo_letter+")|(?:"+c.src_pseudo_letter+"(?:-|"+c.src_pseudo_letter+"){0,61}"+c.src_pseudo_letter+"))",c.src_host="(?:(?:(?:(?:"+c.src_domain+")\\.)*"+c.src_domain+"))",c.tpl_host_fuzzy="(?:"+c.src_ip4+"|(?:(?:(?:"+c.src_domain+")\\.)+(?:%TLDS%)))",c.tpl_host_no_ip_fuzzy="(?:(?:(?:"+c.src_domain+")\\.)+(?:%TLDS%))",c.src_host_strict=c.src_host+c.src_host_terminator,c.tpl_host_fuzzy_strict=c.tpl_host_fuzzy+c.src_host_terminator,c.src_host_port_strict=c.src_host+c.src_port+c.src_host_terminator,c.tpl_host_port_fuzzy_strict=c.tpl_host_fuzzy+c.src_port+c.src_host_terminator,c.tpl_host_port_no_ip_fuzzy_strict=c.tpl_host_no_ip_fuzzy+c.src_port+c.src_host_terminator,c.tpl_host_fuzzy_test="localhost|www\\.|\\.\\d{1,3}\\.|(?:\\.(?:%TLDS%)(?:"+c.src_ZPCc+"|>|$))",c.tpl_email_fuzzy='(^|[><｜]|"|\\(|'+c.src_ZCc+")("+c.src_email_name+"@"+c.tpl_host_fuzzy_strict+")",c.tpl_link_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+c.src_ZPCc+"))((?![$+<=>^`|｜])"+c.tpl_host_port_fuzzy_strict+c.src_path+")",c.tpl_link_no_ip_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+c.src_ZPCc+"))((?![$+<=>^`|｜])"+c.tpl_host_port_no_ip_fuzzy_strict+c.src_path+")",c}}));
;/*!node_modules/linkify-it/index.js*/
amis.define("1dde3a1",(function(t,_,e,i){"use strict";function s(t){var _=Array.prototype.slice.call(arguments,1);return _.forEach((function(_){_&&Object.keys(_).forEach((function(e){t[e]=_[e]}))})),t}function n(t){return Object.prototype.toString.call(t)}function r(t){return"[object Function]"===n(t)}function o(t){return t.replace(/[.?*+^$[\]\\(){}|-]/g,"\\$&")}var a={fuzzyLink:!0,fuzzyEmail:!0,fuzzyIP:!1};var h={"http:":{validate:function(t,_,e){var i=t.slice(_);return e.re.http||(e.re.http=new RegExp("^\\/\\/"+e.re.src_auth+e.re.src_host_port_strict+e.re.src_path,"i")),e.re.http.test(i)?i.match(e.re.http)[0].length:0}},"https:":"http:","ftp:":"http:","//":{validate:function(t,_,e){var i=t.slice(_);return e.re.no_http||(e.re.no_http=new RegExp("^"+e.re.src_auth+"(?:localhost|(?:(?:"+e.re.src_domain+")\\.)+"+e.re.src_domain_root+")"+e.re.src_port+e.re.src_host_terminator+e.re.src_path,"i")),e.re.no_http.test(i)?_>=3&&":"===t[_-3]||_>=3&&"/"===t[_-3]?0:i.match(e.re.no_http)[0].length:0}},"mailto:":{validate:function(t,_,e){var i=t.slice(_);return e.re.mailto||(e.re.mailto=new RegExp("^"+e.re.src_email_name+"@"+e.re.src_host_strict,"i")),e.re.mailto.test(i)?i.match(e.re.mailto)[0].length:0}}},c="biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф".split("|");function l(_){var e=_.re=t("5dc32c0")(_.__opts__),i=_.__tlds__.slice();function s(t){return t.replace("%TLDS%",e.src_tlds)}_.onCompile(),_.__tlds_replaced__||i.push("a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]"),i.push(e.src_xn),e.src_tlds=i.join("|"),e.email_fuzzy=RegExp(s(e.tpl_email_fuzzy),"i"),e.link_fuzzy=RegExp(s(e.tpl_link_fuzzy),"i"),e.link_no_ip_fuzzy=RegExp(s(e.tpl_link_no_ip_fuzzy),"i"),e.host_fuzzy_test=RegExp(s(e.tpl_host_fuzzy_test),"i");var a=[];function h(t,_){throw new Error('(LinkifyIt) Invalid schema "'+t+'": '+_)}_.__compiled__={},Object.keys(_.__schemas__).forEach((function(t){var e=_.__schemas__[t];if(null!==e){var i={validate:null,link:null};if(_.__compiled__[t]=i,"[object Object]"===n(e))return!function(t){return"[object RegExp]"===n(t)}(e.validate)?r(e.validate)?i.validate=e.validate:h(t,e):i.validate=function(t){return function(_,e){var i=_.slice(e);return t.test(i)?i.match(t)[0].length:0}}(e.validate),void(r(e.normalize)?i.normalize=e.normalize:e.normalize?h(t,e):i.normalize=function(t,_){_.normalize(t)});!function(t){return"[object String]"===n(t)}(e)?h(t,e):a.push(t)}})),a.forEach((function(t){_.__compiled__[_.__schemas__[t]]&&(_.__compiled__[t].validate=_.__compiled__[_.__schemas__[t]].validate,_.__compiled__[t].normalize=_.__compiled__[_.__schemas__[t]].normalize)})),_.__compiled__[""]={validate:null,normalize:function(t,_){_.normalize(t)}};var c=Object.keys(_.__compiled__).filter((function(t){return t.length>0&&_.__compiled__[t]})).map(o).join("|");_.re.schema_test=RegExp("(^|(?!_)(?:[><｜]|"+e.src_ZPCc+"))("+c+")","i"),_.re.schema_search=RegExp("(^|(?!_)(?:[><｜]|"+e.src_ZPCc+"))("+c+")","ig"),_.re.pretest=RegExp("("+_.re.schema_test.source+")|("+_.re.host_fuzzy_test.source+")|@","i"),function(t){t.__index__=-1,t.__text_cache__=""}(_)}function u(t,_){var e=t.__index__,i=t.__last_index__,s=t.__text_cache__.slice(e,i);this.schema=t.__schema__.toLowerCase(),this.index=e+_,this.lastIndex=i+_,this.raw=s,this.text=s,this.url=s}function p(t,_){var e=new u(t,_);return t.__compiled__[e.schema].normalize(e,t),e}function m(t,_){if(!(this instanceof m))return new m(t,_);var e;_||(e=t,Object.keys(e||{}).reduce((function(t,_){return t||a.hasOwnProperty(_)}),!1)&&(_=t,t={})),this.__opts__=s({},a,_),this.__index__=-1,this.__last_index__=-1,this.__schema__="",this.__text_cache__="",this.__schemas__=s({},h,t),this.__compiled__={},this.__tlds__=c,this.__tlds_replaced__=!1,this.re={},l(this)}m.prototype.add=function(t,_){return this.__schemas__[t]=_,l(this),this},m.prototype.set=function(t){return this.__opts__=s(this.__opts__,t),this},m.prototype.test=function(t){if(this.__text_cache__=t,this.__index__=-1,!t.length)return!1;var _,e,i,s,n,r,o,a;if(this.re.schema_test.test(t))for((o=this.re.schema_search).lastIndex=0;null!==(_=o.exec(t));)if(s=this.testSchemaAt(t,_[2],o.lastIndex)){this.__schema__=_[2],this.__index__=_.index+_[1].length,this.__last_index__=_.index+_[0].length+s;break}return this.__opts__.fuzzyLink&&this.__compiled__["http:"]&&(a=t.search(this.re.host_fuzzy_test))>=0&&(this.__index__<0||a<this.__index__)&&null!==(e=t.match(this.__opts__.fuzzyIP?this.re.link_fuzzy:this.re.link_no_ip_fuzzy))&&(n=e.index+e[1].length,(this.__index__<0||n<this.__index__)&&(this.__schema__="",this.__index__=n,this.__last_index__=e.index+e[0].length)),this.__opts__.fuzzyEmail&&this.__compiled__["mailto:"]&&t.indexOf("@")>=0&&null!==(i=t.match(this.re.email_fuzzy))&&(n=i.index+i[1].length,r=i.index+i[0].length,(this.__index__<0||n<this.__index__||n===this.__index__&&r>this.__last_index__)&&(this.__schema__="mailto:",this.__index__=n,this.__last_index__=r)),this.__index__>=0},m.prototype.pretest=function(t){return this.re.pretest.test(t)},m.prototype.testSchemaAt=function(t,_,e){return this.__compiled__[_.toLowerCase()]?this.__compiled__[_.toLowerCase()].validate(t,e,this):0},m.prototype.match=function(t){var _=0,e=[];this.__index__>=0&&this.__text_cache__===t&&(e.push(p(this,_)),_=this.__last_index__);for(var i=_?t.slice(_):t;this.test(i);)e.push(p(this,_)),i=i.slice(this.__last_index__),_+=this.__last_index__;return e.length?e:null},m.prototype.tlds=function(t,_){return t=Array.isArray(t)?t:[t],_?(this.__tlds__=this.__tlds__.concat(t).sort().filter((function(t,_,e){return t!==e[_-1]})).reverse(),l(this),this):(this.__tlds__=t.slice(),this.__tlds_replaced__=!0,l(this),this)},m.prototype.normalize=function(t){t.schema||(t.url="http://"+t.url),"mailto:"!==t.schema||/^mailto:/i.test(t.url)||(t.url="mailto:"+t.url)},m.prototype.onCompile=function(){},e.exports=m}));
;/*!node_modules/punycode/punycode.js*/
amis.define("c05e586",(function(t,n,o,e){"use strict";const r=2147483647,c=36,s=/^xn--/,i=/[^\0-\x7F]/,f=/[\x2E\u3002\uFF0E\uFF61]/g,u={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},l=Math.floor,a=String.fromCharCode;function d(t){throw new RangeError(u[t])}function h(t,n){const o=t.split("@");let e="";o.length>1&&(e=o[0]+"@",t=o[1]);const r=function(t,n){const o=[];let e=t.length;for(;e--;)o[e]=n(t[e]);return o}((t=t.replace(f,".")).split("."),n).join(".");return e+r}function p(t){const n=[];let o=0;const e=t.length;for(;o<e;){const r=t.charCodeAt(o++);if(r>=55296&&r<=56319&&o<e){const e=t.charCodeAt(o++);56320==(64512&e)?n.push(((1023&r)<<10)+(1023&e)+65536):(n.push(r),o--)}else n.push(r)}return n}const g=function(t,n){return t+22+75*(t<26)-((0!=n)<<5)},v=function(t,n,o){let e=0;for(t=o?l(t/700):t>>1,t+=l(t/n);t>455;e+=c)t=l(t/35);return l(e+36*t/(t+38))},w=function(t){const n=[],o=t.length;let e=0,s=128,i=72,f=t.lastIndexOf("-");f<0&&(f=0);for(let o=0;o<f;++o)t.charCodeAt(o)>=128&&d("not-basic"),n.push(t.charCodeAt(o));for(let a=f>0?f+1:0;a<o;){const f=e;for(let n=1,s=c;;s+=c){a>=o&&d("invalid-input");const f=(u=t.charCodeAt(a++))>=48&&u<58?u-48+26:u>=65&&u<91?u-65:u>=97&&u<123?u-97:c;f>=c&&d("invalid-input"),f>l((r-e)/n)&&d("overflow"),e+=f*n;const h=s<=i?1:s>=i+26?26:s-i;if(f<h)break;const p=c-h;n>l(r/p)&&d("overflow"),n*=p}const h=n.length+1;i=v(e-f,h,0==f),l(e/h)>r-s&&d("overflow"),s+=l(e/h),e%=h,n.splice(e++,0,s)}var u;return String.fromCodePoint(...n)},C=function(t){const n=[],o=(t=p(t)).length;let e=128,s=0,i=72;for(const o of t)o<128&&n.push(a(o));const f=n.length;let u=f;for(f&&n.push("-");u<o;){let o=r;for(const n of t)n>=e&&n<o&&(o=n);const h=u+1;o-e>l((r-s)/h)&&d("overflow"),s+=(o-e)*h,e=o;for(const o of t)if(o<e&&++s>r&&d("overflow"),o===e){let t=s;for(let o=c;;o+=c){const e=o<=i?1:o>=i+26?26:o-i;if(t<e)break;const r=t-e,s=c-e;n.push(a(g(e+r%s,0))),t=l(r/s)}n.push(a(g(t,0))),i=v(s,h,u===f),s=0,++u}++s,++e}return n.join("")},x={version:"2.3.1",ucs2:{decode:p,encode:t=>String.fromCodePoint(...t)},decode:w,encode:C,toASCII:function(t){return h(t,(function(t){return i.test(t)?"xn--"+C(t):t}))},toUnicode:function(t){return h(t,(function(t){return s.test(t)?w(t.slice(4).toLowerCase()):t}))}};o.exports=x}));
;/*!node_modules/markdown-it/lib/presets/default.js*/
amis.define("6c5e353",(function(e,i,n,t){"use strict";n.exports={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:100},components:{core:{},block:{},inline:{}}}}));
;/*!node_modules/markdown-it/lib/presets/zero.js*/
amis.define("2f5961a",(function(e,l,i,n){"use strict";i.exports={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline"]},block:{rules:["paragraph"]},inline:{rules:["text"],rules2:["balance_pairs","text_collapse"]}}}}));
;/*!node_modules/markdown-it/lib/presets/commonmark.js*/
amis.define("4bfdbc8",(function(e,i,l,n){"use strict";l.exports={options:{html:!0,xhtmlOut:!0,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline"]},block:{rules:["blockquote","code","fence","heading","hr","html_block","lheading","list","reference","paragraph"]},inline:{rules:["autolink","backticks","emphasis","entity","escape","html_inline","image","link","newline","text"],rules2:["balance_pairs","emphasis","text_collapse"]}}}}));
;/*!node_modules/markdown-it/lib/index.js*/
amis.define("c296da3",(function(e,t,n,r){"use strict";var o=e("19ebe76"),i=e("33cc776"),s=e("6b95629"),a=e("24c6892"),c=e("6291969"),h=e("eb747d0"),l=e("1dde3a1"),p=e("de5c5db"),u=e("c05e586"),f={default:e("6c5e353"),zero:e("2f5961a"),commonmark:e("4bfdbc8")},d=/^(vbscript|javascript|file|data):/,m=/^data:image\/(gif|png|jpeg|webp);/;function w(e){var t=e.trim().toLowerCase();return!d.test(t)||!!m.test(t)}var b=["http:","https:","mailto:"];function y(e){var t=p.parse(e,!0);if(t.hostname&&(!t.protocol||b.indexOf(t.protocol)>=0))try{t.hostname=u.toASCII(t.hostname)}catch(e){}return p.encode(p.format(t))}function k(e){var t=p.parse(e,!0);if(t.hostname&&(!t.protocol||b.indexOf(t.protocol)>=0))try{t.hostname=u.toUnicode(t.hostname)}catch(e){}return p.decode(p.format(t),p.decode.defaultChars+"%")}function g(e,t){if(!(this instanceof g))return new g(e,t);t||o.isString(e)||(t=e||{},e="default"),this.inline=new h,this.block=new c,this.core=new a,this.renderer=new s,this.linkify=new l,this.validateLink=w,this.normalizeLink=y,this.normalizeLinkText=k,this.utils=o,this.helpers=o.assign({},i),this.options={},this.configure(e),t&&this.set(t)}g.prototype.set=function(e){return o.assign(this.options,e),this},g.prototype.configure=function(e){var t,n=this;if(o.isString(e)&&!(e=f[t=e]))throw new Error('Wrong `markdown-it` preset "'+t+'", check name');if(!e)throw new Error("Wrong `markdown-it` preset, can't be empty");return e.options&&n.set(e.options),e.components&&Object.keys(e.components).forEach((function(t){e.components[t].rules&&n[t].ruler.enableOnly(e.components[t].rules),e.components[t].rules2&&n[t].ruler2.enableOnly(e.components[t].rules2)})),this},g.prototype.enable=function(e,t){var n=[];Array.isArray(e)||(e=[e]),["core","block","inline"].forEach((function(t){n=n.concat(this[t].ruler.enable(e,!0))}),this),n=n.concat(this.inline.ruler2.enable(e,!0));var r=e.filter((function(e){return n.indexOf(e)<0}));if(r.length&&!t)throw new Error("MarkdownIt. Failed to enable unknown rule(s): "+r);return this},g.prototype.disable=function(e,t){var n=[];Array.isArray(e)||(e=[e]),["core","block","inline"].forEach((function(t){n=n.concat(this[t].ruler.disable(e,!0))}),this),n=n.concat(this.inline.ruler2.disable(e,!0));var r=e.filter((function(e){return n.indexOf(e)<0}));if(r.length&&!t)throw new Error("MarkdownIt. Failed to disable unknown rule(s): "+r);return this},g.prototype.use=function(e){var t=[this].concat(Array.prototype.slice.call(arguments,1));return e.apply(e,t),this},g.prototype.parse=function(e,t){if("string"!=typeof e)throw new Error("Input data should be a String");var n=new this.core.State(e,this,t);return this.core.process(n),n.tokens},g.prototype.render=function(e,t){return t=t||{},this.renderer.render(this.parse(e,t),this.options,t)},g.prototype.parseInline=function(e,t){var n=new this.core.State(e,this,t);return n.inlineMode=!0,this.core.process(n),n.tokens},g.prototype.renderInline=function(e,t){return t=t||{},this.renderer.render(this.parseInline(e,t),this.options,t)},n.exports=g}));
;/*!node_modules/markdown-it/index.js*/
amis.define("acd8551",(function(c,e,i,s){"use strict";i.exports=c("c296da3")}));
;/*!node_modules/markdown-it-html5-media/lib/index.js*/
amis.define("6f2381c",(function(e,t,r,a){"use strict";var s=["aac","m4a","mp3","oga","ogg","wav"],o=["mp4","m4v","ogv","webm","mpg","mpeg"],n={en:{"html5 video not supported":"Your browser does not support playing HTML5 video.","html5 audio not supported":"Your browser does not support playing HTML5 audio.","html5 media fallback link":'You can <a href="%s" download>download the file</a> instead.',"html5 media description":"Here is a description of the content: %s"}},i=function(e,t,r){if(n[e]&&n[e][t]||(e="en"),!n[e])return"";var a=n[e][t]||"";if(r)for(var s=0,o=r;s<o.length;s++){var i=o[s];a=a.replace("%s",i)}return a};function c(e){var t=e.match(/\.([^/.]+)$/);if(null===t)return"image";var r=t[1];return-1!=s.indexOf(r.toLowerCase())?"audio":-1!=o.indexOf(r.toLowerCase())?"video":"image"}r.exports={html5Media:function(e,t){void 0===t&&(t={}),t.messages&&(n=t.messages),t.translateFn&&(i=t.translateFn);var r=void 0!==t.videoAttrs?t.videoAttrs:'controls class="html5-video-player"',a=void 0!==t.audioAttrs?t.audioAttrs:'controls class="html5-audio-player"';e.inline.ruler.at("image",(function(t,r){return function(e,t,r){var a,s,o,n,i,l,d,u,p,m,f,h,v,g="",A=e.pos,k=e.posMax;if(33!==e.src.charCodeAt(e.pos)||91!==e.src.charCodeAt(e.pos+1))return!1;if(l=e.pos+2,(i=e.md.helpers.parseLinkLabel(e,e.pos+1,!1))<0)return!1;if((d=i+1)<k&&40===e.src.charCodeAt(d)){for(d++;d<k&&(s=e.src.charCodeAt(d),r.utils.isSpace(s)||10===s);d++);if(d>=k)return!1;for(v=d,(p=e.md.helpers.parseLinkDestination(e.src,d,e.posMax)).ok&&(g=e.md.normalizeLink(p.str),e.md.validateLink(g)?d=p.pos:g=""),v=d;d<k&&(s=e.src.charCodeAt(d),r.utils.isSpace(s)||10===s);d++);if(p=e.md.helpers.parseLinkTitle(e.src,d,e.posMax),d<k&&v!==d&&p.ok)for(m=p.str,d=p.pos;d<k&&(s=e.src.charCodeAt(d),r.utils.isSpace(s)||10===s);d++);else m="";if(d>=k||41!==e.src.charCodeAt(d))return e.pos=A,!1;d++}else{if(void 0===e.env.references)return!1;if(d<k&&91===e.src.charCodeAt(d)?(v=d+1,(d=e.md.helpers.parseLinkLabel(e,d))>=0?n=e.src.slice(v,d++):d=i+1):d=i+1,n||(n=e.src.slice(l,i)),!(u=e.env.references[r.utils.normalizeReference(n)]))return e.pos=A,!1;g=u.href,m=u.title}if(e.pos=d,e.posMax=k,t)return!0;o=e.src.slice(l,i),e.md.inline.parse(o,e.md,e.env,h=[]);var L=c(g),x="image"==L?"img":L;return(f=e.push(L,x,0)).attrs=a=[["src",g]],"image"==L&&a.push(["alt",""]),f.children=h,f.content=o,m&&a.push(["title",m]),e.pos=d,e.posMax=k,!0}(t,r,e)})),e.renderer.rules.video=e.renderer.rules.audio=function(t,s,o,n){return o.html5Media={videoAttrs:r,audioAttrs:a},function(e,t,r,a,s){var o=e[t],n=o.type;if("video"!==n&&"audio"!==n)return"";var c=r.html5Media["".concat(n,"Attrs")].trim();c&&(c=" "+c);var l=o.attrs[o.attrIndex("src")][1],d=-1!=o.attrIndex("title")?' title="'.concat(s.utils.escapeHtml(o.attrs[o.attrIndex("title")][1]),'"'):"",u=i(a.language,"html5 ".concat(n," not supported"))+"\n"+i(a.language,"html5 media fallback link",[l]),p=o.content?"\n"+i(a.language,"html5 media description",[s.utils.escapeHtml(o.content)]):"";return"<".concat(n,' src="').concat(l,'"').concat(d).concat(c,">\n")+"".concat(u).concat(p,"\n")+"</".concat(n,">")}(t,s,o,n,e)}},messages:n,guessMediaType:c}}));
;/*!node_modules/amis-ui/lib/components/Markdown.js*/
amis.define("d65cc16",(function(t,e,n,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=t("ca5913d"),i=t("57d2347"),d=t("acd8551"),a=t("6f2381c");function f(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var u=f(i),s=f(d),c=t("57d2347"),l=(c.default||c).createElement;(c.default||c).Fragment;var p=s.default();function h(t,e){return e&&p.set(e),p.render(t)}p.use(a.html5Media);var m=function(t){function e(e){var n=t.call(this,e)||this;return n.htmlRef=n.htmlRef.bind(n),n}return o.__extends(e,t),e.prototype.htmlRef=function(t){this.dom=t,t&&this._render()},e.prototype.componentDidUpdate=function(t){this.props.content!==t.content&&this._render()},e.prototype._render=function(){return o.__awaiter(this,void 0,void 0,(function(){var t,e,n;return o.__generator(this,(function(r){return t=this.props,e=t.content,n=t.options,this.dom.innerHTML=h(e,n),"function"==typeof renderMathInElement&&renderMathInElement(this.dom,{delimiters:[{left:"$$",right:"$$",display:!0},{left:"$",right:"$",display:!1}]}),[2]}))}))},e.prototype.render=function(){return l("div",{"data-testid":"markdown-body",className:"markdown-body",ref:this.htmlRef})},e.defaultProps={content:"",options:{linkify:!0}},e}(u.default.Component);e.default=m,e.markdown=h}));