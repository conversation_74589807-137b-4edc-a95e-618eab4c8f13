<!--
/**
 * 车辆申请 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2023-06-10 16:02:16
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
	<title th:text="${lang.translate('车辆申请')}">车辆申请</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="opacity:0">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-5790-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <!-- text_input : 名称 ,  name -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('名称')}">名称</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="name" id="name" name="name" th:placeholder="${ lang.translate('请输入'+'名称') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            
                <!-- text_input : 驾驶员 ,  driver -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('驾驶员')}">驾驶员</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="driver" id="driver" name="driver" th:placeholder="${ lang.translate('请输入'+'驾驶员') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 联系方式 ,  contact -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('联系方式')}">联系方式</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="contact" id="contact" name="contact" th:placeholder="${ lang.translate('请输入'+'联系方式') }" type="text" class="layui-input"  />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <!-- button : 部门 ,  orgId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('部门')}">部门</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="orgId" id="orgId" name="orgId"  type="hidden" class="layui-input"   />
                        <button id="orgId-button" type="button" action-type="org-dialog" class="layui-btn   " style="width: 100%" default-width="100%" auto-width="false"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择组织节点')}" th:default-label="${lang.translate('请选择组织节点')}">按钮文本</span></button>
                    </div>
                </div>
            
                <!-- button : 领用人 ,  receiverId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('领用人')}">领用人</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="receiverId" id="receiverId" name="receiverId"  type="hidden" class="layui-input"    lay-verify="|required"   />
                        <button id="receiverId-button" type="button" action-type="emp-dialog" class="layui-btn   " style="width: 100%" default-width="100%" auto-width="false"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择人员')}" th:default-label="${lang.translate('请选择人员')}">按钮文本</span></button>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <!-- date_input : 领用时间 ,  collectionDate  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('领用时间')}">领用时间</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input input-type="date" lay-filter="collectionDate" id="collectionDate" name="collectionDate"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择'+'领用时间') }" type="text" class="layui-input"    lay-verify="|required"   />
                    </div>
                </div>
            
                <!-- date_input : 预计归还时间 ,  planReturnDate  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('预计归还时间')}">预计归还时间</div></div>
                    <div class="layui-input-block ">
                        <input input-type="date" lay-filter="planReturnDate" id="planReturnDate" name="planReturnDate"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择'+'预计归还时间') }" type="text" class="layui-input"    lay-verify=""   />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-3083-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >

                <!-- text_area : 内容 ,  content  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('内容')}">内容</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="content" id="content" name="content" th:placeholder="${ lang.translate('请输入'+'内容') }" class="layui-textarea" style="height: 30px" ></textarea>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-2291-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >

                <!-- text_area : 备注 ,  notes  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入'+'备注') }" class="layui-textarea" style="height: 30px" ></textarea>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-1217-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >
                <!-- upload : 附件 ,  attach  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('附件')}">附件</div></div>
                    <div class="layui-upload layui-input-block ">
                        <input input-type="upload" id="attach"  name="attach" lay-filter="attach" style="display: none">
                        <button type="button" class="layui-btn" id="attach-button" th:text="${lang.translate('选择附件')}">选择附件</button>
                        <div class="layui-upload-list" id="attach-file-list"></div>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->
        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-4450-fieldset">
        <legend>车辆列表</legend>
        </fieldset>
        <div class="layui-row form-row" style="text-align: center;" id="random-4450-content">
            <div style="display: inline-block;padding-right: 8px;padding-left: 8px" class="layui-col-xs12">
            <iframe id="random-4450-iframe" js-fn="vehicleSelectList" class="form-iframe" frameborder="0" style="width: 100%"></iframe>
            </div>
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 120px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('oa_vehicle_apply:create','oa_vehicle_apply:update','oa_vehicle_apply:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.oa.VehicleHandleStatusEnum')}]];
    var SELECT_IFRETURN_DATA = [[${enum.toArray('com.dt.platform.constants.enums.oa.VehicleApplyReturnEnum')}]];
    var VALIDATE_CONFIG={"receiverId":{"labelInForm":"领用人","inputType":"button","required":true},"planReturnDate":{"date":true,"labelInForm":"预计归还时间","inputType":"date_input"},"actReturnDate":{"date":true,"labelInForm":"实际归还时间","inputType":"date_input"},"name":{"labelInForm":"名称","inputType":"text_input","required":true},"collectionDate":{"date":true,"labelInForm":"领用时间","inputType":"date_input","required":true}};
    var AUTH_PREFIX="oa_vehicle_apply";


</script>



<script th:src="'/business/oa/vehicle_apply/vehicle_apply_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/oa/vehicle_apply/vehicle_apply_form.js?'+${cacheKey}"></script>

</body>
</html>