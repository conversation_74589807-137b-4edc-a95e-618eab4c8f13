<!--
/**
 * 资产报修 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-07-15 15:36:02
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
	<title th:text="${lang.translate('资产报修')}">资产报修</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
        .layui-form-label {
            width: 70px;
        }
        .layui-input-block {
            margin-left: 105px;
        }
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="display:none">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->



        <div class="layui-row form-row" id="random-1672-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('业务名称')}">业务名称</div><div class="layui-required">*</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="name" id="name" name="name" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('业务名称') }" type="text" class="layui-input"    lay-verify="|required"  />
                        </div>
                    </div>

                <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('申请人')}">申请人</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="originatorUserName" id="originatorUserName" name="originatorUserName" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('申请人') }" type="text" class="layui-input"  />
                        </div>
                    </div>

                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->



        <div class="layui-row form-row" id="random-0447-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column"  style="padding-top: 0px" >


                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('维修类型')}">维修类型</div></div>
                        <div class="layui-input-block ">
                            <div id="type" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_repair_type'}" extraParam="{}"></div>
                        </div>
                    </div>


                <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column"  style="padding-top: 0px" >


                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('计划完成日期')}">计划完成日期</div></div>
                        <div class="layui-input-block ">
                            <input input-type="date" lay-filter="planFinishDate" id="planFinishDate" name="planFinishDate"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择') +''+ lang.translate('计划完成日期') }" type="text" class="layui-input"    lay-verify=""   />
                        </div>
                    </div>

                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->



        <div class="layui-row form-row" id="random-8627-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >


                    <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('报修内容')}">报修内容</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="content" id="content" name="content" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('报修内容') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>



                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('图片')}">图片</div></div>
                    <div class="layui-upload layui-input-block ">
                        <input input-type="upload" id="pictureId"  name="pictureId" lay-filter="pictureId" style="display: none">
                        <button type="button" class="layui-btn" id="pictureId-button" th:text="${lang.translate('选择附件')}">选择附件</button>
                        <div class="layui-upload-list" id="pictureId-file-list"></div>
                    </div>
                </div>



                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->

        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-6046-fieldset">
        <legend  th:text="${lang.translate('资产列表')}" >资产列表</legend>
        </fieldset>
        <div class="layui-row form-row" style="text-align: center;" id="random-6046-content">
            <div style="display: inline-block;padding-right: 8px;padding-left: 8px" class="layui-col-xs12">
            <iframe js-fn="assetSelectList" class="form-iframe" frameborder="0" style="width: 100%"></iframe>
            </div>
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_asset_repair:create','eam_asset_repair:update','eam_asset_repair:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}]];
    var SELECT_REPAIRSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetRepairStatusEnum')}]];
    var VALIDATE_CONFIG={"actualFinishDate":{"date":true,"labelInForm":"实际完成日期","inputType":"date_input"},"name":{"labelInForm":"业务名称","inputType":"text_input","required":true},"planFinishDate":{"date":true,"labelInForm":"计划完成日期","inputType":"date_input"},"content":{"labelInForm":"报修内容","inputType":"text_area","required":true}};
    var AUTH_PREFIX="eam_asset_repair";

    //
    var ASSET_DEFAULT_OWN_COMPANY = [[${assetDefaultOwnCompany}]] ;
    // 单据ID
    var BILL_ID = [[${billId}]] ;
    // 单据类型
    var BILL_TYPE = [[${billType}]] ;

</script>



<script th:src="'/business/eam/asset_repair/asset_repair_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset_repair/asset_repair_form.js?'+${cacheKey}"></script>

</body>
</html>
