"use strict";(self.webpackChunktansci_boot_ui_editor=self.webpackChunktansci_boot_ui_editor||[]).push([[2577],{92577:function(t,n,e){e.r(n),e.d(n,{Word:function(){return aa},default:function(){return ra}});var s=e(31635);function a(t){return t.getAttribute("w:val")||t.getAttribute("w14:val")||t.getAttribute("val")||""}function r(t){return parseInt(a(t),10)}function l(t,n){if(void 0===n&&(n=!1),"boolean"==typeof t)return t;if("string"==typeof t){switch(t){case"1":case"on":case"true":return!0;case"0":case"off":case"false":return!1}if("number"==typeof t)return 0!==t}return n}function o(t,n){return void 0===n&&(n=!0),l(a(t),n)}function x(t,n,e){return void 0===e&&(e=!0),l(t.getAttribute(n),e)}function y(t,n,e){void 0===e&&(e=0);var s=t.getAttribute(n);return s?parseInt(s,10):e}function p(t,n){var e=t.getAttribute(n);return e?e.endsWith("%")?parseInt(e,10)/100:parseInt(e,10)/1e5:1}function f(t,n){for(var e=n.replace(/{|}|-/g,""),s=new Array(16),a=0;a<16;a++)s[16-a-1]=parseInt(e.substr(2*a,2),16);for(a=0;a<32;a++)t[a]=t[a]^s[a%16];return t}var c=function(){function t(){}return t.fromXML=function(n,e){var r,l,o=new t;o.name=e.getAttribute("w:name")||"";try{for(var x=(0,s.Ju)(e.children),y=x.next();!y.done;y=x.next()){var p=y.value,f=p.tagName;switch(f){case"w:family":o.family=a(p);break;case"w:altName":o.altName=a(p);break;case"w:panose1":case"w:charset":case"w:sig":case"w:pitch":break;case"w:embedRegular":case"w:embedBold":case"w:embedItalic":case"w:embedBoldItalic":case"w:embedSystemFonts":case"w:embedTrueTypeFonts":var c=p.getAttribute("r:id")||"",i=p.getAttribute("w:fontKey")||"",d=n.loadFont(c,i);d&&(o.url=d);break;default:console.warn("parse Font: Unknown key",f,p)}}}catch(t){r={error:t}}finally{try{y&&!y.done&&(l=x.return)&&l.call(x)}finally{if(r)throw r.error}}return o},t}(),i=function(){function t(){this.fonts=[]}return t.fromXML=function(n,e){var a,r,l=Array.from(e.getElementsByTagName("w:font")),o=new t;try{for(var x=(0,s.Ju)(l),y=x.next();!y.done;y=x.next()){var p=y.value,f=c.fromXML(n,p);o.fonts.push(f)}}catch(t){a={error:t}}finally{try{y&&!y.done&&(r=x.return)&&r.call(x)}finally{if(a)throw a.error}}return o},t}();function d(t,n){return{id:t.getAttribute("Id")||"",type:t.getAttribute("Type")||"",target:t.getAttribute("Target")||"",targetMode:t.getAttribute("TargetMode")||"",part:n}}function h(t,n){var e,a,r={},l=t.getElementsByTagName("Relationship");try{for(var o=(0,s.Ju)(l),x=o.next();!x.done;x=o.next()){var y=d(x.value,n);r[y.id]=y}}catch(t){e={error:t}}finally{try{x&&!x.done&&(a=o.return)&&a.call(o)}finally{if(e)throw e.error}}return r}var m={Dxa:{mul:.066665,unit:"px"},Emu:{mul:1.3333/12700,unit:"px"},FontSize:{mul:.66665,unit:"px"},Border:{mul:.1666625,unit:"px"},Point:{mul:1.3333,unit:"px"},Percent:{mul:.02,unit:"%"},LineHeight:{mul:1/240,unit:""},VmlEmu:{mul:1/12700,unit:""}};function w(t,n){return void 0===n&&(n=m.Dxa),null==t||/.+(p[xt]|[%])$/.test(t)?t:"".concat((parseInt(t)*n.mul).toFixed(2)).concat(n.unit)}function g(t){return t?parseInt(t)/6e4:0}function u(t,n,e){void 0===e&&(e=m.Dxa);var s=t.getAttribute(n);return s?w(String(s),e):""}function T(t,n){var e,a;try{for(var r=(0,s.Ju)(t.children),l=r.next();!l.done;l=r.next()){var o=l.value;switch(o.tagName){case"w:left":case"w:start":n["padding-left"]=u(o,"w:w");break;case"w:right":case"w:end":n["padding-right"]=u(o,"w:w");break;case"w:top":n["padding-top"]=u(o,"w:w");break;case"w:bottom":n["padding-bottom"]=u(o,"w:w")}}}catch(t){e={error:t}}finally{try{l&&!l.done&&(a=r.return)&&a.call(r)}finally{if(e)throw e.error}}}var v={aliceBlue:"#f0f8ff",antiqueWhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedAlmond:"#ffebcd",blue:"#0000ff",blueViolet:"#8a2be2",brown:"#a52a2a",burlyWood:"#deb887",cadetBlue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerBlue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00FFFF",darkBlue:"#00008B",dkBlue:"#00008B",darkCyan:"#008B8B",dkCyan:"#008B8B",darkGoldenrod:"#b8860b",dkGoldenrod:"#b8860b",darkGray:"#A9A9A9",dkGray:"#A9A9A9",darkGreen:"#006400",dkGreen:"#006400",darkGrey:"#a9a9a9",dkGrey:"#a9a9a9",darkKhaki:"#bdb76b",dkKhaki:"#bdb76b",darkMagenta:"#800080",dkMagenta:"#800080",darkOliveGreen:"#556b2f",dkOliveGreen:"#556b2f",darkOrange:"#ff8c00",dkOrange:"#ff8c00",darkOrchid:"#9932cc",dkOrchid:"#9932cc",darkRed:"#8B0000",dkRed:"#8B0000",darkSalmon:"#e9967a",dkSalmon:"#e9967a",darkSeaGreen:"#8fbc8f",dkSeaGreen:"#8fbc8f",darkSlateBlue:"#483d8b",dkSlateBlue:"#483d8b",darkSlateGray:"#2f4f4f",dkSlateGray:"#2f4f4f",darkSlateGrey:"#2f4f4f",dkSlateGrey:"#2f4f4f",darkTurquoise:"#00ced1",dkTurquoise:"#00ced1",darkViolet:"#9400d3",dkViolet:"#9400d3",darkYellow:"#808000",deepPink:"#ff1493",deepSkyBlue:"#00bfff",dimGray:"#696969",dimGrey:"#696969",dodgerBlue:"#1e90ff",firebrick:"#b22222",floralWhite:"#fffaf0",forestGreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostWhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenYellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotPink:"#ff69b4",indianRed:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderBlush:"#fff0f5",lawnGreen:"#7cfc00",lemonChiffon:"#fffacd",lightBlue:"#add8e6",ltBlue:"#add8e6",lightCoral:"#f08080",ltCoral:"#f08080",lightCyan:"#e0ffff",ltCyan:"#e0ffff",lightGoldenrodYellow:"#fafad2",ltGoldenrodYellow:"#fafad2",lightGray:"#D3D3D3",ltGray:"#D3D3D3",lightGreen:"#90ee90",ltGreen:"#90ee90",lightGrey:"#d3d3d3",ltGrey:"#d3d3d3",lightPink:"#ffb6c1",ltPink:"#ffb6c1",lightSalmon:"#ffa07a",ltSalmon:"#ffa07a",lightSeaGreen:"#20b2aa",ltSeaGreen:"#20b2aa",lightSkyBlue:"#87cefa",ltSkyBlue:"#87cefa",lightSlateGray:"#778899",ltSlateGray:"#778899",lightSlateGrey:"#778899",ltSlateGrey:"#778899",lightSteelBlue:"#b0c4de",ltSteelBlue:"#b0c4de",lightYellow:"#ffffe0",ltYellow:"#ffffe0",lime:"#00ff00",limeGreen:"#32cd32",linen:"#faf0e6",magenta:"#FF00FF",maroon:"#800000",mediumAquamarine:"#66cdaa",medAquamarine:"#66cdaa",mediumBlue:"#0000cd",medBlue:"#0000cd",mediumOrchid:"#ba55d3",medOrchid:"#ba55d3",mediumPurple:"#9370db",medPurple:"#9370db",mediumSeaGreen:"#3cb371",medSeaGreen:"#3cb371",mediumSlateBlue:"#7b68ee",medSlateBlue:"#7b68ee",mediumSpringGreen:"#00fa9a",medSpringGreen:"#00fa9a",mediumTurquoise:"#48d1cc",medTurquoise:"#48d1cc",mediumVioletRed:"#c71585",medVioletRed:"#c71585",midnightBlue:"#191970",mintCream:"#f5fffa",mistyRose:"#ffe4e1",moccasin:"#ffe4b5",navajoWhite:"#ffdead",navy:"#000080",none:"transparent",oldLace:"#fdf5e6",olive:"#808000",oliveDrab:"#6b8e23",orange:"#ffa500",orangeRed:"#ff4500",orchid:"#da70d6",paleGoldenrod:"#eee8aa",paleGreen:"#98fb98",paleTurquoise:"#afeeee",paleVioletRed:"#db7093",papayaWhip:"#ffefd5",peachPuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderBlue:"#b0e0e6",purple:"#800080",rebeccaPurple:"#663399",red:"#ff0000",rosyBrown:"#bc8f8f",royalBlue:"#4169e1",saddleBrown:"#8b4513",salmon:"#fa8072",sandyBrown:"#f4a460",seaGreen:"#2e8b57",seaShell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyBlue:"#87ceeb",slateBlue:"#6a5acd",slateGray:"#708090",slateGrey:"#708090",snow:"#fffafa",springGreen:"#00ff7f",steelBlue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whiteSmoke:"#f5f5f5",yellow:"#ffff00",yellowGreen:"#9acd32"},A=["black","blue","green","red","white","yellow"];function b(t,n,e,s){void 0===e&&(e="w:color"),void 0===s&&(s="black");var a=n.getAttribute(e);if(a)return"auto"==a?s:A.includes(a)?a:a in v?v[a]:"#".concat(a);var r=n.getAttribute("w:themeColor");return r?t.getThemeColor(r):""}function k(t,n){var e=n.getAttribute("w:fill")||"",s=a(n);if("auto"===e&&(e="FFFFFF"),6===e.length)switch(s){case"clear":return"#".concat(e);case"pct10":return R(e,.1);case"pct12":return R(e,.125);case"pct15":return R(e,.15);case"pct20":return R(e,.2);case"pct25":return R(e,.25);case"pct30":return R(e,.3);case"pct35":return R(e,.35);case"pct37":return R(e,.375);case"pct40":return R(e,.4);case"pct45":return R(e,.45);case"pct5":return R(e,.05);case"pct50":return R(e,.5);case"pct55":return R(e,.55);case"pct60":return R(e,.6);case"pct65":return R(e,.65);case"pct70":return R(e,.7);case"pct75":return R(e,.75);case"pct80":return R(e,.8);case"pct85":return R(e,.85);case"pct87":return R(e,.87);case"pct90":return R(e,.9);case"pct95":return R(e,.95);default:return console.warn("unsupport shd val",s),"#".concat(e)}return""}function R(t,n){"FFFFFF"===t&&(t="000000");var e=parseInt(t.substring(0,2),16),s=parseInt(t.substring(2,4),16),a=parseInt(t.substring(4,6),16);return"rgba(".concat(e,", ").concat(s,", ").concat(a,", ").concat(n,")")}function L(t,n){return b(t,n,"w:val")}var C="black";function j(t,n){var e=a(n);if("nil"===e||"none"===e)return"none";var s=b(t,n),r=u(n,"w:sz",m.Border);return"".concat(r," solid ").concat("auto"==s?C:s)}function B(t,n,e){var a,r;try{for(var l=(0,s.Ju)(n.children),o=l.next();!o.done;o=l.next()){var x=o.value;switch(x.tagName){case"w:start":case"w:left":e["border-left"]=j(t,x);break;case"w:end":case"w:right":e["border-right"]=j(t,x);break;case"w:top":e["border-top"]=j(t,x);break;case"w:bottom":e["border-bottom"]=j(t,x)}}}catch(t){a={error:t}}finally{try{o&&!o.done&&(r=l.return)&&r.call(l)}finally{if(a)throw a.error}}}function O(t,n){switch(t.getAttribute("w:val")){case"lr":case"lrV":case"btLr":case"lrTb":case"lrTbV":case"tbLrV":n.direction="ltr";break;case"rl":case"rlV":case"tbRl":case"tbRlV":n.direction="rtl"}}function D(t){var n=t.getAttribute("w:type");return n&&"dxa"!==n?"pct"===n?u(t,"w:w",m.Percent):"auto"===n?"auto":(console.warn("parseTblWidth: ignore type",n,t),""):u(t,"w:w")}function F(t,n){var e,s,a=n.getElementsByTagName("w:insideH").item(0);a&&(e=j(t,a));var r=n.getElementsByTagName("w:insideV").item(0);return r&&(s=j(t,r)),{H:e,V:s}}function S(t,n){switch(a(t)){case"bottom":n["vertical-align"]="bottom";break;case"center":n["vertical-align"]="middle";break;case"top":n["vertical-align"]="top"}}function q(t,n){var e=D(t);e&&(n["cell-spacing"]=e)}function E(t,n){var e=D(t);e&&(n.width=e)}function P(t,n){var e,l,x={},y={};x.cssStyle=y;try{for(var p=(0,s.Ju)(n.children),f=p.next();!f.done;f=p.next()){var c=f.value,i=c.tagName;switch(i){case"w:tcMar":T(c,y);break;case"w:shd":y["background-color"]=k(0,c);break;case"w:tcW":E(c,y);break;case"w:noWrap":o(c)&&(y["white-space"]="nowrap");break;case"w:vAlign":S(c,y);break;case"w:tcBorders":B(t,c,y),x.insideBorder=F(t,c);break;case"w:gridSpan":x.gridSpan=r(c);break;case"w:vMerge":x.vMerge=a(c)||"continue";break;case"w:textDirection":O(c,y);break;case"w:cnfStyle":break;case"w:hideMark":x.hideMark=o(c,!0);break;default:console.warn("parseTcPr: ignore",i,c)}}}catch(t){e={error:t}}finally{try{f&&!f.done&&(l=p.return)&&l.call(p)}finally{if(e)throw e.error}}return x}function $(t,n,e){t/=255,n/=255,e/=255;var s,a=Math.max(t,n,e),r=Math.min(t,n,e),l=0,o=(a+r)/2;if(a==r)l=s=0;else{var x=a-r;switch(s=o>.5?x/(2-a-r):x/(a+r),a){case t:l=(n-e)/x+(n<e?6:0);break;case n:l=(e-t)/x+2;break;case e:l=(t-n)/x+4}l/=6}return{h:l,s:s,l:o}}function M(t,n,e){return e<0&&(e+=1),e>1&&(e-=1),e<1/6?t+6*(n-t)*e:e<.5?n:e<2/3?t+(n-t)*(2/3-e)*6:t}function N(t,n,e){var s,a,r;if(t>1&&(t/=360),0==n)s=a=r=e;else{var l=e<.5?e*(1+n):e+n-e*n,o=2*e-l;s=M(o,l,t+1/3),a=M(o,l,t),r=M(o,l,t-1/3)}return{r:255*s,g:255*a,b:255*r}}function I(t){return 1==t.length?"0"+t:""+t}function H(t,n,e){return[I(Math.round(t).toString(16)),I(Math.round(n).toString(16)),I(Math.round(e).toString(16))].join("").toUpperCase()}function z(t){return Math.min(Math.max(t,0),255)}var G=function(){function t(t){var n=t.match(/^#?([0-9a-f]{6})$/i);n&&(this.r=parseInt(n[1].substring(0,2),16),this.g=parseInt(n[1].substring(2,4),16),this.b=parseInt(n[1].substring(4,6),16),this.isValid=!0)}return t.fromHSL=function(n,e,s){var a=N(n,e,s);return new t("#".concat(H(a.r,a.g,a.b)))},t.fromRGB=function(n,e,s){var a=H(n,e,s);return new t("#".concat(a))},t.prototype.lum=function(t){return this.changeHsl(t,"l","set")},t.prototype.lumMod=function(t){return this.changeHsl(t,"l","mod")},t.prototype.lumOff=function(t){return this.changeHsl(t,"l","off")},t.prototype.hue=function(t){return this.changeHsl(t,"h","set")},t.prototype.hueMod=function(t){return this.changeHsl(t,"h","mod")},t.prototype.hueOff=function(t){return this.changeHsl(t,"h","off")},t.prototype.sat=function(t){return this.changeHsl(t,"s","set")},t.prototype.satMod=function(t){return this.changeHsl(t,"s","mod")},t.prototype.satOff=function(t){return this.changeHsl(t,"s","off")},t.prototype.changeHsl=function(t,n,e){var s=$(this.r,this.g,this.b);"set"===e?s[n]=t:"mod"===e?s[n]=s[n]*t:"off"===e&&(s[n]+=s[n]*t);var a=N(s.h,s.s,s.l);return this.r=a.r,this.g=a.g,this.b=a.b,this},t.prototype.comp=function(){var t=$(this.r,this.g,this.b);t.h=t.h+.5,t.h>1&&(t.h-=1);var n=N(t.h,t.s,t.l);return this.r=n.r,this.g=n.g,this.b=n.b,this},t.prototype.shade=function(t){this.r=z(this.r-256*t),this.g=z(this.g-256*t),this.b=z(this.b-256*t)},t.prototype.tint=function(t){this.r=z(this.r+256*t),this.g=z(this.g+256*t),this.b=z(this.b+256*t)},t.prototype.inv=function(){return this.r=255-this.r,this.g=255-this.g,this.b=255-this.b,this},t.prototype.toHex=function(){return"#"+H(this.r,this.g,this.b)},t.prototype.toRgba=function(t){return"rgba(".concat(this.r,", ").concat(this.g,", ").concat(this.b,", ").concat(t,")")},t}();function V(t,n){var e,a,r=new G(n);if(r.isValid){var l=1;try{for(var o=(0,s.Ju)(t.children),x=o.next();!x.done;x=o.next()){var y=x.value;switch(y.tagName){case"a:alpha":case"w14:alpha":l=p(y,"val");break;case"a:blue":r.b=256*p(y,"val");break;case"a:blueMod":r.b=r.b*p(y,"val");break;case"a:blueOff":r.b+=r.b*p(y,"val");break;case"a:comp":r.comp();break;case"a:green":r.g=256*p(y,"val");break;case"a:greenMod":r.g=r.g*p(y,"val");break;case"a:greenOff":r.g+=r.g*p(y,"val");break;case"a:red":r.r=256*p(y,"val");break;case"a:redMod":r.r=r.r*p(y,"val");break;case"a:redOff":r.r+=r.r*p(y,"val");break;case"a:lum":r.lum(p(y,"val"));break;case"a:lumMod":r.lumMod(p(y,"val"));break;case"a:lumOff":r.lumOff(p(y,"val"));break;case"a:hue":r.hue(g(y.getAttribute("hue"))/360);break;case"a:hueMod":r.hueMod(p(y,"val"));break;case"a:hueOff":r.hueOff(p(y,"val"));break;case"a:sat":r.sat(p(y,"val"));break;case"a:satMod":r.satMod(p(y,"val"));break;case"a:satOff":r.satOff(p(y,"val"));break;case"a:shade":r.shade(p(y,"val"));break;case"a:tint":r.tint(p(y,"val"));break;default:console.log("unknown color modify",y)}}}catch(t){e={error:t}}finally{try{x&&!x.done&&(a=o.return)&&a.call(o)}finally{if(e)throw e.error}}return 1!==l?r.toRgba(l):r.toHex()}return n}function X(t,n){var e=n.firstElementChild;if(e){var s=e.tagName;switch(s){case"a:prstClr":var r=a(e)||"";if(r in v)return V(e,v[r]);console.warn("parseOutline: Unknown color ",r,e);break;case"a:srgbClr":case"a:scrgbClr":case"w14:srgbClr":var l=a(e);if(l)return V(e,"#"+l);var o=p(e,"r"),x=p(e,"g"),y=p(e,"b");return V(e,G.fromRGB(o,x,y).toHex());case"a:hslClr":var f=p(e,"r"),c=p(e,"g"),i=p(e,"b"),d=a(e);return V(e,d?"#"+d:G.fromHSL(f,c,i).toHex());case"a:schemeClr":case"w14:schemeClr":var h=a(e);if(h)return V(e,t.getThemeColor(h));console.warn("parseOutline: Unknown schemeClr ",e);break;case"a:sysClr":return a(e);default:console.warn("parseOutline: Unknown color type ",s,e)}}return""}function J(t,n){var e=u(t,"w:firstLine"),s=u(t,"w:hanging"),a=u(t,"w:left"),r=u(t,"w:start"),l=u(t,"w:right"),o=u(t,"w:end");e&&(n["text-indent"]=e),s&&(n["text-indent"]="-".concat(s)),(a||r)&&(n["margin-left"]=a||r),(l||o)&&(n["margin-right"]=l||o)}function U(t,n,e){var s=u(n,"w:before"),a=u(n,"w:after"),r=n.getAttribute("w:lineRule");s&&(e["margin-top"]=s),a&&(e["margin-bottom"]=a);var l=n.getAttribute("w:line");if(l){if(t.renderOptions.forceLineHeight)return void(e["line-height"]=t.renderOptions.forceLineHeight);var o=parseInt(l,10),x=t.renderOptions.minLineHeight||1;switch(r){case"auto":var y=Math.max(x,o/240);e["line-height"]="".concat(y.toFixed(2));break;case"atLeast":break;default:var p=Math.max(x,o/20);e["line-height"]="".concat(p,"pt")}}}function W(t,n,e){var a,r,l=[],o=t.renderOptions.fontMapping;try{for(var x=(0,s.Ju)(n.attributes),y=x.next();!y.done;y=x.next()){var p=y.value,f=p.name,c=p.value;switch(f){case"w:ascii":case"w:cs":case"w:eastAsia":o&&c in o&&(c=o[c]),-1===c.indexOf(" ")?l.push(c):l.push('"'+c+'"');break;case"w:asciiTheme":case"w:csTheme":case"w:eastAsiaTheme":l.push("var(--docx-theme-font-".concat(c,")"))}}}catch(t){a={error:t}}finally{try{y&&!y.done&&(r=x.return)&&r.call(x)}finally{if(a)throw a.error}}l.length&&(e["font-family"]=Array.from(new Set(l)).join(", "))}function Z(t,n){var e=u(t,"w:val"),s=t.getAttribute("w:hRule");"exact"===s?n.height=e:"atLeast"===s&&(n.height=e,n["min-height"]=e)}function Y(t){switch(t){case"start":case"left":return"left";case"center":return"center";case"end":case"right":return"right";case"both":case"distribute":return"justify"}return t}function K(t,n,e){var s=a(n);if(null!=s){switch(s){case"dash":case"dashDotDotHeavy":case"dashDotHeavy":case"dashedHeavy":case"dashLong":case"dashLongHeavy":case"dotDash":case"dotDotDash":e["text-decoration-style"]="dashed";break;case"dotted":case"dottedHeavy":e["text-decoration-style"]="dotted";break;case"double":e["text-decoration-style"]="double";break;case"single":case"thick":case"words":e["text-decoration"]="underline";break;case"wave":case"wavyDouble":case"wavyHeavy":e["text-decoration-style"]="wavy";break;case"none":e["text-decoration"]="none"}var r=b(t,n);r&&(e["text-decoration-color"]=r)}}function _(t,n){var e,a;try{for(var r=(0,s.Ju)(t.attributes),l=r.next();!l.done;l=r.next()){var o=l.value,x=o.name,y=o.value;switch(x){case"w:dropCap":"drop"===y&&(n.float="left");break;case"w:h":"object"!=typeof y||Array.isArray(y)||(n.height=u(y,"w:h"));break;case"w:w":"object"!=typeof y||Array.isArray(y)||(n.width=u(y,"w:w"));break;case"w:hAnchor":case"w:vAnchor":case"w:lines":break;case"w:wrap":"around"!==y&&console.warn("parseFrame: w:wrap not support "+y);break;default:console.warn("parseFrame: unknown attribute "+x,o)}}}catch(t){e={error:t}}finally{try{l&&!l.done&&(a=r.return)&&a.call(r)}finally{if(e)throw e.error}}}function Q(t,n){switch(t){case"dot":case"underDot":n["text-emphasis"]="filled",n["text-emphasis-position"]="under right";break;case"comma":n["text-emphasis"]="filled sesame";break;case"circle":n["text-emphasis"]="open"}}function tt(t,n,e){var l,x,y={};try{for(var p=(0,s.Ju)(n.children),f=p.next();!f.done;f=p.next()){var c=f.value,i=c.tagName;switch(i){case"w:sz":case"w:szCs":y["font-size"]=u(c,"w:val",m.FontSize);break;case"w:jc":y["text-align"]=Y(a(c));break;case"w:framePr":_(c,y);break;case"w:pBdr":B(t,c,y);break;case"w:ind":J(c,y);break;case"w:color":y.color=L(t,c);break;case"w:shd":"background-color"in y||(y["background-color"]=k(0,c));break;case"w:spacing":U(t,c,y);break;case"w:highlight":y["background-color"]=b(t,c,"w:val","yellow");break;case"w:vertAlign":var d=a(c);"superscript"===d?y["vertical-align"]="super":"subscript"===d&&(y["vertical-align"]="sub");break;case"w:position":y["vertical-align"]=u(c,"w:val",m.FontSize);break;case"w:trHeight":Z(c,y);break;case"w:strike":case"w:dstrike":y["text-decoration"]=o(c)?"line-through":"none";break;case"w:b":y["font-weight"]=o(c)?"bold":"normal";break;case"w:adjustRightInd":case"w:bCs":case"w:iCs":case"w:kern":case"w:pStyle":case"w:lang":case"w:noProof":case"w:keepLines":case"w:keepNext":case"w:widowControl":case"w:pageBreakBefore":case"w:outlineLvl":case"w:contextualSpacing":case"w:numPr":case"w:rStyle":case"w:tabs":case"w:snapToGrid":case"w:topLinePunct":case"w:cnfStyle":case"w:autoSpaceDE":case"w:autoSpaceDN":case"w:kinsoku":case"w:overflowPunct":case"w14:reflection":case"w14:textFill":case"w14:ligatures":break;case"w:i":y["font-style"]=o(c)?"italic":"normal";break;case"w:caps":y["text-transform"]=o(c)?"uppercase":"normal";break;case"w:smallCaps":y["text-transform"]=o(c)?"lowercase":"normal";break;case"w:u":K(t,c,y);break;case"w:rFonts":W(t,c,y);break;case"w:tblCellSpacing":y["border-spacing"]=u(c,"w:w"),y["border-collapse"]="separate";break;case"w:bdr":y.border=j(t,c);break;case"w:vanish":o(c)&&(y.display="none");break;case"w:rPr":var h=c.getElementsByTagName("w14:reflection").item(0);if(h){var w=u(h,"w4:dist",m.Emu)||"0px";y["-webkit-box-reflect"]="below ".concat(w," linear-gradient(transparent, white)")}break;case"w:webHidden":y.display="none";break;case"w:wordWrap":o(c)&&(y["word-break"]="break-all");break;case"w:textAlignment":var g=a(c);"center"===g?y["vertical-align"]="middle":"auto"!==g&&(y["vertical-align"]=g);break;case"w:textDirection":O(c,y);break;case"w:bidi":o(c,!0)&&console.warn("w:bidi is not supported.");break;case"w:em":Q(a(c),y);break;case"w:w":var T=r(c);y.transform="scaleX(".concat(T/100,")"),y.display="inline-block";break;case"w:outline":y["text-shadow"]="-1px -1px 0 #AAA, 1px -1px 0 #AAA, -1px 1px 0 #AAA, 1px 1px 0 #AAA";break;case"w:shadown":case"w:imprint":o(c,!0)&&(y["text-shadow"]="1px 1px 2px rgba(0, 0, 0, 0.6)");break;case"w14:shadow":var v=u(c,"w14:blurRad",m.Emu)||"4px",A="rgba(0, 0, 0, 0.6)",R=X(t,c);R&&(A=R),y["text-shadow"]="1px 1px ".concat(v," ").concat(A);break;case"w14:textOutline":var C=u(c,"w14:w",m.Emu)||"1px";y["-webkit-text-stroke-width"]=C;var D="white",F=c.getElementsByTagName("w14:solidFill");F.length>0&&(D=X(t,F.item(0))||"white"),y["-webkit-text-stroke-color"]=D;break;default:console.warn("parsePr Unknown tagName",i,c)}}}catch(t){l={error:t}}finally{try{f&&!f.done&&(x=p.return)&&x.call(p)}finally{if(l)throw l.error}}return y}var nt=function(){function t(t){this.name=t}return t.fromXML=function(n,e){var s=e.getAttribute("w:name");return s?new t(s):(console.warn("Bookmark without name"),new t("unknown"))},t}(),et=function(){function t(){this.type="textWrapping"}return t.fromXML=function(n,e){return new t},t}(),st=function(){function t(){}return t.fromXML=function(n,e){var s=new t,a=e.getAttribute("r:embed")||"",r=n.getDocumentRels(a);return r&&(s.embled=r,s.src=n.loadImage(s.embled)),s},t}(),at=function(){function t(){}return t.fromXML=function(n,e){var s=new t,a=null==e?void 0:e.getElementsByTagName("a:blip").item(0);return a&&(s.blip=st.fromXML(n,a)),s},t}(),rt=function(){function t(){}return t.fromXML=function(n,e){var a,r,l=new t;try{for(var o=(0,s.Ju)(e.children),x=o.next();!x.done;x=o.next()){var y=x.value,p=y.tagName;switch(p){case"a:off":l.off={x:u(y,"x",m.Emu),y:u(y,"y",m.Emu)};break;case"a:ext":l.ext={cx:u(y,"cx",m.Emu),cy:u(y,"cy",m.Emu)};break;case"a:chOff":l.chOff={x:u(y,"x",m.Emu),y:u(y,"y",m.Emu)};break;case"a:chExt":l.chExt={cx:u(y,"cx",m.Emu),cy:u(y,"cy",m.Emu)};break;default:console.warn("Transform: Unknown tag ",p,y)}}}catch(t){a={error:t}}finally{try{x&&!x.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}var f=e.getAttribute("rot");return f&&(l.rot=g(f)),l},t}();function lt(t){var n,e,a=[];try{for(var r=(0,s.Ju)(t.children),l=r.next();!l.done;l=r.next()){var o=l.value,x=o.tagName;if("a:pt"===x||"pt"===x){var y=o.getAttribute("x"),p=o.getAttribute("y");y&&p&&a.push({x:y,y:p})}else console.warn("unknown pt",x,o)}}catch(t){n={error:t}}finally{try{l&&!l.done&&(e=r.return)&&e.call(r)}finally{if(n)throw n.error}}return a}function ot(t){var n,e,a=[];try{for(var r=(0,s.Ju)(t.children),l=r.next();!l.done;l=r.next()){var o=l.value,y=o.tagName;switch(y){case"a:moveTo":case"moveTo":var p=lt(o);if(p.length){var f={type:"moveTo",pt:p[0]};a.push(f)}break;case"a:lnTo":case"lnTo":var c=lt(o);if(c.length){var i={type:"lnTo",pt:c[0]};a.push(i)}break;case"a:quadBezTo":case"quadBezTo":var d=lt(o);if(d.length){var h={type:"quadBezTo",pts:d};a.push(h)}break;case"a:cubicBezTo":case"cubicBezTo":var m=lt(o);if(m.length){var w={type:"cubicBezTo",pts:m};a.push(w)}break;case"a:arcTo":case"arcTo":var g=o.getAttribute("wR"),u=o.getAttribute("hR"),T=o.getAttribute("stAng"),v=o.getAttribute("swAng");if(g&&u&&T&&v){var A={type:"arcTo",wR:g,hR:u,stAng:T,swAng:v};a.push(A)}break;case"a:close":case"close":a.push({type:"close"});break;default:console.warn("parsePath: unknown tag",y,o)}}}catch(t){n={error:t}}finally{try{l&&!l.done&&(e=r.return)&&e.call(r)}finally{if(n)throw n.error}}var b={defines:a},k=t.getAttribute("fill");k&&(b.fill=k),b.extrusionOk=x(t,"extrusionOk",!1),b.stroke=x(t,"stroke",!0);var R=t.getAttribute("w");R&&(b.w=parseInt(R,10));var L=t.getAttribute("h");return L&&(b.h=parseInt(L,10)),b}function xt(t){var n,e,a=[];try{for(var r=(0,s.Ju)(t.children),l=r.next();!l.done;l=r.next()){var o=l.value;switch(o.tagName){case"a:path":case"path":a.push(ot(o))}}}catch(t){n={error:t}}finally{try{l&&!l.done&&(e=r.return)&&e.call(r)}finally{if(n)throw n.error}}return a}function yt(t){var n,e,a=[];try{for(var r=(0,s.Ju)(t.children),l=r.next();!l.done;l=r.next()){var o=l.value;switch(o.tagName){case"a:gd":case"gd":var x=o.getAttribute("name"),y=o.getAttribute("fmla");if(x&&y){var p={n:x,f:y};a.push(p)}}}}catch(t){n={error:t}}finally{try{l&&!l.done&&(e=r.return)&&e.call(r)}finally{if(n)throw n.error}}return a}var pt=function(){function t(){}return t.fromXML=function(n,e){var a,r,l=new t;l.prst=e.getAttribute("prst");try{for(var o=(0,s.Ju)(e.children),x=o.next();!x.done;x=o.next()){var y=x.value;"a:avLst"===y.tagName&&(l.avLst=yt(y))}}catch(t){a={error:t}}finally{try{x&&!x.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return l},t}(),ft=function(){function t(){}return t.fromXML=function(n,e){var a=new t;return a.shape=function(t){var n,e,a={};try{for(var r=(0,s.Ju)(t.children),l=r.next();!l.done;l=r.next()){var o=l.value;switch(o.tagName){case"a:avLst":case"avLst":a.avLst=yt(o);break;case"a:gdLst":case"gdLst":a.gdLst=yt(o);break;case"a:rect":case"react":var x={b:o.getAttribute("b")||"",l:o.getAttribute("l")||"",r:o.getAttribute("r")||"",t:o.getAttribute("t")||""};a.rect=x;break;case"a:pathLst":case"pathLst":a.pathLst=xt(o)}}}catch(t){n={error:t}}finally{try{l&&!l.done&&(e=r.return)&&e.call(r)}finally{if(n)throw n.error}}return a}(e),a},t}();function ct(t){var n="solid";switch(t){case"dash":case"dashDot":case"lgDash":case"lgDashDot":case"lgDashDotDot":case"sysDash":case"sysDashDot":case"sysDashDotDot":n="dashed";break;case"dot":case"sysDot":n="dotted"}return n}function it(t,n){var e,a,r={width:u(n,"w",m.Emu),style:"solid"};try{for(var l=(0,s.Ju)(n.children),o=l.next();!o.done;o=l.next()){var x=o.value,y=x.tagName;switch(y){case"a:solidFill":r.color=X(t,x);break;case"a:noFill":r.style="none";break;case"a:round":r.radius="8%";break;case"a:prstDash":r.style=ct(x.getAttribute("val"));break;default:console.warn("parseOutline: Unknown tag ",y,x)}}}catch(t){e={error:t}}finally{try{o&&!o.done&&(a=l.return)&&a.call(l)}finally{if(e)throw e.error}}return r}var dt=function(){function t(){}return t.fromXML=function(n,e){var a,r,l=new t;if(e)try{for(var o=(0,s.Ju)(e.children),x=o.next();!x.done;x=o.next()){var y=x.value,p=y.tagName;switch(p){case"a:xfrm":l.xfrm=rt.fromXML(n,y);break;case"a:prstGeom":l.geom=pt.fromXML(n,y);break;case"a:custGeom":l.custGeom=ft.fromXML(n,y);break;case"a:ln":l.outline=it(n,y);break;case"a:noFill":l.noFill=!0;break;case"a:solidFill":l.fillColor=X(n,y);break;default:console.warn("ShapePr: Unknown tag ",p,y)}}}catch(t){a={error:t}}finally{try{x&&!x.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return l},t}(),ht=function(){function t(){}return t.fromXML=function(n,e){var s=new t,a=null==e?void 0:e.getElementsByTagName("pic:cNvPr").item(0);return a&&(s.alt=a.getAttribute("descr")||"",s.altVar=a.getAttribute("descrVar")||"",x(a,"hidden",!1))||(s.blipFill=at.fromXML(n,null==e?void 0:e.getElementsByTagName("pic:blipFill").item(0)),s.spPr=dt.fromXML(n,null==e?void 0:e.getElementsByTagName("pic:spPr").item(0))),s},t}(),mt=function(){this.properties={},this.tblGrid=[],this.trs=[]},wt=function(){this.properties={},this.tcs=[]},gt=function(){function t(){this.properties={},this.children=[]}return t.prototype.add=function(t){t&&this.children.push(t)},t}();function ut(t,n,e,a){var r,l,o=new gt;try{for(var x=(0,s.Ju)(n.children),y=x.next();!y.done;y=x.next()){var p=y.value;switch(p.tagName){case"w:tcPr":o.properties=P(t,p);break;case"w:p":o.add(sn.fromXML(t,p));break;case"w:tbl":o.add(Ft(t,p))}}}catch(t){r={error:t}}finally{try{y&&!y.done&&(l=x.return)&&l.call(x)}finally{if(r)throw r.error}}var f=a[e.index];if(o.properties.vMerge){if("restart"===o.properties.vMerge)o.properties.rowSpan=1,a[e.index]=o;else if(f){if(f.properties&&f.properties.rowSpan){f.properties.rowSpan=f.properties.rowSpan+1;var c=o.properties.gridSpan||1;return e.index+=c,null}console.warn("Tc.fromXML: continue but not found lastCol",e.index,o,a)}}else delete a[e.index];var i=o.properties.gridSpan||1;return e.index+=i,o}function Tt(t,n){switch(a(t)){case"left":case"start":break;case"right":case"end":n.float="right"}}function vt(t,n){var e=D(t);e&&(n["margin-left"]=e)}function At(t,n){var e=D(t);e&&(n.width=e)}function bt(t){var n={},e=parseInt(a(t)||"0",16);return(x(t,"firstRow",!1)||32&e)&&(n.firstRow=!0),(x(t,"lastRow",!1)||64&e)&&(n.lastRow=!0),(x(t,"firstColumn",!1)||128&e)&&(n.firstColumn=!0),(x(t,"lastColumn",!1)||256&e)&&(n.lastColumn=!0),x(t,"noHBand",!1)||512&e?n.noHBand=!0:n.noHBand=!1,x(t,"noVBand",!1)||1024&e?n.noVBand=!0:n.noVBand=!1,n}function kt(t,n,e){if(void 0===t.renderOptions.padding){var s=u(n,"w:tblpX"),a=u(n,"w:tblpY");e.top=a,e.left=s}}function Rt(t,n){"fixed"===t.getAttribute("w:type")&&(n["table-layout"]="fixed")}function Lt(t,n){var e,l,o={},x={},y={};o.tblLook={},o.cssStyle=x,o.tcCSSStyle=y;try{for(var p=(0,s.Ju)(n.children),f=p.next();!f.done;f=p.next()){var c=f.value,i=c.tagName;switch(i){case"w:tblBorders":B(t,c,x),o.insideBorder=F(t,c);break;case"w:tcBorders":B(t,c,x);break;case"w:tblInd":vt(c,x);break;case"w:jc":Tt(c,x);break;case"w:tblCellMar":case"w:tcMar":T(c,y);break;case"w:tblStyle":o.pStyle=a(c);break;case"w:tblW":At(c,x);break;case"w:shd":x["background-color"]=k(0,c);break;case"w:tblCaption":o.tblCaption=a(c);break;case"w:tblCellSpacing":q(c,x);break;case"w:tblLayout":Rt(c,x);break;case"w:tblLook":o.tblLook=bt(c);break;case"w:tblStyleRowBandSize":o.rowBandSize=r(c);break;case"w:tblStyleColBandSize":o.colBandSize=r(c);break;case"w:tblpPr":kt(t,c,x);break;default:console.warn("parseTableProperties unknown tag",i,c)}}}catch(t){e={error:t}}finally{try{f&&!f.done&&(l=p.return)&&l.call(p)}finally{if(e)throw e.error}}return o}function Ct(t,n){var e,r,l={},x={};try{for(var y=(0,s.Ju)(n.children),p=y.next();!p.done;p=y.next()){var f=p.value,c=f.tagName;switch(c){case"w:hidden":o(f)&&(l.display="none");break;case"w:trHeight":Z(f,l);break;case"w:jc":l["text-align"]=Y(a(f));break;case"w:cantSplit":case"w:cnfStyle":break;case"w:tblPrEx":var i=Lt(t,f);Object.assign(l,i.cssStyle);break;case"w:tblCellSpacing":q(f,x);break;default:console.warn("Tr: Unknown tag ",c,f)}}}catch(t){e={error:t}}finally{try{p&&!p.done&&(r=y.return)&&r.call(y)}finally{if(e)throw e.error}}return{cssStyle:l}}function jt(t){var n,e,a=t.slice(),r=0,l=!1;try{for(var o=(0,s.Ju)(t),x=o.next();!x.done;x=o.next()){var y=x.value;switch(y.tagName){case"w:smartTag":case"w:customXml":var p=[].slice.call(y.children);a.splice.apply(a,(0,s.fX)([r,1],(0,s.zs)(p),!1)),r+=p.length;continue;case"w:sdt":var f=y.getElementsByTagName("w:sdtContent").item(0);if(y.getElementsByTagName("w:sdt").item(0)&&(l=!0),f){var c=[].slice.call(f.children);a.splice.apply(a,(0,s.fX)([r,1],(0,s.zs)(c),!1)),r+=c.length;continue}}r+=1}}catch(t){n={error:t}}finally{try{x&&!x.done&&(e=o.return)&&e.call(o)}finally{if(n)throw n.error}}return l?jt(a):a}function Bt(t){return jt([].slice.call(t.children))}function Ot(t,n,e){var a,r,l=new wt,o={index:0};try{for(var x=(0,s.Ju)(Bt(n)),y=x.next();!y.done;y=x.next()){var p=y.value,f=p.tagName;switch(f){case"w:tc":var c=ut(t,p,o,e);c&&l.tcs.push(c);break;case"w:trPr":l.properties=Ct(t,p);break;case"w:tblPrEx":var i=Lt(t,p);Object.assign(l.properties.cssStyle||{},i.cssStyle);break;default:console.warn("Tr: Unknown tag ",f,p)}}}catch(t){a={error:t}}finally{try{y&&!y.done&&(r=x.return)&&r.call(x)}finally{if(a)throw a.error}}return l}function Dt(t){var n,e,a=[],r=t.getElementsByTagName("w:gridCol");try{for(var l=(0,s.Ju)(r),o=l.next();!o.done;o=l.next()){var x=u(o.value,"w:w");a.push({w:x})}}catch(t){n={error:t}}finally{try{o&&!o.done&&(e=l.return)&&e.call(l)}finally{if(n)throw n.error}}return a}function Ft(t,n){var e,a,r=new mt,l={};try{for(var o=(0,s.Ju)(Bt(n)),x=o.next();!x.done;x=o.next()){var y=x.value,p=y.tagName;switch(p){case"w:tblPr":r.properties=Lt(t,y);break;case"w:tr":r.trs.push(Ot(t,y,l));break;case"w:tblGrid":r.tblGrid=Dt(y);break;default:console.warn("Table.fromXML unknown tag",p,y)}}}catch(t){e={error:t}}finally{try{x&&!x.done&&(a=o.return)&&a.call(o)}finally{if(e)throw e.error}}return r}var St=function(){function t(){}return t.fromXML=function(n,e){var a,r,l=new t;try{for(var o=(0,s.Ju)(e.children),x=o.next();!x.done;x=o.next()){var y=x.value;switch(y.tagName){case"a:fillRef":l.fillColor=X(n,y);break;case"a:lnRef":l.lineColor=X(n,y);break;case"a:fontRef":l.fontColor=X(n,y)}}}catch(t){a={error:t}}finally{try{x&&!x.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return l},t}();function qt(t,n){var e,a;try{for(var r=(0,s.Ju)(t.attributes),l=r.next();!l.done;l=r.next()){var o=l.value,x=o.name,y=o.value;switch(x){case"numCol":"1"!==y&&(n["column-count"]=y);break;case"vert":switch(y){case"vert":n["writing-mode"]="vertical-rl",n["text-orientation"]="sideways";break;case"vert270":case"eaVert":n["writing-mode"]="vertical-rl",n["text-orientation"]="mixed"}break;case"anchor":switch(y){case"b":n["vertical-align"]="bottom";break;case"t":n["vertical-align"]="top";break;case"ctr":n["vertical-align"]="middle"}break;case"rot":var p=g(y);p&&(n.transform="rotate(".concat(p,"deg)"))}}}catch(t){e={error:t}}finally{try{l&&!l.done&&(a=r.return)&&a.call(r)}finally{if(e)throw e.error}}}var Et,Pt=function(){function t(){this.style={}}return t.fromXML=function(n,e){var a,r,l,o,x=new t;x.txbxContent=[];try{for(var y=(0,s.Ju)(e.children),p=y.next();!p.done;p=y.next()){var f=p.value,c=f.tagName;switch(c){case"wps:cNvSpPr":case"wps:cNvPr":break;case"wps:spPr":x.spPr=dt.fromXML(n,f);break;case"wps:txbx":var i=f.firstElementChild;if(i)try{for(var d=(l=void 0,(0,s.Ju)(i.children)),h=d.next();!h.done;h=d.next()){var m=h.value;switch(m.tagName){case"w:p":x.txbxContent.push(sn.fromXML(n,m));break;case"w:tbl":x.txbxContent.push(Ft(n,m))}}}catch(t){l={error:t}}finally{try{h&&!h.done&&(o=d.return)&&o.call(d)}finally{if(l)throw l.error}}else console.warn("unknown wps:txbx",f);break;case"wps:style":x.wpsStyle=St.fromXML(n,f);break;case"wps:bodyPr":qt(f,x.style);break;default:console.warn("WPS: Unknown tag ",c,f)}}}catch(t){a={error:t}}finally{try{p&&!p.done&&(r=y.return)&&r.call(y)}finally{if(a)throw a.error}}return x},t}(),$t=function(){function t(){}return t.fromXML=function(n,e){var s=new t,a=e.getAttribute("r:dm");if(a){var r=n.getDocumentRels(a);if(r){var l=n.loadWordRelXML(r);console.log(l)}}return s},t}(),Mt=function(){function t(){}return t.fromXML=function(n,e){var a,r,l=new t,o=[];l.wps=o,l.wpg=[];try{for(var x=(0,s.Ju)(e.children),y=x.next();!y.done;y=x.next()){var p=y.value,f=p.tagName;switch(f){case"wpg:cNvGrpSpPr":break;case"wpg:grpSpPr":l.spPr=dt.fromXML(n,p);break;case"wps:wsp":o.push(Pt.fromXML(n,p));break;case"pic:pic":l.pic=ht.fromXML(n,p);break;case"wpg:grpSp":l.wpg.push(t.fromXML(n,p));break;default:console.warn("WPS: Unknown tag ",f,p)}}}catch(t){a={error:t}}finally{try{y&&!y.done&&(r=x.return)&&r.call(x)}finally{if(a)throw a.error}}return l},t}();!function(t){t.inline="inline",t.anchor="anchor"}(Et||(Et={}));var Nt,It=function(){function t(){this.position=Et.inline}return t.fromXML=function(n,e){var a,r,l,o=new t,p={};o.containerStyle=p;var f=e.firstElementChild;if(f){if("wp:anchor"===f.tagName){o.position=Et.anchor,o.anchor=function(t){return{simplePos:x(t,"simplePos",!1),hidden:x(t,"hidden",!1),behindDoc:x(t,"behindDoc",!1)}}(f);var c=y(f,"relativeHeight",1);p["z-index"]=c}try{for(var i=(0,s.Ju)(f.children),d=i.next();!d.done;d=i.next()){var h=d.value,g=h.tagName;switch(g){case"wp:simplePos":(null===(l=o.anchor)||void 0===l?void 0:l.simplePos)&&(p.position="absolute",p.x=u(h,"x",m.Emu),p.y=u(h,"y",m.Emu));break;case"wp:positionH":var T=h.getAttribute("relativeFrom");if("column"===T||"page"===T||"margin"===T){if(A=h.firstElementChild){var v=A.tagName;p.position="absolute","wp:posOffset"===v?p.left=w(A.innerHTML,m.Emu):(p.left="0",console.warn("unsupport positionType",v))}}else console.warn("unsupport positionH relativeFrom",T);break;case"wp:positionV":var A,b=h.getAttribute("relativeFrom");"paragraph"===b||"page"===b?(o.relativeFromV=b,(A=h.firstElementChild)&&(v=A.tagName,p.position="absolute","wp:posOffset"===v?p.top=w(A.innerHTML,m.Emu):(p.top="0",console.warn("unsupport positionType",v)))):console.warn("unsupport positionV relativeFrom",b);break;case"wp:docPr":o.id=h.getAttribute("id")||void 0,o.name=h.getAttribute("name")||void 0;break;case"wp:cNvGraphicFramePr":case"wp:effectExtent":case"wp:wrapNone":case"wp14:sizeRelH":case"wp14:sizeRelV":break;case"a:graphic":var k=h.firstElementChild,R=null==k?void 0:k.firstElementChild;if(R)switch(R.tagName){case"pic:pic":o.pic=ht.fromXML(n,R);break;case"wps:wsp":o.wps=Pt.fromXML(n,R);break;case"wpg:wgp":o.wpg=Mt.fromXML(n,R);break;case"dgm:relIds":o.diagram=$t.fromXML(n,R);break;default:console.warn("unknown graphicData child tag",R)}break;case"wp:extent":p.width=u(h,"cx",m.Emu),p.height=u(h,"cy",m.Emu);break;default:console.warn("drawing unknown tag",g)}}}catch(t){a={error:t}}finally{try{d&&!d.done&&(r=i.return)&&r.call(i)}finally{if(a)throw a.error}}}return o},t}(),Ht=function(t){this.text=t},zt=function(){},Gt=function(){function t(){}return t.fromXML=function(n,e){var s=new t,a=e.getElementsByTagName("v:imagedata").item(0);if(a){var r=a.getAttribute("r:id")||"",l=n.getDocumentRels(r);l&&(s.src=n.loadImage(l))}return s},t}(),Vt=function(){function t(){}return t.fromXML=function(n,e){var a,r,l=new t;l.children=[];try{for(var o=(0,s.Ju)(e.children),x=o.next();!x.done;x=o.next()){var y=x.value,p=y.tagName;if("w:r"===p){var f=Kt.fromXML(n,y);f&&l.children.push(f)}else console.warn("parse Ruby: Unknown key",p,y)}}catch(t){a={error:t}}finally{try{x&&!x.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return l},t}();Nt=Vt,(0,s.C6)((function(){return null!==Nt&&Nt.apply(this,arguments)||this}),Nt);var Xt=function(){function t(){}return t.fromXML=function(n,e){var a,r,l=new t;try{for(var o=(0,s.Ju)(e.children),x=o.next();!x.done;x=o.next()){var y=x.value,p=y.tagName;switch(p){case"w:rubyPr":break;case"w:rt":l.rt=Vt.fromXML(n,y);break;case"w:rubyBase":l.rubyBase=Vt.fromXML(n,y);break;default:console.warn("parse Ruby: Unknown key",p,y)}}}catch(t){a={error:t}}finally{try{x&&!x.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return l},t}(),Jt=function(){},Ut=function(){},Wt=function(){function t(){}return t.parseXML=function(n){var e=new t;return e.font=n.getAttribute("w:font")||"",e.char=n.getAttribute("w:char")||"",e},t}(),Zt=function(){function t(){}return t.fromXML=function(n,e){var s=new t;return s.pos=u(e,"w:pos"),s.type=a(e),s.leader=e.getAttribute("w:leader"),s},t}(),Yt=function(t){this.preserveSpace=!1,this.text=String(t)},Kt=function(){function t(){this.properties={},this.children=[]}return t.prototype.addChild=function(t){t&&this.children.push(t)},t.parseRunPr=function(t,n){var e,s=tt(t,n),r=n.getElementsByTagName("w:rStyle").item(0);return r&&(e=a(r)),{cssStyle:s,rStyle:e}},t.fromXML=function(n,e){var a,r,l=new t;try{for(var o=(0,s.Ju)(e.children),x=o.next();!x.done;x=o.next()){var y=x.value,p=y.tagName;switch(p){case"w:t":var f=y.textContent||"",c=new Yt(f);l.addChild(c);break;case"w:rPr":l.properties=t.parseRunPr(n,y);break;case"w:br":case"w:cr":l.addChild(et.fromXML(n,y));break;case"w:drawing":l.addChild(It.fromXML(n,y));break;case"w:tab":l.addChild(Zt.fromXML(n,y));break;case"w:fldChar":l.fldChar=y.getAttribute("w:fldCharType");break;case"w:instrText":l.addChild(new Ht(y.textContent||""));break;case"w:lastRenderedPageBreak":var i=new et;i.type="page",l.addChild(i);break;case"w:pict":l.addChild(Gt.fromXML(n,y));break;case"w:ruby":l.addChild(Xt.fromXML(n,y));break;case"w:sym":l.addChild(Wt.parseXML(y));break;case"mc:AlternateContent":var d=y.getElementsByTagName("w:drawing").item(0);d&&l.addChild(It.fromXML(n,d));break;case"w:softHyphen":l.addChild(new Ut);break;case"w:noBreakHyphen":l.addChild(new zt);break;case"w:separator":l.addChild(new Jt);break;case"w:continuationSeparator":break;default:console.warn("parse Run: Unknown key",p,y)}}}catch(t){a={error:t}}finally{try{x&&!x.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return l},t}(),_t=function(){function t(){this.children=[]}return t.prototype.addChild=function(t){this.children.push(t)},t.fromXML=function(n,e){var a,r,l=new t,o=e.getAttribute("r:id");if(o){var x=n.getDocumentRels(o);x&&(l.relation=x)}var y=e.getAttribute("w:anchor");y&&(l.anchor=y);var p=e.getAttribute("w:tooltip");p&&(l.tooltip=p);try{for(var f=(0,s.Ju)(e.children),c=f.next();!c.done;c=f.next()){var i=c.value,d=i.tagName;"w:r"===d?l.addChild(Kt.fromXML(n,i)):console.warn("parse Hyperlink: Unknown key",d,i)}}catch(t){a={error:t}}finally{try{c&&!c.done&&(r=f.return)&&r.call(f)}finally{if(a)throw a.error}}return l},t}(),Qt=function(){function t(){}return t.fromXML=function(n,e){var s=new t,r=e.getElementsByTagName("w:ilvl").item(0);r&&(s.ilvl=a(r));var l=e.getElementsByTagName("w:numId").item(0);return l&&(s.numId=a(l)),s},t}(),tn=function(){function t(){this.children=[]}return t.prototype.addChild=function(t){this.children.push(t)},t.fromXML=function(n,e){var a,r,l=new t;try{for(var o=(0,s.Ju)(e.children),x=o.next();!x.done;x=o.next()){var y=x.value,p=y.tagName;switch(p){case"w:r":l.addChild(Kt.fromXML(n,y));break;case"w:hyperlink":l.addChild(_t.fromXML(n,y));break;case"w:bookmarkStart":l.addChild(nt.fromXML(n,y));case"w:bookmarkEnd":case"w:proofErr":case"w:noProof":case"w:smartTagPr":case"w:del":break;default:console.warn("parse Inline: Unknown key",p,y)}}}catch(t){a={error:t}}finally{try{x&&!x.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return l},t}(),nn=function(){function t(){}return t.fromXML=function(n,e){var s=new t;return s.inlineText=tn.fromXML(n,e),s.instr=e.getAttribute("w:instr")||"",s},t}(),en=function(){function t(){}return t.fromXML=function(n,e){var s=new t;return s.element=e,s},t}(),sn=function(){function t(){this.properties={},this.children=[],this.fldSimples=[]}return t.prototype.addChild=function(t){this.children.push(t)},t.parseParagraphPr=function(t,n){var e,r,l,o,x=tt(t,n),y=n.getElementsByTagName("w:pStyle").item(0);y&&(l=a(y));var p=n.getElementsByTagName("w:numPr").item(0);p&&(o=Qt.fromXML(t,p));var f=[],c=n.getElementsByTagName("w:tab");try{for(var i=(0,s.Ju)(c),d=i.next();!d.done;d=i.next()){var h=d.value;f.push(Zt.fromXML(t,h))}}catch(t){e={error:t}}finally{try{d&&!d.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}var m=function(t){var n=t.getElementsByTagName("w:autoSpaceDE").item(0),e=t.getElementsByTagName("w:autoSpaceDN").item(0);return!!n||!!e}(n);return{cssStyle:x,pStyle:l,numPr:o,tabs:f,autoSpace:m}},t.fromXML=function(n,e){var a,r,l=new t;l.fldSimples=[],l.paraId=e.getAttribute("w14:paraId")||"";try{for(var o=(0,s.Ju)(Bt(e)),x=o.next();!x.done;x=o.next()){var y=x.value,p=y.tagName;switch(p){case"w:pPr":l.properties=t.parseParagraphPr(n,y);break;case"w:r":l.addChild(Kt.fromXML(n,y));break;case"w:hyperlink":l.addChild(_t.fromXML(n,y));break;case"w:bookmarkStart":l.addChild(nt.fromXML(n,y));case"w:bookmarkEnd":case"w:proofErr":case"w:noProof":case"w:del":case"w:moveTo":case"w:moveFrom":break;case"w:fldSimple":l.fldSimples.push(nn.fromXML(n,y));break;case"m:oMathPara":case"m:oMath":l.addChild(en.fromXML(n,y));break;default:console.warn("parse Paragraph: Unknown key",p,y)}}}catch(t){a={error:t}}finally{try{x&&!x.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return l},t}();function an(t,n){var e,a,r={};try{for(var l=(0,s.Ju)(n.children),o=l.next();!o.done;o=l.next()){var x=o.value;switch(x.tagName){case"w:rPr":r.rPr=Kt.parseRunPr(t,x);break;case"w:pPr":r.pPr=sn.parseParagraphPr(t,x);break;case"w:tblPr":r.tblPr=Lt(t,x);break;case"w:tcPr":r.tcPr=P(t,x);break;case"w:trPr":r.trPr=Ct(t,x)}}}catch(t){e={error:t}}finally{try{o&&!o.done&&(a=l.return)&&a.call(l)}finally{if(e)throw e.error}}return r}function rn(t,n){var e,r,l={};l.id=n.getAttribute("w:styleId")||"",l.type=n.getAttribute("w:type"),l.tblStylePr={},Object.assign(l,an(t,n));try{for(var o=(0,s.Ju)(n.children),x=o.next();!x.done;x=o.next()){var y=x.value,p=y.tagName;switch(p){case"w:name":l.name=a(y);break;case"w:basedOn":l.basedOn=a(y);break;case"w:rPr":case"w:pPr":case"w:tblPr":case"w:tcPr":case"w:trPr":case"w:next":case"w:link":case"w:unhideWhenUsed":case"w:qFormat":case"w:rsid":case"w:uiPriority":case"w:semiHidden":case"w:autoRedefine":break;case"w:tblStylePr":var f=y.getAttribute("w:type");l.tblStylePr[f]=an(t,y);break;default:console.warn("parseStyle Unknown tag",p,y)}}}catch(t){e={error:t}}finally{try{x&&!x.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return l}function ln(t,n){var e,a,r={styleMap:{}},l=Array.from(n.getElementsByTagName("w:style"));try{for(var o=(0,s.Ju)(l),x=o.next();!x.done;x=o.next()){var y=rn(t,x.value);y.id&&(r.styleMap[y.id]=y)}}catch(t){e={error:t}}finally{try{x&&!x.done&&(a=o.return)&&a.call(o)}finally{if(e)throw e.error}}return r.defaultStyle=function(t,n){var e={};if(!n)return e;var s=n.getElementsByTagName("w:rPrDefault").item(0);if(s){var a=s.getElementsByTagName("w:rPr").item(0);a&&(e.rPr=Kt.parseRunPr(t,a))}var r=n.getElementsByTagName("w:pPrDefault").item(0);if(r){var l=r.getElementsByTagName("w:pPr").item(0);l&&(e.pPr=sn.parseParagraphPr(t,l))}return e}(t,n.getElementsByTagName("w:docDefaults").item(0)),r}var on=function(){this.colors={}};function xn(t){var n,e,r={};return r.themeElements=(n=t.getElementsByTagName("a:themeElements").item(0),e={},n&&(e.clrScheme=function(t){var n,e,r=new on;if(!t)return r;r.name=t.getAttribute("name")||"";try{for(var l=(0,s.Ju)(t.children),o=l.next();!o.done;o=l.next()){var x=o.value,y=x.tagName.replace("a:",""),f=x.firstElementChild;if(f){var c=f.nodeName.replace("a:","");if("sysClr"===c)r.colors[y]=f.getAttribute("lastClr")||"";else if("srgbClr"===c)r.colors[y]="#"+f.getAttribute("val")||0;else if("scrgbClr"===c){var i=256*p(x,"r"),d=256*p(x,"g"),h=256*p(x,"b");r.colors[y]="rgb(".concat(i,", ").concat(d,", ").concat(h,")")}else if("hslClr"===c){var m=g(x.getAttribute("hue")),w=100*p(x,"sat"),u=100*p(x,"lum");r.colors[y]="hsl(".concat(m,", ").concat(w,"%, ").concat(u,"%)")}else"prstClr"===c?r.colors[y]=a(x):console.error("unknown clr name",c)}}}catch(t){n={error:t}}finally{try{o&&!o.done&&(e=l.return)&&e.call(l)}finally{if(n)throw n.error}}return r}(n.getElementsByTagName("a:clrScheme").item(0)),e.fontScheme=(n.getElementsByTagName("a:fontScheme").item(0),{}),e.fmtScheme=(n.getElementsByTagName("a:fmtScheme").item(0),{})),e),r}function yn(t){void 0===t&&(t={});var n="";for(var e in t){var s=t[e];null!=s&&""!==s&&(n+="".concat(e,": ").concat(s,";\n"))}return n}function pn(t,n){if(n)for(var e in n){var s=n[e];null!=s&&""!==s&&t.style.setProperty(e,String(s))}}function fn(t){return document.createElement(t)}function cn(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function dn(t,n){t&&n&&t.appendChild(n)}function hn(t,n){t&&n&&t.classList.add(n)}function mn(t,n){var e;t&&n&&(e=t.classList).add.apply(e,(0,s.fX)([],(0,s.zs)(n),!1))}function wn(t,n){return"page"===n.type&&(t.breakPage=!0),fn("br")}function gn(t,n,e){var s="",a=e.tblPr,r=e.tcPr;if(a){var l=yn(a.cssStyle),o=yn(a.tcCSSStyle);if(s+="\n .".concat(t," .").concat(n," {\n  border-collapse: collapse;\n  ").concat(l,"\n }\n\n .").concat(t," .").concat(n," > tbody > tr > td {\n  ").concat(o,"\n }\n "),a.insideBorder){var x=a.insideBorder;x.H&&(s+="\n      .".concat(t," .").concat(n," > tbody > tr > td {\n        border-top: ").concat(x.H,";\n      }")),x.V&&(s+="\n      .".concat(t," .").concat(n," > tbody > tr > td {\n        border-left: ").concat(x.V,";\n      }"))}}if(r){var y=yn(r.cssStyle);s+="\n    .".concat(t," .").concat(n," > tbody > tr > td {\n     ").concat(y,"\n    }\n    ")}return s}function un(t,n,e,s){var a,r,l,o,x,y,p="",f=yn(null===(a=s.trPr)||void 0===a?void 0:a.cssStyle),c="";switch(e){case"firstCol":c="enable-firstColumn";break;case"lastCol":c="enable-lastColumn";break;case"firstRow":c="enable-firstRow";break;case"lastRow":c="enable-lastRow";break;case"band1Horz":case"band2Horz":c="enable-hBand";break;case"band1Vert":case"band2Vert":c="enable-vBand"}f&&(p+="\n    ".concat(t,".").concat(c," > tbody > tr.").concat(e,"{\n       ").concat(f,"\n    }\n    "));var i=yn(null===(r=s.tcPr)||void 0===r?void 0:r.cssStyle);if(i&&(p+="\n    ".concat(t,".").concat(c," > tbody > tr > td.").concat(e," {\n       ").concat(i,"\n    }\n    "),null===(l=s.tcPr)||void 0===l?void 0:l.insideBorder)){var d=null===(o=s.tcPr)||void 0===o?void 0:o.insideBorder;d.H&&(p+="\n          ".concat(t,".").concat(c," > tbody > tr > td.").concat(e," {\n            border-top: ").concat(d.H,";\n          }")),d.V&&("none"===d.V?p+="\n          ".concat(t,".").concat(c," > tbody > tr > td.").concat(e," {\n            border-left: none;\n            border-right: none;\n          }"):p+="\n          ".concat(t,".").concat(c," > tbody > tr > td.").concat(e," {\n            border-left: ").concat(d.V,";\n          }"))}var h=yn(null===(x=s.pPr)||void 0===x?void 0:x.cssStyle);h&&(p+="\n    ".concat(t,".").concat(c," > tbody > tr > td.").concat(e," > .").concat(n,"-p {\n       ").concat(h,"\n    }\n    "));var m=yn(null===(y=s.rPr)||void 0===y?void 0:y.cssStyle);return m&&(p+="\n    ".concat(t,".").concat(c," > tbody > tr > td.").concat(e," > .").concat(n,"-p > .").concat(n,"-r {\n       ").concat(m,"\n    }\n    ")),p}var Tn=new Set(["wholeTable","band1Horz","band2Horz","band1Vert","band2Vert","firstCol","firstRow","lastCol","lastRow","neCell","nwCell","seCell","swCell"]);function vn(t,n,e){var a,r;if(!e)return"";var l="",o=".".concat(t," .").concat(n);try{for(var x=(0,s.Ju)(Tn),y=x.next();!y.done;y=x.next()){var p=y.value;p in e&&(l+=un(o,t,p,e[p]))}}catch(t){a={error:t}}finally{try{y&&!y.done&&(r=x.return)&&r.call(x)}finally{if(a)throw a.error}}return l}function An(t,n,e){e&&(e.cssStyle&&(pn(n,e.cssStyle),"justify"===e.cssStyle["text-align"]&&hn(n,"justify")),e.pStyle&&mn(n,t.getStyleClassName(e.pStyle)),e.rStyle&&mn(n,t.getStyleClassName(e.rStyle)))}function bn(t,n,e,s,a,r,l){0===t&&0===n&&a.classList.add("nwCell"),0===t&&n===s-1&&a.classList.add("neCell"),t===e-1&&0===n&&a.classList.add("swCell"),t===e-1&&n===s-1&&a.classList.add("seCell"),0===t&&a.classList.add("firstRow"),t===e-1&&a.classList.add("lastRow"),0===n&&a.classList.add("firstCol"),n===s-1&&a.classList.add("lastCol"),kn(t+1)&&a.classList.add("band1Horz"),kn(t+1)||a.classList.add("band2Horz"),kn(n+1)&&a.classList.add("band1Vert"),kn(n+1)||a.classList.add("band2Vert")}function kn(t,n){return!(t%2)}function Rn(t,n){var e,a,r,l,o,x,y=document.createElement("table"),p=n.properties;if(p.tblCaption){var f=document.createElement("caption");f.textContent=p.tblCaption,y.appendChild(f)}if(p.tblLook)for(var c in p.tblLook)"noHBand"===c?p.tblLook[c]||hn(y,"enable-hBand"):"noVBand"===c?p.tblLook[c]||hn(y,"enable-vBand"):p.tblLook[c]&&hn(y,"enable-"+c);An(t,y,p);var i=t.genClassName();y.classList.add(i),t.appendStyle(gn(t.getClassPrefix(),i,{tblPr:p}));var d=document.createElement("tbody");y.appendChild(d);var h=0;try{for(var m=(0,s.Ju)(n.trs),w=m.next();!w.done;w=m.next()){var g=w.value,u=document.createElement("tr");d.appendChild(u);var T=0;try{for(var v=(r=void 0,(0,s.Ju)(g.tcs)),A=v.next();!A.done;A=v.next()){var b=A.value,k=document.createElement("td");u.appendChild(k),bn(h,T,n.trs.length,g.tcs.length,k,p.rowBandSize,p.colBandSize),g.properties.tcStyle&&pn(k,g.properties.tcStyle);var R=b.properties;An(t,k,R),R.gridSpan&&(k.colSpan=R.gridSpan),R.rowSpan&&(k.rowSpan=R.rowSpan);var L=!0;R.hideMark&&(L=!1);try{for(var C=(o=void 0,(0,s.Ju)(b.children)),j=C.next();!j.done;j=C.next()){var B=j.value;B instanceof sn?dn(k,xe(t,B,L)):B instanceof mt?(L=!1,dn(k,Rn(t,B))):console.warn("unknown child type: "+B)}}catch(t){o={error:t}}finally{try{j&&!j.done&&(x=C.return)&&x.call(C)}finally{if(o)throw o.error}}R.rowSpan?T+=R.rowSpan:T++}}catch(t){r={error:t}}finally{try{A&&!A.done&&(l=v.return)&&l.call(v)}finally{if(r)throw r.error}}h++}}catch(t){e={error:t}}finally{try{w&&!w.done&&(a=m.return)&&a.call(m)}finally{if(e)throw e.error}}return y}var Ln={accentBorderCallout1:{avLst:[{n:"adj1",f:"val 18750"},{n:"adj2",f:"val -8333"},{n:"adj3",f:"val 112500"},{n:"adj4",f:"val -38333"}],gdLst:[{n:"y1",f:"*/ h adj1 100000"},{n:"x1",f:"*/ w adj2 100000"},{n:"y2",f:"*/ h adj3 100000"},{n:"x2",f:"*/ w adj4 100000"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"x1",y:"t"}},{type:"close"},{type:"lnTo",pt:{x:"x1",y:"b"}}],fill:"none",extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"x2",y:"y2"}}],fill:"none",extrusionOk:!1,stroke:!0}]},accentBorderCallout2:{avLst:[{n:"adj1",f:"val 18750"},{n:"adj2",f:"val -8333"},{n:"adj3",f:"val 18750"},{n:"adj4",f:"val -16667"},{n:"adj5",f:"val 112500"},{n:"adj6",f:"val -46667"}],gdLst:[{n:"y1",f:"*/ h adj1 100000"},{n:"x1",f:"*/ w adj2 100000"},{n:"y2",f:"*/ h adj3 100000"},{n:"x2",f:"*/ w adj4 100000"},{n:"y3",f:"*/ h adj5 100000"},{n:"x3",f:"*/ w adj6 100000"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"x1",y:"t"}},{type:"close"},{type:"lnTo",pt:{x:"x1",y:"b"}}],fill:"none",extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x3",y:"y3"}}],fill:"none",extrusionOk:!1,stroke:!0}]},accentBorderCallout3:{avLst:[{n:"adj1",f:"val 18750"},{n:"adj2",f:"val -8333"},{n:"adj3",f:"val 18750"},{n:"adj4",f:"val -16667"},{n:"adj5",f:"val 100000"},{n:"adj6",f:"val -16667"},{n:"adj7",f:"val 112963"},{n:"adj8",f:"val -8333"}],gdLst:[{n:"y1",f:"*/ h adj1 100000"},{n:"x1",f:"*/ w adj2 100000"},{n:"y2",f:"*/ h adj3 100000"},{n:"x2",f:"*/ w adj4 100000"},{n:"y3",f:"*/ h adj5 100000"},{n:"x3",f:"*/ w adj6 100000"},{n:"y4",f:"*/ h adj7 100000"},{n:"x4",f:"*/ w adj8 100000"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"x1",y:"t"}},{type:"close"},{type:"lnTo",pt:{x:"x1",y:"b"}}],fill:"none",extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x3",y:"y3"}},{type:"lnTo",pt:{x:"x4",y:"y4"}}],fill:"none",extrusionOk:!1,stroke:!0}]},accentCallout1:{avLst:[{n:"adj1",f:"val 18750"},{n:"adj2",f:"val -8333"},{n:"adj3",f:"val 112500"},{n:"adj4",f:"val -38333"}],gdLst:[{n:"y1",f:"*/ h adj1 100000"},{n:"x1",f:"*/ w adj2 100000"},{n:"y2",f:"*/ h adj3 100000"},{n:"x2",f:"*/ w adj4 100000"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"x1",y:"t"}},{type:"close"},{type:"lnTo",pt:{x:"x1",y:"b"}}],fill:"none",extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"x2",y:"y2"}}],fill:"none",extrusionOk:!1,stroke:!0}]},accentCallout2:{avLst:[{n:"adj1",f:"val 18750"},{n:"adj2",f:"val -8333"},{n:"adj3",f:"val 18750"},{n:"adj4",f:"val -16667"},{n:"adj5",f:"val 112500"},{n:"adj6",f:"val -46667"}],gdLst:[{n:"y1",f:"*/ h adj1 100000"},{n:"x1",f:"*/ w adj2 100000"},{n:"y2",f:"*/ h adj3 100000"},{n:"x2",f:"*/ w adj4 100000"},{n:"y3",f:"*/ h adj5 100000"},{n:"x3",f:"*/ w adj6 100000"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"x1",y:"t"}},{type:"close"},{type:"lnTo",pt:{x:"x1",y:"b"}}],fill:"none",extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x3",y:"y3"}}],fill:"none",extrusionOk:!1,stroke:!0}]},accentCallout3:{avLst:[{n:"adj1",f:"val 18750"},{n:"adj2",f:"val -8333"},{n:"adj3",f:"val 18750"},{n:"adj4",f:"val -16667"},{n:"adj5",f:"val 100000"},{n:"adj6",f:"val -16667"},{n:"adj7",f:"val 112963"},{n:"adj8",f:"val -8333"}],gdLst:[{n:"y1",f:"*/ h adj1 100000"},{n:"x1",f:"*/ w adj2 100000"},{n:"y2",f:"*/ h adj3 100000"},{n:"x2",f:"*/ w adj4 100000"},{n:"y3",f:"*/ h adj5 100000"},{n:"x3",f:"*/ w adj6 100000"},{n:"y4",f:"*/ h adj7 100000"},{n:"x4",f:"*/ w adj8 100000"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"x1",y:"t"}},{type:"close"},{type:"lnTo",pt:{x:"x1",y:"b"}}],fill:"none",extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x3",y:"y3"}},{type:"lnTo",pt:{x:"x4",y:"y4"}}],fill:"none",extrusionOk:!1,stroke:!0}]},actionButtonBackPrevious:{gdLst:[{n:"dx2",f:"*/ ss 3 8"},{n:"g9",f:"+- vc 0 dx2"},{n:"g10",f:"+- vc dx2 0"},{n:"g11",f:"+- hc 0 dx2"},{n:"g12",f:"+- hc dx2 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"},{type:"moveTo",pt:{x:"g11",y:"vc"}},{type:"lnTo",pt:{x:"g12",y:"g9"}},{type:"lnTo",pt:{x:"g12",y:"g10"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"g11",y:"vc"}},{type:"lnTo",pt:{x:"g12",y:"g9"}},{type:"lnTo",pt:{x:"g12",y:"g10"}},{type:"close"}],fill:"darken",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"g11",y:"vc"}},{type:"lnTo",pt:{x:"g12",y:"g9"}},{type:"lnTo",pt:{x:"g12",y:"g10"}},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0}]},actionButtonBeginning:{gdLst:[{n:"dx2",f:"*/ ss 3 8"},{n:"g9",f:"+- vc 0 dx2"},{n:"g10",f:"+- vc dx2 0"},{n:"g11",f:"+- hc 0 dx2"},{n:"g12",f:"+- hc dx2 0"},{n:"g13",f:"*/ ss 3 4"},{n:"g14",f:"*/ g13 1 8"},{n:"g15",f:"*/ g13 1 4"},{n:"g16",f:"+- g11 g14 0"},{n:"g17",f:"+- g11 g15 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"},{type:"moveTo",pt:{x:"g17",y:"vc"}},{type:"lnTo",pt:{x:"g12",y:"g9"}},{type:"lnTo",pt:{x:"g12",y:"g10"}},{type:"close"},{type:"moveTo",pt:{x:"g16",y:"g9"}},{type:"lnTo",pt:{x:"g11",y:"g9"}},{type:"lnTo",pt:{x:"g11",y:"g10"}},{type:"lnTo",pt:{x:"g16",y:"g10"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"g17",y:"vc"}},{type:"lnTo",pt:{x:"g12",y:"g9"}},{type:"lnTo",pt:{x:"g12",y:"g10"}},{type:"close"},{type:"moveTo",pt:{x:"g16",y:"g9"}},{type:"lnTo",pt:{x:"g11",y:"g9"}},{type:"lnTo",pt:{x:"g11",y:"g10"}},{type:"lnTo",pt:{x:"g16",y:"g10"}},{type:"close"}],fill:"darken",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"g17",y:"vc"}},{type:"lnTo",pt:{x:"g12",y:"g9"}},{type:"lnTo",pt:{x:"g12",y:"g10"}},{type:"close"},{type:"moveTo",pt:{x:"g16",y:"g9"}},{type:"lnTo",pt:{x:"g16",y:"g10"}},{type:"lnTo",pt:{x:"g11",y:"g10"}},{type:"lnTo",pt:{x:"g11",y:"g9"}},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0}]},actionButtonBlank:{pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},actionButtonDocument:{gdLst:[{n:"dx2",f:"*/ ss 3 8"},{n:"g9",f:"+- vc 0 dx2"},{n:"g10",f:"+- vc dx2 0"},{n:"dx1",f:"*/ ss 9 32"},{n:"g11",f:"+- hc 0 dx1"},{n:"g12",f:"+- hc dx1 0"},{n:"g13",f:"*/ ss 3 16"},{n:"g14",f:"+- g12 0 g13"},{n:"g15",f:"+- g9 g13 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"},{type:"moveTo",pt:{x:"g11",y:"g9"}},{type:"lnTo",pt:{x:"g14",y:"g9"}},{type:"lnTo",pt:{x:"g12",y:"g15"}},{type:"lnTo",pt:{x:"g12",y:"g10"}},{type:"lnTo",pt:{x:"g11",y:"g10"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"g11",y:"g9"}},{type:"lnTo",pt:{x:"g14",y:"g9"}},{type:"lnTo",pt:{x:"g14",y:"g15"}},{type:"lnTo",pt:{x:"g12",y:"g15"}},{type:"lnTo",pt:{x:"g12",y:"g10"}},{type:"lnTo",pt:{x:"g11",y:"g10"}},{type:"close"}],fill:"darkenLess",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"g14",y:"g9"}},{type:"lnTo",pt:{x:"g14",y:"g15"}},{type:"lnTo",pt:{x:"g12",y:"g15"}},{type:"close"}],fill:"darken",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"g11",y:"g9"}},{type:"lnTo",pt:{x:"g14",y:"g9"}},{type:"lnTo",pt:{x:"g12",y:"g15"}},{type:"lnTo",pt:{x:"g12",y:"g10"}},{type:"lnTo",pt:{x:"g11",y:"g10"}},{type:"close"},{type:"moveTo",pt:{x:"g12",y:"g15"}},{type:"lnTo",pt:{x:"g14",y:"g15"}},{type:"lnTo",pt:{x:"g14",y:"g9"}}],fill:"none",extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0}]},actionButtonEnd:{gdLst:[{n:"dx2",f:"*/ ss 3 8"},{n:"g9",f:"+- vc 0 dx2"},{n:"g10",f:"+- vc dx2 0"},{n:"g11",f:"+- hc 0 dx2"},{n:"g12",f:"+- hc dx2 0"},{n:"g13",f:"*/ ss 3 4"},{n:"g14",f:"*/ g13 3 4"},{n:"g15",f:"*/ g13 7 8"},{n:"g16",f:"+- g11 g14 0"},{n:"g17",f:"+- g11 g15 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"},{type:"moveTo",pt:{x:"g16",y:"vc"}},{type:"lnTo",pt:{x:"g11",y:"g9"}},{type:"lnTo",pt:{x:"g11",y:"g10"}},{type:"close"},{type:"moveTo",pt:{x:"g17",y:"g9"}},{type:"lnTo",pt:{x:"g12",y:"g9"}},{type:"lnTo",pt:{x:"g12",y:"g10"}},{type:"lnTo",pt:{x:"g17",y:"g10"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"g16",y:"vc"}},{type:"lnTo",pt:{x:"g11",y:"g9"}},{type:"lnTo",pt:{x:"g11",y:"g10"}},{type:"close"},{type:"moveTo",pt:{x:"g17",y:"g9"}},{type:"lnTo",pt:{x:"g12",y:"g9"}},{type:"lnTo",pt:{x:"g12",y:"g10"}},{type:"lnTo",pt:{x:"g17",y:"g10"}},{type:"close"}],fill:"darken",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"g16",y:"vc"}},{type:"lnTo",pt:{x:"g11",y:"g10"}},{type:"lnTo",pt:{x:"g11",y:"g9"}},{type:"close"},{type:"moveTo",pt:{x:"g17",y:"g9"}},{type:"lnTo",pt:{x:"g12",y:"g9"}},{type:"lnTo",pt:{x:"g12",y:"g10"}},{type:"lnTo",pt:{x:"g17",y:"g10"}},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0}]},actionButtonForwardNext:{gdLst:[{n:"dx2",f:"*/ ss 3 8"},{n:"g9",f:"+- vc 0 dx2"},{n:"g10",f:"+- vc dx2 0"},{n:"g11",f:"+- hc 0 dx2"},{n:"g12",f:"+- hc dx2 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"},{type:"moveTo",pt:{x:"g12",y:"vc"}},{type:"lnTo",pt:{x:"g11",y:"g9"}},{type:"lnTo",pt:{x:"g11",y:"g10"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"g12",y:"vc"}},{type:"lnTo",pt:{x:"g11",y:"g9"}},{type:"lnTo",pt:{x:"g11",y:"g10"}},{type:"close"}],fill:"darken",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"g12",y:"vc"}},{type:"lnTo",pt:{x:"g11",y:"g10"}},{type:"lnTo",pt:{x:"g11",y:"g9"}},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0}]},actionButtonHelp:{gdLst:[{n:"dx2",f:"*/ ss 3 8"},{n:"g9",f:"+- vc 0 dx2"},{n:"g11",f:"+- hc 0 dx2"},{n:"g13",f:"*/ ss 3 4"},{n:"g14",f:"*/ g13 1 7"},{n:"g15",f:"*/ g13 3 14"},{n:"g16",f:"*/ g13 2 7"},{n:"g19",f:"*/ g13 3 7"},{n:"g20",f:"*/ g13 4 7"},{n:"g21",f:"*/ g13 17 28"},{n:"g23",f:"*/ g13 21 28"},{n:"g24",f:"*/ g13 11 14"},{n:"g27",f:"+- g9 g16 0"},{n:"g29",f:"+- g9 g21 0"},{n:"g30",f:"+- g9 g23 0"},{n:"g31",f:"+- g9 g24 0"},{n:"g33",f:"+- g11 g15 0"},{n:"g36",f:"+- g11 g19 0"},{n:"g37",f:"+- g11 g20 0"},{n:"g41",f:"*/ g13 1 14"},{n:"g42",f:"*/ g13 3 28"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"},{type:"moveTo",pt:{x:"g33",y:"g27"}},{type:"arcTo",wR:"g16",hR:"g16",stAng:"cd2",swAng:"cd2"},{type:"arcTo",wR:"g14",hR:"g15",stAng:"0",swAng:"cd4"},{type:"arcTo",wR:"g41",hR:"g42",stAng:"3cd4",swAng:"-5400000"},{type:"lnTo",pt:{x:"g37",y:"g30"}},{type:"lnTo",pt:{x:"g36",y:"g30"}},{type:"lnTo",pt:{x:"g36",y:"g29"}},{type:"arcTo",wR:"g14",hR:"g15",stAng:"cd2",swAng:"cd4"},{type:"arcTo",wR:"g41",hR:"g42",stAng:"cd4",swAng:"-5400000"},{type:"arcTo",wR:"g14",hR:"g14",stAng:"0",swAng:"-10800000"},{type:"close"},{type:"moveTo",pt:{x:"hc",y:"g31"}},{type:"arcTo",wR:"g42",hR:"g42",stAng:"3cd4",swAng:"21600000"},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"g33",y:"g27"}},{type:"arcTo",wR:"g16",hR:"g16",stAng:"cd2",swAng:"cd2"},{type:"arcTo",wR:"g14",hR:"g15",stAng:"0",swAng:"cd4"},{type:"arcTo",wR:"g41",hR:"g42",stAng:"3cd4",swAng:"-5400000"},{type:"lnTo",pt:{x:"g37",y:"g30"}},{type:"lnTo",pt:{x:"g36",y:"g30"}},{type:"lnTo",pt:{x:"g36",y:"g29"}},{type:"arcTo",wR:"g14",hR:"g15",stAng:"cd2",swAng:"cd4"},{type:"arcTo",wR:"g41",hR:"g42",stAng:"cd4",swAng:"-5400000"},{type:"arcTo",wR:"g14",hR:"g14",stAng:"0",swAng:"-10800000"},{type:"close"},{type:"moveTo",pt:{x:"hc",y:"g31"}},{type:"arcTo",wR:"g42",hR:"g42",stAng:"3cd4",swAng:"21600000"},{type:"close"}],fill:"darken",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"g33",y:"g27"}},{type:"arcTo",wR:"g16",hR:"g16",stAng:"cd2",swAng:"cd2"},{type:"arcTo",wR:"g14",hR:"g15",stAng:"0",swAng:"cd4"},{type:"arcTo",wR:"g41",hR:"g42",stAng:"3cd4",swAng:"-5400000"},{type:"lnTo",pt:{x:"g37",y:"g30"}},{type:"lnTo",pt:{x:"g36",y:"g30"}},{type:"lnTo",pt:{x:"g36",y:"g29"}},{type:"arcTo",wR:"g14",hR:"g15",stAng:"cd2",swAng:"cd4"},{type:"arcTo",wR:"g41",hR:"g42",stAng:"cd4",swAng:"-5400000"},{type:"arcTo",wR:"g14",hR:"g14",stAng:"0",swAng:"-10800000"},{type:"close"},{type:"moveTo",pt:{x:"hc",y:"g31"}},{type:"arcTo",wR:"g42",hR:"g42",stAng:"3cd4",swAng:"21600000"},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0}]},actionButtonHome:{gdLst:[{n:"dx2",f:"*/ ss 3 8"},{n:"g9",f:"+- vc 0 dx2"},{n:"g10",f:"+- vc dx2 0"},{n:"g11",f:"+- hc 0 dx2"},{n:"g12",f:"+- hc dx2 0"},{n:"g13",f:"*/ ss 3 4"},{n:"g14",f:"*/ g13 1 16"},{n:"g15",f:"*/ g13 1 8"},{n:"g16",f:"*/ g13 3 16"},{n:"g17",f:"*/ g13 5 16"},{n:"g18",f:"*/ g13 7 16"},{n:"g19",f:"*/ g13 9 16"},{n:"g20",f:"*/ g13 11 16"},{n:"g21",f:"*/ g13 3 4"},{n:"g22",f:"*/ g13 13 16"},{n:"g23",f:"*/ g13 7 8"},{n:"g24",f:"+- g9 g14 0"},{n:"g25",f:"+- g9 g16 0"},{n:"g26",f:"+- g9 g17 0"},{n:"g27",f:"+- g9 g21 0"},{n:"g28",f:"+- g11 g15 0"},{n:"g29",f:"+- g11 g18 0"},{n:"g30",f:"+- g11 g19 0"},{n:"g31",f:"+- g11 g20 0"},{n:"g32",f:"+- g11 g22 0"},{n:"g33",f:"+- g11 g23 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"},{type:"moveTo",pt:{x:"hc",y:"g9"}},{type:"lnTo",pt:{x:"g11",y:"vc"}},{type:"lnTo",pt:{x:"g28",y:"vc"}},{type:"lnTo",pt:{x:"g28",y:"g10"}},{type:"lnTo",pt:{x:"g33",y:"g10"}},{type:"lnTo",pt:{x:"g33",y:"vc"}},{type:"lnTo",pt:{x:"g12",y:"vc"}},{type:"lnTo",pt:{x:"g32",y:"g26"}},{type:"lnTo",pt:{x:"g32",y:"g24"}},{type:"lnTo",pt:{x:"g31",y:"g24"}},{type:"lnTo",pt:{x:"g31",y:"g25"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"g32",y:"g26"}},{type:"lnTo",pt:{x:"g32",y:"g24"}},{type:"lnTo",pt:{x:"g31",y:"g24"}},{type:"lnTo",pt:{x:"g31",y:"g25"}},{type:"close"},{type:"moveTo",pt:{x:"g28",y:"vc"}},{type:"lnTo",pt:{x:"g28",y:"g10"}},{type:"lnTo",pt:{x:"g29",y:"g10"}},{type:"lnTo",pt:{x:"g29",y:"g27"}},{type:"lnTo",pt:{x:"g30",y:"g27"}},{type:"lnTo",pt:{x:"g30",y:"g10"}},{type:"lnTo",pt:{x:"g33",y:"g10"}},{type:"lnTo",pt:{x:"g33",y:"vc"}},{type:"close"}],fill:"darkenLess",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"hc",y:"g9"}},{type:"lnTo",pt:{x:"g11",y:"vc"}},{type:"lnTo",pt:{x:"g12",y:"vc"}},{type:"close"},{type:"moveTo",pt:{x:"g29",y:"g27"}},{type:"lnTo",pt:{x:"g30",y:"g27"}},{type:"lnTo",pt:{x:"g30",y:"g10"}},{type:"lnTo",pt:{x:"g29",y:"g10"}},{type:"close"}],fill:"darken",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"hc",y:"g9"}},{type:"lnTo",pt:{x:"g31",y:"g25"}},{type:"lnTo",pt:{x:"g31",y:"g24"}},{type:"lnTo",pt:{x:"g32",y:"g24"}},{type:"lnTo",pt:{x:"g32",y:"g26"}},{type:"lnTo",pt:{x:"g12",y:"vc"}},{type:"lnTo",pt:{x:"g33",y:"vc"}},{type:"lnTo",pt:{x:"g33",y:"g10"}},{type:"lnTo",pt:{x:"g28",y:"g10"}},{type:"lnTo",pt:{x:"g28",y:"vc"}},{type:"lnTo",pt:{x:"g11",y:"vc"}},{type:"close"},{type:"moveTo",pt:{x:"g31",y:"g25"}},{type:"lnTo",pt:{x:"g32",y:"g26"}},{type:"moveTo",pt:{x:"g33",y:"vc"}},{type:"lnTo",pt:{x:"g28",y:"vc"}},{type:"moveTo",pt:{x:"g29",y:"g10"}},{type:"lnTo",pt:{x:"g29",y:"g27"}},{type:"lnTo",pt:{x:"g30",y:"g27"}},{type:"lnTo",pt:{x:"g30",y:"g10"}}],fill:"none",extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0}]},actionButtonInformation:{gdLst:[{n:"dx2",f:"*/ ss 3 8"},{n:"g9",f:"+- vc 0 dx2"},{n:"g11",f:"+- hc 0 dx2"},{n:"g13",f:"*/ ss 3 4"},{n:"g14",f:"*/ g13 1 32"},{n:"g17",f:"*/ g13 5 16"},{n:"g18",f:"*/ g13 3 8"},{n:"g19",f:"*/ g13 13 32"},{n:"g20",f:"*/ g13 19 32"},{n:"g22",f:"*/ g13 11 16"},{n:"g23",f:"*/ g13 13 16"},{n:"g24",f:"*/ g13 7 8"},{n:"g25",f:"+- g9 g14 0"},{n:"g28",f:"+- g9 g17 0"},{n:"g29",f:"+- g9 g18 0"},{n:"g30",f:"+- g9 g23 0"},{n:"g31",f:"+- g9 g24 0"},{n:"g32",f:"+- g11 g17 0"},{n:"g34",f:"+- g11 g19 0"},{n:"g35",f:"+- g11 g20 0"},{n:"g37",f:"+- g11 g22 0"},{n:"g38",f:"*/ g13 3 32"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"},{type:"moveTo",pt:{x:"hc",y:"g9"}},{type:"arcTo",wR:"dx2",hR:"dx2",stAng:"3cd4",swAng:"21600000"},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"hc",y:"g9"}},{type:"arcTo",wR:"dx2",hR:"dx2",stAng:"3cd4",swAng:"21600000"},{type:"close"},{type:"moveTo",pt:{x:"hc",y:"g25"}},{type:"arcTo",wR:"g38",hR:"g38",stAng:"3cd4",swAng:"21600000"},{type:"moveTo",pt:{x:"g32",y:"g28"}},{type:"lnTo",pt:{x:"g32",y:"g29"}},{type:"lnTo",pt:{x:"g34",y:"g29"}},{type:"lnTo",pt:{x:"g34",y:"g30"}},{type:"lnTo",pt:{x:"g32",y:"g30"}},{type:"lnTo",pt:{x:"g32",y:"g31"}},{type:"lnTo",pt:{x:"g37",y:"g31"}},{type:"lnTo",pt:{x:"g37",y:"g30"}},{type:"lnTo",pt:{x:"g35",y:"g30"}},{type:"lnTo",pt:{x:"g35",y:"g28"}},{type:"close"}],fill:"darken",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"hc",y:"g25"}},{type:"arcTo",wR:"g38",hR:"g38",stAng:"3cd4",swAng:"21600000"},{type:"moveTo",pt:{x:"g32",y:"g28"}},{type:"lnTo",pt:{x:"g35",y:"g28"}},{type:"lnTo",pt:{x:"g35",y:"g30"}},{type:"lnTo",pt:{x:"g37",y:"g30"}},{type:"lnTo",pt:{x:"g37",y:"g31"}},{type:"lnTo",pt:{x:"g32",y:"g31"}},{type:"lnTo",pt:{x:"g32",y:"g30"}},{type:"lnTo",pt:{x:"g34",y:"g30"}},{type:"lnTo",pt:{x:"g34",y:"g29"}},{type:"lnTo",pt:{x:"g32",y:"g29"}},{type:"close"}],fill:"lighten",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"hc",y:"g9"}},{type:"arcTo",wR:"dx2",hR:"dx2",stAng:"3cd4",swAng:"21600000"},{type:"close"},{type:"moveTo",pt:{x:"hc",y:"g25"}},{type:"arcTo",wR:"g38",hR:"g38",stAng:"3cd4",swAng:"21600000"},{type:"moveTo",pt:{x:"g32",y:"g28"}},{type:"lnTo",pt:{x:"g35",y:"g28"}},{type:"lnTo",pt:{x:"g35",y:"g30"}},{type:"lnTo",pt:{x:"g37",y:"g30"}},{type:"lnTo",pt:{x:"g37",y:"g31"}},{type:"lnTo",pt:{x:"g32",y:"g31"}},{type:"lnTo",pt:{x:"g32",y:"g30"}},{type:"lnTo",pt:{x:"g34",y:"g30"}},{type:"lnTo",pt:{x:"g34",y:"g29"}},{type:"lnTo",pt:{x:"g32",y:"g29"}},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0}]},actionButtonMovie:{gdLst:[{n:"dx2",f:"*/ ss 3 8"},{n:"g9",f:"+- vc 0 dx2"},{n:"g10",f:"+- vc dx2 0"},{n:"g11",f:"+- hc 0 dx2"},{n:"g12",f:"+- hc dx2 0"},{n:"g13",f:"*/ ss 3 4"},{n:"g14",f:"*/ g13 1455 21600"},{n:"g15",f:"*/ g13 1905 21600"},{n:"g16",f:"*/ g13 2325 21600"},{n:"g17",f:"*/ g13 16155 21600"},{n:"g18",f:"*/ g13 17010 21600"},{n:"g19",f:"*/ g13 19335 21600"},{n:"g20",f:"*/ g13 19725 21600"},{n:"g21",f:"*/ g13 20595 21600"},{n:"g22",f:"*/ g13 5280 21600"},{n:"g23",f:"*/ g13 5730 21600"},{n:"g24",f:"*/ g13 6630 21600"},{n:"g25",f:"*/ g13 7492 21600"},{n:"g26",f:"*/ g13 9067 21600"},{n:"g27",f:"*/ g13 9555 21600"},{n:"g28",f:"*/ g13 13342 21600"},{n:"g29",f:"*/ g13 14580 21600"},{n:"g30",f:"*/ g13 15592 21600"},{n:"g31",f:"+- g11 g14 0"},{n:"g32",f:"+- g11 g15 0"},{n:"g33",f:"+- g11 g16 0"},{n:"g34",f:"+- g11 g17 0"},{n:"g35",f:"+- g11 g18 0"},{n:"g36",f:"+- g11 g19 0"},{n:"g37",f:"+- g11 g20 0"},{n:"g38",f:"+- g11 g21 0"},{n:"g39",f:"+- g9 g22 0"},{n:"g40",f:"+- g9 g23 0"},{n:"g41",f:"+- g9 g24 0"},{n:"g42",f:"+- g9 g25 0"},{n:"g43",f:"+- g9 g26 0"},{n:"g44",f:"+- g9 g27 0"},{n:"g45",f:"+- g9 g28 0"},{n:"g46",f:"+- g9 g29 0"},{n:"g47",f:"+- g9 g30 0"},{n:"g48",f:"+- g9 g31 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"},{type:"moveTo",pt:{x:"g11",y:"g39"}},{type:"lnTo",pt:{x:"g11",y:"g44"}},{type:"lnTo",pt:{x:"g31",y:"g44"}},{type:"lnTo",pt:{x:"g32",y:"g43"}},{type:"lnTo",pt:{x:"g33",y:"g43"}},{type:"lnTo",pt:{x:"g33",y:"g47"}},{type:"lnTo",pt:{x:"g35",y:"g47"}},{type:"lnTo",pt:{x:"g35",y:"g45"}},{type:"lnTo",pt:{x:"g36",y:"g45"}},{type:"lnTo",pt:{x:"g38",y:"g46"}},{type:"lnTo",pt:{x:"g12",y:"g46"}},{type:"lnTo",pt:{x:"g12",y:"g41"}},{type:"lnTo",pt:{x:"g38",y:"g41"}},{type:"lnTo",pt:{x:"g37",y:"g42"}},{type:"lnTo",pt:{x:"g35",y:"g42"}},{type:"lnTo",pt:{x:"g35",y:"g41"}},{type:"lnTo",pt:{x:"g34",y:"g40"}},{type:"lnTo",pt:{x:"g32",y:"g40"}},{type:"lnTo",pt:{x:"g31",y:"g39"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"g11",y:"g39"}},{type:"lnTo",pt:{x:"g11",y:"g44"}},{type:"lnTo",pt:{x:"g31",y:"g44"}},{type:"lnTo",pt:{x:"g32",y:"g43"}},{type:"lnTo",pt:{x:"g33",y:"g43"}},{type:"lnTo",pt:{x:"g33",y:"g47"}},{type:"lnTo",pt:{x:"g35",y:"g47"}},{type:"lnTo",pt:{x:"g35",y:"g45"}},{type:"lnTo",pt:{x:"g36",y:"g45"}},{type:"lnTo",pt:{x:"g38",y:"g46"}},{type:"lnTo",pt:{x:"g12",y:"g46"}},{type:"lnTo",pt:{x:"g12",y:"g41"}},{type:"lnTo",pt:{x:"g38",y:"g41"}},{type:"lnTo",pt:{x:"g37",y:"g42"}},{type:"lnTo",pt:{x:"g35",y:"g42"}},{type:"lnTo",pt:{x:"g35",y:"g41"}},{type:"lnTo",pt:{x:"g34",y:"g40"}},{type:"lnTo",pt:{x:"g32",y:"g40"}},{type:"lnTo",pt:{x:"g31",y:"g39"}},{type:"close"}],fill:"darken",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"g11",y:"g39"}},{type:"lnTo",pt:{x:"g31",y:"g39"}},{type:"lnTo",pt:{x:"g32",y:"g40"}},{type:"lnTo",pt:{x:"g34",y:"g40"}},{type:"lnTo",pt:{x:"g35",y:"g41"}},{type:"lnTo",pt:{x:"g35",y:"g42"}},{type:"lnTo",pt:{x:"g37",y:"g42"}},{type:"lnTo",pt:{x:"g38",y:"g41"}},{type:"lnTo",pt:{x:"g12",y:"g41"}},{type:"lnTo",pt:{x:"g12",y:"g46"}},{type:"lnTo",pt:{x:"g38",y:"g46"}},{type:"lnTo",pt:{x:"g36",y:"g45"}},{type:"lnTo",pt:{x:"g35",y:"g45"}},{type:"lnTo",pt:{x:"g35",y:"g47"}},{type:"lnTo",pt:{x:"g33",y:"g47"}},{type:"lnTo",pt:{x:"g33",y:"g43"}},{type:"lnTo",pt:{x:"g32",y:"g43"}},{type:"lnTo",pt:{x:"g31",y:"g44"}},{type:"lnTo",pt:{x:"g11",y:"g44"}},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0}]},actionButtonReturn:{gdLst:[{n:"dx2",f:"*/ ss 3 8"},{n:"g9",f:"+- vc 0 dx2"},{n:"g10",f:"+- vc dx2 0"},{n:"g11",f:"+- hc 0 dx2"},{n:"g12",f:"+- hc dx2 0"},{n:"g13",f:"*/ ss 3 4"},{n:"g14",f:"*/ g13 7 8"},{n:"g15",f:"*/ g13 3 4"},{n:"g16",f:"*/ g13 5 8"},{n:"g17",f:"*/ g13 3 8"},{n:"g18",f:"*/ g13 1 4"},{n:"g19",f:"+- g9 g15 0"},{n:"g20",f:"+- g9 g16 0"},{n:"g21",f:"+- g9 g18 0"},{n:"g22",f:"+- g11 g14 0"},{n:"g23",f:"+- g11 g15 0"},{n:"g24",f:"+- g11 g16 0"},{n:"g25",f:"+- g11 g17 0"},{n:"g26",f:"+- g11 g18 0"},{n:"g27",f:"*/ g13 1 8"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"},{type:"moveTo",pt:{x:"g12",y:"g21"}},{type:"lnTo",pt:{x:"g23",y:"g9"}},{type:"lnTo",pt:{x:"hc",y:"g21"}},{type:"lnTo",pt:{x:"g24",y:"g21"}},{type:"lnTo",pt:{x:"g24",y:"g20"}},{type:"arcTo",wR:"g27",hR:"g27",stAng:"0",swAng:"cd4"},{type:"lnTo",pt:{x:"g25",y:"g19"}},{type:"arcTo",wR:"g27",hR:"g27",stAng:"cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"g26",y:"g21"}},{type:"lnTo",pt:{x:"g11",y:"g21"}},{type:"lnTo",pt:{x:"g11",y:"g20"}},{type:"arcTo",wR:"g17",hR:"g17",stAng:"cd2",swAng:"-5400000"},{type:"lnTo",pt:{x:"hc",y:"g10"}},{type:"arcTo",wR:"g17",hR:"g17",stAng:"cd4",swAng:"-5400000"},{type:"lnTo",pt:{x:"g22",y:"g21"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"g12",y:"g21"}},{type:"lnTo",pt:{x:"g23",y:"g9"}},{type:"lnTo",pt:{x:"hc",y:"g21"}},{type:"lnTo",pt:{x:"g24",y:"g21"}},{type:"lnTo",pt:{x:"g24",y:"g20"}},{type:"arcTo",wR:"g27",hR:"g27",stAng:"0",swAng:"cd4"},{type:"lnTo",pt:{x:"g25",y:"g19"}},{type:"arcTo",wR:"g27",hR:"g27",stAng:"cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"g26",y:"g21"}},{type:"lnTo",pt:{x:"g11",y:"g21"}},{type:"lnTo",pt:{x:"g11",y:"g20"}},{type:"arcTo",wR:"g17",hR:"g17",stAng:"cd2",swAng:"-5400000"},{type:"lnTo",pt:{x:"hc",y:"g10"}},{type:"arcTo",wR:"g17",hR:"g17",stAng:"cd4",swAng:"-5400000"},{type:"lnTo",pt:{x:"g22",y:"g21"}},{type:"close"}],fill:"darken",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"g12",y:"g21"}},{type:"lnTo",pt:{x:"g22",y:"g21"}},{type:"lnTo",pt:{x:"g22",y:"g20"}},{type:"arcTo",wR:"g17",hR:"g17",stAng:"0",swAng:"cd4"},{type:"lnTo",pt:{x:"g25",y:"g10"}},{type:"arcTo",wR:"g17",hR:"g17",stAng:"cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"g11",y:"g21"}},{type:"lnTo",pt:{x:"g26",y:"g21"}},{type:"lnTo",pt:{x:"g26",y:"g20"}},{type:"arcTo",wR:"g27",hR:"g27",stAng:"cd2",swAng:"-5400000"},{type:"lnTo",pt:{x:"hc",y:"g19"}},{type:"arcTo",wR:"g27",hR:"g27",stAng:"cd4",swAng:"-5400000"},{type:"lnTo",pt:{x:"g24",y:"g21"}},{type:"lnTo",pt:{x:"hc",y:"g21"}},{type:"lnTo",pt:{x:"g23",y:"g9"}},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0}]},actionButtonSound:{gdLst:[{n:"dx2",f:"*/ ss 3 8"},{n:"g9",f:"+- vc 0 dx2"},{n:"g10",f:"+- vc dx2 0"},{n:"g11",f:"+- hc 0 dx2"},{n:"g12",f:"+- hc dx2 0"},{n:"g13",f:"*/ ss 3 4"},{n:"g14",f:"*/ g13 1 8"},{n:"g15",f:"*/ g13 5 16"},{n:"g16",f:"*/ g13 5 8"},{n:"g17",f:"*/ g13 11 16"},{n:"g18",f:"*/ g13 3 4"},{n:"g19",f:"*/ g13 7 8"},{n:"g20",f:"+- g9 g14 0"},{n:"g21",f:"+- g9 g15 0"},{n:"g22",f:"+- g9 g17 0"},{n:"g23",f:"+- g9 g19 0"},{n:"g24",f:"+- g11 g15 0"},{n:"g25",f:"+- g11 g16 0"},{n:"g26",f:"+- g11 g18 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"},{type:"moveTo",pt:{x:"g11",y:"g21"}},{type:"lnTo",pt:{x:"g11",y:"g22"}},{type:"lnTo",pt:{x:"g24",y:"g22"}},{type:"lnTo",pt:{x:"g25",y:"g10"}},{type:"lnTo",pt:{x:"g25",y:"g9"}},{type:"lnTo",pt:{x:"g24",y:"g21"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"g11",y:"g21"}},{type:"lnTo",pt:{x:"g11",y:"g22"}},{type:"lnTo",pt:{x:"g24",y:"g22"}},{type:"lnTo",pt:{x:"g25",y:"g10"}},{type:"lnTo",pt:{x:"g25",y:"g9"}},{type:"lnTo",pt:{x:"g24",y:"g21"}},{type:"close"}],fill:"darken",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"g11",y:"g21"}},{type:"lnTo",pt:{x:"g24",y:"g21"}},{type:"lnTo",pt:{x:"g25",y:"g9"}},{type:"lnTo",pt:{x:"g25",y:"g10"}},{type:"lnTo",pt:{x:"g24",y:"g22"}},{type:"lnTo",pt:{x:"g11",y:"g22"}},{type:"close"},{type:"moveTo",pt:{x:"g26",y:"g21"}},{type:"lnTo",pt:{x:"g12",y:"g20"}},{type:"moveTo",pt:{x:"g26",y:"vc"}},{type:"lnTo",pt:{x:"g12",y:"vc"}},{type:"moveTo",pt:{x:"g26",y:"g22"}},{type:"lnTo",pt:{x:"g12",y:"g23"}}],fill:"none",extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0}]},arc:{avLst:[{n:"adj1",f:"val 16200000"},{n:"adj2",f:"val 0"}],gdLst:[{n:"stAng",f:"pin 0 adj1 21599999"},{n:"enAng",f:"pin 0 adj2 21599999"},{n:"sw11",f:"+- enAng 0 stAng"},{n:"sw12",f:"+- sw11 21600000 0"},{n:"swAng",f:"?: sw11 sw11 sw12"},{n:"wt1",f:"sin wd2 stAng"},{n:"ht1",f:"cos hd2 stAng"},{n:"dx1",f:"cat2 wd2 ht1 wt1"},{n:"dy1",f:"sat2 hd2 ht1 wt1"},{n:"wt2",f:"sin wd2 enAng"},{n:"ht2",f:"cos hd2 enAng"},{n:"dx2",f:"cat2 wd2 ht2 wt2"},{n:"dy2",f:"sat2 hd2 ht2 wt2"},{n:"x1",f:"+- hc dx1 0"},{n:"y1",f:"+- vc dy1 0"},{n:"x2",f:"+- hc dx2 0"},{n:"y2",f:"+- vc dy2 0"},{n:"sw0",f:"+- 21600000 0 stAng"},{n:"da1",f:"+- swAng 0 sw0"},{n:"g1",f:"max x1 x2"},{n:"ir",f:"?: da1 r g1"},{n:"sw1",f:"+- cd4 0 stAng"},{n:"sw2",f:"+- 27000000 0 stAng"},{n:"sw3",f:"?: sw1 sw1 sw2"},{n:"da2",f:"+- swAng 0 sw3"},{n:"g5",f:"max y1 y2"},{n:"ib",f:"?: da2 b g5"},{n:"sw4",f:"+- cd2 0 stAng"},{n:"sw5",f:"+- 32400000 0 stAng"},{n:"sw6",f:"?: sw4 sw4 sw5"},{n:"da3",f:"+- swAng 0 sw6"},{n:"g9",f:"min x1 x2"},{n:"il",f:"?: da3 l g9"},{n:"sw7",f:"+- 3cd4 0 stAng"},{n:"sw8",f:"+- 37800000 0 stAng"},{n:"sw9",f:"?: sw7 sw7 sw8"},{n:"da4",f:"+- swAng 0 sw9"},{n:"g13",f:"min y1 y2"},{n:"it",f:"?: da4 t g13"},{n:"cang1",f:"+- stAng 0 cd4"},{n:"cang2",f:"+- enAng cd4 0"},{n:"cang3",f:"+/ cang1 cang2 2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"x1",y:"y1"}},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"stAng",swAng:"swAng"},{type:"lnTo",pt:{x:"hc",y:"vc"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"x1",y:"y1"}},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"stAng",swAng:"swAng"}],fill:"none",extrusionOk:!1,stroke:!0}]},bentArrow:{avLst:[{n:"adj1",f:"val 25000"},{n:"adj2",f:"val 25000"},{n:"adj3",f:"val 25000"},{n:"adj4",f:"val 43750"}],gdLst:[{n:"a2",f:"pin 0 adj2 50000"},{n:"maxAdj1",f:"*/ a2 2 1"},{n:"a1",f:"pin 0 adj1 maxAdj1"},{n:"a3",f:"pin 0 adj3 50000"},{n:"th",f:"*/ ss a1 100000"},{n:"aw2",f:"*/ ss a2 100000"},{n:"th2",f:"*/ th 1 2"},{n:"dh2",f:"+- aw2 0 th2"},{n:"ah",f:"*/ ss a3 100000"},{n:"bw",f:"+- r 0 ah"},{n:"bh",f:"+- b 0 dh2"},{n:"bs",f:"min bw bh"},{n:"maxAdj4",f:"*/ 100000 bs ss"},{n:"a4",f:"pin 0 adj4 maxAdj4"},{n:"bd",f:"*/ ss a4 100000"},{n:"bd3",f:"+- bd 0 th"},{n:"bd2",f:"max bd3 0"},{n:"x3",f:"+- th bd2 0"},{n:"x4",f:"+- r 0 ah"},{n:"y3",f:"+- dh2 th 0"},{n:"y4",f:"+- y3 dh2 0"},{n:"y5",f:"+- dh2 bd 0"},{n:"y6",f:"+- y3 bd2 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"b"}},{type:"lnTo",pt:{x:"l",y:"y5"}},{type:"arcTo",wR:"bd",hR:"bd",stAng:"cd2",swAng:"cd4"},{type:"lnTo",pt:{x:"x4",y:"dh2"}},{type:"lnTo",pt:{x:"x4",y:"t"}},{type:"lnTo",pt:{x:"r",y:"aw2"}},{type:"lnTo",pt:{x:"x4",y:"y4"}},{type:"lnTo",pt:{x:"x4",y:"y3"}},{type:"lnTo",pt:{x:"x3",y:"y3"}},{type:"arcTo",wR:"bd2",hR:"bd2",stAng:"3cd4",swAng:"-5400000"},{type:"lnTo",pt:{x:"th",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},bentConnector2:{pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}}],fill:"none",extrusionOk:!1,stroke:!0}]},bentConnector3:{avLst:[{n:"adj1",f:"val 50000"}],gdLst:[{n:"x1",f:"*/ w adj1 100000"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"x1",y:"t"}},{type:"lnTo",pt:{x:"x1",y:"b"}},{type:"lnTo",pt:{x:"r",y:"b"}}],fill:"none",extrusionOk:!1,stroke:!0}]},bentConnector4:{avLst:[{n:"adj1",f:"val 50000"},{n:"adj2",f:"val 50000"}],gdLst:[{n:"x1",f:"*/ w adj1 100000"},{n:"x2",f:"+/ x1 r 2"},{n:"y2",f:"*/ h adj2 100000"},{n:"y1",f:"+/ t y2 2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"x1",y:"t"}},{type:"lnTo",pt:{x:"x1",y:"y2"}},{type:"lnTo",pt:{x:"r",y:"y2"}},{type:"lnTo",pt:{x:"r",y:"b"}}],fill:"none",extrusionOk:!1,stroke:!0}]},bentConnector5:{avLst:[{n:"adj1",f:"val 50000"},{n:"adj2",f:"val 50000"},{n:"adj3",f:"val 50000"}],gdLst:[{n:"x1",f:"*/ w adj1 100000"},{n:"x3",f:"*/ w adj3 100000"},{n:"x2",f:"+/ x1 x3 2"},{n:"y2",f:"*/ h adj2 100000"},{n:"y1",f:"+/ t y2 2"},{n:"y3",f:"+/ b y2 2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"x1",y:"t"}},{type:"lnTo",pt:{x:"x1",y:"y2"}},{type:"lnTo",pt:{x:"x3",y:"y2"}},{type:"lnTo",pt:{x:"x3",y:"b"}},{type:"lnTo",pt:{x:"r",y:"b"}}],fill:"none",extrusionOk:!1,stroke:!0}]},bentUpArrow:{avLst:[{n:"adj1",f:"val 25000"},{n:"adj2",f:"val 25000"},{n:"adj3",f:"val 25000"}],gdLst:[{n:"a1",f:"pin 0 adj1 50000"},{n:"a2",f:"pin 0 adj2 50000"},{n:"a3",f:"pin 0 adj3 50000"},{n:"y1",f:"*/ ss a3 100000"},{n:"dx1",f:"*/ ss a2 50000"},{n:"x1",f:"+- r 0 dx1"},{n:"dx3",f:"*/ ss a2 100000"},{n:"x3",f:"+- r 0 dx3"},{n:"dx2",f:"*/ ss a1 200000"},{n:"x2",f:"+- x3 0 dx2"},{n:"x4",f:"+- x3 dx2 0"},{n:"dy2",f:"*/ ss a1 100000"},{n:"y2",f:"+- b 0 dy2"},{n:"x0",f:"*/ x4 1 2"},{n:"y3",f:"+/ y2 b 2"},{n:"y15",f:"+/ y1 b 2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"y1"}},{type:"lnTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"x3",y:"t"}},{type:"lnTo",pt:{x:"r",y:"y1"}},{type:"lnTo",pt:{x:"x4",y:"y1"}},{type:"lnTo",pt:{x:"x4",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},bevel:{avLst:[{n:"adj",f:"val 12500"}],gdLst:[{n:"a",f:"pin 0 adj 50000"},{n:"x1",f:"*/ ss a 100000"},{n:"x2",f:"+- r 0 x1"},{n:"y2",f:"+- b 0 x1"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"x1",y:"x1"}},{type:"lnTo",pt:{x:"x2",y:"x1"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x1",y:"y2"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"x2",y:"x1"}},{type:"lnTo",pt:{x:"x1",y:"x1"}},{type:"close"}],fill:"lightenLess",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"l",y:"b"}},{type:"lnTo",pt:{x:"x1",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"close"}],fill:"darkenLess",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"x1",y:"x1"}},{type:"lnTo",pt:{x:"x1",y:"y2"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],fill:"lighten",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"x1"}},{type:"close"}],fill:"darken",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"},{type:"moveTo",pt:{x:"x1",y:"x1"}},{type:"lnTo",pt:{x:"x2",y:"x1"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x1",y:"y2"}},{type:"close"},{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"x1",y:"x1"}},{type:"moveTo",pt:{x:"l",y:"b"}},{type:"lnTo",pt:{x:"x1",y:"y2"}},{type:"moveTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"x2",y:"x1"}},{type:"moveTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"x2",y:"y2"}}],fill:"none",extrusionOk:!1,stroke:!0}]},blockArc:{avLst:[{n:"adj1",f:"val 10800000"},{n:"adj2",f:"val 0"},{n:"adj3",f:"val 25000"}],gdLst:[{n:"stAng",f:"pin 0 adj1 21599999"},{n:"istAng",f:"pin 0 adj2 21599999"},{n:"a3",f:"pin 0 adj3 50000"},{n:"sw11",f:"+- istAng 0 stAng"},{n:"sw12",f:"+- sw11 21600000 0"},{n:"swAng",f:"?: sw11 sw11 sw12"},{n:"iswAng",f:"+- 0 0 swAng"},{n:"wt1",f:"sin wd2 stAng"},{n:"ht1",f:"cos hd2 stAng"},{n:"wt3",f:"sin wd2 istAng"},{n:"ht3",f:"cos hd2 istAng"},{n:"dx1",f:"cat2 wd2 ht1 wt1"},{n:"dy1",f:"sat2 hd2 ht1 wt1"},{n:"dx3",f:"cat2 wd2 ht3 wt3"},{n:"dy3",f:"sat2 hd2 ht3 wt3"},{n:"x1",f:"+- hc dx1 0"},{n:"y1",f:"+- vc dy1 0"},{n:"x3",f:"+- hc dx3 0"},{n:"y3",f:"+- vc dy3 0"},{n:"dr",f:"*/ ss a3 100000"},{n:"iwd2",f:"+- wd2 0 dr"},{n:"ihd2",f:"+- hd2 0 dr"},{n:"wt2",f:"sin iwd2 istAng"},{n:"ht2",f:"cos ihd2 istAng"},{n:"wt4",f:"sin iwd2 stAng"},{n:"ht4",f:"cos ihd2 stAng"},{n:"dx2",f:"cat2 iwd2 ht2 wt2"},{n:"dy2",f:"sat2 ihd2 ht2 wt2"},{n:"dx4",f:"cat2 iwd2 ht4 wt4"},{n:"dy4",f:"sat2 ihd2 ht4 wt4"},{n:"x2",f:"+- hc dx2 0"},{n:"y2",f:"+- vc dy2 0"},{n:"x4",f:"+- hc dx4 0"},{n:"y4",f:"+- vc dy4 0"},{n:"sw0",f:"+- 21600000 0 stAng"},{n:"da1",f:"+- swAng 0 sw0"},{n:"g1",f:"max x1 x2"},{n:"g2",f:"max x3 x4"},{n:"g3",f:"max g1 g2"},{n:"ir",f:"?: da1 r g3"},{n:"sw1",f:"+- cd4 0 stAng"},{n:"sw2",f:"+- 27000000 0 stAng"},{n:"sw3",f:"?: sw1 sw1 sw2"},{n:"da2",f:"+- swAng 0 sw3"},{n:"g5",f:"max y1 y2"},{n:"g6",f:"max y3 y4"},{n:"g7",f:"max g5 g6"},{n:"ib",f:"?: da2 b g7"},{n:"sw4",f:"+- cd2 0 stAng"},{n:"sw5",f:"+- 32400000 0 stAng"},{n:"sw6",f:"?: sw4 sw4 sw5"},{n:"da3",f:"+- swAng 0 sw6"},{n:"g9",f:"min x1 x2"},{n:"g10",f:"min x3 x4"},{n:"g11",f:"min g9 g10"},{n:"il",f:"?: da3 l g11"},{n:"sw7",f:"+- 3cd4 0 stAng"},{n:"sw8",f:"+- 37800000 0 stAng"},{n:"sw9",f:"?: sw7 sw7 sw8"},{n:"da4",f:"+- swAng 0 sw9"},{n:"g13",f:"min y1 y2"},{n:"g14",f:"min y3 y4"},{n:"g15",f:"min g13 g14"},{n:"it",f:"?: da4 t g15"},{n:"x5",f:"+/ x1 x4 2"},{n:"y5",f:"+/ y1 y4 2"},{n:"x6",f:"+/ x3 x2 2"},{n:"y6",f:"+/ y3 y2 2"},{n:"cang1",f:"+- stAng 0 cd4"},{n:"cang2",f:"+- istAng cd4 0"},{n:"cang3",f:"+/ cang1 cang2 2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"x1",y:"y1"}},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"stAng",swAng:"swAng"},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"arcTo",wR:"iwd2",hR:"ihd2",stAng:"istAng",swAng:"iswAng"},{type:"close"}],extrusionOk:!1,stroke:!0}]},borderCallout1:{avLst:[{n:"adj1",f:"val 18750"},{n:"adj2",f:"val -8333"},{n:"adj3",f:"val 112500"},{n:"adj4",f:"val -38333"}],gdLst:[{n:"y1",f:"*/ h adj1 100000"},{n:"x1",f:"*/ w adj2 100000"},{n:"y2",f:"*/ h adj3 100000"},{n:"x2",f:"*/ w adj4 100000"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"x2",y:"y2"}}],fill:"none",extrusionOk:!1,stroke:!0}]},borderCallout2:{avLst:[{n:"adj1",f:"val 18750"},{n:"adj2",f:"val -8333"},{n:"adj3",f:"val 18750"},{n:"adj4",f:"val -16667"},{n:"adj5",f:"val 112500"},{n:"adj6",f:"val -46667"}],gdLst:[{n:"y1",f:"*/ h adj1 100000"},{n:"x1",f:"*/ w adj2 100000"},{n:"y2",f:"*/ h adj3 100000"},{n:"x2",f:"*/ w adj4 100000"},{n:"y3",f:"*/ h adj5 100000"},{n:"x3",f:"*/ w adj6 100000"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x3",y:"y3"}}],fill:"none",extrusionOk:!1,stroke:!0}]},borderCallout3:{avLst:[{n:"adj1",f:"val 18750"},{n:"adj2",f:"val -8333"},{n:"adj3",f:"val 18750"},{n:"adj4",f:"val -16667"},{n:"adj5",f:"val 100000"},{n:"adj6",f:"val -16667"},{n:"adj7",f:"val 112963"},{n:"adj8",f:"val -8333"}],gdLst:[{n:"y1",f:"*/ h adj1 100000"},{n:"x1",f:"*/ w adj2 100000"},{n:"y2",f:"*/ h adj3 100000"},{n:"x2",f:"*/ w adj4 100000"},{n:"y3",f:"*/ h adj5 100000"},{n:"x3",f:"*/ w adj6 100000"},{n:"y4",f:"*/ h adj7 100000"},{n:"x4",f:"*/ w adj8 100000"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x3",y:"y3"}},{type:"lnTo",pt:{x:"x4",y:"y4"}}],fill:"none",extrusionOk:!1,stroke:!0}]},bracePair:{avLst:[{n:"adj",f:"val 8333"}],gdLst:[{n:"a",f:"pin 0 adj 25000"},{n:"x1",f:"*/ ss a 100000"},{n:"x2",f:"*/ ss a 50000"},{n:"x3",f:"+- r 0 x2"},{n:"x4",f:"+- r 0 x1"},{n:"y2",f:"+- vc 0 x1"},{n:"y3",f:"+- vc x1 0"},{n:"y4",f:"+- b 0 x1"},{n:"it",f:"*/ x1 29289 100000"},{n:"il",f:"+- x1 it 0"},{n:"ir",f:"+- r 0 il"},{n:"ib",f:"+- b 0 it"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"x2",y:"b"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"x1",y:"y3"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"0",swAng:"-5400000"},{type:"arcTo",wR:"x1",hR:"x1",stAng:"cd4",swAng:"-5400000"},{type:"lnTo",pt:{x:"x1",y:"x1"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"cd2",swAng:"cd4"},{type:"lnTo",pt:{x:"x3",y:"t"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"3cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"x4",y:"y2"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"cd2",swAng:"-5400000"},{type:"arcTo",wR:"x1",hR:"x1",stAng:"3cd4",swAng:"-5400000"},{type:"lnTo",pt:{x:"x4",y:"y4"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"0",swAng:"cd4"},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"x2",y:"b"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"x1",y:"y3"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"0",swAng:"-5400000"},{type:"arcTo",wR:"x1",hR:"x1",stAng:"cd4",swAng:"-5400000"},{type:"lnTo",pt:{x:"x1",y:"x1"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"cd2",swAng:"cd4"},{type:"moveTo",pt:{x:"x3",y:"t"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"3cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"x4",y:"y2"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"cd2",swAng:"-5400000"},{type:"arcTo",wR:"x1",hR:"x1",stAng:"3cd4",swAng:"-5400000"},{type:"lnTo",pt:{x:"x4",y:"y4"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"0",swAng:"cd4"}],fill:"none",extrusionOk:!1,stroke:!0}]},bracketPair:{avLst:[{n:"adj",f:"val 16667"}],gdLst:[{n:"a",f:"pin 0 adj 50000"},{n:"x1",f:"*/ ss a 100000"},{n:"x2",f:"+- r 0 x1"},{n:"y2",f:"+- b 0 x1"},{n:"il",f:"*/ x1 29289 100000"},{n:"ir",f:"+- r 0 il"},{n:"ib",f:"+- b 0 il"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"x1"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"cd2",swAng:"cd4"},{type:"lnTo",pt:{x:"x2",y:"t"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"3cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"r",y:"y2"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"0",swAng:"cd4"},{type:"lnTo",pt:{x:"x1",y:"b"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"cd4",swAng:"cd4"},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"x1",y:"b"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"l",y:"x1"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"cd2",swAng:"cd4"},{type:"moveTo",pt:{x:"x2",y:"t"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"3cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"r",y:"y2"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"0",swAng:"cd4"}],fill:"none",extrusionOk:!1,stroke:!0}]},callout1:{avLst:[{n:"adj1",f:"val 18750"},{n:"adj2",f:"val -8333"},{n:"adj3",f:"val 112500"},{n:"adj4",f:"val -38333"}],gdLst:[{n:"y1",f:"*/ h adj1 100000"},{n:"x1",f:"*/ w adj2 100000"},{n:"y2",f:"*/ h adj3 100000"},{n:"x2",f:"*/ w adj4 100000"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"x2",y:"y2"}}],fill:"none",extrusionOk:!1,stroke:!0}]},callout2:{avLst:[{n:"adj1",f:"val 18750"},{n:"adj2",f:"val -8333"},{n:"adj3",f:"val 18750"},{n:"adj4",f:"val -16667"},{n:"adj5",f:"val 112500"},{n:"adj6",f:"val -46667"}],gdLst:[{n:"y1",f:"*/ h adj1 100000"},{n:"x1",f:"*/ w adj2 100000"},{n:"y2",f:"*/ h adj3 100000"},{n:"x2",f:"*/ w adj4 100000"},{n:"y3",f:"*/ h adj5 100000"},{n:"x3",f:"*/ w adj6 100000"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x3",y:"y3"}}],fill:"none",extrusionOk:!1,stroke:!0}]},callout3:{avLst:[{n:"adj1",f:"val 18750"},{n:"adj2",f:"val -8333"},{n:"adj3",f:"val 18750"},{n:"adj4",f:"val -16667"},{n:"adj5",f:"val 100000"},{n:"adj6",f:"val -16667"},{n:"adj7",f:"val 112963"},{n:"adj8",f:"val -8333"}],gdLst:[{n:"y1",f:"*/ h adj1 100000"},{n:"x1",f:"*/ w adj2 100000"},{n:"y2",f:"*/ h adj3 100000"},{n:"x2",f:"*/ w adj4 100000"},{n:"y3",f:"*/ h adj5 100000"},{n:"x3",f:"*/ w adj6 100000"},{n:"y4",f:"*/ h adj7 100000"},{n:"x4",f:"*/ w adj8 100000"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x3",y:"y3"}},{type:"lnTo",pt:{x:"x4",y:"y4"}}],fill:"none",extrusionOk:!1,stroke:!0}]},can:{avLst:[{n:"adj",f:"val 25000"}],gdLst:[{n:"maxAdj",f:"*/ 50000 h ss"},{n:"a",f:"pin 0 adj maxAdj"},{n:"y1",f:"*/ ss a 200000"},{n:"y2",f:"+- y1 y1 0"},{n:"y3",f:"+- b 0 y1"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"y1"}},{type:"arcTo",wR:"wd2",hR:"y1",stAng:"cd2",swAng:"-10800000"},{type:"lnTo",pt:{x:"r",y:"y3"}},{type:"arcTo",wR:"wd2",hR:"y1",stAng:"0",swAng:"cd2"},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"l",y:"y1"}},{type:"arcTo",wR:"wd2",hR:"y1",stAng:"cd2",swAng:"cd2"},{type:"arcTo",wR:"wd2",hR:"y1",stAng:"0",swAng:"cd2"},{type:"close"}],fill:"lighten",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"r",y:"y1"}},{type:"arcTo",wR:"wd2",hR:"y1",stAng:"0",swAng:"cd2"},{type:"arcTo",wR:"wd2",hR:"y1",stAng:"cd2",swAng:"cd2"},{type:"lnTo",pt:{x:"r",y:"y3"}},{type:"arcTo",wR:"wd2",hR:"y1",stAng:"0",swAng:"cd2"},{type:"lnTo",pt:{x:"l",y:"y1"}}],fill:"none",extrusionOk:!1,stroke:!0}]},chartPlus:{pathLst:[{defines:[{type:"moveTo",pt:{x:"5",y:"0"}},{type:"lnTo",pt:{x:"5",y:"10"}},{type:"moveTo",pt:{x:"0",y:"5"}},{type:"lnTo",pt:{x:"10",y:"5"}}],fill:"none",extrusionOk:!1,stroke:!0,w:10,h:10},{defines:[{type:"moveTo",pt:{x:"0",y:"0"}},{type:"lnTo",pt:{x:"0",y:"10"}},{type:"lnTo",pt:{x:"10",y:"10"}},{type:"lnTo",pt:{x:"10",y:"0"}},{type:"close"}],extrusionOk:!1,stroke:!1,w:10,h:10}]},chartStar:{pathLst:[{defines:[{type:"moveTo",pt:{x:"0",y:"0"}},{type:"lnTo",pt:{x:"10",y:"10"}},{type:"moveTo",pt:{x:"0",y:"10"}},{type:"lnTo",pt:{x:"10",y:"0"}},{type:"moveTo",pt:{x:"5",y:"0"}},{type:"lnTo",pt:{x:"5",y:"10"}}],fill:"none",extrusionOk:!1,stroke:!0,w:10,h:10},{defines:[{type:"moveTo",pt:{x:"0",y:"0"}},{type:"lnTo",pt:{x:"0",y:"10"}},{type:"lnTo",pt:{x:"10",y:"10"}},{type:"lnTo",pt:{x:"10",y:"0"}},{type:"close"}],extrusionOk:!1,stroke:!1,w:10,h:10}]},chartX:{pathLst:[{defines:[{type:"moveTo",pt:{x:"0",y:"0"}},{type:"lnTo",pt:{x:"10",y:"10"}},{type:"moveTo",pt:{x:"0",y:"10"}},{type:"lnTo",pt:{x:"10",y:"0"}}],fill:"none",extrusionOk:!1,stroke:!0,w:10,h:10},{defines:[{type:"moveTo",pt:{x:"0",y:"0"}},{type:"lnTo",pt:{x:"0",y:"10"}},{type:"lnTo",pt:{x:"10",y:"10"}},{type:"lnTo",pt:{x:"10",y:"0"}},{type:"close"}],extrusionOk:!1,stroke:!1,w:10,h:10}]},chevron:{avLst:[{n:"adj",f:"val 50000"}],gdLst:[{n:"maxAdj",f:"*/ 100000 w ss"},{n:"a",f:"pin 0 adj maxAdj"},{n:"x1",f:"*/ ss a 100000"},{n:"x2",f:"+- r 0 x1"},{n:"x3",f:"*/ x2 1 2"},{n:"dx",f:"+- x2 0 x1"},{n:"il",f:"?: dx x1 l"},{n:"ir",f:"?: dx x2 r"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"x2",y:"t"}},{type:"lnTo",pt:{x:"r",y:"vc"}},{type:"lnTo",pt:{x:"x2",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"lnTo",pt:{x:"x1",y:"vc"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},chord:{avLst:[{n:"adj1",f:"val 2700000"},{n:"adj2",f:"val 16200000"}],gdLst:[{n:"stAng",f:"pin 0 adj1 21599999"},{n:"enAng",f:"pin 0 adj2 21599999"},{n:"sw1",f:"+- enAng 0 stAng"},{n:"sw2",f:"+- sw1 21600000 0"},{n:"swAng",f:"?: sw1 sw1 sw2"},{n:"wt1",f:"sin wd2 stAng"},{n:"ht1",f:"cos hd2 stAng"},{n:"dx1",f:"cat2 wd2 ht1 wt1"},{n:"dy1",f:"sat2 hd2 ht1 wt1"},{n:"wt2",f:"sin wd2 enAng"},{n:"ht2",f:"cos hd2 enAng"},{n:"dx2",f:"cat2 wd2 ht2 wt2"},{n:"dy2",f:"sat2 hd2 ht2 wt2"},{n:"x1",f:"+- hc dx1 0"},{n:"y1",f:"+- vc dy1 0"},{n:"x2",f:"+- hc dx2 0"},{n:"y2",f:"+- vc dy2 0"},{n:"x3",f:"+/ x1 x2 2"},{n:"y3",f:"+/ y1 y2 2"},{n:"midAng0",f:"*/ swAng 1 2"},{n:"midAng",f:"+- stAng midAng0 cd2"},{n:"idx",f:"cos wd2 2700000"},{n:"idy",f:"sin hd2 2700000"},{n:"il",f:"+- hc 0 idx"},{n:"ir",f:"+- hc idx 0"},{n:"it",f:"+- vc 0 idy"},{n:"ib",f:"+- vc idy 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"x1",y:"y1"}},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"stAng",swAng:"swAng"},{type:"close"}],extrusionOk:!1,stroke:!0}]},circularArrow:{avLst:[{n:"adj1",f:"val 12500"},{n:"adj2",f:"val 1142319"},{n:"adj3",f:"val 20457681"},{n:"adj4",f:"val 10800000"},{n:"adj5",f:"val 12500"}],gdLst:[{n:"a5",f:"pin 0 adj5 25000"},{n:"maxAdj1",f:"*/ a5 2 1"},{n:"a1",f:"pin 0 adj1 maxAdj1"},{n:"enAng",f:"pin 1 adj3 21599999"},{n:"stAng",f:"pin 0 adj4 21599999"},{n:"th",f:"*/ ss a1 100000"},{n:"thh",f:"*/ ss a5 100000"},{n:"th2",f:"*/ th 1 2"},{n:"rw1",f:"+- wd2 th2 thh"},{n:"rh1",f:"+- hd2 th2 thh"},{n:"rw2",f:"+- rw1 0 th"},{n:"rh2",f:"+- rh1 0 th"},{n:"rw3",f:"+- rw2 th2 0"},{n:"rh3",f:"+- rh2 th2 0"},{n:"wtH",f:"sin rw3 enAng"},{n:"htH",f:"cos rh3 enAng"},{n:"dxH",f:"cat2 rw3 htH wtH"},{n:"dyH",f:"sat2 rh3 htH wtH"},{n:"xH",f:"+- hc dxH 0"},{n:"yH",f:"+- vc dyH 0"},{n:"rI",f:"min rw2 rh2"},{n:"u1",f:"*/ dxH dxH 1"},{n:"u2",f:"*/ dyH dyH 1"},{n:"u3",f:"*/ rI rI 1"},{n:"u4",f:"+- u1 0 u3"},{n:"u5",f:"+- u2 0 u3"},{n:"u6",f:"*/ u4 u5 u1"},{n:"u7",f:"*/ u6 1 u2"},{n:"u8",f:"+- 1 0 u7"},{n:"u9",f:"sqrt u8"},{n:"u10",f:"*/ u4 1 dxH"},{n:"u11",f:"*/ u10 1 dyH"},{n:"u12",f:"+/ 1 u9 u11"},{n:"u13",f:"at2 1 u12"},{n:"u14",f:"+- u13 21600000 0"},{n:"u15",f:"?: u13 u13 u14"},{n:"u16",f:"+- u15 0 enAng"},{n:"u17",f:"+- u16 21600000 0"},{n:"u18",f:"?: u16 u16 u17"},{n:"u19",f:"+- u18 0 cd2"},{n:"u20",f:"+- u18 0 21600000"},{n:"u21",f:"?: u19 u20 u18"},{n:"maxAng",f:"abs u21"},{n:"aAng",f:"pin 0 adj2 maxAng"},{n:"ptAng",f:"+- enAng aAng 0"},{n:"wtA",f:"sin rw3 ptAng"},{n:"htA",f:"cos rh3 ptAng"},{n:"dxA",f:"cat2 rw3 htA wtA"},{n:"dyA",f:"sat2 rh3 htA wtA"},{n:"xA",f:"+- hc dxA 0"},{n:"yA",f:"+- vc dyA 0"},{n:"wtE",f:"sin rw1 stAng"},{n:"htE",f:"cos rh1 stAng"},{n:"dxE",f:"cat2 rw1 htE wtE"},{n:"dyE",f:"sat2 rh1 htE wtE"},{n:"xE",f:"+- hc dxE 0"},{n:"yE",f:"+- vc dyE 0"},{n:"dxG",f:"cos thh ptAng"},{n:"dyG",f:"sin thh ptAng"},{n:"xG",f:"+- xH dxG 0"},{n:"yG",f:"+- yH dyG 0"},{n:"dxB",f:"cos thh ptAng"},{n:"dyB",f:"sin thh ptAng"},{n:"xB",f:"+- xH 0 dxB 0"},{n:"yB",f:"+- yH 0 dyB 0"},{n:"sx1",f:"+- xB 0 hc"},{n:"sy1",f:"+- yB 0 vc"},{n:"sx2",f:"+- xG 0 hc"},{n:"sy2",f:"+- yG 0 vc"},{n:"rO",f:"min rw1 rh1"},{n:"x1O",f:"*/ sx1 rO rw1"},{n:"y1O",f:"*/ sy1 rO rh1"},{n:"x2O",f:"*/ sx2 rO rw1"},{n:"y2O",f:"*/ sy2 rO rh1"},{n:"dxO",f:"+- x2O 0 x1O"},{n:"dyO",f:"+- y2O 0 y1O"},{n:"dO",f:"mod dxO dyO 0"},{n:"q1",f:"*/ x1O y2O 1"},{n:"q2",f:"*/ x2O y1O 1"},{n:"DO",f:"+- q1 0 q2"},{n:"q3",f:"*/ rO rO 1"},{n:"q4",f:"*/ dO dO 1"},{n:"q5",f:"*/ q3 q4 1"},{n:"q6",f:"*/ DO DO 1"},{n:"q7",f:"+- q5 0 q6"},{n:"q8",f:"max q7 0"},{n:"sdelO",f:"sqrt q8"},{n:"ndyO",f:"*/ dyO -1 1"},{n:"sdyO",f:"?: ndyO -1 1"},{n:"q9",f:"*/ sdyO dxO 1"},{n:"q10",f:"*/ q9 sdelO 1"},{n:"q11",f:"*/ DO dyO 1"},{n:"dxF1",f:"+/ q11 q10 q4"},{n:"q12",f:"+- q11 0 q10"},{n:"dxF2",f:"*/ q12 1 q4"},{n:"adyO",f:"abs dyO"},{n:"q13",f:"*/ adyO sdelO 1"},{n:"q14",f:"*/ DO dxO -1"},{n:"dyF1",f:"+/ q14 q13 q4"},{n:"q15",f:"+- q14 0 q13"},{n:"dyF2",f:"*/ q15 1 q4"},{n:"q16",f:"+- x2O 0 dxF1"},{n:"q17",f:"+- x2O 0 dxF2"},{n:"q18",f:"+- y2O 0 dyF1"},{n:"q19",f:"+- y2O 0 dyF2"},{n:"q20",f:"mod q16 q18 0"},{n:"q21",f:"mod q17 q19 0"},{n:"q22",f:"+- q21 0 q20"},{n:"dxF",f:"?: q22 dxF1 dxF2"},{n:"dyF",f:"?: q22 dyF1 dyF2"},{n:"sdxF",f:"*/ dxF rw1 rO"},{n:"sdyF",f:"*/ dyF rh1 rO"},{n:"xF",f:"+- hc sdxF 0"},{n:"yF",f:"+- vc sdyF 0"},{n:"x1I",f:"*/ sx1 rI rw2"},{n:"y1I",f:"*/ sy1 rI rh2"},{n:"x2I",f:"*/ sx2 rI rw2"},{n:"y2I",f:"*/ sy2 rI rh2"},{n:"dxI",f:"+- x2I 0 x1I"},{n:"dyI",f:"+- y2I 0 y1I"},{n:"dI",f:"mod dxI dyI 0"},{n:"v1",f:"*/ x1I y2I 1"},{n:"v2",f:"*/ x2I y1I 1"},{n:"DI",f:"+- v1 0 v2"},{n:"v3",f:"*/ rI rI 1"},{n:"v4",f:"*/ dI dI 1"},{n:"v5",f:"*/ v3 v4 1"},{n:"v6",f:"*/ DI DI 1"},{n:"v7",f:"+- v5 0 v6"},{n:"v8",f:"max v7 0"},{n:"sdelI",f:"sqrt v8"},{n:"v9",f:"*/ sdyO dxI 1"},{n:"v10",f:"*/ v9 sdelI 1"},{n:"v11",f:"*/ DI dyI 1"},{n:"dxC1",f:"+/ v11 v10 v4"},{n:"v12",f:"+- v11 0 v10"},{n:"dxC2",f:"*/ v12 1 v4"},{n:"adyI",f:"abs dyI"},{n:"v13",f:"*/ adyI sdelI 1"},{n:"v14",f:"*/ DI dxI -1"},{n:"dyC1",f:"+/ v14 v13 v4"},{n:"v15",f:"+- v14 0 v13"},{n:"dyC2",f:"*/ v15 1 v4"},{n:"v16",f:"+- x1I 0 dxC1"},{n:"v17",f:"+- x1I 0 dxC2"},{n:"v18",f:"+- y1I 0 dyC1"},{n:"v19",f:"+- y1I 0 dyC2"},{n:"v20",f:"mod v16 v18 0"},{n:"v21",f:"mod v17 v19 0"},{n:"v22",f:"+- v21 0 v20"},{n:"dxC",f:"?: v22 dxC1 dxC2"},{n:"dyC",f:"?: v22 dyC1 dyC2"},{n:"sdxC",f:"*/ dxC rw2 rI"},{n:"sdyC",f:"*/ dyC rh2 rI"},{n:"xC",f:"+- hc sdxC 0"},{n:"yC",f:"+- vc sdyC 0"},{n:"ist0",f:"at2 sdxC sdyC"},{n:"ist1",f:"+- ist0 21600000 0"},{n:"istAng",f:"?: ist0 ist0 ist1"},{n:"isw1",f:"+- stAng 0 istAng"},{n:"isw2",f:"+- isw1 0 21600000"},{n:"iswAng",f:"?: isw1 isw2 isw1"},{n:"p1",f:"+- xF 0 xC"},{n:"p2",f:"+- yF 0 yC"},{n:"p3",f:"mod p1 p2 0"},{n:"p4",f:"*/ p3 1 2"},{n:"p5",f:"+- p4 0 thh"},{n:"xGp",f:"?: p5 xF xG"},{n:"yGp",f:"?: p5 yF yG"},{n:"xBp",f:"?: p5 xC xB"},{n:"yBp",f:"?: p5 yC yB"},{n:"en0",f:"at2 sdxF sdyF"},{n:"en1",f:"+- en0 21600000 0"},{n:"en2",f:"?: en0 en0 en1"},{n:"sw0",f:"+- en2 0 stAng"},{n:"sw1",f:"+- sw0 21600000 0"},{n:"swAng",f:"?: sw0 sw0 sw1"},{n:"wtI",f:"sin rw3 stAng"},{n:"htI",f:"cos rh3 stAng"},{n:"dxI",f:"cat2 rw3 htI wtI"},{n:"dyI",f:"sat2 rh3 htI wtI"},{n:"xI",f:"+- hc dxI 0"},{n:"yI",f:"+- vc dyI 0"},{n:"aI",f:"+- stAng 0 cd4"},{n:"aA",f:"+- ptAng cd4 0"},{n:"aB",f:"+- ptAng cd2 0"},{n:"idx",f:"cos rw1 2700000"},{n:"idy",f:"sin rh1 2700000"},{n:"il",f:"+- hc 0 idx"},{n:"ir",f:"+- hc idx 0"},{n:"it",f:"+- vc 0 idy"},{n:"ib",f:"+- vc idy 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"xE",y:"yE"}},{type:"arcTo",wR:"rw1",hR:"rh1",stAng:"stAng",swAng:"swAng"},{type:"lnTo",pt:{x:"xGp",y:"yGp"}},{type:"lnTo",pt:{x:"xA",y:"yA"}},{type:"lnTo",pt:{x:"xBp",y:"yBp"}},{type:"lnTo",pt:{x:"xC",y:"yC"}},{type:"arcTo",wR:"rw2",hR:"rh2",stAng:"istAng",swAng:"iswAng"},{type:"close"}],extrusionOk:!1,stroke:!0}]},cloud:{gdLst:[{n:"il",f:"*/ w 2977 21600"},{n:"it",f:"*/ h 3262 21600"},{n:"ir",f:"*/ w 17087 21600"},{n:"ib",f:"*/ h 17337 21600"},{n:"g27",f:"*/ w 67 21600"},{n:"g28",f:"*/ h 21577 21600"},{n:"g29",f:"*/ w 21582 21600"},{n:"g30",f:"*/ h 1235 21600"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"3900",y:"14370"}},{type:"arcTo",wR:"6753",hR:"9190",stAng:"-11429249",swAng:"7426832"},{type:"arcTo",wR:"5333",hR:"7267",stAng:"-8646143",swAng:"5396714"},{type:"arcTo",wR:"4365",hR:"5945",stAng:"-8748475",swAng:"5983381"},{type:"arcTo",wR:"4857",hR:"6595",stAng:"-7859164",swAng:"7034504"},{type:"arcTo",wR:"5333",hR:"7273",stAng:"-4722533",swAng:"6541615"},{type:"arcTo",wR:"6775",hR:"9220",stAng:"-2776035",swAng:"7816140"},{type:"arcTo",wR:"5785",hR:"7867",stAng:"37501",swAng:"6842000"},{type:"arcTo",wR:"6752",hR:"9215",stAng:"1347096",swAng:"6910353"},{type:"arcTo",wR:"7720",hR:"10543",stAng:"3974558",swAng:"4542661"},{type:"arcTo",wR:"4360",hR:"5918",stAng:"-16496525",swAng:"8804134"},{type:"arcTo",wR:"4345",hR:"5945",stAng:"-14809710",swAng:"9151131"},{type:"close"}],extrusionOk:!1,stroke:!0,w:43200,h:43200},{defines:[{type:"moveTo",pt:{x:"4693",y:"26177"}},{type:"arcTo",wR:"4345",hR:"5945",stAng:"5204520",swAng:"1585770"},{type:"moveTo",pt:{x:"6928",y:"34899"}},{type:"arcTo",wR:"4360",hR:"5918",stAng:"4416628",swAng:"686848"},{type:"moveTo",pt:{x:"16478",y:"39090"}},{type:"arcTo",wR:"6752",hR:"9215",stAng:"8257449",swAng:"844866"},{type:"moveTo",pt:{x:"28827",y:"34751"}},{type:"arcTo",wR:"6752",hR:"9215",stAng:"387196",swAng:"959901"},{type:"moveTo",pt:{x:"34129",y:"22954"}},{type:"arcTo",wR:"5785",hR:"7867",stAng:"-4217541",swAng:"4255042"},{type:"moveTo",pt:{x:"41798",y:"15354"}},{type:"arcTo",wR:"5333",hR:"7273",stAng:"1819082",swAng:"1665090"},{type:"moveTo",pt:{x:"38324",y:"5426"}},{type:"arcTo",wR:"4857",hR:"6595",stAng:"-824660",swAng:"891534"},{type:"moveTo",pt:{x:"29078",y:"3952"}},{type:"arcTo",wR:"4857",hR:"6595",stAng:"-8950887",swAng:"1091722"},{type:"moveTo",pt:{x:"22141",y:"4720"}},{type:"arcTo",wR:"4365",hR:"5945",stAng:"-9809656",swAng:"1061181"},{type:"moveTo",pt:{x:"14000",y:"5192"}},{type:"arcTo",wR:"6753",hR:"9190",stAng:"-4002417",swAng:"739161"},{type:"moveTo",pt:{x:"4127",y:"15789"}},{type:"arcTo",wR:"6753",hR:"9190",stAng:"9459261",swAng:"711490"}],fill:"none",extrusionOk:!1,stroke:!0,w:43200,h:43200}]},cloudCallout:{avLst:[{n:"adj1",f:"val -20833"},{n:"adj2",f:"val 62500"}],gdLst:[{n:"dxPos",f:"*/ w adj1 100000"},{n:"dyPos",f:"*/ h adj2 100000"},{n:"xPos",f:"+- hc dxPos 0"},{n:"yPos",f:"+- vc dyPos 0"},{n:"ht",f:"cat2 hd2 dxPos dyPos"},{n:"wt",f:"sat2 wd2 dxPos dyPos"},{n:"g2",f:"cat2 wd2 ht wt"},{n:"g3",f:"sat2 hd2 ht wt"},{n:"g4",f:"+- hc g2 0"},{n:"g5",f:"+- vc g3 0"},{n:"g6",f:"+- g4 0 xPos"},{n:"g7",f:"+- g5 0 yPos"},{n:"g8",f:"mod g6 g7 0"},{n:"g9",f:"*/ ss 6600 21600"},{n:"g10",f:"+- g8 0 g9"},{n:"g11",f:"*/ g10 1 3"},{n:"g12",f:"*/ ss 1800 21600"},{n:"g13",f:"+- g11 g12 0"},{n:"g14",f:"*/ g13 g6 g8"},{n:"g15",f:"*/ g13 g7 g8"},{n:"g16",f:"+- g14 xPos 0"},{n:"g17",f:"+- g15 yPos 0"},{n:"g18",f:"*/ ss 4800 21600"},{n:"g19",f:"*/ g11 2 1"},{n:"g20",f:"+- g18 g19 0"},{n:"g21",f:"*/ g20 g6 g8"},{n:"g22",f:"*/ g20 g7 g8"},{n:"g23",f:"+- g21 xPos 0"},{n:"g24",f:"+- g22 yPos 0"},{n:"g25",f:"*/ ss 1200 21600"},{n:"g26",f:"*/ ss 600 21600"},{n:"x23",f:"+- xPos g26 0"},{n:"x24",f:"+- g16 g25 0"},{n:"x25",f:"+- g23 g12 0"},{n:"il",f:"*/ w 2977 21600"},{n:"it",f:"*/ h 3262 21600"},{n:"ir",f:"*/ w 17087 21600"},{n:"ib",f:"*/ h 17337 21600"},{n:"g27",f:"*/ w 67 21600"},{n:"g28",f:"*/ h 21577 21600"},{n:"g29",f:"*/ w 21582 21600"},{n:"g30",f:"*/ h 1235 21600"},{n:"pang",f:"at2 dxPos dyPos"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"3900",y:"14370"}},{type:"arcTo",wR:"6753",hR:"9190",stAng:"-11429249",swAng:"7426832"},{type:"arcTo",wR:"5333",hR:"7267",stAng:"-8646143",swAng:"5396714"},{type:"arcTo",wR:"4365",hR:"5945",stAng:"-8748475",swAng:"5983381"},{type:"arcTo",wR:"4857",hR:"6595",stAng:"-7859164",swAng:"7034504"},{type:"arcTo",wR:"5333",hR:"7273",stAng:"-4722533",swAng:"6541615"},{type:"arcTo",wR:"6775",hR:"9220",stAng:"-2776035",swAng:"7816140"},{type:"arcTo",wR:"5785",hR:"7867",stAng:"37501",swAng:"6842000"},{type:"arcTo",wR:"6752",hR:"9215",stAng:"1347096",swAng:"6910353"},{type:"arcTo",wR:"7720",hR:"10543",stAng:"3974558",swAng:"4542661"},{type:"arcTo",wR:"4360",hR:"5918",stAng:"-16496525",swAng:"8804134"},{type:"arcTo",wR:"4345",hR:"5945",stAng:"-14809710",swAng:"9151131"},{type:"close"}],extrusionOk:!1,stroke:!0,w:43200,h:43200},{defines:[{type:"moveTo",pt:{x:"x23",y:"yPos"}},{type:"arcTo",wR:"g26",hR:"g26",stAng:"0",swAng:"21600000"},{type:"close"}],extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"x24",y:"g17"}},{type:"arcTo",wR:"g25",hR:"g25",stAng:"0",swAng:"21600000"},{type:"close"}],extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"x25",y:"g24"}},{type:"arcTo",wR:"g12",hR:"g12",stAng:"0",swAng:"21600000"},{type:"close"}],extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"4693",y:"26177"}},{type:"arcTo",wR:"4345",hR:"5945",stAng:"5204520",swAng:"1585770"},{type:"moveTo",pt:{x:"6928",y:"34899"}},{type:"arcTo",wR:"4360",hR:"5918",stAng:"4416628",swAng:"686848"},{type:"moveTo",pt:{x:"16478",y:"39090"}},{type:"arcTo",wR:"6752",hR:"9215",stAng:"8257449",swAng:"844866"},{type:"moveTo",pt:{x:"28827",y:"34751"}},{type:"arcTo",wR:"6752",hR:"9215",stAng:"387196",swAng:"959901"},{type:"moveTo",pt:{x:"34129",y:"22954"}},{type:"arcTo",wR:"5785",hR:"7867",stAng:"-4217541",swAng:"4255042"},{type:"moveTo",pt:{x:"41798",y:"15354"}},{type:"arcTo",wR:"5333",hR:"7273",stAng:"1819082",swAng:"1665090"},{type:"moveTo",pt:{x:"38324",y:"5426"}},{type:"arcTo",wR:"4857",hR:"6595",stAng:"-824660",swAng:"891534"},{type:"moveTo",pt:{x:"29078",y:"3952"}},{type:"arcTo",wR:"4857",hR:"6595",stAng:"-8950887",swAng:"1091722"},{type:"moveTo",pt:{x:"22141",y:"4720"}},{type:"arcTo",wR:"4365",hR:"5945",stAng:"-9809656",swAng:"1061181"},{type:"moveTo",pt:{x:"14000",y:"5192"}},{type:"arcTo",wR:"6753",hR:"9190",stAng:"-4002417",swAng:"739161"},{type:"moveTo",pt:{x:"4127",y:"15789"}},{type:"arcTo",wR:"6753",hR:"9190",stAng:"9459261",swAng:"711490"}],fill:"none",extrusionOk:!1,stroke:!0,w:43200,h:43200}]},corner:{avLst:[{n:"adj1",f:"val 50000"},{n:"adj2",f:"val 50000"}],gdLst:[{n:"maxAdj1",f:"*/ 100000 h ss"},{n:"maxAdj2",f:"*/ 100000 w ss"},{n:"a1",f:"pin 0 adj1 maxAdj1"},{n:"a2",f:"pin 0 adj2 maxAdj2"},{n:"x1",f:"*/ ss a2 100000"},{n:"dy1",f:"*/ ss a1 100000"},{n:"y1",f:"+- b 0 dy1"},{n:"cx1",f:"*/ x1 1 2"},{n:"cy1",f:"+/ y1 b 2"},{n:"d",f:"+- w 0 h"},{n:"it",f:"?: d y1 t"},{n:"ir",f:"?: d r x1"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"x1",y:"t"}},{type:"lnTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"r",y:"y1"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},cornerTabs:{gdLst:[{n:"md",f:"mod w h 0"},{n:"dx",f:"*/ 1 md 20"},{n:"y1",f:"+- 0 b dx"},{n:"x1",f:"+- 0 r dx"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"dx",y:"t"}},{type:"lnTo",pt:{x:"l",y:"dx"}},{type:"close"}],extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"l",y:"y1"}},{type:"lnTo",pt:{x:"dx",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"x1",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"dx"}},{type:"close"}],extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"r",y:"y1"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"x1",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},cube:{avLst:[{n:"adj",f:"val 25000"}],gdLst:[{n:"a",f:"pin 0 adj 100000"},{n:"y1",f:"*/ ss a 100000"},{n:"y4",f:"+- b 0 y1"},{n:"y2",f:"*/ y4 1 2"},{n:"y3",f:"+/ y1 b 2"},{n:"x4",f:"+- r 0 y1"},{n:"x2",f:"*/ x4 1 2"},{n:"x3",f:"+/ y1 r 2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"y1"}},{type:"lnTo",pt:{x:"x4",y:"y1"}},{type:"lnTo",pt:{x:"x4",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"x4",y:"y1"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"y4"}},{type:"lnTo",pt:{x:"x4",y:"b"}},{type:"close"}],fill:"darkenLess",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"l",y:"y1"}},{type:"lnTo",pt:{x:"y1",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"x4",y:"y1"}},{type:"close"}],fill:"lightenLess",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"l",y:"y1"}},{type:"lnTo",pt:{x:"y1",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"y4"}},{type:"lnTo",pt:{x:"x4",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"},{type:"moveTo",pt:{x:"l",y:"y1"}},{type:"lnTo",pt:{x:"x4",y:"y1"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"moveTo",pt:{x:"x4",y:"y1"}},{type:"lnTo",pt:{x:"x4",y:"b"}}],fill:"none",extrusionOk:!1,stroke:!0}]},curvedConnector2:{pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"cubicBezTo",pts:[{x:"wd2",y:"t"},{x:"r",y:"hd2"},{x:"r",y:"b"}]}],fill:"none",extrusionOk:!1,stroke:!0}]},curvedConnector3:{avLst:[{n:"adj1",f:"val 50000"}],gdLst:[{n:"x2",f:"*/ w adj1 100000"},{n:"x1",f:"+/ l x2 2"},{n:"x3",f:"+/ r x2 2"},{n:"y3",f:"*/ h 3 4"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"cubicBezTo",pts:[{x:"x1",y:"t"},{x:"x2",y:"hd4"},{x:"x2",y:"vc"}]},{type:"cubicBezTo",pts:[{x:"x2",y:"y3"},{x:"x3",y:"b"},{x:"r",y:"b"}]}],fill:"none",extrusionOk:!1,stroke:!0}]},curvedConnector4:{avLst:[{n:"adj1",f:"val 50000"},{n:"adj2",f:"val 50000"}],gdLst:[{n:"x2",f:"*/ w adj1 100000"},{n:"x1",f:"+/ l x2 2"},{n:"x3",f:"+/ r x2 2"},{n:"x4",f:"+/ x2 x3 2"},{n:"x5",f:"+/ x3 r 2"},{n:"y4",f:"*/ h adj2 100000"},{n:"y1",f:"+/ t y4 2"},{n:"y2",f:"+/ t y1 2"},{n:"y3",f:"+/ y1 y4 2"},{n:"y5",f:"+/ b y4 2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"cubicBezTo",pts:[{x:"x1",y:"t"},{x:"x2",y:"y2"},{x:"x2",y:"y1"}]},{type:"cubicBezTo",pts:[{x:"x2",y:"y3"},{x:"x4",y:"y4"},{x:"x3",y:"y4"}]},{type:"cubicBezTo",pts:[{x:"x5",y:"y4"},{x:"r",y:"y5"},{x:"r",y:"b"}]}],fill:"none",extrusionOk:!1,stroke:!0}]},curvedConnector5:{avLst:[{n:"adj1",f:"val 50000"},{n:"adj2",f:"val 50000"},{n:"adj3",f:"val 50000"}],gdLst:[{n:"x3",f:"*/ w adj1 100000"},{n:"x6",f:"*/ w adj3 100000"},{n:"x1",f:"+/ x3 x6 2"},{n:"x2",f:"+/ l x3 2"},{n:"x4",f:"+/ x3 x1 2"},{n:"x5",f:"+/ x6 x1 2"},{n:"x7",f:"+/ x6 r 2"},{n:"y4",f:"*/ h adj2 100000"},{n:"y1",f:"+/ t y4 2"},{n:"y2",f:"+/ t y1 2"},{n:"y3",f:"+/ y1 y4 2"},{n:"y5",f:"+/ b y4 2"},{n:"y6",f:"+/ y5 y4 2"},{n:"y7",f:"+/ y5 b 2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"cubicBezTo",pts:[{x:"x2",y:"t"},{x:"x3",y:"y2"},{x:"x3",y:"y1"}]},{type:"cubicBezTo",pts:[{x:"x3",y:"y3"},{x:"x4",y:"y4"},{x:"x1",y:"y4"}]},{type:"cubicBezTo",pts:[{x:"x5",y:"y4"},{x:"x6",y:"y6"},{x:"x6",y:"y5"}]},{type:"cubicBezTo",pts:[{x:"x6",y:"y7"},{x:"x7",y:"b"},{x:"r",y:"b"}]}],fill:"none",extrusionOk:!1,stroke:!0}]},curvedDownArrow:{avLst:[{n:"adj1",f:"val 25000"},{n:"adj2",f:"val 50000"},{n:"adj3",f:"val 25000"}],gdLst:[{n:"maxAdj2",f:"*/ 50000 w ss"},{n:"a2",f:"pin 0 adj2 maxAdj2"},{n:"a1",f:"pin 0 adj1 100000"},{n:"th",f:"*/ ss a1 100000"},{n:"aw",f:"*/ ss a2 100000"},{n:"q1",f:"+/ th aw 4"},{n:"wR",f:"+- wd2 0 q1"},{n:"q7",f:"*/ wR 2 1"},{n:"q8",f:"*/ q7 q7 1"},{n:"q9",f:"*/ th th 1"},{n:"q10",f:"+- q8 0 q9"},{n:"q11",f:"sqrt q10"},{n:"idy",f:"*/ q11 h q7"},{n:"maxAdj3",f:"*/ 100000 idy ss"},{n:"a3",f:"pin 0 adj3 maxAdj3"},{n:"ah",f:"*/ ss adj3 100000"},{n:"x3",f:"+- wR th 0"},{n:"q2",f:"*/ h h 1"},{n:"q3",f:"*/ ah ah 1"},{n:"q4",f:"+- q2 0 q3"},{n:"q5",f:"sqrt q4"},{n:"dx",f:"*/ q5 wR h"},{n:"x5",f:"+- wR dx 0"},{n:"x7",f:"+- x3 dx 0"},{n:"q6",f:"+- aw 0 th"},{n:"dh",f:"*/ q6 1 2"},{n:"x4",f:"+- x5 0 dh"},{n:"x8",f:"+- x7 dh 0"},{n:"aw2",f:"*/ aw 1 2"},{n:"x6",f:"+- r 0 aw2"},{n:"y1",f:"+- b 0 ah"},{n:"swAng",f:"at2 ah dx"},{n:"mswAng",f:"+- 0 0 swAng"},{n:"iy",f:"+- b 0 idy"},{n:"ix",f:"+/ wR x3 2"},{n:"q12",f:"*/ th 1 2"},{n:"dang2",f:"at2 idy q12"},{n:"stAng",f:"+- 3cd4 swAng 0"},{n:"stAng2",f:"+- 3cd4 0 dang2"},{n:"swAng2",f:"+- dang2 0 cd4"},{n:"swAng3",f:"+- cd4 dang2 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"x6",y:"b"}},{type:"lnTo",pt:{x:"x4",y:"y1"}},{type:"lnTo",pt:{x:"x5",y:"y1"}},{type:"arcTo",wR:"wR",hR:"h",stAng:"stAng",swAng:"mswAng"},{type:"lnTo",pt:{x:"x3",y:"t"}},{type:"arcTo",wR:"wR",hR:"h",stAng:"3cd4",swAng:"swAng"},{type:"lnTo",pt:{x:"x8",y:"y1"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"ix",y:"iy"}},{type:"arcTo",wR:"wR",hR:"h",stAng:"stAng2",swAng:"swAng2"},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"arcTo",wR:"wR",hR:"h",stAng:"cd2",swAng:"swAng3"},{type:"close"}],fill:"darkenLess",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"ix",y:"iy"}},{type:"arcTo",wR:"wR",hR:"h",stAng:"stAng2",swAng:"swAng2"},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"arcTo",wR:"wR",hR:"h",stAng:"cd2",swAng:"cd4"},{type:"lnTo",pt:{x:"x3",y:"t"}},{type:"arcTo",wR:"wR",hR:"h",stAng:"3cd4",swAng:"swAng"},{type:"lnTo",pt:{x:"x8",y:"y1"}},{type:"lnTo",pt:{x:"x6",y:"b"}},{type:"lnTo",pt:{x:"x4",y:"y1"}},{type:"lnTo",pt:{x:"x5",y:"y1"}},{type:"arcTo",wR:"wR",hR:"h",stAng:"stAng",swAng:"mswAng"}],fill:"none",extrusionOk:!1,stroke:!0}]},curvedLeftArrow:{avLst:[{n:"adj1",f:"val 25000"},{n:"adj2",f:"val 50000"},{n:"adj3",f:"val 25000"}],gdLst:[{n:"maxAdj2",f:"*/ 50000 h ss"},{n:"a2",f:"pin 0 adj2 maxAdj2"},{n:"a1",f:"pin 0 adj1 a2"},{n:"th",f:"*/ ss a1 100000"},{n:"aw",f:"*/ ss a2 100000"},{n:"q1",f:"+/ th aw 4"},{n:"hR",f:"+- hd2 0 q1"},{n:"q7",f:"*/ hR 2 1"},{n:"q8",f:"*/ q7 q7 1"},{n:"q9",f:"*/ th th 1"},{n:"q10",f:"+- q8 0 q9"},{n:"q11",f:"sqrt q10"},{n:"idx",f:"*/ q11 w q7"},{n:"maxAdj3",f:"*/ 100000 idx ss"},{n:"a3",f:"pin 0 adj3 maxAdj3"},{n:"ah",f:"*/ ss a3 100000"},{n:"y3",f:"+- hR th 0"},{n:"q2",f:"*/ w w 1"},{n:"q3",f:"*/ ah ah 1"},{n:"q4",f:"+- q2 0 q3"},{n:"q5",f:"sqrt q4"},{n:"dy",f:"*/ q5 hR w"},{n:"y5",f:"+- hR dy 0"},{n:"y7",f:"+- y3 dy 0"},{n:"q6",f:"+- aw 0 th"},{n:"dh",f:"*/ q6 1 2"},{n:"y4",f:"+- y5 0 dh"},{n:"y8",f:"+- y7 dh 0"},{n:"aw2",f:"*/ aw 1 2"},{n:"y6",f:"+- b 0 aw2"},{n:"x1",f:"+- l ah 0"},{n:"swAng",f:"at2 ah dy"},{n:"mswAng",f:"+- 0 0 swAng"},{n:"ix",f:"+- l idx 0"},{n:"iy",f:"+/ hR y3 2"},{n:"q12",f:"*/ th 1 2"},{n:"dang2",f:"at2 idx q12"},{n:"swAng2",f:"+- dang2 0 swAng"},{n:"swAng3",f:"+- swAng dang2 0"},{n:"stAng3",f:"+- 0 0 dang2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"y6"}},{type:"lnTo",pt:{x:"x1",y:"y4"}},{type:"lnTo",pt:{x:"x1",y:"y5"}},{type:"arcTo",wR:"w",hR:"hR",stAng:"swAng",swAng:"swAng2"},{type:"arcTo",wR:"w",hR:"hR",stAng:"stAng3",swAng:"swAng3"},{type:"lnTo",pt:{x:"x1",y:"y8"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"r",y:"y3"}},{type:"arcTo",wR:"w",hR:"hR",stAng:"0",swAng:"-5400000"},{type:"lnTo",pt:{x:"l",y:"t"}},{type:"arcTo",wR:"w",hR:"hR",stAng:"3cd4",swAng:"cd4"},{type:"close"}],fill:"darkenLess",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"r",y:"y3"}},{type:"arcTo",wR:"w",hR:"hR",stAng:"0",swAng:"-5400000"},{type:"lnTo",pt:{x:"l",y:"t"}},{type:"arcTo",wR:"w",hR:"hR",stAng:"3cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"r",y:"y3"}},{type:"arcTo",wR:"w",hR:"hR",stAng:"0",swAng:"swAng"},{type:"lnTo",pt:{x:"x1",y:"y8"}},{type:"lnTo",pt:{x:"l",y:"y6"}},{type:"lnTo",pt:{x:"x1",y:"y4"}},{type:"lnTo",pt:{x:"x1",y:"y5"}},{type:"arcTo",wR:"w",hR:"hR",stAng:"swAng",swAng:"swAng2"}],fill:"none",extrusionOk:!1,stroke:!0}]},curvedRightArrow:{avLst:[{n:"adj1",f:"val 25000"},{n:"adj2",f:"val 50000"},{n:"adj3",f:"val 25000"}],gdLst:[{n:"maxAdj2",f:"*/ 50000 h ss"},{n:"a2",f:"pin 0 adj2 maxAdj2"},{n:"a1",f:"pin 0 adj1 a2"},{n:"th",f:"*/ ss a1 100000"},{n:"aw",f:"*/ ss a2 100000"},{n:"q1",f:"+/ th aw 4"},{n:"hR",f:"+- hd2 0 q1"},{n:"q7",f:"*/ hR 2 1"},{n:"q8",f:"*/ q7 q7 1"},{n:"q9",f:"*/ th th 1"},{n:"q10",f:"+- q8 0 q9"},{n:"q11",f:"sqrt q10"},{n:"idx",f:"*/ q11 w q7"},{n:"maxAdj3",f:"*/ 100000 idx ss"},{n:"a3",f:"pin 0 adj3 maxAdj3"},{n:"ah",f:"*/ ss a3 100000"},{n:"y3",f:"+- hR th 0"},{n:"q2",f:"*/ w w 1"},{n:"q3",f:"*/ ah ah 1"},{n:"q4",f:"+- q2 0 q3"},{n:"q5",f:"sqrt q4"},{n:"dy",f:"*/ q5 hR w"},{n:"y5",f:"+- hR dy 0"},{n:"y7",f:"+- y3 dy 0"},{n:"q6",f:"+- aw 0 th"},{n:"dh",f:"*/ q6 1 2"},{n:"y4",f:"+- y5 0 dh"},{n:"y8",f:"+- y7 dh 0"},{n:"aw2",f:"*/ aw 1 2"},{n:"y6",f:"+- b 0 aw2"},{n:"x1",f:"+- r 0 ah"},{n:"swAng",f:"at2 ah dy"},{n:"stAng",f:"+- cd2 0 swAng"},{n:"mswAng",f:"+- 0 0 swAng"},{n:"ix",f:"+- r 0 idx"},{n:"iy",f:"+/ hR y3 2"},{n:"q12",f:"*/ th 1 2"},{n:"dang2",f:"at2 idx q12"},{n:"swAng2",f:"+- dang2 0 cd4"},{n:"swAng3",f:"+- cd4 dang2 0"},{n:"stAng3",f:"+- cd2 0 dang2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"hR"}},{type:"arcTo",wR:"w",hR:"hR",stAng:"cd2",swAng:"mswAng"},{type:"lnTo",pt:{x:"x1",y:"y4"}},{type:"lnTo",pt:{x:"r",y:"y6"}},{type:"lnTo",pt:{x:"x1",y:"y8"}},{type:"lnTo",pt:{x:"x1",y:"y7"}},{type:"arcTo",wR:"w",hR:"hR",stAng:"stAng",swAng:"swAng"},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"r",y:"th"}},{type:"arcTo",wR:"w",hR:"hR",stAng:"3cd4",swAng:"swAng2"},{type:"arcTo",wR:"w",hR:"hR",stAng:"stAng3",swAng:"swAng3"},{type:"close"}],fill:"darkenLess",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"l",y:"hR"}},{type:"arcTo",wR:"w",hR:"hR",stAng:"cd2",swAng:"mswAng"},{type:"lnTo",pt:{x:"x1",y:"y4"}},{type:"lnTo",pt:{x:"r",y:"y6"}},{type:"lnTo",pt:{x:"x1",y:"y8"}},{type:"lnTo",pt:{x:"x1",y:"y7"}},{type:"arcTo",wR:"w",hR:"hR",stAng:"stAng",swAng:"swAng"},{type:"lnTo",pt:{x:"l",y:"hR"}},{type:"arcTo",wR:"w",hR:"hR",stAng:"cd2",swAng:"cd4"},{type:"lnTo",pt:{x:"r",y:"th"}},{type:"arcTo",wR:"w",hR:"hR",stAng:"3cd4",swAng:"swAng2"}],fill:"none",extrusionOk:!1,stroke:!0}]},curvedUpArrow:{avLst:[{n:"adj1",f:"val 25000"},{n:"adj2",f:"val 50000"},{n:"adj3",f:"val 25000"}],gdLst:[{n:"maxAdj2",f:"*/ 50000 w ss"},{n:"a2",f:"pin 0 adj2 maxAdj2"},{n:"a1",f:"pin 0 adj1 100000"},{n:"th",f:"*/ ss a1 100000"},{n:"aw",f:"*/ ss a2 100000"},{n:"q1",f:"+/ th aw 4"},{n:"wR",f:"+- wd2 0 q1"},{n:"q7",f:"*/ wR 2 1"},{n:"q8",f:"*/ q7 q7 1"},{n:"q9",f:"*/ th th 1"},{n:"q10",f:"+- q8 0 q9"},{n:"q11",f:"sqrt q10"},{n:"idy",f:"*/ q11 h q7"},{n:"maxAdj3",f:"*/ 100000 idy ss"},{n:"a3",f:"pin 0 adj3 maxAdj3"},{n:"ah",f:"*/ ss adj3 100000"},{n:"x3",f:"+- wR th 0"},{n:"q2",f:"*/ h h 1"},{n:"q3",f:"*/ ah ah 1"},{n:"q4",f:"+- q2 0 q3"},{n:"q5",f:"sqrt q4"},{n:"dx",f:"*/ q5 wR h"},{n:"x5",f:"+- wR dx 0"},{n:"x7",f:"+- x3 dx 0"},{n:"q6",f:"+- aw 0 th"},{n:"dh",f:"*/ q6 1 2"},{n:"x4",f:"+- x5 0 dh"},{n:"x8",f:"+- x7 dh 0"},{n:"aw2",f:"*/ aw 1 2"},{n:"x6",f:"+- r 0 aw2"},{n:"y1",f:"+- t ah 0"},{n:"swAng",f:"at2 ah dx"},{n:"mswAng",f:"+- 0 0 swAng"},{n:"iy",f:"+- t idy 0"},{n:"ix",f:"+/ wR x3 2"},{n:"q12",f:"*/ th 1 2"},{n:"dang2",f:"at2 idy q12"},{n:"swAng2",f:"+- dang2 0 swAng"},{n:"mswAng2",f:"+- 0 0 swAng2"},{n:"stAng3",f:"+- cd4 0 swAng"},{n:"swAng3",f:"+- swAng dang2 0"},{n:"stAng2",f:"+- cd4 0 dang2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"x6",y:"t"}},{type:"lnTo",pt:{x:"x8",y:"y1"}},{type:"lnTo",pt:{x:"x7",y:"y1"}},{type:"arcTo",wR:"wR",hR:"h",stAng:"stAng3",swAng:"swAng3"},{type:"arcTo",wR:"wR",hR:"h",stAng:"stAng2",swAng:"swAng2"},{type:"lnTo",pt:{x:"x4",y:"y1"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"wR",y:"b"}},{type:"arcTo",wR:"wR",hR:"h",stAng:"cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"th",y:"t"}},{type:"arcTo",wR:"wR",hR:"h",stAng:"cd2",swAng:"-5400000"},{type:"close"}],fill:"darkenLess",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"ix",y:"iy"}},{type:"arcTo",wR:"wR",hR:"h",stAng:"stAng2",swAng:"swAng2"},{type:"lnTo",pt:{x:"x4",y:"y1"}},{type:"lnTo",pt:{x:"x6",y:"t"}},{type:"lnTo",pt:{x:"x8",y:"y1"}},{type:"lnTo",pt:{x:"x7",y:"y1"}},{type:"arcTo",wR:"wR",hR:"h",stAng:"stAng3",swAng:"swAng"},{type:"lnTo",pt:{x:"wR",y:"b"}},{type:"arcTo",wR:"wR",hR:"h",stAng:"cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"th",y:"t"}},{type:"arcTo",wR:"wR",hR:"h",stAng:"cd2",swAng:"-5400000"}],fill:"none",extrusionOk:!1,stroke:!0}]},decagon:{avLst:[{n:"vf",f:"val 105146"}],gdLst:[{n:"shd2",f:"*/ hd2 vf 100000"},{n:"dx1",f:"cos wd2 2160000"},{n:"dx2",f:"cos wd2 4320000"},{n:"x1",f:"+- hc 0 dx1"},{n:"x2",f:"+- hc 0 dx2"},{n:"x3",f:"+- hc dx2 0"},{n:"x4",f:"+- hc dx1 0"},{n:"dy1",f:"sin shd2 4320000"},{n:"dy2",f:"sin shd2 2160000"},{n:"y1",f:"+- vc 0 dy1"},{n:"y2",f:"+- vc 0 dy2"},{n:"y3",f:"+- vc dy2 0"},{n:"y4",f:"+- vc dy1 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"lnTo",pt:{x:"x1",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"y1"}},{type:"lnTo",pt:{x:"x3",y:"y1"}},{type:"lnTo",pt:{x:"x4",y:"y2"}},{type:"lnTo",pt:{x:"r",y:"vc"}},{type:"lnTo",pt:{x:"x4",y:"y3"}},{type:"lnTo",pt:{x:"x3",y:"y4"}},{type:"lnTo",pt:{x:"x2",y:"y4"}},{type:"lnTo",pt:{x:"x1",y:"y3"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},diagStripe:{avLst:[{n:"adj",f:"val 50000"}],gdLst:[{n:"a",f:"pin 0 adj 100000"},{n:"x2",f:"*/ w a 100000"},{n:"x1",f:"*/ x2 1 2"},{n:"x3",f:"+/ x2 r 2"},{n:"y2",f:"*/ h a 100000"},{n:"y1",f:"*/ y2 1 2"},{n:"y3",f:"+/ y2 b 2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},diamond:{gdLst:[{n:"ir",f:"*/ w 3 4"},{n:"ib",f:"*/ h 3 4"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"lnTo",pt:{x:"hc",y:"t"}},{type:"lnTo",pt:{x:"r",y:"vc"}},{type:"lnTo",pt:{x:"hc",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},dodecagon:{gdLst:[{n:"x1",f:"*/ w 2894 21600"},{n:"x2",f:"*/ w 7906 21600"},{n:"x3",f:"*/ w 13694 21600"},{n:"x4",f:"*/ w 18706 21600"},{n:"y1",f:"*/ h 2894 21600"},{n:"y2",f:"*/ h 7906 21600"},{n:"y3",f:"*/ h 13694 21600"},{n:"y4",f:"*/ h 18706 21600"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"y2"}},{type:"lnTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"x2",y:"t"}},{type:"lnTo",pt:{x:"x3",y:"t"}},{type:"lnTo",pt:{x:"x4",y:"y1"}},{type:"lnTo",pt:{x:"r",y:"y2"}},{type:"lnTo",pt:{x:"r",y:"y3"}},{type:"lnTo",pt:{x:"x4",y:"y4"}},{type:"lnTo",pt:{x:"x3",y:"b"}},{type:"lnTo",pt:{x:"x2",y:"b"}},{type:"lnTo",pt:{x:"x1",y:"y4"}},{type:"lnTo",pt:{x:"l",y:"y3"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},donut:{avLst:[{n:"adj",f:"val 25000"}],gdLst:[{n:"a",f:"pin 0 adj 50000"},{n:"dr",f:"*/ ss a 100000"},{n:"iwd2",f:"+- wd2 0 dr"},{n:"ihd2",f:"+- hd2 0 dr"},{n:"idx",f:"cos wd2 2700000"},{n:"idy",f:"sin hd2 2700000"},{n:"il",f:"+- hc 0 idx"},{n:"ir",f:"+- hc idx 0"},{n:"it",f:"+- vc 0 idy"},{n:"ib",f:"+- vc idy 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"cd2",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"3cd4",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"0",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"cd4",swAng:"cd4"},{type:"close"},{type:"moveTo",pt:{x:"dr",y:"vc"}},{type:"arcTo",wR:"iwd2",hR:"ihd2",stAng:"cd2",swAng:"-5400000"},{type:"arcTo",wR:"iwd2",hR:"ihd2",stAng:"cd4",swAng:"-5400000"},{type:"arcTo",wR:"iwd2",hR:"ihd2",stAng:"0",swAng:"-5400000"},{type:"arcTo",wR:"iwd2",hR:"ihd2",stAng:"3cd4",swAng:"-5400000"},{type:"close"}],extrusionOk:!1,stroke:!0}]},doubleWave:{avLst:[{n:"adj1",f:"val 6250"},{n:"adj2",f:"val 0"}],gdLst:[{n:"a1",f:"pin 0 adj1 12500"},{n:"a2",f:"pin -10000 adj2 10000"},{n:"y1",f:"*/ h a1 100000"},{n:"dy2",f:"*/ y1 10 3"},{n:"y2",f:"+- y1 0 dy2"},{n:"y3",f:"+- y1 dy2 0"},{n:"y4",f:"+- b 0 y1"},{n:"y5",f:"+- y4 0 dy2"},{n:"y6",f:"+- y4 dy2 0"},{n:"dx1",f:"*/ w a2 100000"},{n:"of2",f:"*/ w a2 50000"},{n:"x1",f:"abs dx1"},{n:"dx2",f:"?: of2 0 of2"},{n:"x2",f:"+- l 0 dx2"},{n:"dx8",f:"?: of2 of2 0"},{n:"x8",f:"+- r 0 dx8"},{n:"dx3",f:"+/ dx2 x8 6"},{n:"x3",f:"+- x2 dx3 0"},{n:"dx4",f:"+/ dx2 x8 3"},{n:"x4",f:"+- x2 dx4 0"},{n:"x5",f:"+/ x2 x8 2"},{n:"x6",f:"+- x5 dx3 0"},{n:"x7",f:"+/ x6 x8 2"},{n:"x9",f:"+- l dx8 0"},{n:"x15",f:"+- r dx2 0"},{n:"x10",f:"+- x9 dx3 0"},{n:"x11",f:"+- x9 dx4 0"},{n:"x12",f:"+/ x9 x15 2"},{n:"x13",f:"+- x12 dx3 0"},{n:"x14",f:"+/ x13 x15 2"},{n:"x16",f:"+- r 0 x1"},{n:"xAdj",f:"+- hc dx1 0"},{n:"il",f:"max x2 x9"},{n:"ir",f:"min x8 x15"},{n:"it",f:"*/ h a1 50000"},{n:"ib",f:"+- b 0 it"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"x2",y:"y1"}},{type:"cubicBezTo",pts:[{x:"x3",y:"y2"},{x:"x4",y:"y3"},{x:"x5",y:"y1"}]},{type:"cubicBezTo",pts:[{x:"x6",y:"y2"},{x:"x7",y:"y3"},{x:"x8",y:"y1"}]},{type:"lnTo",pt:{x:"x15",y:"y4"}},{type:"cubicBezTo",pts:[{x:"x14",y:"y6"},{x:"x13",y:"y5"},{x:"x12",y:"y4"}]},{type:"cubicBezTo",pts:[{x:"x11",y:"y6"},{x:"x10",y:"y5"},{x:"x9",y:"y4"}]},{type:"close"}],extrusionOk:!1,stroke:!0}]},downArrow:{avLst:[{n:"adj1",f:"val 50000"},{n:"adj2",f:"val 50000"}],gdLst:[{n:"maxAdj2",f:"*/ 100000 h ss"},{n:"a1",f:"pin 0 adj1 100000"},{n:"a2",f:"pin 0 adj2 maxAdj2"},{n:"dy1",f:"*/ ss a2 100000"},{n:"y1",f:"+- b 0 dy1"},{n:"dx1",f:"*/ w a1 200000"},{n:"x1",f:"+- hc 0 dx1"},{n:"x2",f:"+- hc dx1 0"},{n:"dy2",f:"*/ x1 dy1 wd2"},{n:"y2",f:"+- y1 dy2 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"y1"}},{type:"lnTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"x1",y:"t"}},{type:"lnTo",pt:{x:"x2",y:"t"}},{type:"lnTo",pt:{x:"x2",y:"y1"}},{type:"lnTo",pt:{x:"r",y:"y1"}},{type:"lnTo",pt:{x:"hc",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},downArrowCallout:{avLst:[{n:"adj1",f:"val 25000"},{n:"adj2",f:"val 25000"},{n:"adj3",f:"val 25000"},{n:"adj4",f:"val 64977"}],gdLst:[{n:"maxAdj2",f:"*/ 50000 w ss"},{n:"a2",f:"pin 0 adj2 maxAdj2"},{n:"maxAdj1",f:"*/ a2 2 1"},{n:"a1",f:"pin 0 adj1 maxAdj1"},{n:"maxAdj3",f:"*/ 100000 h ss"},{n:"a3",f:"pin 0 adj3 maxAdj3"},{n:"q2",f:"*/ a3 ss h"},{n:"maxAdj4",f:"+- 100000 0 q2"},{n:"a4",f:"pin 0 adj4 maxAdj4"},{n:"dx1",f:"*/ ss a2 100000"},{n:"dx2",f:"*/ ss a1 200000"},{n:"x1",f:"+- hc 0 dx1"},{n:"x2",f:"+- hc 0 dx2"},{n:"x3",f:"+- hc dx2 0"},{n:"x4",f:"+- hc dx1 0"},{n:"dy3",f:"*/ ss a3 100000"},{n:"y3",f:"+- b 0 dy3"},{n:"y2",f:"*/ h a4 100000"},{n:"y1",f:"*/ y2 1 2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"y2"}},{type:"lnTo",pt:{x:"x3",y:"y2"}},{type:"lnTo",pt:{x:"x3",y:"y3"}},{type:"lnTo",pt:{x:"x4",y:"y3"}},{type:"lnTo",pt:{x:"hc",y:"b"}},{type:"lnTo",pt:{x:"x1",y:"y3"}},{type:"lnTo",pt:{x:"x2",y:"y3"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"l",y:"y2"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},ellipse:{gdLst:[{n:"idx",f:"cos wd2 2700000"},{n:"idy",f:"sin hd2 2700000"},{n:"il",f:"+- hc 0 idx"},{n:"ir",f:"+- hc idx 0"},{n:"it",f:"+- vc 0 idy"},{n:"ib",f:"+- vc idy 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"cd2",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"3cd4",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"0",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"cd4",swAng:"cd4"},{type:"close"}],extrusionOk:!1,stroke:!0}]},ellipseRibbon:{avLst:[{n:"adj1",f:"val 25000"},{n:"adj2",f:"val 50000"},{n:"adj3",f:"val 12500"}],gdLst:[{n:"a1",f:"pin 0 adj1 100000"},{n:"a2",f:"pin 25000 adj2 75000"},{n:"q10",f:"+- 100000 0 a1"},{n:"q11",f:"*/ q10 1 2"},{n:"q12",f:"+- a1 0 q11"},{n:"minAdj3",f:"max 0 q12"},{n:"a3",f:"pin minAdj3 adj3 a1"},{n:"dx2",f:"*/ w a2 200000"},{n:"x2",f:"+- hc 0 dx2"},{n:"x3",f:"+- x2 wd8 0"},{n:"x4",f:"+- r 0 x3"},{n:"x5",f:"+- r 0 x2"},{n:"x6",f:"+- r 0 wd8"},{n:"dy1",f:"*/ h a3 100000"},{n:"f1",f:"*/ 4 dy1 w"},{n:"q1",f:"*/ x3 x3 w"},{n:"q2",f:"+- x3 0 q1"},{n:"y1",f:"*/ f1 q2 1"},{n:"cx1",f:"*/ x3 1 2"},{n:"cy1",f:"*/ f1 cx1 1"},{n:"cx2",f:"+- r 0 cx1"},{n:"q1",f:"*/ h a1 100000"},{n:"dy3",f:"+- q1 0 dy1"},{n:"q3",f:"*/ x2 x2 w"},{n:"q4",f:"+- x2 0 q3"},{n:"q5",f:"*/ f1 q4 1"},{n:"y3",f:"+- q5 dy3 0"},{n:"q6",f:"+- dy1 dy3 y3"},{n:"q7",f:"+- q6 dy1 0"},{n:"cy3",f:"+- q7 dy3 0"},{n:"rh",f:"+- b 0 q1"},{n:"q8",f:"*/ dy1 14 16"},{n:"y2",f:"+/ q8 rh 2"},{n:"y5",f:"+- q5 rh 0"},{n:"y6",f:"+- y3 rh 0"},{n:"cx4",f:"*/ x2 1 2"},{n:"q9",f:"*/ f1 cx4 1"},{n:"cy4",f:"+- q9 rh 0"},{n:"cx5",f:"+- r 0 cx4"},{n:"cy6",f:"+- cy3 rh 0"},{n:"y7",f:"+- y1 dy3 0"},{n:"cy7",f:"+- q1 q1 y7"},{n:"y8",f:"+- b 0 dy1"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"quadBezTo",pts:[{x:"cx1",y:"cy1"},{x:"x3",y:"y1"}]},{type:"lnTo",pt:{x:"x2",y:"y3"}},{type:"quadBezTo",pts:[{x:"hc",y:"cy3"},{x:"x5",y:"y3"}]},{type:"lnTo",pt:{x:"x4",y:"y1"}},{type:"quadBezTo",pts:[{x:"cx2",y:"cy1"},{x:"r",y:"t"}]},{type:"lnTo",pt:{x:"x6",y:"y2"}},{type:"lnTo",pt:{x:"r",y:"rh"}},{type:"quadBezTo",pts:[{x:"cx5",y:"cy4"},{x:"x5",y:"y5"}]},{type:"lnTo",pt:{x:"x5",y:"y6"}},{type:"quadBezTo",pts:[{x:"hc",y:"cy6"},{x:"x2",y:"y6"}]},{type:"lnTo",pt:{x:"x2",y:"y5"}},{type:"quadBezTo",pts:[{x:"cx4",y:"cy4"},{x:"l",y:"rh"}]},{type:"lnTo",pt:{x:"wd8",y:"y2"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"x3",y:"y7"}},{type:"lnTo",pt:{x:"x3",y:"y1"}},{type:"lnTo",pt:{x:"x2",y:"y3"}},{type:"quadBezTo",pts:[{x:"hc",y:"cy3"},{x:"x5",y:"y3"}]},{type:"lnTo",pt:{x:"x4",y:"y1"}},{type:"lnTo",pt:{x:"x4",y:"y7"}},{type:"quadBezTo",pts:[{x:"hc",y:"cy7"},{x:"x3",y:"y7"}]},{type:"close"}],fill:"darkenLess",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"quadBezTo",pts:[{x:"cx1",y:"cy1"},{x:"x3",y:"y1"}]},{type:"lnTo",pt:{x:"x2",y:"y3"}},{type:"quadBezTo",pts:[{x:"hc",y:"cy3"},{x:"x5",y:"y3"}]},{type:"lnTo",pt:{x:"x4",y:"y1"}},{type:"quadBezTo",pts:[{x:"cx2",y:"cy1"},{x:"r",y:"t"}]},{type:"lnTo",pt:{x:"x6",y:"y2"}},{type:"lnTo",pt:{x:"r",y:"rh"}},{type:"quadBezTo",pts:[{x:"cx5",y:"cy4"},{x:"x5",y:"y5"}]},{type:"lnTo",pt:{x:"x5",y:"y6"}},{type:"quadBezTo",pts:[{x:"hc",y:"cy6"},{x:"x2",y:"y6"}]},{type:"lnTo",pt:{x:"x2",y:"y5"}},{type:"quadBezTo",pts:[{x:"cx4",y:"cy4"},{x:"l",y:"rh"}]},{type:"lnTo",pt:{x:"wd8",y:"y2"}},{type:"close"},{type:"moveTo",pt:{x:"x2",y:"y5"}},{type:"lnTo",pt:{x:"x2",y:"y3"}},{type:"moveTo",pt:{x:"x5",y:"y3"}},{type:"lnTo",pt:{x:"x5",y:"y5"}},{type:"moveTo",pt:{x:"x3",y:"y1"}},{type:"lnTo",pt:{x:"x3",y:"y7"}},{type:"moveTo",pt:{x:"x4",y:"y7"}},{type:"lnTo",pt:{x:"x4",y:"y1"}}],fill:"none",extrusionOk:!1,stroke:!0}]},ellipseRibbon2:{avLst:[{n:"adj1",f:"val 25000"},{n:"adj2",f:"val 50000"},{n:"adj3",f:"val 12500"}],gdLst:[{n:"a1",f:"pin 0 adj1 100000"},{n:"a2",f:"pin 25000 adj2 75000"},{n:"q10",f:"+- 100000 0 a1"},{n:"q11",f:"*/ q10 1 2"},{n:"q12",f:"+- a1 0 q11"},{n:"minAdj3",f:"max 0 q12"},{n:"a3",f:"pin minAdj3 adj3 a1"},{n:"dx2",f:"*/ w a2 200000"},{n:"x2",f:"+- hc 0 dx2"},{n:"x3",f:"+- x2 wd8 0"},{n:"x4",f:"+- r 0 x3"},{n:"x5",f:"+- r 0 x2"},{n:"x6",f:"+- r 0 wd8"},{n:"dy1",f:"*/ h a3 100000"},{n:"f1",f:"*/ 4 dy1 w"},{n:"q1",f:"*/ x3 x3 w"},{n:"q2",f:"+- x3 0 q1"},{n:"u1",f:"*/ f1 q2 1"},{n:"y1",f:"+- b 0 u1"},{n:"cx1",f:"*/ x3 1 2"},{n:"cu1",f:"*/ f1 cx1 1"},{n:"cy1",f:"+- b 0 cu1"},{n:"cx2",f:"+- r 0 cx1"},{n:"q1",f:"*/ h a1 100000"},{n:"dy3",f:"+- q1 0 dy1"},{n:"q3",f:"*/ x2 x2 w"},{n:"q4",f:"+- x2 0 q3"},{n:"q5",f:"*/ f1 q4 1"},{n:"u3",f:"+- q5 dy3 0"},{n:"y3",f:"+- b 0 u3"},{n:"q6",f:"+- dy1 dy3 u3"},{n:"q7",f:"+- q6 dy1 0"},{n:"cu3",f:"+- q7 dy3 0"},{n:"cy3",f:"+- b 0 cu3"},{n:"rh",f:"+- b 0 q1"},{n:"q8",f:"*/ dy1 14 16"},{n:"u2",f:"+/ q8 rh 2"},{n:"y2",f:"+- b 0 u2"},{n:"u5",f:"+- q5 rh 0"},{n:"y5",f:"+- b 0 u5"},{n:"u6",f:"+- u3 rh 0"},{n:"y6",f:"+- b 0 u6"},{n:"cx4",f:"*/ x2 1 2"},{n:"q9",f:"*/ f1 cx4 1"},{n:"cu4",f:"+- q9 rh 0"},{n:"cy4",f:"+- b 0 cu4"},{n:"cx5",f:"+- r 0 cx4"},{n:"cu6",f:"+- cu3 rh 0"},{n:"cy6",f:"+- b 0 cu6"},{n:"u7",f:"+- u1 dy3 0"},{n:"y7",f:"+- b 0 u7"},{n:"cu7",f:"+- q1 q1 u7"},{n:"cy7",f:"+- b 0 cu7"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"b"}},{type:"quadBezTo",pts:[{x:"cx1",y:"cy1"},{x:"x3",y:"y1"}]},{type:"lnTo",pt:{x:"x2",y:"y3"}},{type:"quadBezTo",pts:[{x:"hc",y:"cy3"},{x:"x5",y:"y3"}]},{type:"lnTo",pt:{x:"x4",y:"y1"}},{type:"quadBezTo",pts:[{x:"cx2",y:"cy1"},{x:"r",y:"b"}]},{type:"lnTo",pt:{x:"x6",y:"y2"}},{type:"lnTo",pt:{x:"r",y:"q1"}},{type:"quadBezTo",pts:[{x:"cx5",y:"cy4"},{x:"x5",y:"y5"}]},{type:"lnTo",pt:{x:"x5",y:"y6"}},{type:"quadBezTo",pts:[{x:"hc",y:"cy6"},{x:"x2",y:"y6"}]},{type:"lnTo",pt:{x:"x2",y:"y5"}},{type:"quadBezTo",pts:[{x:"cx4",y:"cy4"},{x:"l",y:"q1"}]},{type:"lnTo",pt:{x:"wd8",y:"y2"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"x3",y:"y7"}},{type:"lnTo",pt:{x:"x3",y:"y1"}},{type:"lnTo",pt:{x:"x2",y:"y3"}},{type:"quadBezTo",pts:[{x:"hc",y:"cy3"},{x:"x5",y:"y3"}]},{type:"lnTo",pt:{x:"x4",y:"y1"}},{type:"lnTo",pt:{x:"x4",y:"y7"}},{type:"quadBezTo",pts:[{x:"hc",y:"cy7"},{x:"x3",y:"y7"}]},{type:"close"}],fill:"darkenLess",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"l",y:"b"}},{type:"lnTo",pt:{x:"wd8",y:"y2"}},{type:"lnTo",pt:{x:"l",y:"q1"}},{type:"quadBezTo",pts:[{x:"cx4",y:"cy4"},{x:"x2",y:"y5"}]},{type:"lnTo",pt:{x:"x2",y:"y6"}},{type:"quadBezTo",pts:[{x:"hc",y:"cy6"},{x:"x5",y:"y6"}]},{type:"lnTo",pt:{x:"x5",y:"y5"}},{type:"quadBezTo",pts:[{x:"cx5",y:"cy4"},{x:"r",y:"q1"}]},{type:"lnTo",pt:{x:"x6",y:"y2"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"quadBezTo",pts:[{x:"cx2",y:"cy1"},{x:"x4",y:"y1"}]},{type:"lnTo",pt:{x:"x5",y:"y3"}},{type:"quadBezTo",pts:[{x:"hc",y:"cy3"},{x:"x2",y:"y3"}]},{type:"lnTo",pt:{x:"x3",y:"y1"}},{type:"quadBezTo",pts:[{x:"cx1",y:"cy1"},{x:"l",y:"b"}]},{type:"close"},{type:"moveTo",pt:{x:"x2",y:"y3"}},{type:"lnTo",pt:{x:"x2",y:"y5"}},{type:"moveTo",pt:{x:"x5",y:"y5"}},{type:"lnTo",pt:{x:"x5",y:"y3"}},{type:"moveTo",pt:{x:"x3",y:"y7"}},{type:"lnTo",pt:{x:"x3",y:"y1"}},{type:"moveTo",pt:{x:"x4",y:"y1"}},{type:"lnTo",pt:{x:"x4",y:"y7"}}],fill:"none",extrusionOk:!1,stroke:!0}]},flowChartAlternateProcess:{gdLst:[{n:"x2",f:"+- r 0 ssd6"},{n:"y2",f:"+- b 0 ssd6"},{n:"il",f:"*/ ssd6 29289 100000"},{n:"ir",f:"+- r 0 il"},{n:"ib",f:"+- b 0 il"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"ssd6"}},{type:"arcTo",wR:"ssd6",hR:"ssd6",stAng:"cd2",swAng:"cd4"},{type:"lnTo",pt:{x:"x2",y:"t"}},{type:"arcTo",wR:"ssd6",hR:"ssd6",stAng:"3cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"r",y:"y2"}},{type:"arcTo",wR:"ssd6",hR:"ssd6",stAng:"0",swAng:"cd4"},{type:"lnTo",pt:{x:"ssd6",y:"b"}},{type:"arcTo",wR:"ssd6",hR:"ssd6",stAng:"cd4",swAng:"cd4"},{type:"close"}],extrusionOk:!1,stroke:!0}]},flowChartCollate:{gdLst:[{n:"ir",f:"*/ w 3 4"},{n:"ib",f:"*/ h 3 4"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"0",y:"0"}},{type:"lnTo",pt:{x:"2",y:"0"}},{type:"lnTo",pt:{x:"1",y:"1"}},{type:"lnTo",pt:{x:"2",y:"2"}},{type:"lnTo",pt:{x:"0",y:"2"}},{type:"lnTo",pt:{x:"1",y:"1"}},{type:"close"}],extrusionOk:!1,stroke:!0,w:2,h:2}]},flowChartConnector:{gdLst:[{n:"idx",f:"cos wd2 2700000"},{n:"idy",f:"sin hd2 2700000"},{n:"il",f:"+- hc 0 idx"},{n:"ir",f:"+- hc idx 0"},{n:"it",f:"+- vc 0 idy"},{n:"ib",f:"+- vc idy 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"cd2",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"3cd4",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"0",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"cd4",swAng:"cd4"},{type:"close"}],extrusionOk:!1,stroke:!0}]},flowChartDecision:{gdLst:[{n:"ir",f:"*/ w 3 4"},{n:"ib",f:"*/ h 3 4"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"0",y:"1"}},{type:"lnTo",pt:{x:"1",y:"0"}},{type:"lnTo",pt:{x:"2",y:"1"}},{type:"lnTo",pt:{x:"1",y:"2"}},{type:"close"}],extrusionOk:!1,stroke:!0,w:2,h:2}]},flowChartDelay:{gdLst:[{n:"idx",f:"cos wd2 2700000"},{n:"idy",f:"sin hd2 2700000"},{n:"ir",f:"+- hc idx 0"},{n:"it",f:"+- vc 0 idy"},{n:"ib",f:"+- vc idy 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"hc",y:"t"}},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"3cd4",swAng:"cd2"},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},flowChartDisplay:{gdLst:[{n:"x2",f:"*/ w 5 6"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"0",y:"3"}},{type:"lnTo",pt:{x:"1",y:"0"}},{type:"lnTo",pt:{x:"5",y:"0"}},{type:"arcTo",wR:"1",hR:"3",stAng:"3cd4",swAng:"cd2"},{type:"lnTo",pt:{x:"1",y:"6"}},{type:"close"}],extrusionOk:!1,stroke:!0,w:6,h:6}]},flowChartDocument:{gdLst:[{n:"y1",f:"*/ h 17322 21600"},{n:"y2",f:"*/ h 20172 21600"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"0",y:"0"}},{type:"lnTo",pt:{x:"21600",y:"0"}},{type:"lnTo",pt:{x:"21600",y:"17322"}},{type:"cubicBezTo",pts:[{x:"10800",y:"17322"},{x:"10800",y:"23922"},{x:"0",y:"20172"}]},{type:"close"}],extrusionOk:!1,stroke:!0,w:21600,h:21600}]},flowChartExtract:{gdLst:[{n:"x2",f:"*/ w 3 4"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"0",y:"2"}},{type:"lnTo",pt:{x:"1",y:"0"}},{type:"lnTo",pt:{x:"2",y:"2"}},{type:"close"}],extrusionOk:!1,stroke:!0,w:2,h:2}]},flowChartInputOutput:{gdLst:[{n:"x3",f:"*/ w 2 5"},{n:"x4",f:"*/ w 3 5"},{n:"x5",f:"*/ w 4 5"},{n:"x6",f:"*/ w 9 10"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"0",y:"5"}},{type:"lnTo",pt:{x:"1",y:"0"}},{type:"lnTo",pt:{x:"5",y:"0"}},{type:"lnTo",pt:{x:"4",y:"5"}},{type:"close"}],extrusionOk:!1,stroke:!0,w:5,h:5}]},flowChartInternalStorage:{pathLst:[{defines:[{type:"moveTo",pt:{x:"0",y:"0"}},{type:"lnTo",pt:{x:"1",y:"0"}},{type:"lnTo",pt:{x:"1",y:"1"}},{type:"lnTo",pt:{x:"0",y:"1"}},{type:"close"}],extrusionOk:!1,stroke:!1,w:1,h:1},{defines:[{type:"moveTo",pt:{x:"1",y:"0"}},{type:"lnTo",pt:{x:"1",y:"8"}},{type:"moveTo",pt:{x:"0",y:"1"}},{type:"lnTo",pt:{x:"8",y:"1"}}],fill:"none",extrusionOk:!1,stroke:!0,w:8,h:8},{defines:[{type:"moveTo",pt:{x:"0",y:"0"}},{type:"lnTo",pt:{x:"1",y:"0"}},{type:"lnTo",pt:{x:"1",y:"1"}},{type:"lnTo",pt:{x:"0",y:"1"}},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0,w:1,h:1}]},flowChartMagneticDisk:{gdLst:[{n:"y3",f:"*/ h 5 6"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"0",y:"1"}},{type:"arcTo",wR:"3",hR:"1",stAng:"cd2",swAng:"cd2"},{type:"lnTo",pt:{x:"6",y:"5"}},{type:"arcTo",wR:"3",hR:"1",stAng:"0",swAng:"cd2"},{type:"close"}],extrusionOk:!1,stroke:!1,w:6,h:6},{defines:[{type:"moveTo",pt:{x:"6",y:"1"}},{type:"arcTo",wR:"3",hR:"1",stAng:"0",swAng:"cd2"}],fill:"none",extrusionOk:!1,stroke:!0,w:6,h:6},{defines:[{type:"moveTo",pt:{x:"0",y:"1"}},{type:"arcTo",wR:"3",hR:"1",stAng:"cd2",swAng:"cd2"},{type:"lnTo",pt:{x:"6",y:"5"}},{type:"arcTo",wR:"3",hR:"1",stAng:"0",swAng:"cd2"},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0,w:6,h:6}]},flowChartMagneticDrum:{gdLst:[{n:"x2",f:"*/ w 2 3"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"1",y:"0"}},{type:"lnTo",pt:{x:"5",y:"0"}},{type:"arcTo",wR:"1",hR:"3",stAng:"3cd4",swAng:"cd2"},{type:"lnTo",pt:{x:"1",y:"6"}},{type:"arcTo",wR:"1",hR:"3",stAng:"cd4",swAng:"cd2"},{type:"close"}],extrusionOk:!1,stroke:!1,w:6,h:6},{defines:[{type:"moveTo",pt:{x:"5",y:"6"}},{type:"arcTo",wR:"1",hR:"3",stAng:"cd4",swAng:"cd2"}],fill:"none",extrusionOk:!1,stroke:!0,w:6,h:6},{defines:[{type:"moveTo",pt:{x:"1",y:"0"}},{type:"lnTo",pt:{x:"5",y:"0"}},{type:"arcTo",wR:"1",hR:"3",stAng:"3cd4",swAng:"cd2"},{type:"lnTo",pt:{x:"1",y:"6"}},{type:"arcTo",wR:"1",hR:"3",stAng:"cd4",swAng:"cd2"},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0,w:6,h:6}]},flowChartMagneticTape:{gdLst:[{n:"idx",f:"cos wd2 2700000"},{n:"idy",f:"sin hd2 2700000"},{n:"il",f:"+- hc 0 idx"},{n:"ir",f:"+- hc idx 0"},{n:"it",f:"+- vc 0 idy"},{n:"ib",f:"+- vc idy 0"},{n:"ang1",f:"at2 w h"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"hc",y:"b"}},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"cd4",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"cd2",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"3cd4",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"0",swAng:"ang1"},{type:"lnTo",pt:{x:"r",y:"ib"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},flowChartManualInput:{pathLst:[{defines:[{type:"moveTo",pt:{x:"0",y:"1"}},{type:"lnTo",pt:{x:"5",y:"0"}},{type:"lnTo",pt:{x:"5",y:"5"}},{type:"lnTo",pt:{x:"0",y:"5"}},{type:"close"}],extrusionOk:!1,stroke:!0,w:5,h:5}]},flowChartManualOperation:{gdLst:[{n:"x3",f:"*/ w 4 5"},{n:"x4",f:"*/ w 9 10"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"0",y:"0"}},{type:"lnTo",pt:{x:"5",y:"0"}},{type:"lnTo",pt:{x:"4",y:"5"}},{type:"lnTo",pt:{x:"1",y:"5"}},{type:"close"}],extrusionOk:!1,stroke:!0,w:5,h:5}]},flowChartMerge:{gdLst:[{n:"x2",f:"*/ w 3 4"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"0",y:"0"}},{type:"lnTo",pt:{x:"2",y:"0"}},{type:"lnTo",pt:{x:"1",y:"2"}},{type:"close"}],extrusionOk:!1,stroke:!0,w:2,h:2}]},flowChartMultidocument:{gdLst:[{n:"y2",f:"*/ h 3675 21600"},{n:"y8",f:"*/ h 20782 21600"},{n:"x3",f:"*/ w 9298 21600"},{n:"x4",f:"*/ w 12286 21600"},{n:"x5",f:"*/ w 18595 21600"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"0",y:"20782"}},{type:"cubicBezTo",pts:[{x:"9298",y:"23542"},{x:"9298",y:"18022"},{x:"18595",y:"18022"}]},{type:"lnTo",pt:{x:"18595",y:"3675"}},{type:"lnTo",pt:{x:"0",y:"3675"}},{type:"close"},{type:"moveTo",pt:{x:"1532",y:"3675"}},{type:"lnTo",pt:{x:"1532",y:"1815"}},{type:"lnTo",pt:{x:"20000",y:"1815"}},{type:"lnTo",pt:{x:"20000",y:"16252"}},{type:"cubicBezTo",pts:[{x:"19298",y:"16252"},{x:"18595",y:"16352"},{x:"18595",y:"16352"}]},{type:"lnTo",pt:{x:"18595",y:"3675"}},{type:"close"},{type:"moveTo",pt:{x:"2972",y:"1815"}},{type:"lnTo",pt:{x:"2972",y:"0"}},{type:"lnTo",pt:{x:"21600",y:"0"}},{type:"lnTo",pt:{x:"21600",y:"14392"}},{type:"cubicBezTo",pts:[{x:"20800",y:"14392"},{x:"20000",y:"14467"},{x:"20000",y:"14467"}]},{type:"lnTo",pt:{x:"20000",y:"1815"}},{type:"close"}],extrusionOk:!1,stroke:!1,w:21600,h:21600},{defines:[{type:"moveTo",pt:{x:"0",y:"3675"}},{type:"lnTo",pt:{x:"18595",y:"3675"}},{type:"lnTo",pt:{x:"18595",y:"18022"}},{type:"cubicBezTo",pts:[{x:"9298",y:"18022"},{x:"9298",y:"23542"},{x:"0",y:"20782"}]},{type:"close"},{type:"moveTo",pt:{x:"1532",y:"3675"}},{type:"lnTo",pt:{x:"1532",y:"1815"}},{type:"lnTo",pt:{x:"20000",y:"1815"}},{type:"lnTo",pt:{x:"20000",y:"16252"}},{type:"cubicBezTo",pts:[{x:"19298",y:"16252"},{x:"18595",y:"16352"},{x:"18595",y:"16352"}]},{type:"moveTo",pt:{x:"2972",y:"1815"}},{type:"lnTo",pt:{x:"2972",y:"0"}},{type:"lnTo",pt:{x:"21600",y:"0"}},{type:"lnTo",pt:{x:"21600",y:"14392"}},{type:"cubicBezTo",pts:[{x:"20800",y:"14392"},{x:"20000",y:"14467"},{x:"20000",y:"14467"}]}],fill:"none",extrusionOk:!1,stroke:!0,w:21600,h:21600},{defines:[{type:"moveTo",pt:{x:"0",y:"20782"}},{type:"cubicBezTo",pts:[{x:"9298",y:"23542"},{x:"9298",y:"18022"},{x:"18595",y:"18022"}]},{type:"lnTo",pt:{x:"18595",y:"16352"}},{type:"cubicBezTo",pts:[{x:"18595",y:"16352"},{x:"19298",y:"16252"},{x:"20000",y:"16252"}]},{type:"lnTo",pt:{x:"20000",y:"14467"}},{type:"cubicBezTo",pts:[{x:"20000",y:"14467"},{x:"20800",y:"14392"},{x:"21600",y:"14392"}]},{type:"lnTo",pt:{x:"21600",y:"0"}},{type:"lnTo",pt:{x:"2972",y:"0"}},{type:"lnTo",pt:{x:"2972",y:"1815"}},{type:"lnTo",pt:{x:"1532",y:"1815"}},{type:"lnTo",pt:{x:"1532",y:"3675"}},{type:"lnTo",pt:{x:"0",y:"3675"}},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!1,w:21600,h:21600}]},flowChartOfflineStorage:{gdLst:[{n:"x4",f:"*/ w 3 4"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"0",y:"0"}},{type:"lnTo",pt:{x:"2",y:"0"}},{type:"lnTo",pt:{x:"1",y:"2"}},{type:"close"}],extrusionOk:!1,stroke:!1,w:2,h:2},{defines:[{type:"moveTo",pt:{x:"2",y:"4"}},{type:"lnTo",pt:{x:"3",y:"4"}}],fill:"none",extrusionOk:!1,stroke:!0,w:5,h:5},{defines:[{type:"moveTo",pt:{x:"0",y:"0"}},{type:"lnTo",pt:{x:"2",y:"0"}},{type:"lnTo",pt:{x:"1",y:"2"}},{type:"close"}],fill:"none",extrusionOk:!0,stroke:!0,w:2,h:2}]},flowChartOffpageConnector:{gdLst:[{n:"y1",f:"*/ h 4 5"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"0",y:"0"}},{type:"lnTo",pt:{x:"10",y:"0"}},{type:"lnTo",pt:{x:"10",y:"8"}},{type:"lnTo",pt:{x:"5",y:"10"}},{type:"lnTo",pt:{x:"0",y:"8"}},{type:"close"}],extrusionOk:!1,stroke:!0,w:10,h:10}]},flowChartOnlineStorage:{gdLst:[{n:"x2",f:"*/ w 5 6"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"1",y:"0"}},{type:"lnTo",pt:{x:"6",y:"0"}},{type:"arcTo",wR:"1",hR:"3",stAng:"3cd4",swAng:"-10800000"},{type:"lnTo",pt:{x:"1",y:"6"}},{type:"arcTo",wR:"1",hR:"3",stAng:"cd4",swAng:"cd2"},{type:"close"}],extrusionOk:!1,stroke:!0,w:6,h:6}]},flowChartOr:{gdLst:[{n:"idx",f:"cos wd2 2700000"},{n:"idy",f:"sin hd2 2700000"},{n:"il",f:"+- hc 0 idx"},{n:"ir",f:"+- hc idx 0"},{n:"it",f:"+- vc 0 idy"},{n:"ib",f:"+- vc idy 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"cd2",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"3cd4",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"0",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"cd4",swAng:"cd4"},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"hc",y:"t"}},{type:"lnTo",pt:{x:"hc",y:"b"}},{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"lnTo",pt:{x:"r",y:"vc"}}],fill:"none",extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"cd2",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"3cd4",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"0",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"cd4",swAng:"cd4"},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0}]},flowChartPredefinedProcess:{gdLst:[{n:"x2",f:"*/ w 7 8"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"0",y:"0"}},{type:"lnTo",pt:{x:"1",y:"0"}},{type:"lnTo",pt:{x:"1",y:"1"}},{type:"lnTo",pt:{x:"0",y:"1"}},{type:"close"}],extrusionOk:!1,stroke:!1,w:1,h:1},{defines:[{type:"moveTo",pt:{x:"1",y:"0"}},{type:"lnTo",pt:{x:"1",y:"8"}},{type:"moveTo",pt:{x:"7",y:"0"}},{type:"lnTo",pt:{x:"7",y:"8"}}],fill:"none",extrusionOk:!1,stroke:!0,w:8,h:8},{defines:[{type:"moveTo",pt:{x:"0",y:"0"}},{type:"lnTo",pt:{x:"1",y:"0"}},{type:"lnTo",pt:{x:"1",y:"1"}},{type:"lnTo",pt:{x:"0",y:"1"}},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0,w:1,h:1}]},flowChartPreparation:{gdLst:[{n:"x2",f:"*/ w 4 5"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"0",y:"5"}},{type:"lnTo",pt:{x:"2",y:"0"}},{type:"lnTo",pt:{x:"8",y:"0"}},{type:"lnTo",pt:{x:"10",y:"5"}},{type:"lnTo",pt:{x:"8",y:"10"}},{type:"lnTo",pt:{x:"2",y:"10"}},{type:"close"}],extrusionOk:!1,stroke:!0,w:10,h:10}]},flowChartProcess:{pathLst:[{defines:[{type:"moveTo",pt:{x:"0",y:"0"}},{type:"lnTo",pt:{x:"1",y:"0"}},{type:"lnTo",pt:{x:"1",y:"1"}},{type:"lnTo",pt:{x:"0",y:"1"}},{type:"close"}],extrusionOk:!1,stroke:!0,w:1,h:1}]},flowChartPunchedCard:{pathLst:[{defines:[{type:"moveTo",pt:{x:"0",y:"1"}},{type:"lnTo",pt:{x:"1",y:"0"}},{type:"lnTo",pt:{x:"5",y:"0"}},{type:"lnTo",pt:{x:"5",y:"5"}},{type:"lnTo",pt:{x:"0",y:"5"}},{type:"close"}],extrusionOk:!1,stroke:!0,w:5,h:5}]},flowChartPunchedTape:{gdLst:[{n:"y2",f:"*/ h 9 10"},{n:"ib",f:"*/ h 4 5"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"0",y:"2"}},{type:"arcTo",wR:"5",hR:"2",stAng:"cd2",swAng:"-10800000"},{type:"arcTo",wR:"5",hR:"2",stAng:"cd2",swAng:"cd2"},{type:"lnTo",pt:{x:"20",y:"18"}},{type:"arcTo",wR:"5",hR:"2",stAng:"0",swAng:"-10800000"},{type:"arcTo",wR:"5",hR:"2",stAng:"0",swAng:"cd2"},{type:"close"}],extrusionOk:!1,stroke:!0,w:20,h:20}]},flowChartSort:{gdLst:[{n:"ir",f:"*/ w 3 4"},{n:"ib",f:"*/ h 3 4"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"0",y:"1"}},{type:"lnTo",pt:{x:"1",y:"0"}},{type:"lnTo",pt:{x:"2",y:"1"}},{type:"lnTo",pt:{x:"1",y:"2"}},{type:"close"}],extrusionOk:!1,stroke:!1,w:2,h:2},{defines:[{type:"moveTo",pt:{x:"0",y:"1"}},{type:"lnTo",pt:{x:"2",y:"1"}}],fill:"none",extrusionOk:!1,stroke:!0,w:2,h:2},{defines:[{type:"moveTo",pt:{x:"0",y:"1"}},{type:"lnTo",pt:{x:"1",y:"0"}},{type:"lnTo",pt:{x:"2",y:"1"}},{type:"lnTo",pt:{x:"1",y:"2"}},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0,w:2,h:2}]},flowChartSummingJunction:{gdLst:[{n:"idx",f:"cos wd2 2700000"},{n:"idy",f:"sin hd2 2700000"},{n:"il",f:"+- hc 0 idx"},{n:"ir",f:"+- hc idx 0"},{n:"it",f:"+- vc 0 idy"},{n:"ib",f:"+- vc idy 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"cd2",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"3cd4",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"0",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"cd4",swAng:"cd4"},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"il",y:"it"}},{type:"lnTo",pt:{x:"ir",y:"ib"}},{type:"moveTo",pt:{x:"ir",y:"it"}},{type:"lnTo",pt:{x:"il",y:"ib"}}],fill:"none",extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"cd2",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"3cd4",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"0",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"cd4",swAng:"cd4"},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0}]},flowChartTerminator:{gdLst:[{n:"il",f:"*/ w 1018 21600"},{n:"ir",f:"*/ w 20582 21600"},{n:"it",f:"*/ h 3163 21600"},{n:"ib",f:"*/ h 18437 21600"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"3475",y:"0"}},{type:"lnTo",pt:{x:"18125",y:"0"}},{type:"arcTo",wR:"3475",hR:"10800",stAng:"3cd4",swAng:"cd2"},{type:"lnTo",pt:{x:"3475",y:"21600"}},{type:"arcTo",wR:"3475",hR:"10800",stAng:"cd4",swAng:"cd2"},{type:"close"}],extrusionOk:!1,stroke:!0,w:21600,h:21600}]},foldedCorner:{avLst:[{n:"adj",f:"val 16667"}],gdLst:[{n:"a",f:"pin 0 adj 50000"},{n:"dy2",f:"*/ ss a 100000"},{n:"dy1",f:"*/ dy2 1 5"},{n:"x1",f:"+- r 0 dy2"},{n:"x2",f:"+- x1 dy1 0"},{n:"y2",f:"+- b 0 dy2"},{n:"y1",f:"+- y2 dy1 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"y2"}},{type:"lnTo",pt:{x:"x1",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"x1",y:"b"}},{type:"lnTo",pt:{x:"x2",y:"y1"}},{type:"lnTo",pt:{x:"r",y:"y2"}},{type:"close"}],fill:"darkenLess",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"x1",y:"b"}},{type:"lnTo",pt:{x:"x2",y:"y1"}},{type:"lnTo",pt:{x:"r",y:"y2"}},{type:"lnTo",pt:{x:"x1",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"lnTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"y2"}}],fill:"none",extrusionOk:!1,stroke:!0}]},frame:{avLst:[{n:"adj1",f:"val 12500"}],gdLst:[{n:"a1",f:"pin 0 adj1 50000"},{n:"x1",f:"*/ ss a1 100000"},{n:"x4",f:"+- r 0 x1"},{n:"y4",f:"+- b 0 x1"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"},{type:"moveTo",pt:{x:"x1",y:"x1"}},{type:"lnTo",pt:{x:"x1",y:"y4"}},{type:"lnTo",pt:{x:"x4",y:"y4"}},{type:"lnTo",pt:{x:"x4",y:"x1"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},funnel:{gdLst:[{n:"d",f:"*/ ss 1 20"},{n:"rw2",f:"+- wd2 0 d"},{n:"rh2",f:"+- hd4 0 d"},{n:"t1",f:"cos wd2 480000"},{n:"t2",f:"sin hd4 480000"},{n:"da",f:"at2 t1 t2"},{n:"2da",f:"*/ da 2 1"},{n:"stAng1",f:"+- cd2 0 da"},{n:"swAng1",f:"+- cd2 2da 0"},{n:"swAng3",f:"+- cd2 0 2da"},{n:"rw3",f:"*/ wd2 1 4"},{n:"rh3",f:"*/ hd4 1 4"},{n:"ct1",f:"cos hd4 stAng1"},{n:"st1",f:"sin wd2 stAng1"},{n:"m1",f:"mod ct1 st1 0"},{n:"n1",f:"*/ wd2 hd4 m1"},{n:"dx1",f:"cos n1 stAng1"},{n:"dy1",f:"sin n1 stAng1"},{n:"x1",f:"+- hc dx1 0"},{n:"y1",f:"+- hd4 dy1 0"},{n:"ct3",f:"cos rh3 da"},{n:"st3",f:"sin rw3 da"},{n:"m3",f:"mod ct3 st3 0"},{n:"n3",f:"*/ rw3 rh3 m3"},{n:"dx3",f:"cos n3 da"},{n:"dy3",f:"sin n3 da"},{n:"x3",f:"+- hc dx3 0"},{n:"vc3",f:"+- b 0 rh3"},{n:"y2",f:"+- vc3 dy3 0"},{n:"x2",f:"+- wd2 0 rw2"},{n:"cd",f:"*/ cd2 2 1"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"x1",y:"y1"}},{type:"arcTo",wR:"wd2",hR:"hd4",stAng:"stAng1",swAng:"swAng1"},{type:"lnTo",pt:{x:"x3",y:"y2"}},{type:"arcTo",wR:"rw3",hR:"rh3",stAng:"da",swAng:"swAng3"},{type:"close"},{type:"moveTo",pt:{x:"x2",y:"hd4"}},{type:"arcTo",wR:"rw2",hR:"rh2",stAng:"cd2",swAng:"-21600000"},{type:"close"}],extrusionOk:!1,stroke:!0}]},gear6:{avLst:[{n:"adj1",f:"val 15000"},{n:"adj2",f:"val 3526"}],gdLst:[{n:"a1",f:"pin 0 adj1 20000"},{n:"a2",f:"pin 0 adj2 5358"},{n:"th",f:"*/ ss a1 100000"},{n:"lFD",f:"*/ ss a2 100000"},{n:"th2",f:"*/ th 1 2"},{n:"l2",f:"*/ lFD 1 2"},{n:"l3",f:"+- th2 l2 0"},{n:"rh",f:"+- hd2 0 th"},{n:"rw",f:"+- wd2 0 th"},{n:"dr",f:"+- rw 0 rh"},{n:"maxr",f:"?: dr rh rw"},{n:"ha",f:"at2 maxr l3"},{n:"aA1",f:"+- 19800000 0 ha"},{n:"aD1",f:"+- 19800000 ha 0"},{n:"ta11",f:"cos rw aA1"},{n:"ta12",f:"sin rh aA1"},{n:"bA1",f:"at2 ta11 ta12"},{n:"cta1",f:"cos rh bA1"},{n:"sta1",f:"sin rw bA1"},{n:"ma1",f:"mod cta1 sta1 0"},{n:"na1",f:"*/ rw rh ma1"},{n:"dxa1",f:"cos na1 bA1"},{n:"dya1",f:"sin na1 bA1"},{n:"xA1",f:"+- hc dxa1 0"},{n:"yA1",f:"+- vc dya1 0"},{n:"td11",f:"cos rw aD1"},{n:"td12",f:"sin rh aD1"},{n:"bD1",f:"at2 td11 td12"},{n:"ctd1",f:"cos rh bD1"},{n:"std1",f:"sin rw bD1"},{n:"md1",f:"mod ctd1 std1 0"},{n:"nd1",f:"*/ rw rh md1"},{n:"dxd1",f:"cos nd1 bD1"},{n:"dyd1",f:"sin nd1 bD1"},{n:"xD1",f:"+- hc dxd1 0"},{n:"yD1",f:"+- vc dyd1 0"},{n:"xAD1",f:"+- xA1 0 xD1"},{n:"yAD1",f:"+- yA1 0 yD1"},{n:"lAD1",f:"mod xAD1 yAD1 0"},{n:"a1",f:"at2 yAD1 xAD1"},{n:"dxF1",f:"sin lFD a1"},{n:"dyF1",f:"cos lFD a1"},{n:"xF1",f:"+- xD1 dxF1 0"},{n:"yF1",f:"+- yD1 dyF1 0"},{n:"xE1",f:"+- xA1 0 dxF1"},{n:"yE1",f:"+- yA1 0 dyF1"},{n:"yC1t",f:"sin th a1"},{n:"xC1t",f:"cos th a1"},{n:"yC1",f:"+- yF1 yC1t 0"},{n:"xC1",f:"+- xF1 0 xC1t"},{n:"yB1",f:"+- yE1 yC1t 0"},{n:"xB1",f:"+- xE1 0 xC1t"},{n:"aD6",f:"+- 3cd4 ha 0"},{n:"td61",f:"cos rw aD6"},{n:"td62",f:"sin rh aD6"},{n:"bD6",f:"at2 td61 td62"},{n:"ctd6",f:"cos rh bD6"},{n:"std6",f:"sin rw bD6"},{n:"md6",f:"mod ctd6 std6 0"},{n:"nd6",f:"*/ rw rh md6"},{n:"dxd6",f:"cos nd6 bD6"},{n:"dyd6",f:"sin nd6 bD6"},{n:"xD6",f:"+- hc dxd6 0"},{n:"yD6",f:"+- vc dyd6 0"},{n:"xA6",f:"+- hc 0 dxd6"},{n:"xF6",f:"+- xD6 0 lFD"},{n:"xE6",f:"+- xA6 lFD 0"},{n:"yC6",f:"+- yD6 0 th"},{n:"swAng1",f:"+- bA1 0 bD6"},{n:"aA2",f:"+- 1800000 0 ha"},{n:"aD2",f:"+- 1800000 ha 0"},{n:"ta21",f:"cos rw aA2"},{n:"ta22",f:"sin rh aA2"},{n:"bA2",f:"at2 ta21 ta22"},{n:"yA2",f:"+- h 0 yD1"},{n:"td21",f:"cos rw aD2"},{n:"td22",f:"sin rh aD2"},{n:"bD2",f:"at2 td21 td22"},{n:"yD2",f:"+- h 0 yA1"},{n:"yC2",f:"+- h 0 yB1"},{n:"yB2",f:"+- h 0 yC1"},{n:"xB2",f:"val xC1"},{n:"swAng2",f:"+- bA2 0 bD1"},{n:"aD3",f:"+- cd4 ha 0"},{n:"td31",f:"cos rw aD3"},{n:"td32",f:"sin rh aD3"},{n:"bD3",f:"at2 td31 td32"},{n:"yD3",f:"+- h 0 yD6"},{n:"yB3",f:"+- h 0 yC6"},{n:"aD4",f:"+- 9000000 ha 0"},{n:"td41",f:"cos rw aD4"},{n:"td42",f:"sin rh aD4"},{n:"bD4",f:"at2 td41 td42"},{n:"xD4",f:"+- w 0 xD1"},{n:"xC4",f:"+- w 0 xC1"},{n:"xB4",f:"+- w 0 xB1"},{n:"aD5",f:"+- 12600000 ha 0"},{n:"td51",f:"cos rw aD5"},{n:"td52",f:"sin rh aD5"},{n:"bD5",f:"at2 td51 td52"},{n:"xD5",f:"+- w 0 xA1"},{n:"xC5",f:"+- w 0 xB1"},{n:"xB5",f:"+- w 0 xC1"},{n:"xCxn1",f:"+/ xB1 xC1 2"},{n:"yCxn1",f:"+/ yB1 yC1 2"},{n:"yCxn2",f:"+- b 0 yCxn1"},{n:"xCxn4",f:"+/ r 0 xCxn1"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"xA1",y:"yA1"}},{type:"lnTo",pt:{x:"xB1",y:"yB1"}},{type:"lnTo",pt:{x:"xC1",y:"yC1"}},{type:"lnTo",pt:{x:"xD1",y:"yD1"}},{type:"arcTo",wR:"rw",hR:"rh",stAng:"bD1",swAng:"swAng2"},{type:"lnTo",pt:{x:"xC1",y:"yB2"}},{type:"lnTo",pt:{x:"xB1",y:"yC2"}},{type:"lnTo",pt:{x:"xA1",y:"yD2"}},{type:"arcTo",wR:"rw",hR:"rh",stAng:"bD2",swAng:"swAng1"},{type:"lnTo",pt:{x:"xF6",y:"yB3"}},{type:"lnTo",pt:{x:"xE6",y:"yB3"}},{type:"lnTo",pt:{x:"xA6",y:"yD3"}},{type:"arcTo",wR:"rw",hR:"rh",stAng:"bD3",swAng:"swAng1"},{type:"lnTo",pt:{x:"xB4",y:"yC2"}},{type:"lnTo",pt:{x:"xC4",y:"yB2"}},{type:"lnTo",pt:{x:"xD4",y:"yA2"}},{type:"arcTo",wR:"rw",hR:"rh",stAng:"bD4",swAng:"swAng2"},{type:"lnTo",pt:{x:"xB5",y:"yC1"}},{type:"lnTo",pt:{x:"xC5",y:"yB1"}},{type:"lnTo",pt:{x:"xD5",y:"yA1"}},{type:"arcTo",wR:"rw",hR:"rh",stAng:"bD5",swAng:"swAng1"},{type:"lnTo",pt:{x:"xE6",y:"yC6"}},{type:"lnTo",pt:{x:"xF6",y:"yC6"}},{type:"lnTo",pt:{x:"xD6",y:"yD6"}},{type:"arcTo",wR:"rw",hR:"rh",stAng:"bD6",swAng:"swAng1"},{type:"close"}],extrusionOk:!1,stroke:!0}]},gear9:{avLst:[{n:"adj1",f:"val 10000"},{n:"adj2",f:"val 1763"}],gdLst:[{n:"a1",f:"pin 0 adj1 20000"},{n:"a2",f:"pin 0 adj2 2679"},{n:"th",f:"*/ ss a1 100000"},{n:"lFD",f:"*/ ss a2 100000"},{n:"th2",f:"*/ th 1 2"},{n:"l2",f:"*/ lFD 1 2"},{n:"l3",f:"+- th2 l2 0"},{n:"rh",f:"+- hd2 0 th"},{n:"rw",f:"+- wd2 0 th"},{n:"dr",f:"+- rw 0 rh"},{n:"maxr",f:"?: dr rh rw"},{n:"ha",f:"at2 maxr l3"},{n:"aA1",f:"+- 18600000 0 ha"},{n:"aD1",f:"+- 18600000 ha 0"},{n:"ta11",f:"cos rw aA1"},{n:"ta12",f:"sin rh aA1"},{n:"bA1",f:"at2 ta11 ta12"},{n:"cta1",f:"cos rh bA1"},{n:"sta1",f:"sin rw bA1"},{n:"ma1",f:"mod cta1 sta1 0"},{n:"na1",f:"*/ rw rh ma1"},{n:"dxa1",f:"cos na1 bA1"},{n:"dya1",f:"sin na1 bA1"},{n:"xA1",f:"+- hc dxa1 0"},{n:"yA1",f:"+- vc dya1 0"},{n:"td11",f:"cos rw aD1"},{n:"td12",f:"sin rh aD1"},{n:"bD1",f:"at2 td11 td12"},{n:"ctd1",f:"cos rh bD1"},{n:"std1",f:"sin rw bD1"},{n:"md1",f:"mod ctd1 std1 0"},{n:"nd1",f:"*/ rw rh md1"},{n:"dxd1",f:"cos nd1 bD1"},{n:"dyd1",f:"sin nd1 bD1"},{n:"xD1",f:"+- hc dxd1 0"},{n:"yD1",f:"+- vc dyd1 0"},{n:"xAD1",f:"+- xA1 0 xD1"},{n:"yAD1",f:"+- yA1 0 yD1"},{n:"lAD1",f:"mod xAD1 yAD1 0"},{n:"a1",f:"at2 yAD1 xAD1"},{n:"dxF1",f:"sin lFD a1"},{n:"dyF1",f:"cos lFD a1"},{n:"xF1",f:"+- xD1 dxF1 0"},{n:"yF1",f:"+- yD1 dyF1 0"},{n:"xE1",f:"+- xA1 0 dxF1"},{n:"yE1",f:"+- yA1 0 dyF1"},{n:"yC1t",f:"sin th a1"},{n:"xC1t",f:"cos th a1"},{n:"yC1",f:"+- yF1 yC1t 0"},{n:"xC1",f:"+- xF1 0 xC1t"},{n:"yB1",f:"+- yE1 yC1t 0"},{n:"xB1",f:"+- xE1 0 xC1t"},{n:"aA2",f:"+- 21000000 0 ha"},{n:"aD2",f:"+- 21000000 ha 0"},{n:"ta21",f:"cos rw aA2"},{n:"ta22",f:"sin rh aA2"},{n:"bA2",f:"at2 ta21 ta22"},{n:"cta2",f:"cos rh bA2"},{n:"sta2",f:"sin rw bA2"},{n:"ma2",f:"mod cta2 sta2 0"},{n:"na2",f:"*/ rw rh ma2"},{n:"dxa2",f:"cos na2 bA2"},{n:"dya2",f:"sin na2 bA2"},{n:"xA2",f:"+- hc dxa2 0"},{n:"yA2",f:"+- vc dya2 0"},{n:"td21",f:"cos rw aD2"},{n:"td22",f:"sin rh aD2"},{n:"bD2",f:"at2 td21 td22"},{n:"ctd2",f:"cos rh bD2"},{n:"std2",f:"sin rw bD2"},{n:"md2",f:"mod ctd2 std2 0"},{n:"nd2",f:"*/ rw rh md2"},{n:"dxd2",f:"cos nd2 bD2"},{n:"dyd2",f:"sin nd2 bD2"},{n:"xD2",f:"+- hc dxd2 0"},{n:"yD2",f:"+- vc dyd2 0"},{n:"xAD2",f:"+- xA2 0 xD2"},{n:"yAD2",f:"+- yA2 0 yD2"},{n:"lAD2",f:"mod xAD2 yAD2 0"},{n:"a2",f:"at2 yAD2 xAD2"},{n:"dxF2",f:"sin lFD a2"},{n:"dyF2",f:"cos lFD a2"},{n:"xF2",f:"+- xD2 dxF2 0"},{n:"yF2",f:"+- yD2 dyF2 0"},{n:"xE2",f:"+- xA2 0 dxF2"},{n:"yE2",f:"+- yA2 0 dyF2"},{n:"yC2t",f:"sin th a2"},{n:"xC2t",f:"cos th a2"},{n:"yC2",f:"+- yF2 yC2t 0"},{n:"xC2",f:"+- xF2 0 xC2t"},{n:"yB2",f:"+- yE2 yC2t 0"},{n:"xB2",f:"+- xE2 0 xC2t"},{n:"swAng1",f:"+- bA2 0 bD1"},{n:"aA3",f:"+- 1800000 0 ha"},{n:"aD3",f:"+- 1800000 ha 0"},{n:"ta31",f:"cos rw aA3"},{n:"ta32",f:"sin rh aA3"},{n:"bA3",f:"at2 ta31 ta32"},{n:"cta3",f:"cos rh bA3"},{n:"sta3",f:"sin rw bA3"},{n:"ma3",f:"mod cta3 sta3 0"},{n:"na3",f:"*/ rw rh ma3"},{n:"dxa3",f:"cos na3 bA3"},{n:"dya3",f:"sin na3 bA3"},{n:"xA3",f:"+- hc dxa3 0"},{n:"yA3",f:"+- vc dya3 0"},{n:"td31",f:"cos rw aD3"},{n:"td32",f:"sin rh aD3"},{n:"bD3",f:"at2 td31 td32"},{n:"ctd3",f:"cos rh bD3"},{n:"std3",f:"sin rw bD3"},{n:"md3",f:"mod ctd3 std3 0"},{n:"nd3",f:"*/ rw rh md3"},{n:"dxd3",f:"cos nd3 bD3"},{n:"dyd3",f:"sin nd3 bD3"},{n:"xD3",f:"+- hc dxd3 0"},{n:"yD3",f:"+- vc dyd3 0"},{n:"xAD3",f:"+- xA3 0 xD3"},{n:"yAD3",f:"+- yA3 0 yD3"},{n:"lAD3",f:"mod xAD3 yAD3 0"},{n:"a3",f:"at2 yAD3 xAD3"},{n:"dxF3",f:"sin lFD a3"},{n:"dyF3",f:"cos lFD a3"},{n:"xF3",f:"+- xD3 dxF3 0"},{n:"yF3",f:"+- yD3 dyF3 0"},{n:"xE3",f:"+- xA3 0 dxF3"},{n:"yE3",f:"+- yA3 0 dyF3"},{n:"yC3t",f:"sin th a3"},{n:"xC3t",f:"cos th a3"},{n:"yC3",f:"+- yF3 yC3t 0"},{n:"xC3",f:"+- xF3 0 xC3t"},{n:"yB3",f:"+- yE3 yC3t 0"},{n:"xB3",f:"+- xE3 0 xC3t"},{n:"swAng2",f:"+- bA3 0 bD2"},{n:"aA4",f:"+- 4200000 0 ha"},{n:"aD4",f:"+- 4200000 ha 0"},{n:"ta41",f:"cos rw aA4"},{n:"ta42",f:"sin rh aA4"},{n:"bA4",f:"at2 ta41 ta42"},{n:"cta4",f:"cos rh bA4"},{n:"sta4",f:"sin rw bA4"},{n:"ma4",f:"mod cta4 sta4 0"},{n:"na4",f:"*/ rw rh ma4"},{n:"dxa4",f:"cos na4 bA4"},{n:"dya4",f:"sin na4 bA4"},{n:"xA4",f:"+- hc dxa4 0"},{n:"yA4",f:"+- vc dya4 0"},{n:"td41",f:"cos rw aD4"},{n:"td42",f:"sin rh aD4"},{n:"bD4",f:"at2 td41 td42"},{n:"ctd4",f:"cos rh bD4"},{n:"std4",f:"sin rw bD4"},{n:"md4",f:"mod ctd4 std4 0"},{n:"nd4",f:"*/ rw rh md4"},{n:"dxd4",f:"cos nd4 bD4"},{n:"dyd4",f:"sin nd4 bD4"},{n:"xD4",f:"+- hc dxd4 0"},{n:"yD4",f:"+- vc dyd4 0"},{n:"xAD4",f:"+- xA4 0 xD4"},{n:"yAD4",f:"+- yA4 0 yD4"},{n:"lAD4",f:"mod xAD4 yAD4 0"},{n:"a4",f:"at2 yAD4 xAD4"},{n:"dxF4",f:"sin lFD a4"},{n:"dyF4",f:"cos lFD a4"},{n:"xF4",f:"+- xD4 dxF4 0"},{n:"yF4",f:"+- yD4 dyF4 0"},{n:"xE4",f:"+- xA4 0 dxF4"},{n:"yE4",f:"+- yA4 0 dyF4"},{n:"yC4t",f:"sin th a4"},{n:"xC4t",f:"cos th a4"},{n:"yC4",f:"+- yF4 yC4t 0"},{n:"xC4",f:"+- xF4 0 xC4t"},{n:"yB4",f:"+- yE4 yC4t 0"},{n:"xB4",f:"+- xE4 0 xC4t"},{n:"swAng3",f:"+- bA4 0 bD3"},{n:"aA5",f:"+- 6600000 0 ha"},{n:"aD5",f:"+- 6600000 ha 0"},{n:"ta51",f:"cos rw aA5"},{n:"ta52",f:"sin rh aA5"},{n:"bA5",f:"at2 ta51 ta52"},{n:"td51",f:"cos rw aD5"},{n:"td52",f:"sin rh aD5"},{n:"bD5",f:"at2 td51 td52"},{n:"xD5",f:"+- w 0 xA4"},{n:"xC5",f:"+- w 0 xB4"},{n:"xB5",f:"+- w 0 xC4"},{n:"swAng4",f:"+- bA5 0 bD4"},{n:"aD6",f:"+- 9000000 ha 0"},{n:"td61",f:"cos rw aD6"},{n:"td62",f:"sin rh aD6"},{n:"bD6",f:"at2 td61 td62"},{n:"xD6",f:"+- w 0 xA3"},{n:"xC6",f:"+- w 0 xB3"},{n:"xB6",f:"+- w 0 xC3"},{n:"aD7",f:"+- 11400000 ha 0"},{n:"td71",f:"cos rw aD7"},{n:"td72",f:"sin rh aD7"},{n:"bD7",f:"at2 td71 td72"},{n:"xD7",f:"+- w 0 xA2"},{n:"xC7",f:"+- w 0 xB2"},{n:"xB7",f:"+- w 0 xC2"},{n:"aD8",f:"+- 13800000 ha 0"},{n:"td81",f:"cos rw aD8"},{n:"td82",f:"sin rh aD8"},{n:"bD8",f:"at2 td81 td82"},{n:"xA8",f:"+- w 0 xD1"},{n:"xD8",f:"+- w 0 xA1"},{n:"xC8",f:"+- w 0 xB1"},{n:"xB8",f:"+- w 0 xC1"},{n:"aA9",f:"+- 3cd4 0 ha"},{n:"aD9",f:"+- 3cd4 ha 0"},{n:"td91",f:"cos rw aD9"},{n:"td92",f:"sin rh aD9"},{n:"bD9",f:"at2 td91 td92"},{n:"ctd9",f:"cos rh bD9"},{n:"std9",f:"sin rw bD9"},{n:"md9",f:"mod ctd9 std9 0"},{n:"nd9",f:"*/ rw rh md9"},{n:"dxd9",f:"cos nd9 bD9"},{n:"dyd9",f:"sin nd9 bD9"},{n:"xD9",f:"+- hc dxd9 0"},{n:"yD9",f:"+- vc dyd9 0"},{n:"ta91",f:"cos rw aA9"},{n:"ta92",f:"sin rh aA9"},{n:"bA9",f:"at2 ta91 ta92"},{n:"xA9",f:"+- hc 0 dxd9"},{n:"xF9",f:"+- xD9 0 lFD"},{n:"xE9",f:"+- xA9 lFD 0"},{n:"yC9",f:"+- yD9 0 th"},{n:"swAng5",f:"+- bA9 0 bD8"},{n:"xCxn1",f:"+/ xB1 xC1 2"},{n:"yCxn1",f:"+/ yB1 yC1 2"},{n:"xCxn2",f:"+/ xB2 xC2 2"},{n:"yCxn2",f:"+/ yB2 yC2 2"},{n:"xCxn3",f:"+/ xB3 xC3 2"},{n:"yCxn3",f:"+/ yB3 yC3 2"},{n:"xCxn4",f:"+/ xB4 xC4 2"},{n:"yCxn4",f:"+/ yB4 yC4 2"},{n:"xCxn5",f:"+/ r 0 xCxn4"},{n:"xCxn6",f:"+/ r 0 xCxn3"},{n:"xCxn7",f:"+/ r 0 xCxn2"},{n:"xCxn8",f:"+/ r 0 xCxn1"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"xA1",y:"yA1"}},{type:"lnTo",pt:{x:"xB1",y:"yB1"}},{type:"lnTo",pt:{x:"xC1",y:"yC1"}},{type:"lnTo",pt:{x:"xD1",y:"yD1"}},{type:"arcTo",wR:"rw",hR:"rh",stAng:"bD1",swAng:"swAng1"},{type:"lnTo",pt:{x:"xB2",y:"yB2"}},{type:"lnTo",pt:{x:"xC2",y:"yC2"}},{type:"lnTo",pt:{x:"xD2",y:"yD2"}},{type:"arcTo",wR:"rw",hR:"rh",stAng:"bD2",swAng:"swAng2"},{type:"lnTo",pt:{x:"xB3",y:"yB3"}},{type:"lnTo",pt:{x:"xC3",y:"yC3"}},{type:"lnTo",pt:{x:"xD3",y:"yD3"}},{type:"arcTo",wR:"rw",hR:"rh",stAng:"bD3",swAng:"swAng3"},{type:"lnTo",pt:{x:"xB4",y:"yB4"}},{type:"lnTo",pt:{x:"xC4",y:"yC4"}},{type:"lnTo",pt:{x:"xD4",y:"yD4"}},{type:"arcTo",wR:"rw",hR:"rh",stAng:"bD4",swAng:"swAng4"},{type:"lnTo",pt:{x:"xB5",y:"yC4"}},{type:"lnTo",pt:{x:"xC5",y:"yB4"}},{type:"lnTo",pt:{x:"xD5",y:"yA4"}},{type:"arcTo",wR:"rw",hR:"rh",stAng:"bD5",swAng:"swAng3"},{type:"lnTo",pt:{x:"xB6",y:"yC3"}},{type:"lnTo",pt:{x:"xC6",y:"yB3"}},{type:"lnTo",pt:{x:"xD6",y:"yA3"}},{type:"arcTo",wR:"rw",hR:"rh",stAng:"bD6",swAng:"swAng2"},{type:"lnTo",pt:{x:"xB7",y:"yC2"}},{type:"lnTo",pt:{x:"xC7",y:"yB2"}},{type:"lnTo",pt:{x:"xD7",y:"yA2"}},{type:"arcTo",wR:"rw",hR:"rh",stAng:"bD7",swAng:"swAng1"},{type:"lnTo",pt:{x:"xB8",y:"yC1"}},{type:"lnTo",pt:{x:"xC8",y:"yB1"}},{type:"lnTo",pt:{x:"xD8",y:"yA1"}},{type:"arcTo",wR:"rw",hR:"rh",stAng:"bD8",swAng:"swAng5"},{type:"lnTo",pt:{x:"xE9",y:"yC9"}},{type:"lnTo",pt:{x:"xF9",y:"yC9"}},{type:"lnTo",pt:{x:"xD9",y:"yD9"}},{type:"arcTo",wR:"rw",hR:"rh",stAng:"bD9",swAng:"swAng5"},{type:"close"}],extrusionOk:!1,stroke:!0}]},halfFrame:{avLst:[{n:"adj1",f:"val 33333"},{n:"adj2",f:"val 33333"}],gdLst:[{n:"maxAdj2",f:"*/ 100000 w ss"},{n:"a2",f:"pin 0 adj2 maxAdj2"},{n:"x1",f:"*/ ss a2 100000"},{n:"g1",f:"*/ h x1 w"},{n:"g2",f:"+- h 0 g1"},{n:"maxAdj1",f:"*/ 100000 g2 ss"},{n:"a1",f:"pin 0 adj1 maxAdj1"},{n:"y1",f:"*/ ss a1 100000"},{n:"dx2",f:"*/ y1 w h"},{n:"x2",f:"+- r 0 dx2"},{n:"dy2",f:"*/ x1 h w"},{n:"y2",f:"+- b 0 dy2"},{n:"cx1",f:"*/ x1 1 2"},{n:"cy1",f:"+/ y2 b 2"},{n:"cx2",f:"+/ x2 r 2"},{n:"cy2",f:"*/ y1 1 2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"x2",y:"y1"}},{type:"lnTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"x1",y:"y2"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},heart:{gdLst:[{n:"dx1",f:"*/ w 49 48"},{n:"dx2",f:"*/ w 10 48"},{n:"x1",f:"+- hc 0 dx1"},{n:"x2",f:"+- hc 0 dx2"},{n:"x3",f:"+- hc dx2 0"},{n:"x4",f:"+- hc dx1 0"},{n:"y1",f:"+- t 0 hd3"},{n:"il",f:"*/ w 1 6"},{n:"ir",f:"*/ w 5 6"},{n:"ib",f:"*/ h 2 3"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"hc",y:"hd4"}},{type:"cubicBezTo",pts:[{x:"x3",y:"y1"},{x:"x4",y:"hd4"},{x:"hc",y:"b"}]},{type:"cubicBezTo",pts:[{x:"x1",y:"hd4"},{x:"x2",y:"y1"},{x:"hc",y:"hd4"}]},{type:"close"}],extrusionOk:!1,stroke:!0}]},heptagon:{avLst:[{n:"hf",f:"val 102572"},{n:"vf",f:"val 105210"}],gdLst:[{n:"swd2",f:"*/ wd2 hf 100000"},{n:"shd2",f:"*/ hd2 vf 100000"},{n:"svc",f:"*/ vc  vf 100000"},{n:"dx1",f:"*/ swd2 97493 100000"},{n:"dx2",f:"*/ swd2 78183 100000"},{n:"dx3",f:"*/ swd2 43388 100000"},{n:"dy1",f:"*/ shd2 62349 100000"},{n:"dy2",f:"*/ shd2 22252 100000"},{n:"dy3",f:"*/ shd2 90097 100000"},{n:"x1",f:"+- hc 0 dx1"},{n:"x2",f:"+- hc 0 dx2"},{n:"x3",f:"+- hc 0 dx3"},{n:"x4",f:"+- hc dx3 0"},{n:"x5",f:"+- hc dx2 0"},{n:"x6",f:"+- hc dx1 0"},{n:"y1",f:"+- svc 0 dy1"},{n:"y2",f:"+- svc dy2 0"},{n:"y3",f:"+- svc dy3 0"},{n:"ib",f:"+- b 0 y1"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"x1",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"y1"}},{type:"lnTo",pt:{x:"hc",y:"t"}},{type:"lnTo",pt:{x:"x5",y:"y1"}},{type:"lnTo",pt:{x:"x6",y:"y2"}},{type:"lnTo",pt:{x:"x4",y:"y3"}},{type:"lnTo",pt:{x:"x3",y:"y3"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},hexagon:{avLst:[{n:"adj",f:"val 25000"},{n:"vf",f:"val 115470"}],gdLst:[{n:"maxAdj",f:"*/ 50000 w ss"},{n:"a",f:"pin 0 adj maxAdj"},{n:"shd2",f:"*/ hd2 vf 100000"},{n:"x1",f:"*/ ss a 100000"},{n:"x2",f:"+- r 0 x1"},{n:"dy1",f:"sin shd2 3600000"},{n:"y1",f:"+- vc 0 dy1"},{n:"y2",f:"+- vc dy1 0"},{n:"q1",f:"*/ maxAdj -1 2"},{n:"q2",f:"+- a q1 0"},{n:"q3",f:"?: q2 4 2"},{n:"q4",f:"?: q2 3 2"},{n:"q5",f:"?: q2 q1 0"},{n:"q6",f:"+/ a q5 q1"},{n:"q7",f:"*/ q6 q4 -1"},{n:"q8",f:"+- q3 q7 0"},{n:"il",f:"*/ w q8 24"},{n:"it",f:"*/ h q8 24"},{n:"ir",f:"+- r 0 il"},{n:"ib",f:"+- b 0 it"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"lnTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"x2",y:"y1"}},{type:"lnTo",pt:{x:"r",y:"vc"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x1",y:"y2"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},homePlate:{avLst:[{n:"adj",f:"val 50000"}],gdLst:[{n:"maxAdj",f:"*/ 100000 w ss"},{n:"a",f:"pin 0 adj maxAdj"},{n:"dx1",f:"*/ ss a 100000"},{n:"x1",f:"+- r 0 dx1"},{n:"ir",f:"+/ x1 r 2"},{n:"x2",f:"*/ x1 1 2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"x1",y:"t"}},{type:"lnTo",pt:{x:"r",y:"vc"}},{type:"lnTo",pt:{x:"x1",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},horizontalScroll:{avLst:[{n:"adj",f:"val 12500"}],gdLst:[{n:"a",f:"pin 0 adj 25000"},{n:"ch",f:"*/ ss a 100000"},{n:"ch2",f:"*/ ch 1 2"},{n:"ch4",f:"*/ ch 1 4"},{n:"y3",f:"+- ch ch2 0"},{n:"y4",f:"+- ch ch 0"},{n:"y6",f:"+- b 0 ch"},{n:"y7",f:"+- b 0 ch2"},{n:"y5",f:"+- y6 0 ch2"},{n:"x3",f:"+- r 0 ch"},{n:"x4",f:"+- r 0 ch2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"r",y:"ch2"}},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"0",swAng:"cd4"},{type:"lnTo",pt:{x:"x4",y:"ch2"}},{type:"arcTo",wR:"ch4",hR:"ch4",stAng:"0",swAng:"cd2"},{type:"lnTo",pt:{x:"x3",y:"ch"}},{type:"lnTo",pt:{x:"ch2",y:"ch"}},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"3cd4",swAng:"-5400000"},{type:"lnTo",pt:{x:"l",y:"y7"}},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"cd2",swAng:"-10800000"},{type:"lnTo",pt:{x:"ch",y:"y6"}},{type:"lnTo",pt:{x:"x4",y:"y6"}},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"cd4",swAng:"-5400000"},{type:"close"},{type:"moveTo",pt:{x:"ch2",y:"y4"}},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"cd4",swAng:"-5400000"},{type:"arcTo",wR:"ch4",hR:"ch4",stAng:"0",swAng:"-10800000"},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"ch2",y:"y4"}},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"cd4",swAng:"-5400000"},{type:"arcTo",wR:"ch4",hR:"ch4",stAng:"0",swAng:"-10800000"},{type:"close"},{type:"moveTo",pt:{x:"x4",y:"ch"}},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"cd4",swAng:"-16200000"},{type:"arcTo",wR:"ch4",hR:"ch4",stAng:"cd2",swAng:"-10800000"},{type:"close"}],fill:"darkenLess",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"l",y:"y3"}},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"cd2",swAng:"cd4"},{type:"lnTo",pt:{x:"x3",y:"ch"}},{type:"lnTo",pt:{x:"x3",y:"ch2"}},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"cd2",swAng:"cd2"},{type:"lnTo",pt:{x:"r",y:"y5"}},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"0",swAng:"cd4"},{type:"lnTo",pt:{x:"ch",y:"y6"}},{type:"lnTo",pt:{x:"ch",y:"y7"}},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"0",swAng:"cd2"},{type:"close"},{type:"moveTo",pt:{x:"x3",y:"ch"}},{type:"lnTo",pt:{x:"x4",y:"ch"}},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"cd4",swAng:"-5400000"},{type:"moveTo",pt:{x:"x4",y:"ch"}},{type:"lnTo",pt:{x:"x4",y:"ch2"}},{type:"arcTo",wR:"ch4",hR:"ch4",stAng:"0",swAng:"cd2"},{type:"moveTo",pt:{x:"ch2",y:"y4"}},{type:"lnTo",pt:{x:"ch2",y:"y3"}},{type:"arcTo",wR:"ch4",hR:"ch4",stAng:"cd2",swAng:"cd2"},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"0",swAng:"cd2"},{type:"moveTo",pt:{x:"ch",y:"y3"}},{type:"lnTo",pt:{x:"ch",y:"y6"}}],fill:"none",extrusionOk:!1,stroke:!0}]},irregularSeal1:{gdLst:[{n:"x5",f:"*/ w 4627 21600"},{n:"x12",f:"*/ w 8485 21600"},{n:"x21",f:"*/ w 16702 21600"},{n:"x24",f:"*/ w 14522 21600"},{n:"y3",f:"*/ h 6320 21600"},{n:"y6",f:"*/ h 8615 21600"},{n:"y9",f:"*/ h 13937 21600"},{n:"y18",f:"*/ h 13290 21600"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"10800",y:"5800"}},{type:"lnTo",pt:{x:"14522",y:"0"}},{type:"lnTo",pt:{x:"14155",y:"5325"}},{type:"lnTo",pt:{x:"18380",y:"4457"}},{type:"lnTo",pt:{x:"16702",y:"7315"}},{type:"lnTo",pt:{x:"21097",y:"8137"}},{type:"lnTo",pt:{x:"17607",y:"10475"}},{type:"lnTo",pt:{x:"21600",y:"13290"}},{type:"lnTo",pt:{x:"16837",y:"12942"}},{type:"lnTo",pt:{x:"18145",y:"18095"}},{type:"lnTo",pt:{x:"14020",y:"14457"}},{type:"lnTo",pt:{x:"13247",y:"19737"}},{type:"lnTo",pt:{x:"10532",y:"14935"}},{type:"lnTo",pt:{x:"8485",y:"21600"}},{type:"lnTo",pt:{x:"7715",y:"15627"}},{type:"lnTo",pt:{x:"4762",y:"17617"}},{type:"lnTo",pt:{x:"5667",y:"13937"}},{type:"lnTo",pt:{x:"135",y:"14587"}},{type:"lnTo",pt:{x:"3722",y:"11775"}},{type:"lnTo",pt:{x:"0",y:"8615"}},{type:"lnTo",pt:{x:"4627",y:"7617"}},{type:"lnTo",pt:{x:"370",y:"2295"}},{type:"lnTo",pt:{x:"7312",y:"6320"}},{type:"lnTo",pt:{x:"8352",y:"2295"}},{type:"close"}],extrusionOk:!1,stroke:!0,w:21600,h:21600}]},irregularSeal2:{gdLst:[{n:"x2",f:"*/ w 9722 21600"},{n:"x5",f:"*/ w 5372 21600"},{n:"x16",f:"*/ w 11612 21600"},{n:"x19",f:"*/ w 14640 21600"},{n:"y2",f:"*/ h 1887 21600"},{n:"y3",f:"*/ h 6382 21600"},{n:"y8",f:"*/ h 12877 21600"},{n:"y14",f:"*/ h 19712 21600"},{n:"y16",f:"*/ h 18842 21600"},{n:"y17",f:"*/ h 15935 21600"},{n:"y24",f:"*/ h 6645 21600"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"11462",y:"4342"}},{type:"lnTo",pt:{x:"14790",y:"0"}},{type:"lnTo",pt:{x:"14525",y:"5777"}},{type:"lnTo",pt:{x:"18007",y:"3172"}},{type:"lnTo",pt:{x:"16380",y:"6532"}},{type:"lnTo",pt:{x:"21600",y:"6645"}},{type:"lnTo",pt:{x:"16985",y:"9402"}},{type:"lnTo",pt:{x:"18270",y:"11290"}},{type:"lnTo",pt:{x:"16380",y:"12310"}},{type:"lnTo",pt:{x:"18877",y:"15632"}},{type:"lnTo",pt:{x:"14640",y:"14350"}},{type:"lnTo",pt:{x:"14942",y:"17370"}},{type:"lnTo",pt:{x:"12180",y:"15935"}},{type:"lnTo",pt:{x:"11612",y:"18842"}},{type:"lnTo",pt:{x:"9872",y:"17370"}},{type:"lnTo",pt:{x:"8700",y:"19712"}},{type:"lnTo",pt:{x:"7527",y:"18125"}},{type:"lnTo",pt:{x:"4917",y:"21600"}},{type:"lnTo",pt:{x:"4805",y:"18240"}},{type:"lnTo",pt:{x:"1285",y:"17825"}},{type:"lnTo",pt:{x:"3330",y:"15370"}},{type:"lnTo",pt:{x:"0",y:"12877"}},{type:"lnTo",pt:{x:"3935",y:"11592"}},{type:"lnTo",pt:{x:"1172",y:"8270"}},{type:"lnTo",pt:{x:"5372",y:"7817"}},{type:"lnTo",pt:{x:"4502",y:"3625"}},{type:"lnTo",pt:{x:"8550",y:"6382"}},{type:"lnTo",pt:{x:"9722",y:"1887"}},{type:"close"}],extrusionOk:!1,stroke:!0,w:21600,h:21600}]},leftArrow:{avLst:[{n:"adj1",f:"val 50000"},{n:"adj2",f:"val 50000"}],gdLst:[{n:"maxAdj2",f:"*/ 100000 w ss"},{n:"a1",f:"pin 0 adj1 100000"},{n:"a2",f:"pin 0 adj2 maxAdj2"},{n:"dx2",f:"*/ ss a2 100000"},{n:"x2",f:"+- l dx2 0"},{n:"dy1",f:"*/ h a1 200000"},{n:"y1",f:"+- vc 0 dy1"},{n:"y2",f:"+- vc dy1 0"},{n:"dx1",f:"*/ y1 dx2 hd2"},{n:"x1",f:"+- x2  0 dx1"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"lnTo",pt:{x:"x2",y:"t"}},{type:"lnTo",pt:{x:"x2",y:"y1"}},{type:"lnTo",pt:{x:"r",y:"y1"}},{type:"lnTo",pt:{x:"r",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},leftArrowCallout:{avLst:[{n:"adj1",f:"val 25000"},{n:"adj2",f:"val 25000"},{n:"adj3",f:"val 25000"},{n:"adj4",f:"val 64977"}],gdLst:[{n:"maxAdj2",f:"*/ 50000 h ss"},{n:"a2",f:"pin 0 adj2 maxAdj2"},{n:"maxAdj1",f:"*/ a2 2 1"},{n:"a1",f:"pin 0 adj1 maxAdj1"},{n:"maxAdj3",f:"*/ 100000 w ss"},{n:"a3",f:"pin 0 adj3 maxAdj3"},{n:"q2",f:"*/ a3 ss w"},{n:"maxAdj4",f:"+- 100000 0 q2"},{n:"a4",f:"pin 0 adj4 maxAdj4"},{n:"dy1",f:"*/ ss a2 100000"},{n:"dy2",f:"*/ ss a1 200000"},{n:"y1",f:"+- vc 0 dy1"},{n:"y2",f:"+- vc 0 dy2"},{n:"y3",f:"+- vc dy2 0"},{n:"y4",f:"+- vc dy1 0"},{n:"x1",f:"*/ ss a3 100000"},{n:"dx2",f:"*/ w a4 100000"},{n:"x2",f:"+- r 0 dx2"},{n:"x3",f:"+/ x2 r 2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"lnTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"x1",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"x2",y:"b"}},{type:"lnTo",pt:{x:"x2",y:"y3"}},{type:"lnTo",pt:{x:"x1",y:"y3"}},{type:"lnTo",pt:{x:"x1",y:"y4"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},leftBrace:{avLst:[{n:"adj1",f:"val 8333"},{n:"adj2",f:"val 50000"}],gdLst:[{n:"a2",f:"pin 0 adj2 100000"},{n:"q1",f:"+- 100000 0 a2"},{n:"q2",f:"min q1 a2"},{n:"q3",f:"*/ q2 1 2"},{n:"maxAdj1",f:"*/ q3 h ss"},{n:"a1",f:"pin 0 adj1 maxAdj1"},{n:"y1",f:"*/ ss a1 100000"},{n:"y3",f:"*/ h a2 100000"},{n:"y4",f:"+- y3 y1 0"},{n:"dx1",f:"cos wd2 2700000"},{n:"dy1",f:"sin y1 2700000"},{n:"il",f:"+- r 0 dx1"},{n:"it",f:"+- y1 0 dy1"},{n:"ib",f:"+- b dy1 y1"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"r",y:"b"}},{type:"arcTo",wR:"wd2",hR:"y1",stAng:"cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"hc",y:"y4"}},{type:"arcTo",wR:"wd2",hR:"y1",stAng:"0",swAng:"-5400000"},{type:"arcTo",wR:"wd2",hR:"y1",stAng:"cd4",swAng:"-5400000"},{type:"lnTo",pt:{x:"hc",y:"y1"}},{type:"arcTo",wR:"wd2",hR:"y1",stAng:"cd2",swAng:"cd4"},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"r",y:"b"}},{type:"arcTo",wR:"wd2",hR:"y1",stAng:"cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"hc",y:"y4"}},{type:"arcTo",wR:"wd2",hR:"y1",stAng:"0",swAng:"-5400000"},{type:"arcTo",wR:"wd2",hR:"y1",stAng:"cd4",swAng:"-5400000"},{type:"lnTo",pt:{x:"hc",y:"y1"}},{type:"arcTo",wR:"wd2",hR:"y1",stAng:"cd2",swAng:"cd4"}],fill:"none",extrusionOk:!1,stroke:!0}]},leftBracket:{avLst:[{n:"adj",f:"val 8333"}],gdLst:[{n:"maxAdj",f:"*/ 50000 h ss"},{n:"a",f:"pin 0 adj maxAdj"},{n:"y1",f:"*/ ss a 100000"},{n:"y2",f:"+- b 0 y1"},{n:"dx1",f:"cos w 2700000"},{n:"dy1",f:"sin y1 2700000"},{n:"il",f:"+- r 0 dx1"},{n:"it",f:"+- y1 0 dy1"},{n:"ib",f:"+- b dy1 y1"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"r",y:"b"}},{type:"arcTo",wR:"w",hR:"y1",stAng:"cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"l",y:"y1"}},{type:"arcTo",wR:"w",hR:"y1",stAng:"cd2",swAng:"cd4"},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"r",y:"b"}},{type:"arcTo",wR:"w",hR:"y1",stAng:"cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"l",y:"y1"}},{type:"arcTo",wR:"w",hR:"y1",stAng:"cd2",swAng:"cd4"}],fill:"none",extrusionOk:!1,stroke:!0}]},leftCircularArrow:{avLst:[{n:"adj1",f:"val 12500"},{n:"adj2",f:"val -1142319"},{n:"adj3",f:"val 1142319"},{n:"adj4",f:"val 10800000"},{n:"adj5",f:"val 12500"}],gdLst:[{n:"a5",f:"pin 0 adj5 25000"},{n:"maxAdj1",f:"*/ a5 2 1"},{n:"a1",f:"pin 0 adj1 maxAdj1"},{n:"enAng",f:"pin 1 adj3 21599999"},{n:"stAng",f:"pin 0 adj4 21599999"},{n:"th",f:"*/ ss a1 100000"},{n:"thh",f:"*/ ss a5 100000"},{n:"th2",f:"*/ th 1 2"},{n:"rw1",f:"+- wd2 th2 thh"},{n:"rh1",f:"+- hd2 th2 thh"},{n:"rw2",f:"+- rw1 0 th"},{n:"rh2",f:"+- rh1 0 th"},{n:"rw3",f:"+- rw2 th2 0"},{n:"rh3",f:"+- rh2 th2 0"},{n:"wtH",f:"sin rw3 enAng"},{n:"htH",f:"cos rh3 enAng"},{n:"dxH",f:"cat2 rw3 htH wtH"},{n:"dyH",f:"sat2 rh3 htH wtH"},{n:"xH",f:"+- hc dxH 0"},{n:"yH",f:"+- vc dyH 0"},{n:"rI",f:"min rw2 rh2"},{n:"u1",f:"*/ dxH dxH 1"},{n:"u2",f:"*/ dyH dyH 1"},{n:"u3",f:"*/ rI rI 1"},{n:"u4",f:"+- u1 0 u3"},{n:"u5",f:"+- u2 0 u3"},{n:"u6",f:"*/ u4 u5 u1"},{n:"u7",f:"*/ u6 1 u2"},{n:"u8",f:"+- 1 0 u7"},{n:"u9",f:"sqrt u8"},{n:"u10",f:"*/ u4 1 dxH"},{n:"u11",f:"*/ u10 1 dyH"},{n:"u12",f:"+/ 1 u9 u11"},{n:"u13",f:"at2 1 u12"},{n:"u14",f:"+- u13 21600000 0"},{n:"u15",f:"?: u13 u13 u14"},{n:"u16",f:"+- u15 0 enAng"},{n:"u17",f:"+- u16 21600000 0"},{n:"u18",f:"?: u16 u16 u17"},{n:"u19",f:"+- u18 0 cd2"},{n:"u20",f:"+- u18 0 21600000"},{n:"u21",f:"?: u19 u20 u18"},{n:"u22",f:"abs u21"},{n:"minAng",f:"*/ u22 -1 1"},{n:"u23",f:"abs adj2"},{n:"a2",f:"*/ u23 -1 1"},{n:"aAng",f:"pin minAng a2 0"},{n:"ptAng",f:"+- enAng aAng 0"},{n:"wtA",f:"sin rw3 ptAng"},{n:"htA",f:"cos rh3 ptAng"},{n:"dxA",f:"cat2 rw3 htA wtA"},{n:"dyA",f:"sat2 rh3 htA wtA"},{n:"xA",f:"+- hc dxA 0"},{n:"yA",f:"+- vc dyA 0"},{n:"wtE",f:"sin rw1 stAng"},{n:"htE",f:"cos rh1 stAng"},{n:"dxE",f:"cat2 rw1 htE wtE"},{n:"dyE",f:"sat2 rh1 htE wtE"},{n:"xE",f:"+- hc dxE 0"},{n:"yE",f:"+- vc dyE 0"},{n:"wtD",f:"sin rw2 stAng"},{n:"htD",f:"cos rh2 stAng"},{n:"dxD",f:"cat2 rw2 htD wtD"},{n:"dyD",f:"sat2 rh2 htD wtD"},{n:"xD",f:"+- hc dxD 0"},{n:"yD",f:"+- vc dyD 0"},{n:"dxG",f:"cos thh ptAng"},{n:"dyG",f:"sin thh ptAng"},{n:"xG",f:"+- xH dxG 0"},{n:"yG",f:"+- yH dyG 0"},{n:"dxB",f:"cos thh ptAng"},{n:"dyB",f:"sin thh ptAng"},{n:"xB",f:"+- xH 0 dxB 0"},{n:"yB",f:"+- yH 0 dyB 0"},{n:"sx1",f:"+- xB 0 hc"},{n:"sy1",f:"+- yB 0 vc"},{n:"sx2",f:"+- xG 0 hc"},{n:"sy2",f:"+- yG 0 vc"},{n:"rO",f:"min rw1 rh1"},{n:"x1O",f:"*/ sx1 rO rw1"},{n:"y1O",f:"*/ sy1 rO rh1"},{n:"x2O",f:"*/ sx2 rO rw1"},{n:"y2O",f:"*/ sy2 rO rh1"},{n:"dxO",f:"+- x2O 0 x1O"},{n:"dyO",f:"+- y2O 0 y1O"},{n:"dO",f:"mod dxO dyO 0"},{n:"q1",f:"*/ x1O y2O 1"},{n:"q2",f:"*/ x2O y1O 1"},{n:"DO",f:"+- q1 0 q2"},{n:"q3",f:"*/ rO rO 1"},{n:"q4",f:"*/ dO dO 1"},{n:"q5",f:"*/ q3 q4 1"},{n:"q6",f:"*/ DO DO 1"},{n:"q7",f:"+- q5 0 q6"},{n:"q8",f:"max q7 0"},{n:"sdelO",f:"sqrt q8"},{n:"ndyO",f:"*/ dyO -1 1"},{n:"sdyO",f:"?: ndyO -1 1"},{n:"q9",f:"*/ sdyO dxO 1"},{n:"q10",f:"*/ q9 sdelO 1"},{n:"q11",f:"*/ DO dyO 1"},{n:"dxF1",f:"+/ q11 q10 q4"},{n:"q12",f:"+- q11 0 q10"},{n:"dxF2",f:"*/ q12 1 q4"},{n:"adyO",f:"abs dyO"},{n:"q13",f:"*/ adyO sdelO 1"},{n:"q14",f:"*/ DO dxO -1"},{n:"dyF1",f:"+/ q14 q13 q4"},{n:"q15",f:"+- q14 0 q13"},{n:"dyF2",f:"*/ q15 1 q4"},{n:"q16",f:"+- x2O 0 dxF1"},{n:"q17",f:"+- x2O 0 dxF2"},{n:"q18",f:"+- y2O 0 dyF1"},{n:"q19",f:"+- y2O 0 dyF2"},{n:"q20",f:"mod q16 q18 0"},{n:"q21",f:"mod q17 q19 0"},{n:"q22",f:"+- q21 0 q20"},{n:"dxF",f:"?: q22 dxF1 dxF2"},{n:"dyF",f:"?: q22 dyF1 dyF2"},{n:"sdxF",f:"*/ dxF rw1 rO"},{n:"sdyF",f:"*/ dyF rh1 rO"},{n:"xF",f:"+- hc sdxF 0"},{n:"yF",f:"+- vc sdyF 0"},{n:"x1I",f:"*/ sx1 rI rw2"},{n:"y1I",f:"*/ sy1 rI rh2"},{n:"x2I",f:"*/ sx2 rI rw2"},{n:"y2I",f:"*/ sy2 rI rh2"},{n:"dxI",f:"+- x2I 0 x1I"},{n:"dyI",f:"+- y2I 0 y1I"},{n:"dI",f:"mod dxI dyI 0"},{n:"v1",f:"*/ x1I y2I 1"},{n:"v2",f:"*/ x2I y1I 1"},{n:"DI",f:"+- v1 0 v2"},{n:"v3",f:"*/ rI rI 1"},{n:"v4",f:"*/ dI dI 1"},{n:"v5",f:"*/ v3 v4 1"},{n:"v6",f:"*/ DI DI 1"},{n:"v7",f:"+- v5 0 v6"},{n:"v8",f:"max v7 0"},{n:"sdelI",f:"sqrt v8"},{n:"v9",f:"*/ sdyO dxI 1"},{n:"v10",f:"*/ v9 sdelI 1"},{n:"v11",f:"*/ DI dyI 1"},{n:"dxC1",f:"+/ v11 v10 v4"},{n:"v12",f:"+- v11 0 v10"},{n:"dxC2",f:"*/ v12 1 v4"},{n:"adyI",f:"abs dyI"},{n:"v13",f:"*/ adyI sdelI 1"},{n:"v14",f:"*/ DI dxI -1"},{n:"dyC1",f:"+/ v14 v13 v4"},{n:"v15",f:"+- v14 0 v13"},{n:"dyC2",f:"*/ v15 1 v4"},{n:"v16",f:"+- x1I 0 dxC1"},{n:"v17",f:"+- x1I 0 dxC2"},{n:"v18",f:"+- y1I 0 dyC1"},{n:"v19",f:"+- y1I 0 dyC2"},{n:"v20",f:"mod v16 v18 0"},{n:"v21",f:"mod v17 v19 0"},{n:"v22",f:"+- v21 0 v20"},{n:"dxC",f:"?: v22 dxC1 dxC2"},{n:"dyC",f:"?: v22 dyC1 dyC2"},{n:"sdxC",f:"*/ dxC rw2 rI"},{n:"sdyC",f:"*/ dyC rh2 rI"},{n:"xC",f:"+- hc sdxC 0"},{n:"yC",f:"+- vc sdyC 0"},{n:"ist0",f:"at2 sdxC sdyC"},{n:"ist1",f:"+- ist0 21600000 0"},{n:"istAng0",f:"?: ist0 ist0 ist1"},{n:"isw1",f:"+- stAng 0 istAng0"},{n:"isw2",f:"+- isw1 21600000 0"},{n:"iswAng0",f:"?: isw1 isw1 isw2"},{n:"istAng",f:"+- istAng0 iswAng0 0"},{n:"iswAng",f:"+- 0 0 iswAng0"},{n:"p1",f:"+- xF 0 xC"},{n:"p2",f:"+- yF 0 yC"},{n:"p3",f:"mod p1 p2 0"},{n:"p4",f:"*/ p3 1 2"},{n:"p5",f:"+- p4 0 thh"},{n:"xGp",f:"?: p5 xF xG"},{n:"yGp",f:"?: p5 yF yG"},{n:"xBp",f:"?: p5 xC xB"},{n:"yBp",f:"?: p5 yC yB"},{n:"en0",f:"at2 sdxF sdyF"},{n:"en1",f:"+- en0 21600000 0"},{n:"en2",f:"?: en0 en0 en1"},{n:"sw0",f:"+- en2 0 stAng"},{n:"sw1",f:"+- sw0 0 21600000"},{n:"swAng",f:"?: sw0 sw1 sw0"},{n:"stAng0",f:"+- stAng swAng 0"},{n:"swAng0",f:"+- 0 0 swAng"},{n:"wtI",f:"sin rw3 stAng"},{n:"htI",f:"cos rh3 stAng"},{n:"dxI",f:"cat2 rw3 htI wtI"},{n:"dyI",f:"sat2 rh3 htI wtI"},{n:"xI",f:"+- hc dxI 0"},{n:"yI",f:"+- vc dyI 0"},{n:"aI",f:"+- stAng cd4 0"},{n:"aA",f:"+- ptAng 0 cd4"},{n:"aB",f:"+- ptAng cd2 0"},{n:"idx",f:"cos rw1 2700000"},{n:"idy",f:"sin rh1 2700000"},{n:"il",f:"+- hc 0 idx"},{n:"ir",f:"+- hc idx 0"},{n:"it",f:"+- vc 0 idy"},{n:"ib",f:"+- vc idy 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"xE",y:"yE"}},{type:"lnTo",pt:{x:"xD",y:"yD"}},{type:"arcTo",wR:"rw2",hR:"rh2",stAng:"istAng",swAng:"iswAng"},{type:"lnTo",pt:{x:"xBp",y:"yBp"}},{type:"lnTo",pt:{x:"xA",y:"yA"}},{type:"lnTo",pt:{x:"xGp",y:"yGp"}},{type:"lnTo",pt:{x:"xF",y:"yF"}},{type:"arcTo",wR:"rw1",hR:"rh1",stAng:"stAng0",swAng:"swAng0"},{type:"close"}],extrusionOk:!1,stroke:!0}]},leftRightArrow:{avLst:[{n:"adj1",f:"val 50000"},{n:"adj2",f:"val 50000"}],gdLst:[{n:"maxAdj2",f:"*/ 50000 w ss"},{n:"a1",f:"pin 0 adj1 100000"},{n:"a2",f:"pin 0 adj2 maxAdj2"},{n:"x2",f:"*/ ss a2 100000"},{n:"x3",f:"+- r 0 x2"},{n:"dy",f:"*/ h a1 200000"},{n:"y1",f:"+- vc 0 dy"},{n:"y2",f:"+- vc dy 0"},{n:"dx1",f:"*/ y1 x2 hd2"},{n:"x1",f:"+- x2 0 dx1"},{n:"x4",f:"+- x3 dx1 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"lnTo",pt:{x:"x2",y:"t"}},{type:"lnTo",pt:{x:"x2",y:"y1"}},{type:"lnTo",pt:{x:"x3",y:"y1"}},{type:"lnTo",pt:{x:"x3",y:"t"}},{type:"lnTo",pt:{x:"r",y:"vc"}},{type:"lnTo",pt:{x:"x3",y:"b"}},{type:"lnTo",pt:{x:"x3",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},leftRightArrowCallout:{avLst:[{n:"adj1",f:"val 25000"},{n:"adj2",f:"val 25000"},{n:"adj3",f:"val 25000"},{n:"adj4",f:"val 48123"}],gdLst:[{n:"maxAdj2",f:"*/ 50000 h ss"},{n:"a2",f:"pin 0 adj2 maxAdj2"},{n:"maxAdj1",f:"*/ a2 2 1"},{n:"a1",f:"pin 0 adj1 maxAdj1"},{n:"maxAdj3",f:"*/ 50000 w ss"},{n:"a3",f:"pin 0 adj3 maxAdj3"},{n:"q2",f:"*/ a3 ss wd2"},{n:"maxAdj4",f:"+- 100000 0 q2"},{n:"a4",f:"pin 0 adj4 maxAdj4"},{n:"dy1",f:"*/ ss a2 100000"},{n:"dy2",f:"*/ ss a1 200000"},{n:"y1",f:"+- vc 0 dy1"},{n:"y2",f:"+- vc 0 dy2"},{n:"y3",f:"+- vc dy2 0"},{n:"y4",f:"+- vc dy1 0"},{n:"x1",f:"*/ ss a3 100000"},{n:"x4",f:"+- r 0 x1"},{n:"dx2",f:"*/ w a4 200000"},{n:"x2",f:"+- hc 0 dx2"},{n:"x3",f:"+- hc dx2 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"lnTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"x1",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"t"}},{type:"lnTo",pt:{x:"x3",y:"t"}},{type:"lnTo",pt:{x:"x3",y:"y2"}},{type:"lnTo",pt:{x:"x4",y:"y2"}},{type:"lnTo",pt:{x:"x4",y:"y1"}},{type:"lnTo",pt:{x:"r",y:"vc"}},{type:"lnTo",pt:{x:"x4",y:"y4"}},{type:"lnTo",pt:{x:"x4",y:"y3"}},{type:"lnTo",pt:{x:"x3",y:"y3"}},{type:"lnTo",pt:{x:"x3",y:"b"}},{type:"lnTo",pt:{x:"x2",y:"b"}},{type:"lnTo",pt:{x:"x2",y:"y3"}},{type:"lnTo",pt:{x:"x1",y:"y3"}},{type:"lnTo",pt:{x:"x1",y:"y4"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},leftRightCircularArrow:{avLst:[{n:"adj1",f:"val 12500"},{n:"adj2",f:"val 1142319"},{n:"adj3",f:"val 20457681"},{n:"adj4",f:"val 11942319"},{n:"adj5",f:"val 12500"}],gdLst:[{n:"a5",f:"pin 0 adj5 25000"},{n:"maxAdj1",f:"*/ a5 2 1"},{n:"a1",f:"pin 0 adj1 maxAdj1"},{n:"enAng",f:"pin 1 adj3 21599999"},{n:"stAng",f:"pin 0 adj4 21599999"},{n:"th",f:"*/ ss a1 100000"},{n:"thh",f:"*/ ss a5 100000"},{n:"th2",f:"*/ th 1 2"},{n:"rw1",f:"+- wd2 th2 thh"},{n:"rh1",f:"+- hd2 th2 thh"},{n:"rw2",f:"+- rw1 0 th"},{n:"rh2",f:"+- rh1 0 th"},{n:"rw3",f:"+- rw2 th2 0"},{n:"rh3",f:"+- rh2 th2 0"},{n:"wtH",f:"sin rw3 enAng"},{n:"htH",f:"cos rh3 enAng"},{n:"dxH",f:"cat2 rw3 htH wtH"},{n:"dyH",f:"sat2 rh3 htH wtH"},{n:"xH",f:"+- hc dxH 0"},{n:"yH",f:"+- vc dyH 0"},{n:"rI",f:"min rw2 rh2"},{n:"u1",f:"*/ dxH dxH 1"},{n:"u2",f:"*/ dyH dyH 1"},{n:"u3",f:"*/ rI rI 1"},{n:"u4",f:"+- u1 0 u3"},{n:"u5",f:"+- u2 0 u3"},{n:"u6",f:"*/ u4 u5 u1"},{n:"u7",f:"*/ u6 1 u2"},{n:"u8",f:"+- 1 0 u7"},{n:"u9",f:"sqrt u8"},{n:"u10",f:"*/ u4 1 dxH"},{n:"u11",f:"*/ u10 1 dyH"},{n:"u12",f:"+/ 1 u9 u11"},{n:"u13",f:"at2 1 u12"},{n:"u14",f:"+- u13 21600000 0"},{n:"u15",f:"?: u13 u13 u14"},{n:"u16",f:"+- u15 0 enAng"},{n:"u17",f:"+- u16 21600000 0"},{n:"u18",f:"?: u16 u16 u17"},{n:"u19",f:"+- u18 0 cd2"},{n:"u20",f:"+- u18 0 21600000"},{n:"u21",f:"?: u19 u20 u18"},{n:"maxAng",f:"abs u21"},{n:"aAng",f:"pin 0 adj2 maxAng"},{n:"ptAng",f:"+- enAng aAng 0"},{n:"wtA",f:"sin rw3 ptAng"},{n:"htA",f:"cos rh3 ptAng"},{n:"dxA",f:"cat2 rw3 htA wtA"},{n:"dyA",f:"sat2 rh3 htA wtA"},{n:"xA",f:"+- hc dxA 0"},{n:"yA",f:"+- vc dyA 0"},{n:"dxG",f:"cos thh ptAng"},{n:"dyG",f:"sin thh ptAng"},{n:"xG",f:"+- xH dxG 0"},{n:"yG",f:"+- yH dyG 0"},{n:"dxB",f:"cos thh ptAng"},{n:"dyB",f:"sin thh ptAng"},{n:"xB",f:"+- xH 0 dxB 0"},{n:"yB",f:"+- yH 0 dyB 0"},{n:"sx1",f:"+- xB 0 hc"},{n:"sy1",f:"+- yB 0 vc"},{n:"sx2",f:"+- xG 0 hc"},{n:"sy2",f:"+- yG 0 vc"},{n:"rO",f:"min rw1 rh1"},{n:"x1O",f:"*/ sx1 rO rw1"},{n:"y1O",f:"*/ sy1 rO rh1"},{n:"x2O",f:"*/ sx2 rO rw1"},{n:"y2O",f:"*/ sy2 rO rh1"},{n:"dxO",f:"+- x2O 0 x1O"},{n:"dyO",f:"+- y2O 0 y1O"},{n:"dO",f:"mod dxO dyO 0"},{n:"q1",f:"*/ x1O y2O 1"},{n:"q2",f:"*/ x2O y1O 1"},{n:"DO",f:"+- q1 0 q2"},{n:"q3",f:"*/ rO rO 1"},{n:"q4",f:"*/ dO dO 1"},{n:"q5",f:"*/ q3 q4 1"},{n:"q6",f:"*/ DO DO 1"},{n:"q7",f:"+- q5 0 q6"},{n:"q8",f:"max q7 0"},{n:"sdelO",f:"sqrt q8"},{n:"ndyO",f:"*/ dyO -1 1"},{n:"sdyO",f:"?: ndyO -1 1"},{n:"q9",f:"*/ sdyO dxO 1"},{n:"q10",f:"*/ q9 sdelO 1"},{n:"q11",f:"*/ DO dyO 1"},{n:"dxF1",f:"+/ q11 q10 q4"},{n:"q12",f:"+- q11 0 q10"},{n:"dxF2",f:"*/ q12 1 q4"},{n:"adyO",f:"abs dyO"},{n:"q13",f:"*/ adyO sdelO 1"},{n:"q14",f:"*/ DO dxO -1"},{n:"dyF1",f:"+/ q14 q13 q4"},{n:"q15",f:"+- q14 0 q13"},{n:"dyF2",f:"*/ q15 1 q4"},{n:"q16",f:"+- x2O 0 dxF1"},{n:"q17",f:"+- x2O 0 dxF2"},{n:"q18",f:"+- y2O 0 dyF1"},{n:"q19",f:"+- y2O 0 dyF2"},{n:"q20",f:"mod q16 q18 0"},{n:"q21",f:"mod q17 q19 0"},{n:"q22",f:"+- q21 0 q20"},{n:"dxF",f:"?: q22 dxF1 dxF2"},{n:"dyF",f:"?: q22 dyF1 dyF2"},{n:"sdxF",f:"*/ dxF rw1 rO"},{n:"sdyF",f:"*/ dyF rh1 rO"},{n:"xF",f:"+- hc sdxF 0"},{n:"yF",f:"+- vc sdyF 0"},{n:"x1I",f:"*/ sx1 rI rw2"},{n:"y1I",f:"*/ sy1 rI rh2"},{n:"x2I",f:"*/ sx2 rI rw2"},{n:"y2I",f:"*/ sy2 rI rh2"},{n:"dxI",f:"+- x2I 0 x1I"},{n:"dyI",f:"+- y2I 0 y1I"},{n:"dI",f:"mod dxI dyI 0"},{n:"v1",f:"*/ x1I y2I 1"},{n:"v2",f:"*/ x2I y1I 1"},{n:"DI",f:"+- v1 0 v2"},{n:"v3",f:"*/ rI rI 1"},{n:"v4",f:"*/ dI dI 1"},{n:"v5",f:"*/ v3 v4 1"},{n:"v6",f:"*/ DI DI 1"},{n:"v7",f:"+- v5 0 v6"},{n:"v8",f:"max v7 0"},{n:"sdelI",f:"sqrt v8"},{n:"v9",f:"*/ sdyO dxI 1"},{n:"v10",f:"*/ v9 sdelI 1"},{n:"v11",f:"*/ DI dyI 1"},{n:"dxC1",f:"+/ v11 v10 v4"},{n:"v12",f:"+- v11 0 v10"},{n:"dxC2",f:"*/ v12 1 v4"},{n:"adyI",f:"abs dyI"},{n:"v13",f:"*/ adyI sdelI 1"},{n:"v14",f:"*/ DI dxI -1"},{n:"dyC1",f:"+/ v14 v13 v4"},{n:"v15",f:"+- v14 0 v13"},{n:"dyC2",f:"*/ v15 1 v4"},{n:"v16",f:"+- x1I 0 dxC1"},{n:"v17",f:"+- x1I 0 dxC2"},{n:"v18",f:"+- y1I 0 dyC1"},{n:"v19",f:"+- y1I 0 dyC2"},{n:"v20",f:"mod v16 v18 0"},{n:"v21",f:"mod v17 v19 0"},{n:"v22",f:"+- v21 0 v20"},{n:"dxC",f:"?: v22 dxC1 dxC2"},{n:"dyC",f:"?: v22 dyC1 dyC2"},{n:"sdxC",f:"*/ dxC rw2 rI"},{n:"sdyC",f:"*/ dyC rh2 rI"},{n:"xC",f:"+- hc sdxC 0"},{n:"yC",f:"+- vc sdyC 0"},{n:"wtI",f:"sin rw3 stAng"},{n:"htI",f:"cos rh3 stAng"},{n:"dxI",f:"cat2 rw3 htI wtI"},{n:"dyI",f:"sat2 rh3 htI wtI"},{n:"xI",f:"+- hc dxI 0"},{n:"yI",f:"+- vc dyI 0"},{n:"lptAng",f:"+- stAng 0 aAng"},{n:"wtL",f:"sin rw3 lptAng"},{n:"htL",f:"cos rh3 lptAng"},{n:"dxL",f:"cat2 rw3 htL wtL"},{n:"dyL",f:"sat2 rh3 htL wtL"},{n:"xL",f:"+- hc dxL 0"},{n:"yL",f:"+- vc dyL 0"},{n:"dxK",f:"cos thh lptAng"},{n:"dyK",f:"sin thh lptAng"},{n:"xK",f:"+- xI dxK 0"},{n:"yK",f:"+- yI dyK 0"},{n:"dxJ",f:"cos thh lptAng"},{n:"dyJ",f:"sin thh lptAng"},{n:"xJ",f:"+- xI 0 dxJ 0"},{n:"yJ",f:"+- yI 0 dyJ 0"},{n:"p1",f:"+- xF 0 xC"},{n:"p2",f:"+- yF 0 yC"},{n:"p3",f:"mod p1 p2 0"},{n:"p4",f:"*/ p3 1 2"},{n:"p5",f:"+- p4 0 thh"},{n:"xGp",f:"?: p5 xF xG"},{n:"yGp",f:"?: p5 yF yG"},{n:"xBp",f:"?: p5 xC xB"},{n:"yBp",f:"?: p5 yC yB"},{n:"en0",f:"at2 sdxF sdyF"},{n:"en1",f:"+- en0 21600000 0"},{n:"en2",f:"?: en0 en0 en1"},{n:"od0",f:"+- en2 0 enAng"},{n:"od1",f:"+- od0 21600000 0"},{n:"od2",f:"?: od0 od0 od1"},{n:"st0",f:"+- stAng 0 od2"},{n:"st1",f:"+- st0 21600000 0"},{n:"st2",f:"?: st0 st0 st1"},{n:"sw0",f:"+- en2 0 st2"},{n:"sw1",f:"+- sw0 21600000 0"},{n:"swAng",f:"?: sw0 sw0 sw1"},{n:"ist0",f:"at2 sdxC sdyC"},{n:"ist1",f:"+- ist0 21600000 0"},{n:"istAng",f:"?: ist0 ist0 ist1"},{n:"id0",f:"+- istAng 0 enAng"},{n:"id1",f:"+- id0 0 21600000"},{n:"id2",f:"?: id0 id1 id0"},{n:"ien0",f:"+- stAng 0 id2"},{n:"ien1",f:"+- ien0 0 21600000"},{n:"ien2",f:"?: ien1 ien1 ien0"},{n:"isw1",f:"+- ien2 0 istAng"},{n:"isw2",f:"+- isw1 0 21600000"},{n:"iswAng",f:"?: isw1 isw2 isw1"},{n:"wtE",f:"sin rw1 st2"},{n:"htE",f:"cos rh1 st2"},{n:"dxE",f:"cat2 rw1 htE wtE"},{n:"dyE",f:"sat2 rh1 htE wtE"},{n:"xE",f:"+- hc dxE 0"},{n:"yE",f:"+- vc dyE 0"},{n:"wtD",f:"sin rw2 ien2"},{n:"htD",f:"cos rh2 ien2"},{n:"dxD",f:"cat2 rw2 htD wtD"},{n:"dyD",f:"sat2 rh2 htD wtD"},{n:"xD",f:"+- hc dxD 0"},{n:"yD",f:"+- vc dyD 0"},{n:"xKp",f:"?: p5 xE xK"},{n:"yKp",f:"?: p5 yE yK"},{n:"xJp",f:"?: p5 xD xJ"},{n:"yJp",f:"?: p5 yD yJ"},{n:"aL",f:"+- lptAng 0 cd4"},{n:"aA",f:"+- ptAng cd4 0"},{n:"aB",f:"+- ptAng cd2 0"},{n:"aJ",f:"+- lptAng cd2 0"},{n:"idx",f:"cos rw1 2700000"},{n:"idy",f:"sin rh1 2700000"},{n:"il",f:"+- hc 0 idx"},{n:"ir",f:"+- hc idx 0"},{n:"it",f:"+- vc 0 idy"},{n:"ib",f:"+- vc idy 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"xL",y:"yL"}},{type:"lnTo",pt:{x:"xKp",y:"yKp"}},{type:"lnTo",pt:{x:"xE",y:"yE"}},{type:"arcTo",wR:"rw1",hR:"rh1",stAng:"st2",swAng:"swAng"},{type:"lnTo",pt:{x:"xGp",y:"yGp"}},{type:"lnTo",pt:{x:"xA",y:"yA"}},{type:"lnTo",pt:{x:"xBp",y:"yBp"}},{type:"lnTo",pt:{x:"xC",y:"yC"}},{type:"arcTo",wR:"rw2",hR:"rh2",stAng:"istAng",swAng:"iswAng"},{type:"lnTo",pt:{x:"xJp",y:"yJp"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},leftRightRibbon:{avLst:[{n:"adj1",f:"val 50000"},{n:"adj2",f:"val 50000"},{n:"adj3",f:"val 16667"}],gdLst:[{n:"a3",f:"pin 0 adj3 33333"},{n:"maxAdj1",f:"+- 100000 0 a3"},{n:"a1",f:"pin 0 adj1 maxAdj1"},{n:"w1",f:"+- wd2 0 wd32"},{n:"maxAdj2",f:"*/ 100000 w1 ss"},{n:"a2",f:"pin 0 adj2 maxAdj2"},{n:"x1",f:"*/ ss a2 100000"},{n:"x4",f:"+- r 0 x1"},{n:"dy1",f:"*/ h a1 200000"},{n:"dy2",f:"*/ h a3 -200000"},{n:"ly1",f:"+- vc dy2 dy1"},{n:"ry4",f:"+- vc dy1 dy2"},{n:"ly2",f:"+- ly1 dy1 0"},{n:"ry3",f:"+- b 0 ly2"},{n:"ly4",f:"*/ ly2 2 1"},{n:"ry1",f:"+- b 0 ly4"},{n:"ly3",f:"+- ly4 0 ly1"},{n:"ry2",f:"+- b 0 ly3"},{n:"hR",f:"*/ a3 ss 400000"},{n:"x2",f:"+- hc 0 wd32"},{n:"x3",f:"+- hc wd32 0"},{n:"y1",f:"+- ly1 hR 0"},{n:"y2",f:"+- ry2 0 hR"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"ly2"}},{type:"lnTo",pt:{x:"x1",y:"t"}},{type:"lnTo",pt:{x:"x1",y:"ly1"}},{type:"lnTo",pt:{x:"hc",y:"ly1"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"3cd4",swAng:"cd2"},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"3cd4",swAng:"-10800000"},{type:"lnTo",pt:{x:"x4",y:"ry2"}},{type:"lnTo",pt:{x:"x4",y:"ry1"}},{type:"lnTo",pt:{x:"r",y:"ry3"}},{type:"lnTo",pt:{x:"x4",y:"b"}},{type:"lnTo",pt:{x:"x4",y:"ry4"}},{type:"lnTo",pt:{x:"hc",y:"ry4"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"x2",y:"ly3"}},{type:"lnTo",pt:{x:"x1",y:"ly3"}},{type:"lnTo",pt:{x:"x1",y:"ly4"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"x3",y:"y1"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"0",swAng:"cd4"},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"3cd4",swAng:"-10800000"},{type:"lnTo",pt:{x:"x3",y:"ry2"}},{type:"close"}],fill:"darkenLess",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"l",y:"ly2"}},{type:"lnTo",pt:{x:"x1",y:"t"}},{type:"lnTo",pt:{x:"x1",y:"ly1"}},{type:"lnTo",pt:{x:"hc",y:"ly1"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"3cd4",swAng:"cd2"},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"3cd4",swAng:"-10800000"},{type:"lnTo",pt:{x:"x4",y:"ry2"}},{type:"lnTo",pt:{x:"x4",y:"ry1"}},{type:"lnTo",pt:{x:"r",y:"ry3"}},{type:"lnTo",pt:{x:"x4",y:"b"}},{type:"lnTo",pt:{x:"x4",y:"ry4"}},{type:"lnTo",pt:{x:"hc",y:"ry4"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"x2",y:"ly3"}},{type:"lnTo",pt:{x:"x1",y:"ly3"}},{type:"lnTo",pt:{x:"x1",y:"ly4"}},{type:"close"},{type:"moveTo",pt:{x:"x3",y:"y1"}},{type:"lnTo",pt:{x:"x3",y:"ry2"}},{type:"moveTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"ly3"}}],fill:"none",extrusionOk:!1,stroke:!0}]},leftRightUpArrow:{avLst:[{n:"adj1",f:"val 25000"},{n:"adj2",f:"val 25000"},{n:"adj3",f:"val 25000"}],gdLst:[{n:"a2",f:"pin 0 adj2 50000"},{n:"maxAdj1",f:"*/ a2 2 1"},{n:"a1",f:"pin 0 adj1 maxAdj1"},{n:"q1",f:"+- 100000 0 maxAdj1"},{n:"maxAdj3",f:"*/ q1 1 2"},{n:"a3",f:"pin 0 adj3 maxAdj3"},{n:"x1",f:"*/ ss a3 100000"},{n:"dx2",f:"*/ ss a2 100000"},{n:"x2",f:"+- hc 0 dx2"},{n:"x5",f:"+- hc dx2 0"},{n:"dx3",f:"*/ ss a1 200000"},{n:"x3",f:"+- hc 0 dx3"},{n:"x4",f:"+- hc dx3 0"},{n:"x6",f:"+- r 0 x1"},{n:"dy2",f:"*/ ss a2 50000"},{n:"y2",f:"+- b 0 dy2"},{n:"y4",f:"+- b 0 dx2"},{n:"y3",f:"+- y4 0 dx3"},{n:"y5",f:"+- y4 dx3 0"},{n:"il",f:"*/ dx3 x1 dx2"},{n:"ir",f:"+- r 0 il"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"y4"}},{type:"lnTo",pt:{x:"x1",y:"y2"}},{type:"lnTo",pt:{x:"x1",y:"y3"}},{type:"lnTo",pt:{x:"x3",y:"y3"}},{type:"lnTo",pt:{x:"x3",y:"x1"}},{type:"lnTo",pt:{x:"x2",y:"x1"}},{type:"lnTo",pt:{x:"hc",y:"t"}},{type:"lnTo",pt:{x:"x5",y:"x1"}},{type:"lnTo",pt:{x:"x4",y:"x1"}},{type:"lnTo",pt:{x:"x4",y:"y3"}},{type:"lnTo",pt:{x:"x6",y:"y3"}},{type:"lnTo",pt:{x:"x6",y:"y2"}},{type:"lnTo",pt:{x:"r",y:"y4"}},{type:"lnTo",pt:{x:"x6",y:"b"}},{type:"lnTo",pt:{x:"x6",y:"y5"}},{type:"lnTo",pt:{x:"x1",y:"y5"}},{type:"lnTo",pt:{x:"x1",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},leftUpArrow:{avLst:[{n:"adj1",f:"val 25000"},{n:"adj2",f:"val 25000"},{n:"adj3",f:"val 25000"}],gdLst:[{n:"a2",f:"pin 0 adj2 50000"},{n:"maxAdj1",f:"*/ a2 2 1"},{n:"a1",f:"pin 0 adj1 maxAdj1"},{n:"maxAdj3",f:"+- 100000 0 maxAdj1"},{n:"a3",f:"pin 0 adj3 maxAdj3"},{n:"x1",f:"*/ ss a3 100000"},{n:"dx2",f:"*/ ss a2 50000"},{n:"x2",f:"+- r 0 dx2"},{n:"y2",f:"+- b 0 dx2"},{n:"dx4",f:"*/ ss a2 100000"},{n:"x4",f:"+- r 0 dx4"},{n:"y4",f:"+- b 0 dx4"},{n:"dx3",f:"*/ ss a1 200000"},{n:"x3",f:"+- x4 0 dx3"},{n:"x5",f:"+- x4 dx3 0"},{n:"y3",f:"+- y4 0 dx3"},{n:"y5",f:"+- y4 dx3 0"},{n:"il",f:"*/ dx3 x1 dx4"},{n:"cx1",f:"+/ x1 x5 2"},{n:"cy1",f:"+/ x1 y5 2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"y4"}},{type:"lnTo",pt:{x:"x1",y:"y2"}},{type:"lnTo",pt:{x:"x1",y:"y3"}},{type:"lnTo",pt:{x:"x3",y:"y3"}},{type:"lnTo",pt:{x:"x3",y:"x1"}},{type:"lnTo",pt:{x:"x2",y:"x1"}},{type:"lnTo",pt:{x:"x4",y:"t"}},{type:"lnTo",pt:{x:"r",y:"x1"}},{type:"lnTo",pt:{x:"x5",y:"x1"}},{type:"lnTo",pt:{x:"x5",y:"y5"}},{type:"lnTo",pt:{x:"x1",y:"y5"}},{type:"lnTo",pt:{x:"x1",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},lightningBolt:{gdLst:[{n:"x1",f:"*/ w 5022 21600"},{n:"x3",f:"*/ w 8472 21600"},{n:"x4",f:"*/ w 8757 21600"},{n:"x5",f:"*/ w 10012 21600"},{n:"x8",f:"*/ w 12860 21600"},{n:"x9",f:"*/ w 13917 21600"},{n:"x11",f:"*/ w 16577 21600"},{n:"y1",f:"*/ h 3890 21600"},{n:"y2",f:"*/ h 6080 21600"},{n:"y4",f:"*/ h 7437 21600"},{n:"y6",f:"*/ h 9705 21600"},{n:"y7",f:"*/ h 12007 21600"},{n:"y10",f:"*/ h 14277 21600"},{n:"y11",f:"*/ h 14915 21600"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"8472",y:"0"}},{type:"lnTo",pt:{x:"12860",y:"6080"}},{type:"lnTo",pt:{x:"11050",y:"6797"}},{type:"lnTo",pt:{x:"16577",y:"12007"}},{type:"lnTo",pt:{x:"14767",y:"12877"}},{type:"lnTo",pt:{x:"21600",y:"21600"}},{type:"lnTo",pt:{x:"10012",y:"14915"}},{type:"lnTo",pt:{x:"12222",y:"13987"}},{type:"lnTo",pt:{x:"5022",y:"9705"}},{type:"lnTo",pt:{x:"7602",y:"8382"}},{type:"lnTo",pt:{x:"0",y:"3890"}},{type:"close"}],extrusionOk:!1,stroke:!0,w:21600,h:21600}]},line:{pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}}],extrusionOk:!1,stroke:!0}]},lineInv:{pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"b"}},{type:"lnTo",pt:{x:"r",y:"t"}}],extrusionOk:!1,stroke:!0}]},mathDivide:{avLst:[{n:"adj1",f:"val 23520"},{n:"adj2",f:"val 5880"},{n:"adj3",f:"val 11760"}],gdLst:[{n:"a1",f:"pin 1000 adj1 36745"},{n:"ma1",f:"+- 0 0 a1"},{n:"ma3h",f:"+/ 73490 ma1 4"},{n:"ma3w",f:"*/ 36745 w h"},{n:"maxAdj3",f:"min ma3h ma3w"},{n:"a3",f:"pin 1000 adj3 maxAdj3"},{n:"m4a3",f:"*/ -4 a3 1"},{n:"maxAdj2",f:"+- 73490 m4a3 a1"},{n:"a2",f:"pin 0 adj2 maxAdj2"},{n:"dy1",f:"*/ h a1 200000"},{n:"yg",f:"*/ h a2 100000"},{n:"rad",f:"*/ h a3 100000"},{n:"dx1",f:"*/ w 73490 200000"},{n:"y3",f:"+- vc 0 dy1"},{n:"y4",f:"+- vc dy1 0"},{n:"a",f:"+- yg rad 0"},{n:"y2",f:"+- y3 0 a"},{n:"y1",f:"+- y2 0 rad"},{n:"y5",f:"+- b 0 y1"},{n:"x1",f:"+- hc 0 dx1"},{n:"x3",f:"+- hc dx1 0"},{n:"x2",f:"+- hc 0 rad"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"hc",y:"y1"}},{type:"arcTo",wR:"rad",hR:"rad",stAng:"3cd4",swAng:"21600000"},{type:"close"},{type:"moveTo",pt:{x:"hc",y:"y5"}},{type:"arcTo",wR:"rad",hR:"rad",stAng:"cd4",swAng:"21600000"},{type:"close"},{type:"moveTo",pt:{x:"x1",y:"y3"}},{type:"lnTo",pt:{x:"x3",y:"y3"}},{type:"lnTo",pt:{x:"x3",y:"y4"}},{type:"lnTo",pt:{x:"x1",y:"y4"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},mathEqual:{avLst:[{n:"adj1",f:"val 23520"},{n:"adj2",f:"val 11760"}],gdLst:[{n:"a1",f:"pin 0 adj1 36745"},{n:"2a1",f:"*/ a1 2 1"},{n:"mAdj2",f:"+- 100000 0 2a1"},{n:"a2",f:"pin 0 adj2 mAdj2"},{n:"dy1",f:"*/ h a1 100000"},{n:"dy2",f:"*/ h a2 200000"},{n:"dx1",f:"*/ w 73490 200000"},{n:"y2",f:"+- vc 0 dy2"},{n:"y3",f:"+- vc dy2 0"},{n:"y1",f:"+- y2 0 dy1"},{n:"y4",f:"+- y3 dy1 0"},{n:"x1",f:"+- hc 0 dx1"},{n:"x2",f:"+- hc dx1 0"},{n:"yC1",f:"+/ y1 y2 2"},{n:"yC2",f:"+/ y3 y4 2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"x2",y:"y1"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x1",y:"y2"}},{type:"close"},{type:"moveTo",pt:{x:"x1",y:"y3"}},{type:"lnTo",pt:{x:"x2",y:"y3"}},{type:"lnTo",pt:{x:"x2",y:"y4"}},{type:"lnTo",pt:{x:"x1",y:"y4"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},mathMinus:{avLst:[{n:"adj1",f:"val 23520"}],gdLst:[{n:"a1",f:"pin 0 adj1 100000"},{n:"dy1",f:"*/ h a1 200000"},{n:"dx1",f:"*/ w 73490 200000"},{n:"y1",f:"+- vc 0 dy1"},{n:"y2",f:"+- vc dy1 0"},{n:"x1",f:"+- hc 0 dx1"},{n:"x2",f:"+- hc dx1 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"x2",y:"y1"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x1",y:"y2"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},mathMultiply:{avLst:[{n:"adj1",f:"val 23520"}],gdLst:[{n:"a1",f:"pin 0 adj1 51965"},{n:"th",f:"*/ ss a1 100000"},{n:"a",f:"at2 w h"},{n:"sa",f:"sin 1 a"},{n:"ca",f:"cos 1 a"},{n:"ta",f:"tan 1 a"},{n:"dl",f:"mod w h 0"},{n:"rw",f:"*/ dl 51965 100000"},{n:"lM",f:"+- dl 0 rw"},{n:"xM",f:"*/ ca lM 2"},{n:"yM",f:"*/ sa lM 2"},{n:"dxAM",f:"*/ sa th 2"},{n:"dyAM",f:"*/ ca th 2"},{n:"xA",f:"+- xM 0 dxAM"},{n:"yA",f:"+- yM dyAM 0"},{n:"xB",f:"+- xM dxAM 0"},{n:"yB",f:"+- yM 0 dyAM"},{n:"xBC",f:"+- hc 0 xB"},{n:"yBC",f:"*/ xBC ta 1"},{n:"yC",f:"+- yBC yB 0"},{n:"xD",f:"+- r 0 xB"},{n:"xE",f:"+- r 0 xA"},{n:"yFE",f:"+- vc 0 yA"},{n:"xFE",f:"*/ yFE 1 ta"},{n:"xF",f:"+- xE 0 xFE"},{n:"xL",f:"+- xA xFE 0"},{n:"yG",f:"+- b 0 yA"},{n:"yH",f:"+- b 0 yB"},{n:"yI",f:"+- b 0 yC"},{n:"xC2",f:"+- r 0 xM"},{n:"yC3",f:"+- b 0 yM"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"xA",y:"yA"}},{type:"lnTo",pt:{x:"xB",y:"yB"}},{type:"lnTo",pt:{x:"hc",y:"yC"}},{type:"lnTo",pt:{x:"xD",y:"yB"}},{type:"lnTo",pt:{x:"xE",y:"yA"}},{type:"lnTo",pt:{x:"xF",y:"vc"}},{type:"lnTo",pt:{x:"xE",y:"yG"}},{type:"lnTo",pt:{x:"xD",y:"yH"}},{type:"lnTo",pt:{x:"hc",y:"yI"}},{type:"lnTo",pt:{x:"xB",y:"yH"}},{type:"lnTo",pt:{x:"xA",y:"yG"}},{type:"lnTo",pt:{x:"xL",y:"vc"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},mathNotEqual:{avLst:[{n:"adj1",f:"val 23520"},{n:"adj2",f:"val 6600000"},{n:"adj3",f:"val 11760"}],gdLst:[{n:"a1",f:"pin 0 adj1 50000"},{n:"crAng",f:"pin 4200000 adj2 6600000"},{n:"2a1",f:"*/ a1 2 1"},{n:"maxAdj3",f:"+- 100000 0 2a1"},{n:"a3",f:"pin 0 adj3 maxAdj3"},{n:"dy1",f:"*/ h a1 100000"},{n:"dy2",f:"*/ h a3 200000"},{n:"dx1",f:"*/ w 73490 200000"},{n:"x1",f:"+- hc 0 dx1"},{n:"x8",f:"+- hc dx1 0"},{n:"y2",f:"+- vc 0 dy2"},{n:"y3",f:"+- vc dy2 0"},{n:"y1",f:"+- y2 0 dy1"},{n:"y4",f:"+- y3 dy1 0"},{n:"cadj2",f:"+- crAng 0 cd4"},{n:"xadj2",f:"tan hd2 cadj2"},{n:"len",f:"mod xadj2 hd2 0"},{n:"bhw",f:"*/ len dy1 hd2"},{n:"bhw2",f:"*/ bhw 1 2"},{n:"x7",f:"+- hc xadj2 bhw2"},{n:"dx67",f:"*/ xadj2 y1 hd2"},{n:"x6",f:"+- x7 0 dx67"},{n:"dx57",f:"*/ xadj2 y2 hd2"},{n:"x5",f:"+- x7 0 dx57"},{n:"dx47",f:"*/ xadj2 y3 hd2"},{n:"x4",f:"+- x7 0 dx47"},{n:"dx37",f:"*/ xadj2 y4 hd2"},{n:"x3",f:"+- x7 0 dx37"},{n:"dx27",f:"*/ xadj2 2 1"},{n:"x2",f:"+- x7 0 dx27"},{n:"rx7",f:"+- x7 bhw 0"},{n:"rx6",f:"+- x6 bhw 0"},{n:"rx5",f:"+- x5 bhw 0"},{n:"rx4",f:"+- x4 bhw 0"},{n:"rx3",f:"+- x3 bhw 0"},{n:"rx2",f:"+- x2 bhw 0"},{n:"dx7",f:"*/ dy1 hd2 len"},{n:"rxt",f:"+- x7 dx7 0"},{n:"lxt",f:"+- rx7 0 dx7"},{n:"rx",f:"?: cadj2 rxt rx7"},{n:"lx",f:"?: cadj2 x7 lxt"},{n:"dy3",f:"*/ dy1 xadj2 len"},{n:"dy4",f:"+- 0 0 dy3"},{n:"ry",f:"?: cadj2 dy3 t"},{n:"ly",f:"?: cadj2 t dy4"},{n:"dlx",f:"+- w 0 rx"},{n:"drx",f:"+- w 0 lx"},{n:"dly",f:"+- h 0 ry"},{n:"dry",f:"+- h 0 ly"},{n:"xC1",f:"+/ rx lx 2"},{n:"xC2",f:"+/ drx dlx 2"},{n:"yC1",f:"+/ ry ly 2"},{n:"yC2",f:"+/ y1 y2 2"},{n:"yC3",f:"+/ y3 y4 2"},{n:"yC4",f:"+/ dry dly 2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"x6",y:"y1"}},{type:"lnTo",pt:{x:"lx",y:"ly"}},{type:"lnTo",pt:{x:"rx",y:"ry"}},{type:"lnTo",pt:{x:"rx6",y:"y1"}},{type:"lnTo",pt:{x:"x8",y:"y1"}},{type:"lnTo",pt:{x:"x8",y:"y2"}},{type:"lnTo",pt:{x:"rx5",y:"y2"}},{type:"lnTo",pt:{x:"rx4",y:"y3"}},{type:"lnTo",pt:{x:"x8",y:"y3"}},{type:"lnTo",pt:{x:"x8",y:"y4"}},{type:"lnTo",pt:{x:"rx3",y:"y4"}},{type:"lnTo",pt:{x:"drx",y:"dry"}},{type:"lnTo",pt:{x:"dlx",y:"dly"}},{type:"lnTo",pt:{x:"x3",y:"y4"}},{type:"lnTo",pt:{x:"x1",y:"y4"}},{type:"lnTo",pt:{x:"x1",y:"y3"}},{type:"lnTo",pt:{x:"x4",y:"y3"}},{type:"lnTo",pt:{x:"x5",y:"y2"}},{type:"lnTo",pt:{x:"x1",y:"y2"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},mathPlus:{avLst:[{n:"adj1",f:"val 23520"}],gdLst:[{n:"a1",f:"pin 0 adj1 73490"},{n:"dx1",f:"*/ w 73490 200000"},{n:"dy1",f:"*/ h 73490 200000"},{n:"dx2",f:"*/ ss a1 200000"},{n:"x1",f:"+- hc 0 dx1"},{n:"x2",f:"+- hc 0 dx2"},{n:"x3",f:"+- hc dx2 0"},{n:"x4",f:"+- hc dx1 0"},{n:"y1",f:"+- vc 0 dy1"},{n:"y2",f:"+- vc 0 dx2"},{n:"y3",f:"+- vc dx2 0"},{n:"y4",f:"+- vc dy1 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"x1",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"y1"}},{type:"lnTo",pt:{x:"x3",y:"y1"}},{type:"lnTo",pt:{x:"x3",y:"y2"}},{type:"lnTo",pt:{x:"x4",y:"y2"}},{type:"lnTo",pt:{x:"x4",y:"y3"}},{type:"lnTo",pt:{x:"x3",y:"y3"}},{type:"lnTo",pt:{x:"x3",y:"y4"}},{type:"lnTo",pt:{x:"x2",y:"y4"}},{type:"lnTo",pt:{x:"x2",y:"y3"}},{type:"lnTo",pt:{x:"x1",y:"y3"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},moon:{avLst:[{n:"adj",f:"val 50000"}],gdLst:[{n:"a",f:"pin 0 adj 87500"},{n:"g0",f:"*/ ss a 100000"},{n:"g0w",f:"*/ g0 w ss"},{n:"g1",f:"+- ss 0 g0"},{n:"g2",f:"*/ g0 g0 g1"},{n:"g3",f:"*/ ss ss g1"},{n:"g4",f:"*/ g3 2 1"},{n:"g5",f:"+- g4 0 g2"},{n:"g6",f:"+- g5 0 g0"},{n:"g6w",f:"*/ g6 w ss"},{n:"g7",f:"*/ g5 1 2"},{n:"g8",f:"+- g7 0 g0"},{n:"dy1",f:"*/ g8 hd2 ss"},{n:"g10h",f:"+- vc 0 dy1"},{n:"g11h",f:"+- vc dy1 0"},{n:"g12",f:"*/ g0 9598 32768"},{n:"g12w",f:"*/ g12 w ss"},{n:"g13",f:"+- ss 0 g12"},{n:"q1",f:"*/ ss ss 1"},{n:"q2",f:"*/ g13 g13 1"},{n:"q3",f:"+- q1 0 q2"},{n:"q4",f:"sqrt q3"},{n:"dy4",f:"*/ q4 hd2 ss"},{n:"g15h",f:"+- vc 0 dy4"},{n:"g16h",f:"+- vc dy4 0"},{n:"g17w",f:"+- g6w 0 g0w"},{n:"g18w",f:"*/ g17w 1 2"},{n:"dx2p",f:"+- g0w g18w w"},{n:"dx2",f:"*/ dx2p -1 1"},{n:"dy2",f:"*/ hd2 -1 1"},{n:"stAng1",f:"at2 dx2 dy2"},{n:"enAngp1",f:"at2 dx2 hd2"},{n:"enAng1",f:"+- enAngp1 0 21600000"},{n:"swAng1",f:"+- enAng1 0 stAng1"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"r",y:"b"}},{type:"arcTo",wR:"w",hR:"hd2",stAng:"cd4",swAng:"cd2"},{type:"arcTo",wR:"g18w",hR:"dy1",stAng:"stAng1",swAng:"swAng1"},{type:"close"}],extrusionOk:!1,stroke:!0}]},nonIsoscelesTrapezoid:{avLst:[{n:"adj1",f:"val 25000"},{n:"adj2",f:"val 25000"}],gdLst:[{n:"maxAdj",f:"*/ 50000 w ss"},{n:"a1",f:"pin 0 adj1 maxAdj"},{n:"a2",f:"pin 0 adj2 maxAdj"},{n:"x1",f:"*/ ss a1 200000"},{n:"x2",f:"*/ ss a1 100000"},{n:"dx3",f:"*/ ss a2 100000"},{n:"x3",f:"+- r 0 dx3"},{n:"x4",f:"+/ r x3 2"},{n:"il",f:"*/ wd3 a1 maxAdj"},{n:"adjm",f:"max a1 a2"},{n:"it",f:"*/ hd3 adjm maxAdj"},{n:"irt",f:"*/ wd3 a2 maxAdj"},{n:"ir",f:"+- r 0 irt"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"b"}},{type:"lnTo",pt:{x:"x2",y:"t"}},{type:"lnTo",pt:{x:"x3",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},noSmoking:{avLst:[{n:"adj",f:"val 18750"}],gdLst:[{n:"a",f:"pin 0 adj 50000"},{n:"dr",f:"*/ ss a 100000"},{n:"iwd2",f:"+- wd2 0 dr"},{n:"ihd2",f:"+- hd2 0 dr"},{n:"ang",f:"at2 w h"},{n:"ct",f:"cos ihd2 ang"},{n:"st",f:"sin iwd2 ang"},{n:"m",f:"mod ct st 0"},{n:"n",f:"*/ iwd2 ihd2 m"},{n:"drd2",f:"*/ dr 1 2"},{n:"dang",f:"at2 n drd2"},{n:"dang2",f:"*/ dang 2 1"},{n:"swAng",f:"+- -10800000 dang2 0"},{n:"t3",f:"at2 w h"},{n:"stAng1",f:"+- t3 0 dang"},{n:"stAng2",f:"+- stAng1 0 cd2"},{n:"ct1",f:"cos ihd2 stAng1"},{n:"st1",f:"sin iwd2 stAng1"},{n:"m1",f:"mod ct1 st1 0"},{n:"n1",f:"*/ iwd2 ihd2 m1"},{n:"dx1",f:"cos n1 stAng1"},{n:"dy1",f:"sin n1 stAng1"},{n:"x1",f:"+- hc dx1 0"},{n:"y1",f:"+- vc dy1 0"},{n:"x2",f:"+- hc 0 dx1"},{n:"y2",f:"+- vc 0 dy1"},{n:"idx",f:"cos wd2 2700000"},{n:"idy",f:"sin hd2 2700000"},{n:"il",f:"+- hc 0 idx"},{n:"ir",f:"+- hc idx 0"},{n:"it",f:"+- vc 0 idy"},{n:"ib",f:"+- vc idy 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"cd2",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"3cd4",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"0",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"cd4",swAng:"cd4"},{type:"close"},{type:"moveTo",pt:{x:"x1",y:"y1"}},{type:"arcTo",wR:"iwd2",hR:"ihd2",stAng:"stAng1",swAng:"swAng"},{type:"close"},{type:"moveTo",pt:{x:"x2",y:"y2"}},{type:"arcTo",wR:"iwd2",hR:"ihd2",stAng:"stAng2",swAng:"swAng"},{type:"close"}],extrusionOk:!1,stroke:!0}]},notchedRightArrow:{avLst:[{n:"adj1",f:"val 50000"},{n:"adj2",f:"val 50000"}],gdLst:[{n:"maxAdj2",f:"*/ 100000 w ss"},{n:"a1",f:"pin 0 adj1 100000"},{n:"a2",f:"pin 0 adj2 maxAdj2"},{n:"dx2",f:"*/ ss a2 100000"},{n:"x2",f:"+- r 0 dx2"},{n:"dy1",f:"*/ h a1 200000"},{n:"y1",f:"+- vc 0 dy1"},{n:"y2",f:"+- vc dy1 0"},{n:"x1",f:"*/ dy1 dx2 hd2"},{n:"x3",f:"+- r 0 x1"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"y1"}},{type:"lnTo",pt:{x:"x2",y:"y1"}},{type:"lnTo",pt:{x:"x2",y:"t"}},{type:"lnTo",pt:{x:"r",y:"vc"}},{type:"lnTo",pt:{x:"x2",y:"b"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"l",y:"y2"}},{type:"lnTo",pt:{x:"x1",y:"vc"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},octagon:{avLst:[{n:"adj",f:"val 29289"}],gdLst:[{n:"a",f:"pin 0 adj 50000"},{n:"x1",f:"*/ ss a 100000"},{n:"x2",f:"+- r 0 x1"},{n:"y2",f:"+- b 0 x1"},{n:"il",f:"*/ x1 1 2"},{n:"ir",f:"+- r 0 il"},{n:"ib",f:"+- b 0 il"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"x1"}},{type:"lnTo",pt:{x:"x1",y:"t"}},{type:"lnTo",pt:{x:"x2",y:"t"}},{type:"lnTo",pt:{x:"r",y:"x1"}},{type:"lnTo",pt:{x:"r",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"b"}},{type:"lnTo",pt:{x:"x1",y:"b"}},{type:"lnTo",pt:{x:"l",y:"y2"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},parallelogram:{avLst:[{n:"adj",f:"val 25000"}],gdLst:[{n:"maxAdj",f:"*/ 100000 w ss"},{n:"a",f:"pin 0 adj maxAdj"},{n:"x1",f:"*/ ss a 200000"},{n:"x2",f:"*/ ss a 100000"},{n:"x6",f:"+- r 0 x1"},{n:"x5",f:"+- r 0 x2"},{n:"x3",f:"*/ x5 1 2"},{n:"x4",f:"+- r 0 x3"},{n:"il",f:"*/ wd2 a maxAdj"},{n:"q1",f:"*/ 5 a maxAdj"},{n:"q2",f:"+/ 1 q1 12"},{n:"il",f:"*/ q2 w 1"},{n:"it",f:"*/ q2 h 1"},{n:"ir",f:"+- r 0 il"},{n:"ib",f:"+- b 0 it"},{n:"q3",f:"*/ h hc x2"},{n:"y1",f:"pin 0 q3 h"},{n:"y2",f:"+- b 0 y1"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"b"}},{type:"lnTo",pt:{x:"x2",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"x5",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},pentagon:{avLst:[{n:"hf",f:"val 105146"},{n:"vf",f:"val 110557"}],gdLst:[{n:"swd2",f:"*/ wd2 hf 100000"},{n:"shd2",f:"*/ hd2 vf 100000"},{n:"svc",f:"*/ vc  vf 100000"},{n:"dx1",f:"cos swd2 1080000"},{n:"dx2",f:"cos swd2 18360000"},{n:"dy1",f:"sin shd2 1080000"},{n:"dy2",f:"sin shd2 18360000"},{n:"x1",f:"+- hc 0 dx1"},{n:"x2",f:"+- hc 0 dx2"},{n:"x3",f:"+- hc dx2 0"},{n:"x4",f:"+- hc dx1 0"},{n:"y1",f:"+- svc 0 dy1"},{n:"y2",f:"+- svc 0 dy2"},{n:"it",f:"*/ y1 dx2 dx1"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"hc",y:"t"}},{type:"lnTo",pt:{x:"x4",y:"y1"}},{type:"lnTo",pt:{x:"x3",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},pie:{avLst:[{n:"adj1",f:"val 0"},{n:"adj2",f:"val 16200000"}],gdLst:[{n:"stAng",f:"pin 0 adj1 21599999"},{n:"enAng",f:"pin 0 adj2 21599999"},{n:"sw1",f:"+- enAng 0 stAng"},{n:"sw2",f:"+- sw1 21600000 0"},{n:"swAng",f:"?: sw1 sw1 sw2"},{n:"wt1",f:"sin wd2 stAng"},{n:"ht1",f:"cos hd2 stAng"},{n:"dx1",f:"cat2 wd2 ht1 wt1"},{n:"dy1",f:"sat2 hd2 ht1 wt1"},{n:"x1",f:"+- hc dx1 0"},{n:"y1",f:"+- vc dy1 0"},{n:"wt2",f:"sin wd2 enAng"},{n:"ht2",f:"cos hd2 enAng"},{n:"dx2",f:"cat2 wd2 ht2 wt2"},{n:"dy2",f:"sat2 hd2 ht2 wt2"},{n:"x2",f:"+- hc dx2 0"},{n:"y2",f:"+- vc dy2 0"},{n:"idx",f:"cos wd2 2700000"},{n:"idy",f:"sin hd2 2700000"},{n:"il",f:"+- hc 0 idx"},{n:"ir",f:"+- hc idx 0"},{n:"it",f:"+- vc 0 idy"},{n:"ib",f:"+- vc idy 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"x1",y:"y1"}},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"stAng",swAng:"swAng"},{type:"lnTo",pt:{x:"hc",y:"vc"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},pieWedge:{gdLst:[{n:"g1",f:"cos w 13500000"},{n:"g2",f:"sin h 13500000"},{n:"x1",f:"+- r g1 0"},{n:"y1",f:"+- b g2 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"b"}},{type:"arcTo",wR:"w",hR:"h",stAng:"cd2",swAng:"cd4"},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},plaque:{avLst:[{n:"adj",f:"val 16667"}],gdLst:[{n:"a",f:"pin 0 adj 50000"},{n:"x1",f:"*/ ss a 100000"},{n:"x2",f:"+- r 0 x1"},{n:"y2",f:"+- b 0 x1"},{n:"il",f:"*/ x1 70711 100000"},{n:"ir",f:"+- r 0 il"},{n:"ib",f:"+- b 0 il"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"x1"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"cd4",swAng:"-5400000"},{type:"lnTo",pt:{x:"x2",y:"t"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"cd2",swAng:"-5400000"},{type:"lnTo",pt:{x:"r",y:"y2"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"3cd4",swAng:"-5400000"},{type:"lnTo",pt:{x:"x1",y:"b"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"0",swAng:"-5400000"},{type:"close"}],extrusionOk:!1,stroke:!0}]},plaqueTabs:{gdLst:[{n:"md",f:"mod w h 0"},{n:"dx",f:"*/ 1 md 20"},{n:"y1",f:"+- 0 b dx"},{n:"x1",f:"+- 0 r dx"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"dx",y:"t"}},{type:"arcTo",wR:"dx",hR:"dx",stAng:"0",swAng:"cd4"},{type:"close"}],extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"l",y:"y1"}},{type:"arcTo",wR:"dx",hR:"dx",stAng:"3cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"dx"}},{type:"arcTo",wR:"dx",hR:"dx",stAng:"cd4",swAng:"cd4"},{type:"close"}],extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"x1",y:"b"}},{type:"arcTo",wR:"dx",hR:"dx",stAng:"cd2",swAng:"cd4"},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},plus:{avLst:[{n:"adj",f:"val 25000"}],gdLst:[{n:"a",f:"pin 0 adj 50000"},{n:"x1",f:"*/ ss a 100000"},{n:"x2",f:"+- r 0 x1"},{n:"y2",f:"+- b 0 x1"},{n:"d",f:"+- w 0 h"},{n:"il",f:"?: d l x1"},{n:"ir",f:"?: d r x2"},{n:"it",f:"?: d x1 t"},{n:"ib",f:"?: d y2 b"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"x1"}},{type:"lnTo",pt:{x:"x1",y:"x1"}},{type:"lnTo",pt:{x:"x1",y:"t"}},{type:"lnTo",pt:{x:"x2",y:"t"}},{type:"lnTo",pt:{x:"x2",y:"x1"}},{type:"lnTo",pt:{x:"r",y:"x1"}},{type:"lnTo",pt:{x:"r",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"b"}},{type:"lnTo",pt:{x:"x1",y:"b"}},{type:"lnTo",pt:{x:"x1",y:"y2"}},{type:"lnTo",pt:{x:"l",y:"y2"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},quadArrow:{avLst:[{n:"adj1",f:"val 22500"},{n:"adj2",f:"val 22500"},{n:"adj3",f:"val 22500"}],gdLst:[{n:"a2",f:"pin 0 adj2 50000"},{n:"maxAdj1",f:"*/ a2 2 1"},{n:"a1",f:"pin 0 adj1 maxAdj1"},{n:"q1",f:"+- 100000 0 maxAdj1"},{n:"maxAdj3",f:"*/ q1 1 2"},{n:"a3",f:"pin 0 adj3 maxAdj3"},{n:"x1",f:"*/ ss a3 100000"},{n:"dx2",f:"*/ ss a2 100000"},{n:"x2",f:"+- hc 0 dx2"},{n:"x5",f:"+- hc dx2 0"},{n:"dx3",f:"*/ ss a1 200000"},{n:"x3",f:"+- hc 0 dx3"},{n:"x4",f:"+- hc dx3 0"},{n:"x6",f:"+- r 0 x1"},{n:"y2",f:"+- vc 0 dx2"},{n:"y5",f:"+- vc dx2 0"},{n:"y3",f:"+- vc 0 dx3"},{n:"y4",f:"+- vc dx3 0"},{n:"y6",f:"+- b 0 x1"},{n:"il",f:"*/ dx3 x1 dx2"},{n:"ir",f:"+- r 0 il"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"lnTo",pt:{x:"x1",y:"y2"}},{type:"lnTo",pt:{x:"x1",y:"y3"}},{type:"lnTo",pt:{x:"x3",y:"y3"}},{type:"lnTo",pt:{x:"x3",y:"x1"}},{type:"lnTo",pt:{x:"x2",y:"x1"}},{type:"lnTo",pt:{x:"hc",y:"t"}},{type:"lnTo",pt:{x:"x5",y:"x1"}},{type:"lnTo",pt:{x:"x4",y:"x1"}},{type:"lnTo",pt:{x:"x4",y:"y3"}},{type:"lnTo",pt:{x:"x6",y:"y3"}},{type:"lnTo",pt:{x:"x6",y:"y2"}},{type:"lnTo",pt:{x:"r",y:"vc"}},{type:"lnTo",pt:{x:"x6",y:"y5"}},{type:"lnTo",pt:{x:"x6",y:"y4"}},{type:"lnTo",pt:{x:"x4",y:"y4"}},{type:"lnTo",pt:{x:"x4",y:"y6"}},{type:"lnTo",pt:{x:"x5",y:"y6"}},{type:"lnTo",pt:{x:"hc",y:"b"}},{type:"lnTo",pt:{x:"x2",y:"y6"}},{type:"lnTo",pt:{x:"x3",y:"y6"}},{type:"lnTo",pt:{x:"x3",y:"y4"}},{type:"lnTo",pt:{x:"x1",y:"y4"}},{type:"lnTo",pt:{x:"x1",y:"y5"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},quadArrowCallout:{avLst:[{n:"adj1",f:"val 18515"},{n:"adj2",f:"val 18515"},{n:"adj3",f:"val 18515"},{n:"adj4",f:"val 48123"}],gdLst:[{n:"a2",f:"pin 0 adj2 50000"},{n:"maxAdj1",f:"*/ a2 2 1"},{n:"a1",f:"pin 0 adj1 maxAdj1"},{n:"maxAdj3",f:"+- 50000 0 a2"},{n:"a3",f:"pin 0 adj3 maxAdj3"},{n:"q2",f:"*/ a3 2 1"},{n:"maxAdj4",f:"+- 100000 0 q2"},{n:"a4",f:"pin a1 adj4 maxAdj4"},{n:"dx2",f:"*/ ss a2 100000"},{n:"dx3",f:"*/ ss a1 200000"},{n:"ah",f:"*/ ss a3 100000"},{n:"dx1",f:"*/ w a4 200000"},{n:"dy1",f:"*/ h a4 200000"},{n:"x8",f:"+- r 0 ah"},{n:"x2",f:"+- hc 0 dx1"},{n:"x7",f:"+- hc dx1 0"},{n:"x3",f:"+- hc 0 dx2"},{n:"x6",f:"+- hc dx2 0"},{n:"x4",f:"+- hc 0 dx3"},{n:"x5",f:"+- hc dx3 0"},{n:"y8",f:"+- b 0 ah"},{n:"y2",f:"+- vc 0 dy1"},{n:"y7",f:"+- vc dy1 0"},{n:"y3",f:"+- vc 0 dx2"},{n:"y6",f:"+- vc dx2 0"},{n:"y4",f:"+- vc 0 dx3"},{n:"y5",f:"+- vc dx3 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"lnTo",pt:{x:"ah",y:"y3"}},{type:"lnTo",pt:{x:"ah",y:"y4"}},{type:"lnTo",pt:{x:"x2",y:"y4"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x4",y:"y2"}},{type:"lnTo",pt:{x:"x4",y:"ah"}},{type:"lnTo",pt:{x:"x3",y:"ah"}},{type:"lnTo",pt:{x:"hc",y:"t"}},{type:"lnTo",pt:{x:"x6",y:"ah"}},{type:"lnTo",pt:{x:"x5",y:"ah"}},{type:"lnTo",pt:{x:"x5",y:"y2"}},{type:"lnTo",pt:{x:"x7",y:"y2"}},{type:"lnTo",pt:{x:"x7",y:"y4"}},{type:"lnTo",pt:{x:"x8",y:"y4"}},{type:"lnTo",pt:{x:"x8",y:"y3"}},{type:"lnTo",pt:{x:"r",y:"vc"}},{type:"lnTo",pt:{x:"x8",y:"y6"}},{type:"lnTo",pt:{x:"x8",y:"y5"}},{type:"lnTo",pt:{x:"x7",y:"y5"}},{type:"lnTo",pt:{x:"x7",y:"y7"}},{type:"lnTo",pt:{x:"x5",y:"y7"}},{type:"lnTo",pt:{x:"x5",y:"y8"}},{type:"lnTo",pt:{x:"x6",y:"y8"}},{type:"lnTo",pt:{x:"hc",y:"b"}},{type:"lnTo",pt:{x:"x3",y:"y8"}},{type:"lnTo",pt:{x:"x4",y:"y8"}},{type:"lnTo",pt:{x:"x4",y:"y7"}},{type:"lnTo",pt:{x:"x2",y:"y7"}},{type:"lnTo",pt:{x:"x2",y:"y5"}},{type:"lnTo",pt:{x:"ah",y:"y5"}},{type:"lnTo",pt:{x:"ah",y:"y6"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},rect:{pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},ribbon:{avLst:[{n:"adj1",f:"val 16667"},{n:"adj2",f:"val 50000"}],gdLst:[{n:"a1",f:"pin 0 adj1 33333"},{n:"a2",f:"pin 25000 adj2 75000"},{n:"x10",f:"+- r 0 wd8"},{n:"dx2",f:"*/ w a2 200000"},{n:"x2",f:"+- hc 0 dx2"},{n:"x9",f:"+- hc dx2 0"},{n:"x3",f:"+- x2 wd32 0"},{n:"x8",f:"+- x9 0 wd32"},{n:"x5",f:"+- x2 wd8 0"},{n:"x6",f:"+- x9 0 wd8"},{n:"x4",f:"+- x5 0 wd32"},{n:"x7",f:"+- x6 wd32 0"},{n:"y1",f:"*/ h a1 200000"},{n:"y2",f:"*/ h a1 100000"},{n:"y4",f:"+- b 0 y2"},{n:"y3",f:"*/ y4 1 2"},{n:"hR",f:"*/ h a1 400000"},{n:"y5",f:"+- b 0 hR"},{n:"y6",f:"+- y2 0 hR"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"x4",y:"t"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"3cd4",swAng:"cd2"},{type:"lnTo",pt:{x:"x3",y:"y1"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"3cd4",swAng:"-10800000"},{type:"lnTo",pt:{x:"x8",y:"y2"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"cd4",swAng:"-10800000"},{type:"lnTo",pt:{x:"x7",y:"y1"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"cd4",swAng:"cd2"},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"x10",y:"y3"}},{type:"lnTo",pt:{x:"r",y:"y4"}},{type:"lnTo",pt:{x:"x9",y:"y4"}},{type:"lnTo",pt:{x:"x9",y:"y5"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"0",swAng:"cd4"},{type:"lnTo",pt:{x:"x3",y:"b"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"x2",y:"y4"}},{type:"lnTo",pt:{x:"l",y:"y4"}},{type:"lnTo",pt:{x:"wd8",y:"y3"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"x5",y:"hR"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"0",swAng:"cd4"},{type:"lnTo",pt:{x:"x3",y:"y1"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"3cd4",swAng:"-10800000"},{type:"lnTo",pt:{x:"x5",y:"y2"}},{type:"close"},{type:"moveTo",pt:{x:"x6",y:"hR"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"cd2",swAng:"-5400000"},{type:"lnTo",pt:{x:"x8",y:"y1"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"3cd4",swAng:"cd2"},{type:"lnTo",pt:{x:"x6",y:"y2"}},{type:"close"}],fill:"darkenLess",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"x4",y:"t"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"3cd4",swAng:"cd2"},{type:"lnTo",pt:{x:"x3",y:"y1"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"3cd4",swAng:"-10800000"},{type:"lnTo",pt:{x:"x8",y:"y2"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"cd4",swAng:"-10800000"},{type:"lnTo",pt:{x:"x7",y:"y1"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"cd4",swAng:"cd2"},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"x10",y:"y3"}},{type:"lnTo",pt:{x:"r",y:"y4"}},{type:"lnTo",pt:{x:"x9",y:"y4"}},{type:"lnTo",pt:{x:"x9",y:"y5"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"0",swAng:"cd4"},{type:"lnTo",pt:{x:"x3",y:"b"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"x2",y:"y4"}},{type:"lnTo",pt:{x:"l",y:"y4"}},{type:"lnTo",pt:{x:"wd8",y:"y3"}},{type:"close"},{type:"moveTo",pt:{x:"x5",y:"hR"}},{type:"lnTo",pt:{x:"x5",y:"y2"}},{type:"moveTo",pt:{x:"x6",y:"y2"}},{type:"lnTo",pt:{x:"x6",y:"hR"}},{type:"moveTo",pt:{x:"x2",y:"y4"}},{type:"lnTo",pt:{x:"x2",y:"y6"}},{type:"moveTo",pt:{x:"x9",y:"y6"}},{type:"lnTo",pt:{x:"x9",y:"y4"}}],fill:"none",extrusionOk:!1,stroke:!0}]},ribbon2:{avLst:[{n:"adj1",f:"val 16667"},{n:"adj2",f:"val 50000"}],gdLst:[{n:"a1",f:"pin 0 adj1 33333"},{n:"a2",f:"pin 25000 adj2 75000"},{n:"x10",f:"+- r 0 wd8"},{n:"dx2",f:"*/ w a2 200000"},{n:"x2",f:"+- hc 0 dx2"},{n:"x9",f:"+- hc dx2 0"},{n:"x3",f:"+- x2 wd32 0"},{n:"x8",f:"+- x9 0 wd32"},{n:"x5",f:"+- x2 wd8 0"},{n:"x6",f:"+- x9 0 wd8"},{n:"x4",f:"+- x5 0 wd32"},{n:"x7",f:"+- x6 wd32 0"},{n:"dy1",f:"*/ h a1 200000"},{n:"y1",f:"+- b 0 dy1"},{n:"dy2",f:"*/ h a1 100000"},{n:"y2",f:"+- b 0 dy2"},{n:"y4",f:"+- t dy2 0"},{n:"y3",f:"+/ y4 b 2"},{n:"hR",f:"*/ h a1 400000"},{n:"y6",f:"+- b 0 hR"},{n:"y7",f:"+- y1 0 hR"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"b"}},{type:"lnTo",pt:{x:"x4",y:"b"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"cd4",swAng:"-10800000"},{type:"lnTo",pt:{x:"x3",y:"y1"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"cd4",swAng:"cd2"},{type:"lnTo",pt:{x:"x8",y:"y2"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"3cd4",swAng:"cd2"},{type:"lnTo",pt:{x:"x7",y:"y1"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"3cd4",swAng:"-10800000"},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"x10",y:"y3"}},{type:"lnTo",pt:{x:"r",y:"y4"}},{type:"lnTo",pt:{x:"x9",y:"y4"}},{type:"lnTo",pt:{x:"x9",y:"hR"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"0",swAng:"-5400000"},{type:"lnTo",pt:{x:"x3",y:"t"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"3cd4",swAng:"-5400000"},{type:"lnTo",pt:{x:"x2",y:"y4"}},{type:"lnTo",pt:{x:"l",y:"y4"}},{type:"lnTo",pt:{x:"wd8",y:"y3"}},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"x5",y:"y6"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"0",swAng:"-5400000"},{type:"lnTo",pt:{x:"x3",y:"y1"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"cd4",swAng:"cd2"},{type:"lnTo",pt:{x:"x5",y:"y2"}},{type:"close"},{type:"moveTo",pt:{x:"x6",y:"y6"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"cd2",swAng:"cd4"},{type:"lnTo",pt:{x:"x8",y:"y1"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"cd4",swAng:"-10800000"},{type:"lnTo",pt:{x:"x6",y:"y2"}},{type:"close"}],fill:"darkenLess",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"l",y:"b"}},{type:"lnTo",pt:{x:"wd8",y:"y3"}},{type:"lnTo",pt:{x:"l",y:"y4"}},{type:"lnTo",pt:{x:"x2",y:"y4"}},{type:"lnTo",pt:{x:"x2",y:"hR"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"cd2",swAng:"cd4"},{type:"lnTo",pt:{x:"x8",y:"t"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"3cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"x9",y:"y4"}},{type:"lnTo",pt:{x:"x9",y:"y4"}},{type:"lnTo",pt:{x:"r",y:"y4"}},{type:"lnTo",pt:{x:"x10",y:"y3"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"x7",y:"b"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"cd4",swAng:"cd2"},{type:"lnTo",pt:{x:"x8",y:"y1"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"cd4",swAng:"-10800000"},{type:"lnTo",pt:{x:"x3",y:"y2"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"3cd4",swAng:"-10800000"},{type:"lnTo",pt:{x:"x4",y:"y1"}},{type:"arcTo",wR:"wd32",hR:"hR",stAng:"3cd4",swAng:"cd2"},{type:"close"},{type:"moveTo",pt:{x:"x5",y:"y2"}},{type:"lnTo",pt:{x:"x5",y:"y6"}},{type:"moveTo",pt:{x:"x6",y:"y6"}},{type:"lnTo",pt:{x:"x6",y:"y2"}},{type:"moveTo",pt:{x:"x2",y:"y7"}},{type:"lnTo",pt:{x:"x2",y:"y4"}},{type:"moveTo",pt:{x:"x9",y:"y4"}},{type:"lnTo",pt:{x:"x9",y:"y7"}}],fill:"none",extrusionOk:!1,stroke:!0}]},rightArrow:{avLst:[{n:"adj1",f:"val 50000"},{n:"adj2",f:"val 50000"}],gdLst:[{n:"maxAdj2",f:"*/ 100000 w ss"},{n:"a1",f:"pin 0 adj1 100000"},{n:"a2",f:"pin 0 adj2 maxAdj2"},{n:"dx1",f:"*/ ss a2 100000"},{n:"x1",f:"+- r 0 dx1"},{n:"dy1",f:"*/ h a1 200000"},{n:"y1",f:"+- vc 0 dy1"},{n:"y2",f:"+- vc dy1 0"},{n:"dx2",f:"*/ y1 dx1 hd2"},{n:"x2",f:"+- x1 dx2 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"y1"}},{type:"lnTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"x1",y:"t"}},{type:"lnTo",pt:{x:"r",y:"vc"}},{type:"lnTo",pt:{x:"x1",y:"b"}},{type:"lnTo",pt:{x:"x1",y:"y2"}},{type:"lnTo",pt:{x:"l",y:"y2"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},rightArrowCallout:{avLst:[{n:"adj1",f:"val 25000"},{n:"adj2",f:"val 25000"},{n:"adj3",f:"val 25000"},{n:"adj4",f:"val 64977"}],gdLst:[{n:"maxAdj2",f:"*/ 50000 h ss"},{n:"a2",f:"pin 0 adj2 maxAdj2"},{n:"maxAdj1",f:"*/ a2 2 1"},{n:"a1",f:"pin 0 adj1 maxAdj1"},{n:"maxAdj3",f:"*/ 100000 w ss"},{n:"a3",f:"pin 0 adj3 maxAdj3"},{n:"q2",f:"*/ a3 ss w"},{n:"maxAdj4",f:"+- 100000 0 q2"},{n:"a4",f:"pin 0 adj4 maxAdj4"},{n:"dy1",f:"*/ ss a2 100000"},{n:"dy2",f:"*/ ss a1 200000"},{n:"y1",f:"+- vc 0 dy1"},{n:"y2",f:"+- vc 0 dy2"},{n:"y3",f:"+- vc dy2 0"},{n:"y4",f:"+- vc dy1 0"},{n:"dx3",f:"*/ ss a3 100000"},{n:"x3",f:"+- r 0 dx3"},{n:"x2",f:"*/ w a4 100000"},{n:"x1",f:"*/ x2 1 2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"x2",y:"t"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x3",y:"y2"}},{type:"lnTo",pt:{x:"x3",y:"y1"}},{type:"lnTo",pt:{x:"r",y:"vc"}},{type:"lnTo",pt:{x:"x3",y:"y4"}},{type:"lnTo",pt:{x:"x3",y:"y3"}},{type:"lnTo",pt:{x:"x2",y:"y3"}},{type:"lnTo",pt:{x:"x2",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},rightBrace:{avLst:[{n:"adj1",f:"val 8333"},{n:"adj2",f:"val 50000"}],gdLst:[{n:"a2",f:"pin 0 adj2 100000"},{n:"q1",f:"+- 100000 0 a2"},{n:"q2",f:"min q1 a2"},{n:"q3",f:"*/ q2 1 2"},{n:"maxAdj1",f:"*/ q3 h ss"},{n:"a1",f:"pin 0 adj1 maxAdj1"},{n:"y1",f:"*/ ss a1 100000"},{n:"y3",f:"*/ h a2 100000"},{n:"y2",f:"+- y3 0 y1"},{n:"y4",f:"+- b 0 y1"},{n:"dx1",f:"cos wd2 2700000"},{n:"dy1",f:"sin y1 2700000"},{n:"ir",f:"+- l dx1 0"},{n:"it",f:"+- y1 0 dy1"},{n:"ib",f:"+- b dy1 y1"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"arcTo",wR:"wd2",hR:"y1",stAng:"3cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"hc",y:"y2"}},{type:"arcTo",wR:"wd2",hR:"y1",stAng:"cd2",swAng:"-5400000"},{type:"arcTo",wR:"wd2",hR:"y1",stAng:"3cd4",swAng:"-5400000"},{type:"lnTo",pt:{x:"hc",y:"y4"}},{type:"arcTo",wR:"wd2",hR:"y1",stAng:"0",swAng:"cd4"},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"arcTo",wR:"wd2",hR:"y1",stAng:"3cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"hc",y:"y2"}},{type:"arcTo",wR:"wd2",hR:"y1",stAng:"cd2",swAng:"-5400000"},{type:"arcTo",wR:"wd2",hR:"y1",stAng:"3cd4",swAng:"-5400000"},{type:"lnTo",pt:{x:"hc",y:"y4"}},{type:"arcTo",wR:"wd2",hR:"y1",stAng:"0",swAng:"cd4"}],fill:"none",extrusionOk:!1,stroke:!0}]},rightBracket:{avLst:[{n:"adj",f:"val 8333"}],gdLst:[{n:"maxAdj",f:"*/ 50000 h ss"},{n:"a",f:"pin 0 adj maxAdj"},{n:"y1",f:"*/ ss a 100000"},{n:"y2",f:"+- b 0 y1"},{n:"dx1",f:"cos w 2700000"},{n:"dy1",f:"sin y1 2700000"},{n:"ir",f:"+- l dx1 0"},{n:"it",f:"+- y1 0 dy1"},{n:"ib",f:"+- b dy1 y1"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"arcTo",wR:"w",hR:"y1",stAng:"3cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"r",y:"y2"}},{type:"arcTo",wR:"w",hR:"y1",stAng:"0",swAng:"cd4"},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"arcTo",wR:"w",hR:"y1",stAng:"3cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"r",y:"y2"}},{type:"arcTo",wR:"w",hR:"y1",stAng:"0",swAng:"cd4"}],fill:"none",extrusionOk:!1,stroke:!0}]},round1Rect:{avLst:[{n:"adj",f:"val 16667"}],gdLst:[{n:"a",f:"pin 0 adj 50000"},{n:"dx1",f:"*/ ss a 100000"},{n:"x1",f:"+- r 0 dx1"},{n:"idx",f:"*/ dx1 29289 100000"},{n:"ir",f:"+- r 0 idx"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"x1",y:"t"}},{type:"arcTo",wR:"dx1",hR:"dx1",stAng:"3cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},round2DiagRect:{avLst:[{n:"adj1",f:"val 16667"},{n:"adj2",f:"val 0"}],gdLst:[{n:"a1",f:"pin 0 adj1 50000"},{n:"a2",f:"pin 0 adj2 50000"},{n:"x1",f:"*/ ss a1 100000"},{n:"y1",f:"+- b 0 x1"},{n:"a",f:"*/ ss a2 100000"},{n:"x2",f:"+- r 0 a"},{n:"y2",f:"+- b 0 a"},{n:"dx1",f:"*/ x1 29289 100000"},{n:"dx2",f:"*/ a 29289 100000"},{n:"d",f:"+- dx1 0 dx2"},{n:"dx",f:"?: d dx1 dx2"},{n:"ir",f:"+- r 0 dx"},{n:"ib",f:"+- b 0 dx"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"x1",y:"t"}},{type:"lnTo",pt:{x:"x2",y:"t"}},{type:"arcTo",wR:"a",hR:"a",stAng:"3cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"r",y:"y1"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"0",swAng:"cd4"},{type:"lnTo",pt:{x:"a",y:"b"}},{type:"arcTo",wR:"a",hR:"a",stAng:"cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"l",y:"x1"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"cd2",swAng:"cd4"},{type:"close"}],extrusionOk:!1,stroke:!0}]},round2SameRect:{avLst:[{n:"adj1",f:"val 16667"},{n:"adj2",f:"val 0"}],gdLst:[{n:"a1",f:"pin 0 adj1 50000"},{n:"a2",f:"pin 0 adj2 50000"},{n:"tx1",f:"*/ ss a1 100000"},{n:"tx2",f:"+- r 0 tx1"},{n:"bx1",f:"*/ ss a2 100000"},{n:"bx2",f:"+- r 0 bx1"},{n:"by1",f:"+- b 0 bx1"},{n:"d",f:"+- tx1 0 bx1"},{n:"tdx",f:"*/ tx1 29289 100000"},{n:"bdx",f:"*/ bx1 29289 100000"},{n:"il",f:"?: d tdx bdx"},{n:"ir",f:"+- r 0 il"},{n:"ib",f:"+- b 0 bdx"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"tx1",y:"t"}},{type:"lnTo",pt:{x:"tx2",y:"t"}},{type:"arcTo",wR:"tx1",hR:"tx1",stAng:"3cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"r",y:"by1"}},{type:"arcTo",wR:"bx1",hR:"bx1",stAng:"0",swAng:"cd4"},{type:"lnTo",pt:{x:"bx1",y:"b"}},{type:"arcTo",wR:"bx1",hR:"bx1",stAng:"cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"l",y:"tx1"}},{type:"arcTo",wR:"tx1",hR:"tx1",stAng:"cd2",swAng:"cd4"},{type:"close"}],extrusionOk:!1,stroke:!0}]},roundRect:{avLst:[{n:"adj",f:"val 16667"}],gdLst:[{n:"a",f:"pin 0 adj 50000"},{n:"x1",f:"*/ ss a 100000"},{n:"x2",f:"+- r 0 x1"},{n:"y2",f:"+- b 0 x1"},{n:"il",f:"*/ x1 29289 100000"},{n:"ir",f:"+- r 0 il"},{n:"ib",f:"+- b 0 il"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"x1"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"cd2",swAng:"cd4"},{type:"lnTo",pt:{x:"x2",y:"t"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"3cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"r",y:"y2"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"0",swAng:"cd4"},{type:"lnTo",pt:{x:"x1",y:"b"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"cd4",swAng:"cd4"},{type:"close"}],extrusionOk:!1,stroke:!0}]},rtTriangle:{gdLst:[{n:"it",f:"*/ h 7 12"},{n:"ir",f:"*/ w 7 12"},{n:"ib",f:"*/ h 11 12"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"b"}},{type:"lnTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},smileyFace:{avLst:[{n:"adj",f:"val 4653"}],gdLst:[{n:"a",f:"pin -4653 adj 4653"},{n:"x1",f:"*/ w 4969 21699"},{n:"x2",f:"*/ w 6215 21600"},{n:"x3",f:"*/ w 13135 21600"},{n:"x4",f:"*/ w 16640 21600"},{n:"y1",f:"*/ h 7570 21600"},{n:"y3",f:"*/ h 16515 21600"},{n:"dy2",f:"*/ h a 100000"},{n:"y2",f:"+- y3 0 dy2"},{n:"y4",f:"+- y3 dy2 0"},{n:"dy3",f:"*/ h a 50000"},{n:"y5",f:"+- y4 dy3 0"},{n:"idx",f:"cos wd2 2700000"},{n:"idy",f:"sin hd2 2700000"},{n:"il",f:"+- hc 0 idx"},{n:"ir",f:"+- hc idx 0"},{n:"it",f:"+- vc 0 idy"},{n:"ib",f:"+- vc idy 0"},{n:"wR",f:"*/ w 1125 21600"},{n:"hR",f:"*/ h 1125 21600"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"cd2",swAng:"21600000"},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"x2",y:"y1"}},{type:"arcTo",wR:"wR",hR:"hR",stAng:"cd2",swAng:"21600000"},{type:"moveTo",pt:{x:"x3",y:"y1"}},{type:"arcTo",wR:"wR",hR:"hR",stAng:"cd2",swAng:"21600000"}],fill:"darkenLess",extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"x1",y:"y2"}},{type:"quadBezTo",pts:[{x:"hc",y:"y5"},{x:"x4",y:"y2"}]}],fill:"none",extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"cd2",swAng:"21600000"},{type:"close"}],fill:"none",extrusionOk:!1,stroke:!0}]},snip1Rect:{avLst:[{n:"adj",f:"val 16667"}],gdLst:[{n:"a",f:"pin 0 adj 50000"},{n:"dx1",f:"*/ ss a 100000"},{n:"x1",f:"+- r 0 dx1"},{n:"it",f:"*/ dx1 1 2"},{n:"ir",f:"+/ x1 r 2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"x1",y:"t"}},{type:"lnTo",pt:{x:"r",y:"dx1"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},snip2DiagRect:{avLst:[{n:"adj1",f:"val 0"},{n:"adj2",f:"val 16667"}],gdLst:[{n:"a1",f:"pin 0 adj1 50000"},{n:"a2",f:"pin 0 adj2 50000"},{n:"lx1",f:"*/ ss a1 100000"},{n:"lx2",f:"+- r 0 lx1"},{n:"ly1",f:"+- b 0 lx1"},{n:"rx1",f:"*/ ss a2 100000"},{n:"rx2",f:"+- r 0 rx1"},{n:"ry1",f:"+- b 0 rx1"},{n:"d",f:"+- lx1 0 rx1"},{n:"dx",f:"?: d lx1 rx1"},{n:"il",f:"*/ dx 1 2"},{n:"ir",f:"+- r 0 il"},{n:"ib",f:"+- b 0 il"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"lx1",y:"t"}},{type:"lnTo",pt:{x:"rx2",y:"t"}},{type:"lnTo",pt:{x:"r",y:"rx1"}},{type:"lnTo",pt:{x:"r",y:"ly1"}},{type:"lnTo",pt:{x:"lx2",y:"b"}},{type:"lnTo",pt:{x:"rx1",y:"b"}},{type:"lnTo",pt:{x:"l",y:"ry1"}},{type:"lnTo",pt:{x:"l",y:"lx1"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},snip2SameRect:{avLst:[{n:"adj1",f:"val 16667"},{n:"adj2",f:"val 0"}],gdLst:[{n:"a1",f:"pin 0 adj1 50000"},{n:"a2",f:"pin 0 adj2 50000"},{n:"tx1",f:"*/ ss a1 100000"},{n:"tx2",f:"+- r 0 tx1"},{n:"bx1",f:"*/ ss a2 100000"},{n:"bx2",f:"+- r 0 bx1"},{n:"by1",f:"+- b 0 bx1"},{n:"d",f:"+- tx1 0 bx1"},{n:"dx",f:"?: d tx1 bx1"},{n:"il",f:"*/ dx 1 2"},{n:"ir",f:"+- r 0 il"},{n:"it",f:"*/ tx1 1 2"},{n:"ib",f:"+/ by1 b 2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"tx1",y:"t"}},{type:"lnTo",pt:{x:"tx2",y:"t"}},{type:"lnTo",pt:{x:"r",y:"tx1"}},{type:"lnTo",pt:{x:"r",y:"by1"}},{type:"lnTo",pt:{x:"bx2",y:"b"}},{type:"lnTo",pt:{x:"bx1",y:"b"}},{type:"lnTo",pt:{x:"l",y:"by1"}},{type:"lnTo",pt:{x:"l",y:"tx1"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},snipRoundRect:{avLst:[{n:"adj1",f:"val 16667"},{n:"adj2",f:"val 16667"}],gdLst:[{n:"a1",f:"pin 0 adj1 50000"},{n:"a2",f:"pin 0 adj2 50000"},{n:"x1",f:"*/ ss a1 100000"},{n:"dx2",f:"*/ ss a2 100000"},{n:"x2",f:"+- r 0 dx2"},{n:"il",f:"*/ x1 29289 100000"},{n:"ir",f:"+/ x2 r 2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"x1",y:"t"}},{type:"lnTo",pt:{x:"x2",y:"t"}},{type:"lnTo",pt:{x:"r",y:"dx2"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"lnTo",pt:{x:"l",y:"x1"}},{type:"arcTo",wR:"x1",hR:"x1",stAng:"cd2",swAng:"cd4"},{type:"close"}],extrusionOk:!1,stroke:!0}]},squareTabs:{gdLst:[{n:"md",f:"mod w h 0"},{n:"dx",f:"*/ 1 md 20"},{n:"y1",f:"+- 0 b dx"},{n:"x1",f:"+- 0 r dx"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"dx",y:"t"}},{type:"lnTo",pt:{x:"dx",y:"dx"}},{type:"lnTo",pt:{x:"l",y:"dx"}},{type:"close"}],extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"l",y:"y1"}},{type:"lnTo",pt:{x:"dx",y:"y1"}},{type:"lnTo",pt:{x:"dx",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"x1",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"dx"}},{type:"lnTo",pt:{x:"x1",y:"dx"}},{type:"close"}],extrusionOk:!1,stroke:!0},{defines:[{type:"moveTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"r",y:"y1"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"x1",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},star10:{avLst:[{n:"adj",f:"val 42533"},{n:"hf",f:"val 105146"}],gdLst:[{n:"a",f:"pin 0 adj 50000"},{n:"swd2",f:"*/ wd2 hf 100000"},{n:"dx1",f:"*/ swd2 95106 100000"},{n:"dx2",f:"*/ swd2 58779 100000"},{n:"x1",f:"+- hc 0 dx1"},{n:"x2",f:"+- hc 0 dx2"},{n:"x3",f:"+- hc dx2 0"},{n:"x4",f:"+- hc dx1 0"},{n:"dy1",f:"*/ hd2 80902 100000"},{n:"dy2",f:"*/ hd2 30902 100000"},{n:"y1",f:"+- vc 0 dy1"},{n:"y2",f:"+- vc 0 dy2"},{n:"y3",f:"+- vc dy2 0"},{n:"y4",f:"+- vc dy1 0"},{n:"iwd2",f:"*/ swd2 a 50000"},{n:"ihd2",f:"*/ hd2 a 50000"},{n:"sdx1",f:"*/ iwd2 80902 100000"},{n:"sdx2",f:"*/ iwd2 30902 100000"},{n:"sdy1",f:"*/ ihd2 95106 100000"},{n:"sdy2",f:"*/ ihd2 58779 100000"},{n:"sx1",f:"+- hc 0 iwd2"},{n:"sx2",f:"+- hc 0 sdx1"},{n:"sx3",f:"+- hc 0 sdx2"},{n:"sx4",f:"+- hc sdx2 0"},{n:"sx5",f:"+- hc sdx1 0"},{n:"sx6",f:"+- hc iwd2 0"},{n:"sy1",f:"+- vc 0 sdy1"},{n:"sy2",f:"+- vc 0 sdy2"},{n:"sy3",f:"+- vc sdy2 0"},{n:"sy4",f:"+- vc sdy1 0"},{n:"yAdj",f:"+- vc 0 ihd2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"x1",y:"y2"}},{type:"lnTo",pt:{x:"sx2",y:"sy2"}},{type:"lnTo",pt:{x:"x2",y:"y1"}},{type:"lnTo",pt:{x:"sx3",y:"sy1"}},{type:"lnTo",pt:{x:"hc",y:"t"}},{type:"lnTo",pt:{x:"sx4",y:"sy1"}},{type:"lnTo",pt:{x:"x3",y:"y1"}},{type:"lnTo",pt:{x:"sx5",y:"sy2"}},{type:"lnTo",pt:{x:"x4",y:"y2"}},{type:"lnTo",pt:{x:"sx6",y:"vc"}},{type:"lnTo",pt:{x:"x4",y:"y3"}},{type:"lnTo",pt:{x:"sx5",y:"sy3"}},{type:"lnTo",pt:{x:"x3",y:"y4"}},{type:"lnTo",pt:{x:"sx4",y:"sy4"}},{type:"lnTo",pt:{x:"hc",y:"b"}},{type:"lnTo",pt:{x:"sx3",y:"sy4"}},{type:"lnTo",pt:{x:"x2",y:"y4"}},{type:"lnTo",pt:{x:"sx2",y:"sy3"}},{type:"lnTo",pt:{x:"x1",y:"y3"}},{type:"lnTo",pt:{x:"sx1",y:"vc"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},star12:{avLst:[{n:"adj",f:"val 37500"}],gdLst:[{n:"a",f:"pin 0 adj 50000"},{n:"dx1",f:"cos wd2 1800000"},{n:"dy1",f:"sin hd2 3600000"},{n:"x1",f:"+- hc 0 dx1"},{n:"x3",f:"*/ w 3 4"},{n:"x4",f:"+- hc dx1 0"},{n:"y1",f:"+- vc 0 dy1"},{n:"y3",f:"*/ h 3 4"},{n:"y4",f:"+- vc dy1 0"},{n:"iwd2",f:"*/ wd2 a 50000"},{n:"ihd2",f:"*/ hd2 a 50000"},{n:"sdx1",f:"cos iwd2 900000"},{n:"sdx2",f:"cos iwd2 2700000"},{n:"sdx3",f:"cos iwd2 4500000"},{n:"sdy1",f:"sin ihd2 4500000"},{n:"sdy2",f:"sin ihd2 2700000"},{n:"sdy3",f:"sin ihd2 900000"},{n:"sx1",f:"+- hc 0 sdx1"},{n:"sx2",f:"+- hc 0 sdx2"},{n:"sx3",f:"+- hc 0 sdx3"},{n:"sx4",f:"+- hc sdx3 0"},{n:"sx5",f:"+- hc sdx2 0"},{n:"sx6",f:"+- hc sdx1 0"},{n:"sy1",f:"+- vc 0 sdy1"},{n:"sy2",f:"+- vc 0 sdy2"},{n:"sy3",f:"+- vc 0 sdy3"},{n:"sy4",f:"+- vc sdy3 0"},{n:"sy5",f:"+- vc sdy2 0"},{n:"sy6",f:"+- vc sdy1 0"},{n:"yAdj",f:"+- vc 0 ihd2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"lnTo",pt:{x:"sx1",y:"sy3"}},{type:"lnTo",pt:{x:"x1",y:"hd4"}},{type:"lnTo",pt:{x:"sx2",y:"sy2"}},{type:"lnTo",pt:{x:"wd4",y:"y1"}},{type:"lnTo",pt:{x:"sx3",y:"sy1"}},{type:"lnTo",pt:{x:"hc",y:"t"}},{type:"lnTo",pt:{x:"sx4",y:"sy1"}},{type:"lnTo",pt:{x:"x3",y:"y1"}},{type:"lnTo",pt:{x:"sx5",y:"sy2"}},{type:"lnTo",pt:{x:"x4",y:"hd4"}},{type:"lnTo",pt:{x:"sx6",y:"sy3"}},{type:"lnTo",pt:{x:"r",y:"vc"}},{type:"lnTo",pt:{x:"sx6",y:"sy4"}},{type:"lnTo",pt:{x:"x4",y:"y3"}},{type:"lnTo",pt:{x:"sx5",y:"sy5"}},{type:"lnTo",pt:{x:"x3",y:"y4"}},{type:"lnTo",pt:{x:"sx4",y:"sy6"}},{type:"lnTo",pt:{x:"hc",y:"b"}},{type:"lnTo",pt:{x:"sx3",y:"sy6"}},{type:"lnTo",pt:{x:"wd4",y:"y4"}},{type:"lnTo",pt:{x:"sx2",y:"sy5"}},{type:"lnTo",pt:{x:"x1",y:"y3"}},{type:"lnTo",pt:{x:"sx1",y:"sy4"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},star16:{avLst:[{n:"adj",f:"val 37500"}],gdLst:[{n:"a",f:"pin 0 adj 50000"},{n:"dx1",f:"*/ wd2 92388 100000"},{n:"dx2",f:"*/ wd2 70711 100000"},{n:"dx3",f:"*/ wd2 38268 100000"},{n:"dy1",f:"*/ hd2 92388 100000"},{n:"dy2",f:"*/ hd2 70711 100000"},{n:"dy3",f:"*/ hd2 38268 100000"},{n:"x1",f:"+- hc 0 dx1"},{n:"x2",f:"+- hc 0 dx2"},{n:"x3",f:"+- hc 0 dx3"},{n:"x4",f:"+- hc dx3 0"},{n:"x5",f:"+- hc dx2 0"},{n:"x6",f:"+- hc dx1 0"},{n:"y1",f:"+- vc 0 dy1"},{n:"y2",f:"+- vc 0 dy2"},{n:"y3",f:"+- vc 0 dy3"},{n:"y4",f:"+- vc dy3 0"},{n:"y5",f:"+- vc dy2 0"},{n:"y6",f:"+- vc dy1 0"},{n:"iwd2",f:"*/ wd2 a 50000"},{n:"ihd2",f:"*/ hd2 a 50000"},{n:"sdx1",f:"*/ iwd2 98079 100000"},{n:"sdx2",f:"*/ iwd2 83147 100000"},{n:"sdx3",f:"*/ iwd2 55557 100000"},{n:"sdx4",f:"*/ iwd2 19509 100000"},{n:"sdy1",f:"*/ ihd2 98079 100000"},{n:"sdy2",f:"*/ ihd2 83147 100000"},{n:"sdy3",f:"*/ ihd2 55557 100000"},{n:"sdy4",f:"*/ ihd2 19509 100000"},{n:"sx1",f:"+- hc 0 sdx1"},{n:"sx2",f:"+- hc 0 sdx2"},{n:"sx3",f:"+- hc 0 sdx3"},{n:"sx4",f:"+- hc 0 sdx4"},{n:"sx5",f:"+- hc sdx4 0"},{n:"sx6",f:"+- hc sdx3 0"},{n:"sx7",f:"+- hc sdx2 0"},{n:"sx8",f:"+- hc sdx1 0"},{n:"sy1",f:"+- vc 0 sdy1"},{n:"sy2",f:"+- vc 0 sdy2"},{n:"sy3",f:"+- vc 0 sdy3"},{n:"sy4",f:"+- vc 0 sdy4"},{n:"sy5",f:"+- vc sdy4 0"},{n:"sy6",f:"+- vc sdy3 0"},{n:"sy7",f:"+- vc sdy2 0"},{n:"sy8",f:"+- vc sdy1 0"},{n:"idx",f:"cos iwd2 2700000"},{n:"idy",f:"sin ihd2 2700000"},{n:"il",f:"+- hc 0 idx"},{n:"it",f:"+- vc 0 idy"},{n:"ir",f:"+- hc idx 0"},{n:"ib",f:"+- vc idy 0"},{n:"yAdj",f:"+- vc 0 ihd2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"lnTo",pt:{x:"sx1",y:"sy4"}},{type:"lnTo",pt:{x:"x1",y:"y3"}},{type:"lnTo",pt:{x:"sx2",y:"sy3"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"sx3",y:"sy2"}},{type:"lnTo",pt:{x:"x3",y:"y1"}},{type:"lnTo",pt:{x:"sx4",y:"sy1"}},{type:"lnTo",pt:{x:"hc",y:"t"}},{type:"lnTo",pt:{x:"sx5",y:"sy1"}},{type:"lnTo",pt:{x:"x4",y:"y1"}},{type:"lnTo",pt:{x:"sx6",y:"sy2"}},{type:"lnTo",pt:{x:"x5",y:"y2"}},{type:"lnTo",pt:{x:"sx7",y:"sy3"}},{type:"lnTo",pt:{x:"x6",y:"y3"}},{type:"lnTo",pt:{x:"sx8",y:"sy4"}},{type:"lnTo",pt:{x:"r",y:"vc"}},{type:"lnTo",pt:{x:"sx8",y:"sy5"}},{type:"lnTo",pt:{x:"x6",y:"y4"}},{type:"lnTo",pt:{x:"sx7",y:"sy6"}},{type:"lnTo",pt:{x:"x5",y:"y5"}},{type:"lnTo",pt:{x:"sx6",y:"sy7"}},{type:"lnTo",pt:{x:"x4",y:"y6"}},{type:"lnTo",pt:{x:"sx5",y:"sy8"}},{type:"lnTo",pt:{x:"hc",y:"b"}},{type:"lnTo",pt:{x:"sx4",y:"sy8"}},{type:"lnTo",pt:{x:"x3",y:"y6"}},{type:"lnTo",pt:{x:"sx3",y:"sy7"}},{type:"lnTo",pt:{x:"x2",y:"y5"}},{type:"lnTo",pt:{x:"sx2",y:"sy6"}},{type:"lnTo",pt:{x:"x1",y:"y4"}},{type:"lnTo",pt:{x:"sx1",y:"sy5"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},star24:{avLst:[{n:"adj",f:"val 37500"}],gdLst:[{n:"a",f:"pin 0 adj 50000"},{n:"dx1",f:"cos wd2 900000"},{n:"dx2",f:"cos wd2 1800000"},{n:"dx3",f:"cos wd2 2700000"},{n:"dx4",f:"val wd4"},{n:"dx5",f:"cos wd2 4500000"},{n:"dy1",f:"sin hd2 4500000"},{n:"dy2",f:"sin hd2 3600000"},{n:"dy3",f:"sin hd2 2700000"},{n:"dy4",f:"val hd4"},{n:"dy5",f:"sin hd2 900000"},{n:"x1",f:"+- hc 0 dx1"},{n:"x2",f:"+- hc 0 dx2"},{n:"x3",f:"+- hc 0 dx3"},{n:"x4",f:"+- hc 0 dx4"},{n:"x5",f:"+- hc 0 dx5"},{n:"x6",f:"+- hc dx5 0"},{n:"x7",f:"+- hc dx4 0"},{n:"x8",f:"+- hc dx3 0"},{n:"x9",f:"+- hc dx2 0"},{n:"x10",f:"+- hc dx1 0"},{n:"y1",f:"+- vc 0 dy1"},{n:"y2",f:"+- vc 0 dy2"},{n:"y3",f:"+- vc 0 dy3"},{n:"y4",f:"+- vc 0 dy4"},{n:"y5",f:"+- vc 0 dy5"},{n:"y6",f:"+- vc dy5 0"},{n:"y7",f:"+- vc dy4 0"},{n:"y8",f:"+- vc dy3 0"},{n:"y9",f:"+- vc dy2 0"},{n:"y10",f:"+- vc dy1 0"},{n:"iwd2",f:"*/ wd2 a 50000"},{n:"ihd2",f:"*/ hd2 a 50000"},{n:"sdx1",f:"*/ iwd2 99144 100000"},{n:"sdx2",f:"*/ iwd2 92388 100000"},{n:"sdx3",f:"*/ iwd2 79335 100000"},{n:"sdx4",f:"*/ iwd2 60876 100000"},{n:"sdx5",f:"*/ iwd2 38268 100000"},{n:"sdx6",f:"*/ iwd2 13053 100000"},{n:"sdy1",f:"*/ ihd2 99144 100000"},{n:"sdy2",f:"*/ ihd2 92388 100000"},{n:"sdy3",f:"*/ ihd2 79335 100000"},{n:"sdy4",f:"*/ ihd2 60876 100000"},{n:"sdy5",f:"*/ ihd2 38268 100000"},{n:"sdy6",f:"*/ ihd2 13053 100000"},{n:"sx1",f:"+- hc 0 sdx1"},{n:"sx2",f:"+- hc 0 sdx2"},{n:"sx3",f:"+- hc 0 sdx3"},{n:"sx4",f:"+- hc 0 sdx4"},{n:"sx5",f:"+- hc 0 sdx5"},{n:"sx6",f:"+- hc 0 sdx6"},{n:"sx7",f:"+- hc sdx6 0"},{n:"sx8",f:"+- hc sdx5 0"},{n:"sx9",f:"+- hc sdx4 0"},{n:"sx10",f:"+- hc sdx3 0"},{n:"sx11",f:"+- hc sdx2 0"},{n:"sx12",f:"+- hc sdx1 0"},{n:"sy1",f:"+- vc 0 sdy1"},{n:"sy2",f:"+- vc 0 sdy2"},{n:"sy3",f:"+- vc 0 sdy3"},{n:"sy4",f:"+- vc 0 sdy4"},{n:"sy5",f:"+- vc 0 sdy5"},{n:"sy6",f:"+- vc 0 sdy6"},{n:"sy7",f:"+- vc sdy6 0"},{n:"sy8",f:"+- vc sdy5 0"},{n:"sy9",f:"+- vc sdy4 0"},{n:"sy10",f:"+- vc sdy3 0"},{n:"sy11",f:"+- vc sdy2 0"},{n:"sy12",f:"+- vc sdy1 0"},{n:"idx",f:"cos iwd2 2700000"},{n:"idy",f:"sin ihd2 2700000"},{n:"il",f:"+- hc 0 idx"},{n:"it",f:"+- vc 0 idy"},{n:"ir",f:"+- hc idx 0"},{n:"ib",f:"+- vc idy 0"},{n:"yAdj",f:"+- vc 0 ihd2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"lnTo",pt:{x:"sx1",y:"sy6"}},{type:"lnTo",pt:{x:"x1",y:"y5"}},{type:"lnTo",pt:{x:"sx2",y:"sy5"}},{type:"lnTo",pt:{x:"x2",y:"y4"}},{type:"lnTo",pt:{x:"sx3",y:"sy4"}},{type:"lnTo",pt:{x:"x3",y:"y3"}},{type:"lnTo",pt:{x:"sx4",y:"sy3"}},{type:"lnTo",pt:{x:"x4",y:"y2"}},{type:"lnTo",pt:{x:"sx5",y:"sy2"}},{type:"lnTo",pt:{x:"x5",y:"y1"}},{type:"lnTo",pt:{x:"sx6",y:"sy1"}},{type:"lnTo",pt:{x:"hc",y:"t"}},{type:"lnTo",pt:{x:"sx7",y:"sy1"}},{type:"lnTo",pt:{x:"x6",y:"y1"}},{type:"lnTo",pt:{x:"sx8",y:"sy2"}},{type:"lnTo",pt:{x:"x7",y:"y2"}},{type:"lnTo",pt:{x:"sx9",y:"sy3"}},{type:"lnTo",pt:{x:"x8",y:"y3"}},{type:"lnTo",pt:{x:"sx10",y:"sy4"}},{type:"lnTo",pt:{x:"x9",y:"y4"}},{type:"lnTo",pt:{x:"sx11",y:"sy5"}},{type:"lnTo",pt:{x:"x10",y:"y5"}},{type:"lnTo",pt:{x:"sx12",y:"sy6"}},{type:"lnTo",pt:{x:"r",y:"vc"}},{type:"lnTo",pt:{x:"sx12",y:"sy7"}},{type:"lnTo",pt:{x:"x10",y:"y6"}},{type:"lnTo",pt:{x:"sx11",y:"sy8"}},{type:"lnTo",pt:{x:"x9",y:"y7"}},{type:"lnTo",pt:{x:"sx10",y:"sy9"}},{type:"lnTo",pt:{x:"x8",y:"y8"}},{type:"lnTo",pt:{x:"sx9",y:"sy10"}},{type:"lnTo",pt:{x:"x7",y:"y9"}},{type:"lnTo",pt:{x:"sx8",y:"sy11"}},{type:"lnTo",pt:{x:"x6",y:"y10"}},{type:"lnTo",pt:{x:"sx7",y:"sy12"}},{type:"lnTo",pt:{x:"hc",y:"b"}},{type:"lnTo",pt:{x:"sx6",y:"sy12"}},{type:"lnTo",pt:{x:"x5",y:"y10"}},{type:"lnTo",pt:{x:"sx5",y:"sy11"}},{type:"lnTo",pt:{x:"x4",y:"y9"}},{type:"lnTo",pt:{x:"sx4",y:"sy10"}},{type:"lnTo",pt:{x:"x3",y:"y8"}},{type:"lnTo",pt:{x:"sx3",y:"sy9"}},{type:"lnTo",pt:{x:"x2",y:"y7"}},{type:"lnTo",pt:{x:"sx2",y:"sy8"}},{type:"lnTo",pt:{x:"x1",y:"y6"}},{type:"lnTo",pt:{x:"sx1",y:"sy7"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},star32:{avLst:[{n:"adj",f:"val 37500"}],gdLst:[{n:"a",f:"pin 0 adj 50000"},{n:"dx1",f:"*/ wd2 98079 100000"},{n:"dx2",f:"*/ wd2 92388 100000"},{n:"dx3",f:"*/ wd2 83147 100000"},{n:"dx4",f:"cos wd2 2700000"},{n:"dx5",f:"*/ wd2 55557 100000"},{n:"dx6",f:"*/ wd2 38268 100000"},{n:"dx7",f:"*/ wd2 19509 100000"},{n:"dy1",f:"*/ hd2 98079 100000"},{n:"dy2",f:"*/ hd2 92388 100000"},{n:"dy3",f:"*/ hd2 83147 100000"},{n:"dy4",f:"sin hd2 2700000"},{n:"dy5",f:"*/ hd2 55557 100000"},{n:"dy6",f:"*/ hd2 38268 100000"},{n:"dy7",f:"*/ hd2 19509 100000"},{n:"x1",f:"+- hc 0 dx1"},{n:"x2",f:"+- hc 0 dx2"},{n:"x3",f:"+- hc 0 dx3"},{n:"x4",f:"+- hc 0 dx4"},{n:"x5",f:"+- hc 0 dx5"},{n:"x6",f:"+- hc 0 dx6"},{n:"x7",f:"+- hc 0 dx7"},{n:"x8",f:"+- hc dx7 0"},{n:"x9",f:"+- hc dx6 0"},{n:"x10",f:"+- hc dx5 0"},{n:"x11",f:"+- hc dx4 0"},{n:"x12",f:"+- hc dx3 0"},{n:"x13",f:"+- hc dx2 0"},{n:"x14",f:"+- hc dx1 0"},{n:"y1",f:"+- vc 0 dy1"},{n:"y2",f:"+- vc 0 dy2"},{n:"y3",f:"+- vc 0 dy3"},{n:"y4",f:"+- vc 0 dy4"},{n:"y5",f:"+- vc 0 dy5"},{n:"y6",f:"+- vc 0 dy6"},{n:"y7",f:"+- vc 0 dy7"},{n:"y8",f:"+- vc dy7 0"},{n:"y9",f:"+- vc dy6 0"},{n:"y10",f:"+- vc dy5 0"},{n:"y11",f:"+- vc dy4 0"},{n:"y12",f:"+- vc dy3 0"},{n:"y13",f:"+- vc dy2 0"},{n:"y14",f:"+- vc dy1 0"},{n:"iwd2",f:"*/ wd2 a 50000"},{n:"ihd2",f:"*/ hd2 a 50000"},{n:"sdx1",f:"*/ iwd2 99518 100000"},{n:"sdx2",f:"*/ iwd2 95694 100000"},{n:"sdx3",f:"*/ iwd2 88192 100000"},{n:"sdx4",f:"*/ iwd2 77301 100000"},{n:"sdx5",f:"*/ iwd2 63439 100000"},{n:"sdx6",f:"*/ iwd2 47140 100000"},{n:"sdx7",f:"*/ iwd2 29028 100000"},{n:"sdx8",f:"*/ iwd2 9802 100000"},{n:"sdy1",f:"*/ ihd2 99518 100000"},{n:"sdy2",f:"*/ ihd2 95694 100000"},{n:"sdy3",f:"*/ ihd2 88192 100000"},{n:"sdy4",f:"*/ ihd2 77301 100000"},{n:"sdy5",f:"*/ ihd2 63439 100000"},{n:"sdy6",f:"*/ ihd2 47140 100000"},{n:"sdy7",f:"*/ ihd2 29028 100000"},{n:"sdy8",f:"*/ ihd2 9802 100000"},{n:"sx1",f:"+- hc 0 sdx1"},{n:"sx2",f:"+- hc 0 sdx2"},{n:"sx3",f:"+- hc 0 sdx3"},{n:"sx4",f:"+- hc 0 sdx4"},{n:"sx5",f:"+- hc 0 sdx5"},{n:"sx6",f:"+- hc 0 sdx6"},{n:"sx7",f:"+- hc 0 sdx7"},{n:"sx8",f:"+- hc 0 sdx8"},{n:"sx9",f:"+- hc sdx8 0"},{n:"sx10",f:"+- hc sdx7 0"},{n:"sx11",f:"+- hc sdx6 0"},{n:"sx12",f:"+- hc sdx5 0"},{n:"sx13",f:"+- hc sdx4 0"},{n:"sx14",f:"+- hc sdx3 0"},{n:"sx15",f:"+- hc sdx2 0"},{n:"sx16",f:"+- hc sdx1 0"},{n:"sy1",f:"+- vc 0 sdy1"},{n:"sy2",f:"+- vc 0 sdy2"},{n:"sy3",f:"+- vc 0 sdy3"},{n:"sy4",f:"+- vc 0 sdy4"},{n:"sy5",f:"+- vc 0 sdy5"},{n:"sy6",f:"+- vc 0 sdy6"},{n:"sy7",f:"+- vc 0 sdy7"},{n:"sy8",f:"+- vc 0 sdy8"},{n:"sy9",f:"+- vc sdy8 0"},{n:"sy10",f:"+- vc sdy7 0"},{n:"sy11",f:"+- vc sdy6 0"},{n:"sy12",f:"+- vc sdy5 0"},{n:"sy13",f:"+- vc sdy4 0"},{n:"sy14",f:"+- vc sdy3 0"},{n:"sy15",f:"+- vc sdy2 0"},{n:"sy16",f:"+- vc sdy1 0"},{n:"idx",f:"cos iwd2 2700000"},{n:"idy",f:"sin ihd2 2700000"},{n:"il",f:"+- hc 0 idx"},{n:"it",f:"+- vc 0 idy"},{n:"ir",f:"+- hc idx 0"},{n:"ib",f:"+- vc idy 0"},{n:"yAdj",f:"+- vc 0 ihd2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"lnTo",pt:{x:"sx1",y:"sy8"}},{type:"lnTo",pt:{x:"x1",y:"y7"}},{type:"lnTo",pt:{x:"sx2",y:"sy7"}},{type:"lnTo",pt:{x:"x2",y:"y6"}},{type:"lnTo",pt:{x:"sx3",y:"sy6"}},{type:"lnTo",pt:{x:"x3",y:"y5"}},{type:"lnTo",pt:{x:"sx4",y:"sy5"}},{type:"lnTo",pt:{x:"x4",y:"y4"}},{type:"lnTo",pt:{x:"sx5",y:"sy4"}},{type:"lnTo",pt:{x:"x5",y:"y3"}},{type:"lnTo",pt:{x:"sx6",y:"sy3"}},{type:"lnTo",pt:{x:"x6",y:"y2"}},{type:"lnTo",pt:{x:"sx7",y:"sy2"}},{type:"lnTo",pt:{x:"x7",y:"y1"}},{type:"lnTo",pt:{x:"sx8",y:"sy1"}},{type:"lnTo",pt:{x:"hc",y:"t"}},{type:"lnTo",pt:{x:"sx9",y:"sy1"}},{type:"lnTo",pt:{x:"x8",y:"y1"}},{type:"lnTo",pt:{x:"sx10",y:"sy2"}},{type:"lnTo",pt:{x:"x9",y:"y2"}},{type:"lnTo",pt:{x:"sx11",y:"sy3"}},{type:"lnTo",pt:{x:"x10",y:"y3"}},{type:"lnTo",pt:{x:"sx12",y:"sy4"}},{type:"lnTo",pt:{x:"x11",y:"y4"}},{type:"lnTo",pt:{x:"sx13",y:"sy5"}},{type:"lnTo",pt:{x:"x12",y:"y5"}},{type:"lnTo",pt:{x:"sx14",y:"sy6"}},{type:"lnTo",pt:{x:"x13",y:"y6"}},{type:"lnTo",pt:{x:"sx15",y:"sy7"}},{type:"lnTo",pt:{x:"x14",y:"y7"}},{type:"lnTo",pt:{x:"sx16",y:"sy8"}},{type:"lnTo",pt:{x:"r",y:"vc"}},{type:"lnTo",pt:{x:"sx16",y:"sy9"}},{type:"lnTo",pt:{x:"x14",y:"y8"}},{type:"lnTo",pt:{x:"sx15",y:"sy10"}},{type:"lnTo",pt:{x:"x13",y:"y9"}},{type:"lnTo",pt:{x:"sx14",y:"sy11"}},{type:"lnTo",pt:{x:"x12",y:"y10"}},{type:"lnTo",pt:{x:"sx13",y:"sy12"}},{type:"lnTo",pt:{x:"x11",y:"y11"}},{type:"lnTo",pt:{x:"sx12",y:"sy13"}},{type:"lnTo",pt:{x:"x10",y:"y12"}},{type:"lnTo",pt:{x:"sx11",y:"sy14"}},{type:"lnTo",pt:{x:"x9",y:"y13"}},{type:"lnTo",pt:{x:"sx10",y:"sy15"}},{type:"lnTo",pt:{x:"x8",y:"y14"}},{type:"lnTo",pt:{x:"sx9",y:"sy16"}},{type:"lnTo",pt:{x:"hc",y:"b"}},{type:"lnTo",pt:{x:"sx8",y:"sy16"}},{type:"lnTo",pt:{x:"x7",y:"y14"}},{type:"lnTo",pt:{x:"sx7",y:"sy15"}},{type:"lnTo",pt:{x:"x6",y:"y13"}},{type:"lnTo",pt:{x:"sx6",y:"sy14"}},{type:"lnTo",pt:{x:"x5",y:"y12"}},{type:"lnTo",pt:{x:"sx5",y:"sy13"}},{type:"lnTo",pt:{x:"x4",y:"y11"}},{type:"lnTo",pt:{x:"sx4",y:"sy12"}},{type:"lnTo",pt:{x:"x3",y:"y10"}},{type:"lnTo",pt:{x:"sx3",y:"sy11"}},{type:"lnTo",pt:{x:"x2",y:"y9"}},{type:"lnTo",pt:{x:"sx2",y:"sy10"}},{type:"lnTo",pt:{x:"x1",y:"y8"}},{type:"lnTo",pt:{x:"sx1",y:"sy9"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},star4:{avLst:[{n:"adj",f:"val 12500"}],gdLst:[{n:"a",f:"pin 0 adj 50000"},{n:"iwd2",f:"*/ wd2 a 50000"},{n:"ihd2",f:"*/ hd2 a 50000"},{n:"sdx",f:"cos iwd2 2700000"},{n:"sdy",f:"sin ihd2 2700000"},{n:"sx1",f:"+- hc 0 sdx"},{n:"sx2",f:"+- hc sdx 0"},{n:"sy1",f:"+- vc 0 sdy"},{n:"sy2",f:"+- vc sdy 0"},{n:"yAdj",f:"+- vc 0 ihd2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"lnTo",pt:{x:"sx1",y:"sy1"}},{type:"lnTo",pt:{x:"hc",y:"t"}},{type:"lnTo",pt:{x:"sx2",y:"sy1"}},{type:"lnTo",pt:{x:"r",y:"vc"}},{type:"lnTo",pt:{x:"sx2",y:"sy2"}},{type:"lnTo",pt:{x:"hc",y:"b"}},{type:"lnTo",pt:{x:"sx1",y:"sy2"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},star5:{avLst:[{n:"adj",f:"val 19098"},{n:"hf",f:"val 105146"},{n:"vf",f:"val 110557"}],gdLst:[{n:"a",f:"pin 0 adj 50000"},{n:"swd2",f:"*/ wd2 hf 100000"},{n:"shd2",f:"*/ hd2 vf 100000"},{n:"svc",f:"*/ vc  vf 100000"},{n:"dx1",f:"cos swd2 1080000"},{n:"dx2",f:"cos swd2 18360000"},{n:"dy1",f:"sin shd2 1080000"},{n:"dy2",f:"sin shd2 18360000"},{n:"x1",f:"+- hc 0 dx1"},{n:"x2",f:"+- hc 0 dx2"},{n:"x3",f:"+- hc dx2 0"},{n:"x4",f:"+- hc dx1 0"},{n:"y1",f:"+- svc 0 dy1"},{n:"y2",f:"+- svc 0 dy2"},{n:"iwd2",f:"*/ swd2 a 50000"},{n:"ihd2",f:"*/ shd2 a 50000"},{n:"sdx1",f:"cos iwd2 20520000"},{n:"sdx2",f:"cos iwd2 3240000"},{n:"sdy1",f:"sin ihd2 3240000"},{n:"sdy2",f:"sin ihd2 20520000"},{n:"sx1",f:"+- hc 0 sdx1"},{n:"sx2",f:"+- hc 0 sdx2"},{n:"sx3",f:"+- hc sdx2 0"},{n:"sx4",f:"+- hc sdx1 0"},{n:"sy1",f:"+- svc 0 sdy1"},{n:"sy2",f:"+- svc 0 sdy2"},{n:"sy3",f:"+- svc ihd2 0"},{n:"yAdj",f:"+- svc 0 ihd2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"sx2",y:"sy1"}},{type:"lnTo",pt:{x:"hc",y:"t"}},{type:"lnTo",pt:{x:"sx3",y:"sy1"}},{type:"lnTo",pt:{x:"x4",y:"y1"}},{type:"lnTo",pt:{x:"sx4",y:"sy2"}},{type:"lnTo",pt:{x:"x3",y:"y2"}},{type:"lnTo",pt:{x:"hc",y:"sy3"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"sx1",y:"sy2"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},star6:{avLst:[{n:"adj",f:"val 28868"},{n:"hf",f:"val 115470"}],gdLst:[{n:"a",f:"pin 0 adj 50000"},{n:"swd2",f:"*/ wd2 hf 100000"},{n:"dx1",f:"cos swd2 1800000"},{n:"x1",f:"+- hc 0 dx1"},{n:"x2",f:"+- hc dx1 0"},{n:"y2",f:"+- vc hd4 0"},{n:"iwd2",f:"*/ swd2 a 50000"},{n:"ihd2",f:"*/ hd2 a 50000"},{n:"sdx2",f:"*/ iwd2 1 2"},{n:"sx1",f:"+- hc 0 iwd2"},{n:"sx2",f:"+- hc 0 sdx2"},{n:"sx3",f:"+- hc sdx2 0"},{n:"sx4",f:"+- hc iwd2 0"},{n:"sdy1",f:"sin ihd2 3600000"},{n:"sy1",f:"+- vc 0 sdy1"},{n:"sy2",f:"+- vc sdy1 0"},{n:"yAdj",f:"+- vc 0 ihd2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"x1",y:"hd4"}},{type:"lnTo",pt:{x:"sx2",y:"sy1"}},{type:"lnTo",pt:{x:"hc",y:"t"}},{type:"lnTo",pt:{x:"sx3",y:"sy1"}},{type:"lnTo",pt:{x:"x2",y:"hd4"}},{type:"lnTo",pt:{x:"sx4",y:"vc"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"sx3",y:"sy2"}},{type:"lnTo",pt:{x:"hc",y:"b"}},{type:"lnTo",pt:{x:"sx2",y:"sy2"}},{type:"lnTo",pt:{x:"x1",y:"y2"}},{type:"lnTo",pt:{x:"sx1",y:"vc"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},star7:{avLst:[{n:"adj",f:"val 34601"},{n:"hf",f:"val 102572"},{n:"vf",f:"val 105210"}],gdLst:[{n:"a",f:"pin 0 adj 50000"},{n:"swd2",f:"*/ wd2 hf 100000"},{n:"shd2",f:"*/ hd2 vf 100000"},{n:"svc",f:"*/ vc  vf 100000"},{n:"dx1",f:"*/ swd2 97493 100000"},{n:"dx2",f:"*/ swd2 78183 100000"},{n:"dx3",f:"*/ swd2 43388 100000"},{n:"dy1",f:"*/ shd2 62349 100000"},{n:"dy2",f:"*/ shd2 22252 100000"},{n:"dy3",f:"*/ shd2 90097 100000"},{n:"x1",f:"+- hc 0 dx1"},{n:"x2",f:"+- hc 0 dx2"},{n:"x3",f:"+- hc 0 dx3"},{n:"x4",f:"+- hc dx3 0"},{n:"x5",f:"+- hc dx2 0"},{n:"x6",f:"+- hc dx1 0"},{n:"y1",f:"+- svc 0 dy1"},{n:"y2",f:"+- svc dy2 0"},{n:"y3",f:"+- svc dy3 0"},{n:"iwd2",f:"*/ swd2 a 50000"},{n:"ihd2",f:"*/ shd2 a 50000"},{n:"sdx1",f:"*/ iwd2 97493 100000"},{n:"sdx2",f:"*/ iwd2 78183 100000"},{n:"sdx3",f:"*/ iwd2 43388 100000"},{n:"sx1",f:"+- hc 0 sdx1"},{n:"sx2",f:"+- hc 0 sdx2"},{n:"sx3",f:"+- hc 0 sdx3"},{n:"sx4",f:"+- hc sdx3 0"},{n:"sx5",f:"+- hc sdx2 0"},{n:"sx6",f:"+- hc sdx1 0"},{n:"sdy1",f:"*/ ihd2 90097 100000"},{n:"sdy2",f:"*/ ihd2 22252 100000"},{n:"sdy3",f:"*/ ihd2 62349 100000"},{n:"sy1",f:"+- svc 0 sdy1"},{n:"sy2",f:"+- svc 0 sdy2"},{n:"sy3",f:"+- svc sdy3 0"},{n:"sy4",f:"+- svc ihd2 0"},{n:"yAdj",f:"+- svc 0 ihd2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"x1",y:"y2"}},{type:"lnTo",pt:{x:"sx1",y:"sy2"}},{type:"lnTo",pt:{x:"x2",y:"y1"}},{type:"lnTo",pt:{x:"sx3",y:"sy1"}},{type:"lnTo",pt:{x:"hc",y:"t"}},{type:"lnTo",pt:{x:"sx4",y:"sy1"}},{type:"lnTo",pt:{x:"x5",y:"y1"}},{type:"lnTo",pt:{x:"sx6",y:"sy2"}},{type:"lnTo",pt:{x:"x6",y:"y2"}},{type:"lnTo",pt:{x:"sx5",y:"sy3"}},{type:"lnTo",pt:{x:"x4",y:"y3"}},{type:"lnTo",pt:{x:"hc",y:"sy4"}},{type:"lnTo",pt:{x:"x3",y:"y3"}},{type:"lnTo",pt:{x:"sx2",y:"sy3"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},star8:{avLst:[{n:"adj",f:"val 37500"}],gdLst:[{n:"a",f:"pin 0 adj 50000"},{n:"dx1",f:"cos wd2 2700000"},{n:"x1",f:"+- hc 0 dx1"},{n:"x2",f:"+- hc dx1 0"},{n:"dy1",f:"sin hd2 2700000"},{n:"y1",f:"+- vc 0 dy1"},{n:"y2",f:"+- vc dy1 0"},{n:"iwd2",f:"*/ wd2 a 50000"},{n:"ihd2",f:"*/ hd2 a 50000"},{n:"sdx1",f:"*/ iwd2 92388 100000"},{n:"sdx2",f:"*/ iwd2 38268 100000"},{n:"sdy1",f:"*/ ihd2 92388 100000"},{n:"sdy2",f:"*/ ihd2 38268 100000"},{n:"sx1",f:"+- hc 0 sdx1"},{n:"sx2",f:"+- hc 0 sdx2"},{n:"sx3",f:"+- hc sdx2 0"},{n:"sx4",f:"+- hc sdx1 0"},{n:"sy1",f:"+- vc 0 sdy1"},{n:"sy2",f:"+- vc 0 sdy2"},{n:"sy3",f:"+- vc sdy2 0"},{n:"sy4",f:"+- vc sdy1 0"},{n:"yAdj",f:"+- vc 0 ihd2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"lnTo",pt:{x:"sx1",y:"sy2"}},{type:"lnTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"sx2",y:"sy1"}},{type:"lnTo",pt:{x:"hc",y:"t"}},{type:"lnTo",pt:{x:"sx3",y:"sy1"}},{type:"lnTo",pt:{x:"x2",y:"y1"}},{type:"lnTo",pt:{x:"sx4",y:"sy2"}},{type:"lnTo",pt:{x:"r",y:"vc"}},{type:"lnTo",pt:{x:"sx4",y:"sy3"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"sx3",y:"sy4"}},{type:"lnTo",pt:{x:"hc",y:"b"}},{type:"lnTo",pt:{x:"sx2",y:"sy4"}},{type:"lnTo",pt:{x:"x1",y:"y2"}},{type:"lnTo",pt:{x:"sx1",y:"sy3"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},straightConnector1:{pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}}],fill:"none",extrusionOk:!1,stroke:!0}]},stripedRightArrow:{avLst:[{n:"adj1",f:"val 50000"},{n:"adj2",f:"val 50000"}],gdLst:[{n:"maxAdj2",f:"*/ 84375 w ss"},{n:"a1",f:"pin 0 adj1 100000"},{n:"a2",f:"pin 0 adj2 maxAdj2"},{n:"x4",f:"*/ ss 5 32"},{n:"dx5",f:"*/ ss a2 100000"},{n:"x5",f:"+- r 0 dx5"},{n:"dy1",f:"*/ h a1 200000"},{n:"y1",f:"+- vc 0 dy1"},{n:"y2",f:"+- vc dy1 0"},{n:"dx6",f:"*/ dy1 dx5 hd2"},{n:"x6",f:"+- r 0 dx6"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"y1"}},{type:"lnTo",pt:{x:"ssd32",y:"y1"}},{type:"lnTo",pt:{x:"ssd32",y:"y2"}},{type:"lnTo",pt:{x:"l",y:"y2"}},{type:"close"},{type:"moveTo",pt:{x:"ssd16",y:"y1"}},{type:"lnTo",pt:{x:"ssd8",y:"y1"}},{type:"lnTo",pt:{x:"ssd8",y:"y2"}},{type:"lnTo",pt:{x:"ssd16",y:"y2"}},{type:"close"},{type:"moveTo",pt:{x:"x4",y:"y1"}},{type:"lnTo",pt:{x:"x5",y:"y1"}},{type:"lnTo",pt:{x:"x5",y:"t"}},{type:"lnTo",pt:{x:"r",y:"vc"}},{type:"lnTo",pt:{x:"x5",y:"b"}},{type:"lnTo",pt:{x:"x5",y:"y2"}},{type:"lnTo",pt:{x:"x4",y:"y2"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},sun:{avLst:[{n:"adj",f:"val 25000"}],gdLst:[{n:"a",f:"pin 12500 adj 46875"},{n:"g0",f:"+- 50000 0 a"},{n:"g1",f:"*/ g0 30274 32768"},{n:"g2",f:"*/ g0 12540 32768"},{n:"g3",f:"+- g1 50000 0"},{n:"g4",f:"+- g2 50000 0"},{n:"g5",f:"+- 50000 0 g1"},{n:"g6",f:"+- 50000 0 g2"},{n:"g7",f:"*/ g0 23170 32768"},{n:"g8",f:"+- 50000 g7 0"},{n:"g9",f:"+- 50000 0 g7"},{n:"g10",f:"*/ g5 3 4"},{n:"g11",f:"*/ g6 3 4"},{n:"g12",f:"+- g10 3662 0"},{n:"g13",f:"+- g11 3662 0"},{n:"g14",f:"+- g11 12500 0"},{n:"g15",f:"+- 100000 0 g10"},{n:"g16",f:"+- 100000 0 g12"},{n:"g17",f:"+- 100000 0 g13"},{n:"g18",f:"+- 100000 0 g14"},{n:"ox1",f:"*/ w 18436 21600"},{n:"oy1",f:"*/ h 3163 21600"},{n:"ox2",f:"*/ w 3163 21600"},{n:"oy2",f:"*/ h 18436 21600"},{n:"x8",f:"*/ w g8 100000"},{n:"x9",f:"*/ w g9 100000"},{n:"x10",f:"*/ w g10 100000"},{n:"x12",f:"*/ w g12 100000"},{n:"x13",f:"*/ w g13 100000"},{n:"x14",f:"*/ w g14 100000"},{n:"x15",f:"*/ w g15 100000"},{n:"x16",f:"*/ w g16 100000"},{n:"x17",f:"*/ w g17 100000"},{n:"x18",f:"*/ w g18 100000"},{n:"x19",f:"*/ w a 100000"},{n:"wR",f:"*/ w g0 100000"},{n:"hR",f:"*/ h g0 100000"},{n:"y8",f:"*/ h g8 100000"},{n:"y9",f:"*/ h g9 100000"},{n:"y10",f:"*/ h g10 100000"},{n:"y12",f:"*/ h g12 100000"},{n:"y13",f:"*/ h g13 100000"},{n:"y14",f:"*/ h g14 100000"},{n:"y15",f:"*/ h g15 100000"},{n:"y16",f:"*/ h g16 100000"},{n:"y17",f:"*/ h g17 100000"},{n:"y18",f:"*/ h g18 100000"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"r",y:"vc"}},{type:"lnTo",pt:{x:"x15",y:"y18"}},{type:"lnTo",pt:{x:"x15",y:"y14"}},{type:"close"},{type:"moveTo",pt:{x:"ox1",y:"oy1"}},{type:"lnTo",pt:{x:"x16",y:"y13"}},{type:"lnTo",pt:{x:"x17",y:"y12"}},{type:"close"},{type:"moveTo",pt:{x:"hc",y:"t"}},{type:"lnTo",pt:{x:"x18",y:"y10"}},{type:"lnTo",pt:{x:"x14",y:"y10"}},{type:"close"},{type:"moveTo",pt:{x:"ox2",y:"oy1"}},{type:"lnTo",pt:{x:"x13",y:"y12"}},{type:"lnTo",pt:{x:"x12",y:"y13"}},{type:"close"},{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"lnTo",pt:{x:"x10",y:"y14"}},{type:"lnTo",pt:{x:"x10",y:"y18"}},{type:"close"},{type:"moveTo",pt:{x:"ox2",y:"oy2"}},{type:"lnTo",pt:{x:"x12",y:"y17"}},{type:"lnTo",pt:{x:"x13",y:"y16"}},{type:"close"},{type:"moveTo",pt:{x:"hc",y:"b"}},{type:"lnTo",pt:{x:"x14",y:"y15"}},{type:"lnTo",pt:{x:"x18",y:"y15"}},{type:"close"},{type:"moveTo",pt:{x:"ox1",y:"oy2"}},{type:"lnTo",pt:{x:"x17",y:"y16"}},{type:"lnTo",pt:{x:"x16",y:"y17"}},{type:"close"},{type:"moveTo",pt:{x:"x19",y:"vc"}},{type:"arcTo",wR:"wR",hR:"hR",stAng:"cd2",swAng:"21600000"},{type:"close"}],extrusionOk:!1,stroke:!0}]},swooshArrow:{avLst:[{n:"adj1",f:"val 25000"},{n:"adj2",f:"val 16667"}],gdLst:[{n:"a1",f:"pin 1 adj1 75000"},{n:"maxAdj2",f:"*/ 70000 w ss"},{n:"a2",f:"pin 0 adj2 maxAdj2"},{n:"ad1",f:"*/ h a1 100000"},{n:"ad2",f:"*/ ss a2 100000"},{n:"xB",f:"+- r 0 ad2"},{n:"yB",f:"+- t ssd8 0"},{n:"alfa",f:"*/ cd4 1 14"},{n:"dx0",f:"tan ssd8 alfa"},{n:"xC",f:"+- xB 0 dx0"},{n:"dx1",f:"tan ad1 alfa"},{n:"yF",f:"+- yB ad1 0"},{n:"xF",f:"+- xB dx1 0"},{n:"xE",f:"+- xF dx0 0"},{n:"yE",f:"+- yF ssd8 0"},{n:"dy2",f:"+- yE 0 t"},{n:"dy22",f:"*/ dy2 1 2"},{n:"dy3",f:"*/ h 1 20"},{n:"yD",f:"+- t dy22 dy3"},{n:"dy4",f:"*/ hd6 1 1"},{n:"yP1",f:"+- hd6 dy4 0"},{n:"xP1",f:"val wd6"},{n:"dy5",f:"*/ hd6 1 2"},{n:"yP2",f:"+- yF dy5 0"},{n:"xP2",f:"val wd4"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"b"}},{type:"quadBezTo",pts:[{x:"xP1",y:"yP1"},{x:"xB",y:"yB"}]},{type:"lnTo",pt:{x:"xC",y:"t"}},{type:"lnTo",pt:{x:"r",y:"yD"}},{type:"lnTo",pt:{x:"xE",y:"yE"}},{type:"lnTo",pt:{x:"xF",y:"yF"}},{type:"quadBezTo",pts:[{x:"xP2",y:"yP2"},{x:"l",y:"b"}]},{type:"close"}],extrusionOk:!1,stroke:!0}]},teardrop:{avLst:[{n:"adj",f:"val 100000"}],gdLst:[{n:"a",f:"pin 0 adj 200000"},{n:"r2",f:"sqrt 2"},{n:"tw",f:"*/ wd2 r2 1"},{n:"th",f:"*/ hd2 r2 1"},{n:"sw",f:"*/ tw a 100000"},{n:"sh",f:"*/ th a 100000"},{n:"dx1",f:"cos sw 2700000"},{n:"dy1",f:"sin sh 2700000"},{n:"x1",f:"+- hc dx1 0"},{n:"y1",f:"+- vc 0 dy1"},{n:"x2",f:"+/ hc x1 2"},{n:"y2",f:"+/ vc y1 2"},{n:"idx",f:"cos wd2 2700000"},{n:"idy",f:"sin hd2 2700000"},{n:"il",f:"+- hc 0 idx"},{n:"ir",f:"+- hc idx 0"},{n:"it",f:"+- vc 0 idy"},{n:"ib",f:"+- vc idy 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"vc"}},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"cd2",swAng:"cd4"},{type:"quadBezTo",pts:[{x:"x2",y:"t"},{x:"x1",y:"y1"}]},{type:"quadBezTo",pts:[{x:"r",y:"y2"},{x:"r",y:"vc"}]},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"0",swAng:"cd4"},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"cd4",swAng:"cd4"},{type:"close"}],extrusionOk:!1,stroke:!0}]},trapezoid:{avLst:[{n:"adj",f:"val 25000"}],gdLst:[{n:"maxAdj",f:"*/ 50000 w ss"},{n:"a",f:"pin 0 adj maxAdj"},{n:"x1",f:"*/ ss a 200000"},{n:"x2",f:"*/ ss a 100000"},{n:"x3",f:"+- r 0 x2"},{n:"x4",f:"+- r 0 x1"},{n:"il",f:"*/ wd3 a maxAdj"},{n:"it",f:"*/ hd3 a maxAdj"},{n:"ir",f:"+- r 0 il"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"b"}},{type:"lnTo",pt:{x:"x2",y:"t"}},{type:"lnTo",pt:{x:"x3",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},triangle:{avLst:[{n:"adj",f:"val 50000"}],gdLst:[{n:"a",f:"pin 0 adj 100000"},{n:"x1",f:"*/ w a 200000"},{n:"x2",f:"*/ w a 100000"},{n:"x3",f:"+- x1 wd2 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"b"}},{type:"lnTo",pt:{x:"x2",y:"t"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},upArrowCallout:{avLst:[{n:"adj1",f:"val 25000"},{n:"adj2",f:"val 25000"},{n:"adj3",f:"val 25000"},{n:"adj4",f:"val 64977"}],gdLst:[{n:"maxAdj2",f:"*/ 50000 w ss"},{n:"a2",f:"pin 0 adj2 maxAdj2"},{n:"maxAdj1",f:"*/ a2 2 1"},{n:"a1",f:"pin 0 adj1 maxAdj1"},{n:"maxAdj3",f:"*/ 100000 h ss"},{n:"a3",f:"pin 0 adj3 maxAdj3"},{n:"q2",f:"*/ a3 ss h"},{n:"maxAdj4",f:"+- 100000 0 q2"},{n:"a4",f:"pin 0 adj4 maxAdj4"},{n:"dx1",f:"*/ ss a2 100000"},{n:"dx2",f:"*/ ss a1 200000"},{n:"x1",f:"+- hc 0 dx1"},{n:"x2",f:"+- hc 0 dx2"},{n:"x3",f:"+- hc dx2 0"},{n:"x4",f:"+- hc dx1 0"},{n:"y1",f:"*/ ss a3 100000"},{n:"dy2",f:"*/ h a4 100000"},{n:"y2",f:"+- b 0 dy2"},{n:"y3",f:"+/ y2 b 2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"y1"}},{type:"lnTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"hc",y:"t"}},{type:"lnTo",pt:{x:"x4",y:"y1"}},{type:"lnTo",pt:{x:"x3",y:"y1"}},{type:"lnTo",pt:{x:"x3",y:"y2"}},{type:"lnTo",pt:{x:"r",y:"y2"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},upDownArrow:{avLst:[{n:"adj1",f:"val 50000"},{n:"adj2",f:"val 50000"}],gdLst:[{n:"maxAdj2",f:"*/ 50000 h ss"},{n:"a1",f:"pin 0 adj1 100000"},{n:"a2",f:"pin 0 adj2 maxAdj2"},{n:"y2",f:"*/ ss a2 100000"},{n:"y3",f:"+- b 0 y2"},{n:"dx1",f:"*/ w a1 200000"},{n:"x1",f:"+- hc 0 dx1"},{n:"x2",f:"+- hc dx1 0"},{n:"dy1",f:"*/ x1 y2 wd2"},{n:"y1",f:"+- y2 0 dy1"},{n:"y4",f:"+- y3 dy1 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"y2"}},{type:"lnTo",pt:{x:"hc",y:"t"}},{type:"lnTo",pt:{x:"r",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"y3"}},{type:"lnTo",pt:{x:"r",y:"y3"}},{type:"lnTo",pt:{x:"hc",y:"b"}},{type:"lnTo",pt:{x:"l",y:"y3"}},{type:"lnTo",pt:{x:"x1",y:"y3"}},{type:"lnTo",pt:{x:"x1",y:"y2"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},upArrow:{avLst:[{n:"adj1",f:"val 50000"},{n:"adj2",f:"val 50000"}],gdLst:[{n:"maxAdj2",f:"*/ 100000 h ss"},{n:"a1",f:"pin 0 adj1 100000"},{n:"a2",f:"pin 0 adj2 maxAdj2"},{n:"dy2",f:"*/ ss a2 100000"},{n:"y2",f:"+- t dy2 0"},{n:"dx1",f:"*/ w a1 200000"},{n:"x1",f:"+- hc 0 dx1"},{n:"x2",f:"+- hc dx1 0"},{n:"dy1",f:"*/ x1 dy2 wd2"},{n:"y1",f:"+- y2  0 dy1"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"y2"}},{type:"lnTo",pt:{x:"hc",y:"t"}},{type:"lnTo",pt:{x:"r",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"b"}},{type:"lnTo",pt:{x:"x1",y:"b"}},{type:"lnTo",pt:{x:"x1",y:"y2"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},upDownArrowCallout:{avLst:[{n:"adj1",f:"val 25000"},{n:"adj2",f:"val 25000"},{n:"adj3",f:"val 25000"},{n:"adj4",f:"val 48123"}],gdLst:[{n:"maxAdj2",f:"*/ 50000 w ss"},{n:"a2",f:"pin 0 adj2 maxAdj2"},{n:"maxAdj1",f:"*/ a2 2 1"},{n:"a1",f:"pin 0 adj1 maxAdj1"},{n:"maxAdj3",f:"*/ 50000 h ss"},{n:"a3",f:"pin 0 adj3 maxAdj3"},{n:"q2",f:"*/ a3 ss hd2"},{n:"maxAdj4",f:"+- 100000 0 q2"},{n:"a4",f:"pin 0 adj4 maxAdj4"},{n:"dx1",f:"*/ ss a2 100000"},{n:"dx2",f:"*/ ss a1 200000"},{n:"x1",f:"+- hc 0 dx1"},{n:"x2",f:"+- hc 0 dx2"},{n:"x3",f:"+- hc dx2 0"},{n:"x4",f:"+- hc dx1 0"},{n:"y1",f:"*/ ss a3 100000"},{n:"y4",f:"+- b 0 y1"},{n:"dy2",f:"*/ h a4 200000"},{n:"y2",f:"+- vc 0 dy2"},{n:"y3",f:"+- vc dy2 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"y2"}},{type:"lnTo",pt:{x:"x2",y:"y1"}},{type:"lnTo",pt:{x:"x1",y:"y1"}},{type:"lnTo",pt:{x:"hc",y:"t"}},{type:"lnTo",pt:{x:"x4",y:"y1"}},{type:"lnTo",pt:{x:"x3",y:"y1"}},{type:"lnTo",pt:{x:"x3",y:"y2"}},{type:"lnTo",pt:{x:"r",y:"y2"}},{type:"lnTo",pt:{x:"r",y:"y3"}},{type:"lnTo",pt:{x:"x3",y:"y3"}},{type:"lnTo",pt:{x:"x3",y:"y4"}},{type:"lnTo",pt:{x:"x4",y:"y4"}},{type:"lnTo",pt:{x:"hc",y:"b"}},{type:"lnTo",pt:{x:"x1",y:"y4"}},{type:"lnTo",pt:{x:"x2",y:"y4"}},{type:"lnTo",pt:{x:"x2",y:"y3"}},{type:"lnTo",pt:{x:"l",y:"y3"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},uturnArrow:{avLst:[{n:"adj1",f:"val 25000"},{n:"adj2",f:"val 25000"},{n:"adj3",f:"val 25000"},{n:"adj4",f:"val 43750"},{n:"adj5",f:"val 75000"}],gdLst:[{n:"a2",f:"pin 0 adj2 25000"},{n:"maxAdj1",f:"*/ a2 2 1"},{n:"a1",f:"pin 0 adj1 maxAdj1"},{n:"q2",f:"*/ a1 ss h"},{n:"q3",f:"+- 100000 0 q2"},{n:"maxAdj3",f:"*/ q3 h ss"},{n:"a3",f:"pin 0 adj3 maxAdj3"},{n:"q1",f:"+- a3 a1 0"},{n:"minAdj5",f:"*/ q1 ss h"},{n:"a5",f:"pin minAdj5 adj5 100000"},{n:"th",f:"*/ ss a1 100000"},{n:"aw2",f:"*/ ss a2 100000"},{n:"th2",f:"*/ th 1 2"},{n:"dh2",f:"+- aw2 0 th2"},{n:"y5",f:"*/ h a5 100000"},{n:"ah",f:"*/ ss a3 100000"},{n:"y4",f:"+- y5 0 ah"},{n:"x9",f:"+- r 0 dh2"},{n:"bw",f:"*/ x9 1 2"},{n:"bs",f:"min bw y4"},{n:"maxAdj4",f:"*/ bs 100000 ss"},{n:"a4",f:"pin 0 adj4 maxAdj4"},{n:"bd",f:"*/ ss a4 100000"},{n:"bd3",f:"+- bd 0 th"},{n:"bd2",f:"max bd3 0"},{n:"x3",f:"+- th bd2 0"},{n:"x8",f:"+- r 0 aw2"},{n:"x6",f:"+- x8 0 aw2"},{n:"x7",f:"+- x6 dh2 0"},{n:"x4",f:"+- x9 0 bd"},{n:"x5",f:"+- x7 0 bd2"},{n:"cx",f:"+/ th x7 2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"b"}},{type:"lnTo",pt:{x:"l",y:"bd"}},{type:"arcTo",wR:"bd",hR:"bd",stAng:"cd2",swAng:"cd4"},{type:"lnTo",pt:{x:"x4",y:"t"}},{type:"arcTo",wR:"bd",hR:"bd",stAng:"3cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"x9",y:"y4"}},{type:"lnTo",pt:{x:"r",y:"y4"}},{type:"lnTo",pt:{x:"x8",y:"y5"}},{type:"lnTo",pt:{x:"x6",y:"y4"}},{type:"lnTo",pt:{x:"x7",y:"y4"}},{type:"lnTo",pt:{x:"x7",y:"x3"}},{type:"arcTo",wR:"bd2",hR:"bd2",stAng:"0",swAng:"-5400000"},{type:"lnTo",pt:{x:"x3",y:"th"}},{type:"arcTo",wR:"bd2",hR:"bd2",stAng:"3cd4",swAng:"-5400000"},{type:"lnTo",pt:{x:"th",y:"b"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},verticalScroll:{avLst:[{n:"adj",f:"val 12500"}],gdLst:[{n:"a",f:"pin 0 adj 25000"},{n:"ch",f:"*/ ss a 100000"},{n:"ch2",f:"*/ ch 1 2"},{n:"ch4",f:"*/ ch 1 4"},{n:"x3",f:"+- ch ch2 0"},{n:"x4",f:"+- ch ch 0"},{n:"x6",f:"+- r 0 ch"},{n:"x7",f:"+- r 0 ch2"},{n:"x5",f:"+- x6 0 ch2"},{n:"y3",f:"+- b 0 ch"},{n:"y4",f:"+- b 0 ch2"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"ch2",y:"b"}},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"cd4",swAng:"-5400000"},{type:"lnTo",pt:{x:"ch2",y:"y4"}},{type:"arcTo",wR:"ch4",hR:"ch4",stAng:"cd4",swAng:"-10800000"},{type:"lnTo",pt:{x:"ch",y:"y3"}},{type:"lnTo",pt:{x:"ch",y:"ch2"}},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"cd2",swAng:"cd4"},{type:"lnTo",pt:{x:"x7",y:"t"}},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"3cd4",swAng:"cd2"},{type:"lnTo",pt:{x:"x6",y:"ch"}},{type:"lnTo",pt:{x:"x6",y:"y4"}},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"0",swAng:"cd4"},{type:"close"},{type:"moveTo",pt:{x:"x4",y:"ch2"}},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"0",swAng:"cd4"},{type:"arcTo",wR:"ch4",hR:"ch4",stAng:"cd4",swAng:"cd2"},{type:"close"}],extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"x4",y:"ch2"}},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"0",swAng:"cd4"},{type:"arcTo",wR:"ch4",hR:"ch4",stAng:"cd4",swAng:"cd2"},{type:"close"},{type:"moveTo",pt:{x:"ch",y:"y4"}},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"0",swAng:"3cd4"},{type:"arcTo",wR:"ch4",hR:"ch4",stAng:"3cd4",swAng:"cd2"},{type:"close"}],fill:"darkenLess",extrusionOk:!1,stroke:!1},{defines:[{type:"moveTo",pt:{x:"ch",y:"y3"}},{type:"lnTo",pt:{x:"ch",y:"ch2"}},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"cd2",swAng:"cd4"},{type:"lnTo",pt:{x:"x7",y:"t"}},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"3cd4",swAng:"cd2"},{type:"lnTo",pt:{x:"x6",y:"ch"}},{type:"lnTo",pt:{x:"x6",y:"y4"}},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"0",swAng:"cd4"},{type:"lnTo",pt:{x:"ch2",y:"b"}},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"cd4",swAng:"cd2"},{type:"close"},{type:"moveTo",pt:{x:"x3",y:"t"}},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"3cd4",swAng:"cd2"},{type:"arcTo",wR:"ch4",hR:"ch4",stAng:"cd4",swAng:"cd2"},{type:"lnTo",pt:{x:"x4",y:"ch2"}},{type:"moveTo",pt:{x:"x6",y:"ch"}},{type:"lnTo",pt:{x:"x3",y:"ch"}},{type:"moveTo",pt:{x:"ch2",y:"y3"}},{type:"arcTo",wR:"ch4",hR:"ch4",stAng:"3cd4",swAng:"cd2"},{type:"lnTo",pt:{x:"ch",y:"y4"}},{type:"moveTo",pt:{x:"ch2",y:"b"}},{type:"arcTo",wR:"ch2",hR:"ch2",stAng:"cd4",swAng:"-5400000"},{type:"lnTo",pt:{x:"ch",y:"y3"}}],fill:"none",extrusionOk:!1,stroke:!0}]},wave:{avLst:[{n:"adj1",f:"val 12500"},{n:"adj2",f:"val 0"}],gdLst:[{n:"a1",f:"pin 0 adj1 20000"},{n:"a2",f:"pin -10000 adj2 10000"},{n:"y1",f:"*/ h a1 100000"},{n:"dy2",f:"*/ y1 10 3"},{n:"y2",f:"+- y1 0 dy2"},{n:"y3",f:"+- y1 dy2 0"},{n:"y4",f:"+- b 0 y1"},{n:"y5",f:"+- y4 0 dy2"},{n:"y6",f:"+- y4 dy2 0"},{n:"dx1",f:"*/ w a2 100000"},{n:"of2",f:"*/ w a2 50000"},{n:"x1",f:"abs dx1"},{n:"dx2",f:"?: of2 0 of2"},{n:"x2",f:"+- l 0 dx2"},{n:"dx5",f:"?: of2 of2 0"},{n:"x5",f:"+- r 0 dx5"},{n:"dx3",f:"+/ dx2 x5 3"},{n:"x3",f:"+- x2 dx3 0"},{n:"x4",f:"+/ x3 x5 2"},{n:"x6",f:"+- l dx5 0"},{n:"x10",f:"+- r dx2 0"},{n:"x7",f:"+- x6 dx3 0"},{n:"x8",f:"+/ x7 x10 2"},{n:"x9",f:"+- r 0 x1"},{n:"xAdj",f:"+- hc dx1 0"},{n:"xAdj2",f:"+- hc 0 dx1"},{n:"il",f:"max x2 x6"},{n:"ir",f:"min x5 x10"},{n:"it",f:"*/ h a1 50000"},{n:"ib",f:"+- b 0 it"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"x2",y:"y1"}},{type:"cubicBezTo",pts:[{x:"x3",y:"y2"},{x:"x4",y:"y3"},{x:"x5",y:"y1"}]},{type:"lnTo",pt:{x:"x10",y:"y4"}},{type:"cubicBezTo",pts:[{x:"x8",y:"y6"},{x:"x7",y:"y5"},{x:"x6",y:"y4"}]},{type:"close"}],extrusionOk:!1,stroke:!0}]},wedgeEllipseCallout:{avLst:[{n:"adj1",f:"val -20833"},{n:"adj2",f:"val 62500"}],gdLst:[{n:"dxPos",f:"*/ w adj1 100000"},{n:"dyPos",f:"*/ h adj2 100000"},{n:"xPos",f:"+- hc dxPos 0"},{n:"yPos",f:"+- vc dyPos 0"},{n:"sdx",f:"*/ dxPos h 1"},{n:"sdy",f:"*/ dyPos w 1"},{n:"pang",f:"at2 sdx sdy"},{n:"stAng",f:"+- pang 660000 0"},{n:"enAng",f:"+- pang 0 660000"},{n:"dx1",f:"cos wd2 stAng"},{n:"dy1",f:"sin hd2 stAng"},{n:"x1",f:"+- hc dx1 0"},{n:"y1",f:"+- vc dy1 0"},{n:"dx2",f:"cos wd2 enAng"},{n:"dy2",f:"sin hd2 enAng"},{n:"x2",f:"+- hc dx2 0"},{n:"y2",f:"+- vc dy2 0"},{n:"stAng1",f:"at2 dx1 dy1"},{n:"enAng1",f:"at2 dx2 dy2"},{n:"swAng1",f:"+- enAng1 0 stAng1"},{n:"swAng2",f:"+- swAng1 21600000 0"},{n:"swAng",f:"?: swAng1 swAng1 swAng2"},{n:"idx",f:"cos wd2 2700000"},{n:"idy",f:"sin hd2 2700000"},{n:"il",f:"+- hc 0 idx"},{n:"ir",f:"+- hc idx 0"},{n:"it",f:"+- vc 0 idy"},{n:"ib",f:"+- vc idy 0"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"xPos",y:"yPos"}},{type:"lnTo",pt:{x:"x1",y:"y1"}},{type:"arcTo",wR:"wd2",hR:"hd2",stAng:"stAng1",swAng:"swAng"},{type:"close"}],extrusionOk:!1,stroke:!0}]},wedgeRectCallout:{avLst:[{n:"adj1",f:"val -20833"},{n:"adj2",f:"val 62500"}],gdLst:[{n:"dxPos",f:"*/ w adj1 100000"},{n:"dyPos",f:"*/ h adj2 100000"},{n:"xPos",f:"+- hc dxPos 0"},{n:"yPos",f:"+- vc dyPos 0"},{n:"dx",f:"+- xPos 0 hc"},{n:"dy",f:"+- yPos 0 vc"},{n:"dq",f:"*/ dxPos h w"},{n:"ady",f:"abs dyPos"},{n:"adq",f:"abs dq"},{n:"dz",f:"+- ady 0 adq"},{n:"xg1",f:"?: dxPos 7 2"},{n:"xg2",f:"?: dxPos 10 5"},{n:"x1",f:"*/ w xg1 12"},{n:"x2",f:"*/ w xg2 12"},{n:"yg1",f:"?: dyPos 7 2"},{n:"yg2",f:"?: dyPos 10 5"},{n:"y1",f:"*/ h yg1 12"},{n:"y2",f:"*/ h yg2 12"},{n:"t1",f:"?: dxPos l xPos"},{n:"xl",f:"?: dz l t1"},{n:"t2",f:"?: dyPos x1 xPos"},{n:"xt",f:"?: dz t2 x1"},{n:"t3",f:"?: dxPos xPos r"},{n:"xr",f:"?: dz r t3"},{n:"t4",f:"?: dyPos xPos x1"},{n:"xb",f:"?: dz t4 x1"},{n:"t5",f:"?: dxPos y1 yPos"},{n:"yl",f:"?: dz y1 t5"},{n:"t6",f:"?: dyPos t yPos"},{n:"yt",f:"?: dz t6 t"},{n:"t7",f:"?: dxPos yPos y1"},{n:"yr",f:"?: dz y1 t7"},{n:"t8",f:"?: dyPos yPos b"},{n:"yb",f:"?: dz t8 b"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"t"}},{type:"lnTo",pt:{x:"x1",y:"t"}},{type:"lnTo",pt:{x:"xt",y:"yt"}},{type:"lnTo",pt:{x:"x2",y:"t"}},{type:"lnTo",pt:{x:"r",y:"t"}},{type:"lnTo",pt:{x:"r",y:"y1"}},{type:"lnTo",pt:{x:"xr",y:"yr"}},{type:"lnTo",pt:{x:"r",y:"y2"}},{type:"lnTo",pt:{x:"r",y:"b"}},{type:"lnTo",pt:{x:"x2",y:"b"}},{type:"lnTo",pt:{x:"xb",y:"yb"}},{type:"lnTo",pt:{x:"x1",y:"b"}},{type:"lnTo",pt:{x:"l",y:"b"}},{type:"lnTo",pt:{x:"l",y:"y2"}},{type:"lnTo",pt:{x:"xl",y:"yl"}},{type:"lnTo",pt:{x:"l",y:"y1"}},{type:"close"}],extrusionOk:!1,stroke:!0}]},wedgeRoundRectCallout:{avLst:[{n:"adj1",f:"val -20833"},{n:"adj2",f:"val 62500"},{n:"adj3",f:"val 16667"}],gdLst:[{n:"dxPos",f:"*/ w adj1 100000"},{n:"dyPos",f:"*/ h adj2 100000"},{n:"xPos",f:"+- hc dxPos 0"},{n:"yPos",f:"+- vc dyPos 0"},{n:"dq",f:"*/ dxPos h w"},{n:"ady",f:"abs dyPos"},{n:"adq",f:"abs dq"},{n:"dz",f:"+- ady 0 adq"},{n:"xg1",f:"?: dxPos 7 2"},{n:"xg2",f:"?: dxPos 10 5"},{n:"x1",f:"*/ w xg1 12"},{n:"x2",f:"*/ w xg2 12"},{n:"yg1",f:"?: dyPos 7 2"},{n:"yg2",f:"?: dyPos 10 5"},{n:"y1",f:"*/ h yg1 12"},{n:"y2",f:"*/ h yg2 12"},{n:"t1",f:"?: dxPos l xPos"},{n:"xl",f:"?: dz l t1"},{n:"t2",f:"?: dyPos x1 xPos"},{n:"xt",f:"?: dz t2 x1"},{n:"t3",f:"?: dxPos xPos r"},{n:"xr",f:"?: dz r t3"},{n:"t4",f:"?: dyPos xPos x1"},{n:"xb",f:"?: dz t4 x1"},{n:"t5",f:"?: dxPos y1 yPos"},{n:"yl",f:"?: dz y1 t5"},{n:"t6",f:"?: dyPos t yPos"},{n:"yt",f:"?: dz t6 t"},{n:"t7",f:"?: dxPos yPos y1"},{n:"yr",f:"?: dz y1 t7"},{n:"t8",f:"?: dyPos yPos b"},{n:"yb",f:"?: dz t8 b"},{n:"u1",f:"*/ ss adj3 100000"},{n:"u2",f:"+- r 0 u1"},{n:"v2",f:"+- b 0 u1"},{n:"il",f:"*/ u1 29289 100000"},{n:"ir",f:"+- r 0 il"},{n:"ib",f:"+- b 0 il"}],pathLst:[{defines:[{type:"moveTo",pt:{x:"l",y:"u1"}},{type:"arcTo",wR:"u1",hR:"u1",stAng:"cd2",swAng:"cd4"},{type:"lnTo",pt:{x:"x1",y:"t"}},{type:"lnTo",pt:{x:"xt",y:"yt"}},{type:"lnTo",pt:{x:"x2",y:"t"}},{type:"lnTo",pt:{x:"u2",y:"t"}},{type:"arcTo",wR:"u1",hR:"u1",stAng:"3cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"r",y:"y1"}},{type:"lnTo",pt:{x:"xr",y:"yr"}},{type:"lnTo",pt:{x:"r",y:"y2"}},{type:"lnTo",pt:{x:"r",y:"v2"}},{type:"arcTo",wR:"u1",hR:"u1",stAng:"0",swAng:"cd4"},{type:"lnTo",pt:{x:"x2",y:"b"}},{type:"lnTo",pt:{x:"xb",y:"yb"}},{type:"lnTo",pt:{x:"x1",y:"b"}},{type:"lnTo",pt:{x:"u1",y:"b"}},{type:"arcTo",wR:"u1",hR:"u1",stAng:"cd4",swAng:"cd4"},{type:"lnTo",pt:{x:"l",y:"y2"}},{type:"lnTo",pt:{x:"xl",y:"yl"}},{type:"lnTo",pt:{x:"l",y:"y1"}},{type:"close"}],extrusionOk:!1,stroke:!0}]}},Cn=1/6e4/180*Math.PI,jn={"*/":function(t,n,e){return t*n/e},"+-":function(t,n,e){return t+n-e},"+/":function(t,n,e){return(t+n)/e},"?:":function(t,n,e){return t>0?n:e},abs:function(t){return Math.abs(t)},at2:function(t,n){return 180*Math.atan2(n,t)*6e4/Math.PI},cat2:function(t,n,e){return t*Math.cos(Math.atan2(e,n))},cos:function(t,n){return t*Math.cos(n*Cn)},max:function(t,n){return Math.max(t,n)},min:function(t,n){return Math.min(t,n)},mod:function(t,n,e){return Math.sqrt(Math.pow(t,2)+Math.pow(n,2)+Math.pow(e,2))},pin:function(t,n,e){return n<t?t:n>e?e:n},sat2:function(t,n,e){return t*Math.sin(Math.atan2(e,n))},sin:function(t,n){return t*Math.sin(n*Cn)},sqrt:function(t){return Math.sqrt(t)},tan:function(t,n){return t*Math.tan(n*Cn)},val:function(t){var n=parseInt(t,10);return isNaN(n),n}};function Bn(t,n,e){var s=n.split(/[ ]+/);s.length<=1&&console.warn("fmla format error",n);var a=s[0],r=s.slice(1).map((function(t){if(t in e)return e[t];var s=parseInt(t,10);return isNaN(s)?(console.warn("fmla arg error",t,n),0):s}));if(a in jn){var l=jn[a].apply(null,r);if(isNaN(l))return console.warn("fmla eval error",n,t),0;e[t]=l}return 0}var On=function(t){return Math.PI*(t/6e4/180)};function Dn(t,n,e,s,a,r){var l=On(e),o=On(s),x=On(e+s);(function(t,n){if(t===n)return!0;var e=Math.abs(t-n);return e<Number.EPSILON||e<=Number.EPSILON*Math.min(Math.abs(t),Math.abs(n))})(s,216e5)&&(x-=1e-4);var y=function(t,n,e,s,a,r,l){var o=e,x=s,y=[r,l],p=[[Math.cos(0),-Math.sin(0)],[Math.sin(0),Math.cos(0)]],f=Fn(p,[t*Math.cos(o),n*Math.sin(o)]),c=[y[0]-f[0],y[1]-f[1]],i=Fn(p,[t*Math.cos(x),n*Math.sin(x)]),d=[c[0]+i[0],c[1]+i[1]];return{x:d[0],y:d[1]}}(t,n,l,x,0,a,r),p=Math.abs(o)>Math.PI?1:0,f=s>0?1:0;return{path:"A ".concat(t," ").concat(n," 0 ").concat(p," ").concat(f," ").concat(y.x,",").concat(y.y),end:y}}function Fn(t,n){return[t[0][0]*n[0]+t[0][1]*n[1],t[1][0]*n[0]+t[1][1]*n[1]]}function Sn(t,n,e){var s=0;if(t in n)s=n[t];else if(s=parseInt(t,10),isNaN(s))return console.warn("var not found",t),0;return e?s*e:s}function qn(t,n,e){var a,r,l=t.defines,o=[],x=t.w,y=t.h,p=1,f=1;x&&(p=n.w/x),y&&(f=n.h/y);try{for(var c=(0,s.Ju)(l),i=c.next();!i.done;i=c.next()){var d=i.value;switch(d.type){case"moveTo":var h=Sn((w=d.pt).x,n,p),m=Sn(w.y,n,f);o.push("M ".concat(h," ").concat(m)),e.push({x:h,y:m});break;case"lnTo":var w;h=Sn((w=d.pt).x,n,p),m=Sn(w.y,n,f),o.push("L ".concat(h," ").concat(m)),e.push({x:h,y:m});break;case"arcTo":var g=d,u=Sn(g.wR,n,p),T=Sn(g.hR,n,f),v=Sn(g.stAng,n),A=Sn(g.swAng,n),b={x:0,y:0};e.length>0&&(b=e[e.length-1]);var k=Dn(u,T,v,A,b.x,b.y);o.push(k.path),e.push({x:k.end.x,y:k.end.y});break;case"quadBezTo":var R=d;if(R.pts.length>=2){var L=R.pts[0],C=R.pts[1],j=Sn(L.x,n,p),B=Sn(L.y,n,f),O=Sn(C.x,n,p),D=Sn(C.y,n,f);if(o.push("Q ".concat(j,",").concat(B," ").concat(O,",").concat(D)),R.pts.length>2){var F=Sn((E=R.pts[2]).x,n,p),S=Sn(E.y,n,f);o.push("T ".concat(F,",").concat(S)),e.push({x:F,y:S})}else e.push({x:O,y:D})}else console.warn("quadBezTo pts length must large than 2",d);break;case"cubicBezTo":var q=d;if(3===q.pts.length){L=q.pts[0],C=q.pts[1];var E=q.pts[2];j=Sn(L.x,n,p),B=Sn(L.y,n,f),O=Sn(C.x,n,p),D=Sn(C.y,n,f),F=Sn(E.x,n,p),S=Sn(E.y,n,f),o.push("C ".concat(j,",").concat(B," ").concat(O,",").concat(D," ").concat(F,",").concat(S)),e.push({x:F,y:S})}else console.warn("cubicBezTo pts length must be 3",d);break;case"close":o.push("Z")}}}catch(t){a={error:t}}finally{try{i&&!i.done&&(r=c.return)&&r.call(c)}finally{if(a)throw a.error}}return o.join(" ")}function En(t,n,e,a,r,l){var o,x,y,p,f,c,i,d,h=cn("svg");h.style.display="block",h.setAttribute("style","display: block; overflow: visible; position: absolute; z-index: -1"),h.setAttribute("width",a.toString()+"px"),h.setAttribute("height",r.toString()+"px");var m,w,g,u,T,v,A,b,k=(m=a,w=r,u=(g=Math.min(m,w))/6,T=g/6,v=g/8,A=g/32,b=g/16,{t:0,"3cd4":162e5,"3cd8":81e5,"5cd8":135e5,"7cd8":189e5,b:w,cd2:108e5,cd4:54e5,cd8:27e5,h:w,hd2:w/2,hd3:w/3,hd4:w/4,hd6:w/6,hd8:w/8,l:0,ls:Math.max(m,w),r:m,ss:g,ssd2:u,ssd6:T,ssd8:v,ssd16:b,ssd32:A,hc:m/2,vc:w/2,w:m,wd2:m/2,wd3:m/3,wd4:m/4,wd6:m/6,wd8:m/8,wd10:m/10,wd16:m/16,wd32:m/32});try{for(var R=(0,s.Ju)(t.avLst||[]),L=R.next();!L.done;L=R.next())Bn((D=L.value).n,D.f,k)}catch(t){o={error:t}}finally{try{L&&!L.done&&(x=R.return)&&x.call(R)}finally{if(o)throw o.error}}try{for(var C=(0,s.Ju)(n),j=C.next();!j.done;j=C.next())Bn((D=j.value).n,D.f,k)}catch(t){y={error:t}}finally{try{j&&!j.done&&(p=C.return)&&p.call(C)}finally{if(y)throw y.error}}try{for(var B=(0,s.Ju)(t.gdLst||[]),O=B.next();!O.done;O=B.next()){var D;Bn((D=O.value).n,D.f,k)}}catch(t){f={error:t}}finally{try{O&&!O.done&&(c=B.return)&&c.call(B)}finally{if(f)throw f.error}}var F=e.outline,S=[];try{for(var q=(0,s.Ju)(t.pathLst||[]),E=q.next();!E.done;E=q.next()){var P=E.value,$=cn("path"),M=qn(P,k,S);$.setAttribute("d",M),e.fillColor?$.setAttribute("fill",e.fillColor):l&&l.fillColor?$.setAttribute("fill",l.fillColor):$.setAttribute("fill","none"),F?(F.color&&$.setAttribute("stroke",F.color),F.width&&$.setAttribute("stroke-width",F.width),"none"===F.style&&$.setAttribute("stroke","none")):l&&l.lineColor?$.setAttribute("stroke",l.lineColor):$.setAttribute("stroke","none");var N=$.getAttribute("fill");if(N&&"none"!==N){var I=new G(N),H=P.fill,z="";switch(H){case"darken":z=I.lumOff(-.5).toHex();break;case"darkenLess":z=I.lumOff(-.2).toHex();break;case"lighten":z=I.lumOff(.5).toHex();break;case"lightenLess":z=I.lumOff(.2).toHex()}z&&$.setAttribute("fill",z)}"none"===P.fill&&$.setAttribute("fill","none"),!1===P.stroke&&($.setAttribute("stroke","none"),P.fill||$.setAttribute("fill","none")),e.noFill&&$.setAttribute("fill","none"),h.appendChild($)}}catch(t){i={error:t}}finally{try{E&&!E.done&&(d=q.return)&&d.call(q)}finally{if(i)throw i.error}}return h}function Pn(t,n,e){var s,a,r;void 0===e&&(e=null);var l=null===(s=t.blipFill)||void 0===s?void 0:s.blip;if(l&&l.src){var o=document.createElement("img");if(o.style.position="relative",o.alt=t.alt||"",o.src=l.src,t.alt&&n.renderOptions.enableVar)if(t.altVar)o.src=t.altVar;else if(t.alt.startsWith("{{")){var x=n.replaceText(t.alt);x&&(o.src=x)}var y=null===(a=t.spPr)||void 0===a?void 0:a.xfrm;if(y){if(e){var p=$n(y,null===(r=e.spPr)||void 0===r?void 0:r.xfrm);p&&(o.style.position="absolute",o.style.left=p.left+"px",o.style.top=p.top+"px",o.style.width=p.width+"px",o.style.height=p.height+"px")}else{var f=y.off;f&&(o.style.left=f.x,o.style.top=f.y);var c=y.ext;c&&(o.style.width=c.cx,o.style.height=c.cy)}y.rot&&(o.style.transform="rotate(".concat(y.rot,"deg)"))}return o}return null}function $n(t,n){var e=t.off,s=parseFloat(t.ext.cx.replace("px","")),a=parseFloat(t.ext.cy.replace("px",""));if(e&&n.chOff&&n.ext&&n.chExt){var r=parseFloat(n.ext.cx.replace("px",""))/parseFloat(n.chExt.cx.replace("px","")),l=parseFloat(n.ext.cy.replace("px",""))/parseFloat(n.chExt.cy.replace("px","")),o=parseFloat(n.chOff.x.replace("px","")),x=parseFloat(n.chOff.y.replace("px",""));return{left:r*(parseFloat(e.x.replace("px",""))-o),top:l*(parseFloat(e.y.replace("px",""))-x),width:r*s,height:l*a}}return null}function Mn(t,n,e,a){var r,l,o;void 0===a&&(a=null);var x=e.wpsStyle,y=e.spPr;if(pn(n,e.style),(null==x?void 0:x.fontColor)&&(n.style.color=x.fontColor),null==y?void 0:y.xfrm){var p=y.xfrm.ext;if(p){var f=parseFloat(p.cx.replace("px","")),c=parseFloat(p.cy.replace("px",""));if(a){n.style.position="absolute";var i=$n(y.xfrm,null===(o=a.spPr)||void 0===o?void 0:o.xfrm);i&&(n.style.left=i.left+"px",n.style.top=i.top+"px",f=i.width,c=i.height)}n.style.width=f+"px",n.style.height=c+"px",y.geom&&dn(n,function(t,n,e,s,a){if(t.prst){var r=Ln[t.prst];if(r)return En(r,t.avLst||[],n,e,s,a)}return null}(y.geom,y,f,c,e.wpsStyle)),y.custGeom&&dn(n,function(t,n,e,s,a){return t.shape?En(t.shape,[],n,e,s,a):null}(y.custGeom,y,f,c,e.wpsStyle))}y.xfrm.rot&&(n.style.transform="rotate(".concat(y.xfrm.rot,"deg)"))}var d=e.txbxContent;if(d.length){var h=document.createElement("div");h.dataset.name="textContainer",n.style.display="table",h.style.display="table-cell",h.style.verticalAlign="middle",e.style&&e.style["vertical-align"]&&(h.style.verticalAlign=e.style["vertical-align"],n.style.verticalAlign=""),dn(n,h);try{for(var m=(0,s.Ju)(d),w=m.next();!w.done;w=m.next()){var g=w.value;g instanceof sn?dn(h,xe(t,g)):g instanceof mt&&dn(h,Rn(t,g))}}catch(t){r={error:t}}finally{try{w&&!w.done&&(l=m.return)&&l.call(m)}finally{if(r)throw r.error}}}}function Nn(t,n){var e,a,r,l,o,x,y=document.createElement("div"),p=document.createElement("div"),f=n.spPr;if(null==f?void 0:f.xfrm){var c=null===(o=null==f?void 0:f.xfrm)||void 0===o?void 0:o.ext;c&&(p.style.width=c.cx,p.style.height=c.cy);var i=null===(x=null==f?void 0:f.xfrm)||void 0===x?void 0:x.rot;i&&(p.style.transform="rotate(".concat(i,"deg)"))}try{for(var d=(0,s.Ju)(n.wps),h=d.next();!h.done;h=d.next()){var m=h.value,w=document.createElement("div");Mn(t,w,m,n),dn(p,w)}}catch(t){e={error:t}}finally{try{h&&!h.done&&(a=d.return)&&a.call(d)}finally{if(e)throw e.error}}try{for(var g=(0,s.Ju)(n.wpg),u=g.next();!u.done;u=g.next())dn(y,Nn(t,u.value))}catch(t){r={error:t}}finally{try{u&&!u.done&&(l=g.return)&&l.call(g)}finally{if(r)throw r.error}}return n.pic&&dn(p,Pn(n.pic,t,n)),dn(y,p),y}function In(t,n,e){var s=document.createElement("div");return"inline"===n.position?s.style.display="inline-block":n.position,n.pic&&dn(s,Pn(n.pic,t)),"page"===n.relativeFromV&&console.warn('暂不支持 drawing.relativeFromV === "page"'),pn(s,n.containerStyle),s.dataset.id=n.id||"",s.dataset.name=n.name||"",n.wps&&Mn(t,s,n.wps),n.wpg&&dn(s,Nn(t,n.wpg)),0===s.children.length?null:s}function Hn(t,n,e){void 0===e&&(e=!1);var s=fn("span");return s.style.display="inline-block",s.style.width="2em",s.innerHTML="&emsp;","dot"===n.leader&&(s.style.borderBottom="1pt dotted"),e&&n.pos&&("start"===n.type||"left"==n.type)&&(s.style.width=n.pos),s}function zn(t,n){if(n.src){var e=document.createElement("img");return e.style.position="relative",e.src=n.src,e}return null}function Gn(t,n){var e,a,r,l,o=fn("ruby");if(n.rubyBase){try{for(var x=(0,s.Ju)(n.rubyBase.children),y=x.next();!y.done;y=x.next()){var p=y.value;o.appendChild(ee(t,p))}}catch(t){e={error:t}}finally{try{y&&!y.done&&(a=x.return)&&a.call(x)}finally{if(e)throw e.error}}if(n.rt){var f=fn("rp");f.innerText="(",o.appendChild(f);var c=fn("rt");try{for(var i=(0,s.Ju)(n.rt.children),d=i.next();!d.done;d=i.next())p=d.value,c.appendChild(ee(t,p))}catch(t){r={error:t}}finally{try{d&&!d.done&&(l=i.return)&&l.call(i)}finally{if(r)throw r.error}}o.appendChild(c);var h=fn("rp");h.innerText=")",o.appendChild(h)}}return o}function Vn(t,n,e){var a,r,l=fn("a");if(n.relation){var o=n.relation;o&&"External"===o.targetMode&&(l.href=o.target,l.target="_blank")}n.anchor&&(l.href="#"+n.anchor),n.tooltip&&(l.title=n.tooltip);try{for(var x=(0,s.Ju)(n.children),y=x.next();!y.done;y=x.next()){var p=y.value;p instanceof Kt&&dn(l,ee(t,p,e))}}catch(t){a={error:t}}finally{try{y&&!y.done&&(r=x.return)&&r.call(x)}finally{if(a)throw a.error}}return l}function Xn(t,n){var e=n.name;if(e){var s=fn("a");return s.name=e,s.id=e,s}return null}function Jn(t,n,e){var a,r;try{for(var l=(0,s.Ju)(n.children),o=l.next();!o.done;o=l.next()){var x=o.value;x instanceof Kt?dn(e,ee(t,x)):x instanceof nt?dn(e,Xn(0,x)):x instanceof _t&&dn(e,Vn(t,x))}}catch(t){a={error:t}}finally{try{o&&!o.done&&(r=l.return)&&r.call(l)}finally{if(a)throw a.error}}}function Un(t,n){var e,a,r,l=n.text,o=fn("span"),x=null===(r=t.currentParagraph)||void 0===r?void 0:r.fldSimples;if(x)try{for(var y=(0,s.Ju)(x),p=y.next();!p.done;p=y.next()){var f=p.value;if(f.instr===l.trim()||l.startsWith(f.instr+" ")){Jn(t,f.inlineText,o);break}}}catch(t){e={error:t}}finally{try{p&&!p.done&&(a=y.return)&&a.call(y)}finally{if(e)throw e.error}}return o}function Wn(t,n){var e=fn("span");return e.style.fontFamily=n.font,e.innerHTML="&#x".concat(n.char,";"),e}var Zn=/\p{Punctuation}/u,Yn=/\p{Separator}/u,Kn=/\p{Script=Han}|\p{Script=Katakana}|\p{Script=Hiragana}|\p{Script=Hangul}/u,_n=function(t){var n,e,s=t.filter((function(t){return void 0!==t&&""!==t}));return e=function(t,n){return function(t,n){return Kn.test(t)?!(Zn.test(n)||Yn.test(n)||Kn.test(n)):Kn.test(n)&&!Zn.test(t)&&!Yn.test(t)}(t,n)?" ":""},(n=s).reduce((function(t,s,a){return t+(0!==a?e(s,n[a-1]):"")+s}),"")};function Qn(){var t=fn("span");return t.innerHTML="&ndash;",t}var te="variable";function ne(t,n,e,s){var a;if(-1===e.indexOf("{{")){var r;r=(null===(a=null==s?void 0:s.properties)||void 0===a?void 0:a.autoSpace)?_n(e.split("")):e,t.textContent=r}else t.dataset.originText=e,t.classList.add(te),t.textContent=n.replaceText(e);var l=t.innerHTML.split("  ").join("&nbsp;&nbsp;");t.innerHTML=l}function ee(t,n,e,a,r){var l,o,x,y,p,f,c=fn("span");if(t.addClass(c,"r"),An(t,c,n.properties),null===(x=n.properties)||void 0===x?void 0:x.rStyle){var i=t.getStyle(n.properties.rStyle);(null===(y=null==i?void 0:i.rPr)||void 0===y?void 0:y.cssStyle)&&pn(c,i.rPr.cssStyle)}if(1===n.children.length&&n.children[0]instanceof Yt)ne(c,t,n.children[0].text,e);else try{for(var d=(0,s.Ju)(n.children),h=d.next();!h.done;h=d.next()){var m=h.value;if(m instanceof Yt){var w=fn("span");ne(w,t,m.text,e),dn(c,w)}else m instanceof et?dn(c,wn(t,m)):m instanceof It?dn(c,In(t,m)):m instanceof Zt?dn(c,Hn(0,m)):m instanceof Gt?dn(c,zn(0,m)):m instanceof Xt?dn(c,Gn(t,m)):m instanceof Ht?dn(c,Un(t,m)):m instanceof Wt?dn(c,Wn(0,m)):m instanceof Ut?dn(c,(f=void 0,(f=fn("span")).innerHTML="&shy;",f)):m instanceof zt?dn(c,Qn()):m instanceof Jt?dn(c,(p=void 0,(p=fn("hr")).style.borderTop="1pt solid #bbb",p)):console.warn("unknown child",m)}}catch(t){l={error:t}}finally{try{h&&!h.done&&(o=d.return)&&o.call(d)}finally{if(l)throw l.error}}return c}function se(t){var n={M:1e3,CM:900,D:500,CD:400,C:100,XC:90,L:50,XL:40,X:10,IX:9,V:5,IV:4,I:1},e="";for(var s in n)for(;t>=n[s];)e+=s,t-=n[s];return e}function ae(t,n){switch(t){case"decimal":default:return n.toString();case"lowerLetter":return String.fromCharCode(96+n);case"upperLetter":return String.fromCharCode(64+n);case"lowerRoman":return se(n).toLowerCase();case"upperRoman":return se(n).toUpperCase();case"bullet":return"&bull;"}}function re(t){return(new XMLSerializer).serializeToString(t)}var le,oe=(le='\n<?xml version="1.0" encoding="UTF-8" ?>\n<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:mml="http://www.w3.org/1998/Math/MathML"\n\txmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math">\n  <xsl:output method="xml" encoding="UTF-16" />\n\n  \x3c!-- %% Global Definitions --\x3e\n\n  \x3c!-- Every single unicode character that is recognized by OMML as an operator --\x3e\n  <xsl:variable name="sOperators"\n\t\tselect="concat(\n          \'&#x00A8;&#x0021;&#x0022;&#x0023;&#x0026;&#x0028;&#x0029;&#x002B;&#x002C;&#x002D;&#x002E;&#x002F;&#x003A;\',\n          \'&#x003B;&#x003C;&#x003D;&#x003E;&#x003F;&#x0040;&#x005B;&#x005C;&#x005D;&#x005E;&#x005F;&#x0060;&#x007B;\',\n          \'&#x007C;&#x007D;&#x007E;&#x00A1;&#x00A6;&#x00AC;&#x00AF;&#x00B0;&#x00B1;&#x00B2;&#x00B3;&#x00B4;&#x00B7;&#x00B9;&#x00BF;\',\n          \'&#x00D7;&#x007E;&#x00F7;&#x02C7;&#x02D8;&#x02D9;&#x02DC;&#x02DD;&#x0300;&#x0301;&#x0302;&#x0303;&#x0304;&#x0305;&#x0306;&#x0307;&#x0308;&#x0309;\',\n          \'&#x030A;&#x030B;&#x030C;&#x030D;&#x030E;&#x030F;&#x0310;&#x0311;&#x0312;&#x0313;&#x0314;&#x0315;\',\n          \'&#x0316;&#x0317;&#x0318;&#x0319;&#x031A;&#x031B;&#x031C;&#x031D;&#x031E;&#x031F;&#x0320;&#x0321;\',\n          \'&#x0322;&#x0323;&#x0324;&#x0325;&#x0326;&#x0327;&#x0328;&#x0329;&#x032A;&#x032B;&#x032C;&#x032D;\',\n          \'&#x032E;&#x032F;&#x0330;&#x0331;&#x0332;&#x0333;&#x0334;&#x0335;&#x0336;&#x0337;&#x0338;&#x033F;\',\n          \'&#x2000;&#x2001;&#x2002;&#x2003;&#x2004;&#x2005;&#x2006;&#x2009;&#x200A;&#x2010;&#x2012;&#x2013;\',\n          \'&#x2014;&#x2016;&#x2020;&#x2021;&#x2022;&#x2024;&#x2025;&#x2026;&#x2032;&#x2033;&#x2034;&#x203C;\',\n          \'&#x2040;&#x2044;&#x204E;&#x204F;&#x2050;&#x2057;&#x2061;&#x2062;&#x2063;&#x2070;&#x2074;&#x2075;\',\n          \'&#x2076;&#x2077;&#x2078;&#x2079;&#x207A;&#x207B;&#x207C;&#x207D;&#x207E;&#x2080;&#x2081;&#x2082;\',\n          \'&#x2083;&#x2084;&#x2085;&#x2086;&#x2087;&#x2088;&#x2089;&#x208A;&#x208B;&#x208C;&#x208D;&#x208E;\',\n          \'&#x20D0;&#x20D1;&#x20D2;&#x20D3;&#x20D4;&#x20D5;&#x20D6;&#x20D7;&#x20D8;&#x20D9;&#x20DA;&#x20DB;\',\n          \'&#x20DC;&#x20DD;&#x20DE;&#x20DF;&#x20E0;&#x20E1;&#x20E4;&#x20E5;&#x20E6;&#x20E7;&#x20E8;&#x20E9;\',\n          \'&#x20EA;&#x2140;&#x2146;&#x2190;&#x2191;&#x2192;&#x2193;&#x2194;&#x2195;&#x2196;&#x2197;&#x2198;&#x2199;\',\n          \'&#x219A;&#x219B;&#x219C;&#x219D;&#x219E;&#x219F;&#x21A0;&#x21A1;&#x21A2;&#x21A3;&#x21A4;&#x21A5;\',\n          \'&#x21A6;&#x21A7;&#x21A8;&#x21A9;&#x21AA;&#x21AB;&#x21AC;&#x21AD;&#x21AE;&#x21AF;&#x21B0;&#x21B1;\',\n          \'&#x21B2;&#x21B3;&#x21B6;&#x21B7;&#x21BA;&#x21BB;&#x21BC;&#x21BD;&#x21BE;&#x21BF;&#x21C0;&#x21C1;\',\n          \'&#x21C2;&#x21C3;&#x21C4;&#x21C5;&#x21C6;&#x21C7;&#x21C8;&#x21C9;&#x21CA;&#x21CB;&#x21CC;&#x21CD;\',\n          \'&#x21CE;&#x21CF;&#x21D0;&#x21D1;&#x21D2;&#x21D3;&#x21D4;&#x21D5;&#x21D6;&#x21D7;&#x21D8;&#x21D9;\',\n          \'&#x21DA;&#x21DB;&#x21DC;&#x21DD;&#x21DE;&#x21DF;&#x21E0;&#x21E1;&#x21E2;&#x21E3;&#x21E4;&#x21E5;\',\n          \'&#x21E6;&#x21E7;&#x21E8;&#x21E9;&#x21F3;&#x21F4;&#x21F5;&#x21F6;&#x21F7;&#x21F8;&#x21F9;&#x21FA;\',\n          \'&#x21FB;&#x21FC;&#x21FD;&#x21FE;&#x21FF;&#x2200;&#x2201;&#x2202;&#x2203;&#x2204;&#x2206;&#x2207;\',\n          \'&#x2208;&#x2209;&#x220A;&#x220B;&#x220C;&#x220D;&#x220F;&#x2210;&#x2211;&#x2212;&#x2213;&#x2214;\',\n          \'&#x2215;&#x2216;&#x2217;&#x2218;&#x2219;&#x221A;&#x221B;&#x221C;&#x221D;&#x2223;&#x2224;&#x2225;\',\n          \'&#x2226;&#x2227;&#x2228;&#x2229;&#x222A;&#x222B;&#x222C;&#x222D;&#x222E;&#x222F;&#x2230;&#x2231;\',\n          \'&#x2232;&#x2233;&#x2234;&#x2235;&#x2236;&#x2237;&#x2238;&#x2239;&#x223A;&#x223B;&#x223C;&#x223D;\',\n          \'&#x223E;&#x2240;&#x2241;&#x2242;&#x2243;&#x2244;&#x2245;&#x2246;&#x2247;&#x2248;&#x2249;&#x224A;\',\n          \'&#x224B;&#x224C;&#x224D;&#x224E;&#x224F;&#x2250;&#x2251;&#x2252;&#x2253;&#x2254;&#x2255;&#x2256;\',\n          \'&#x2257;&#x2258;&#x2259;&#x225A;&#x225B;&#x225C;&#x225D;&#x225E;&#x225F;&#x2260;&#x2261;&#x2262;\',\n          \'&#x2263;&#x2264;&#x2265;&#x2266;&#x2267;&#x2268;&#x2269;&#x226A;&#x226B;&#x226C;&#x226D;&#x226E;\',\n          \'&#x226F;&#x2270;&#x2271;&#x2272;&#x2273;&#x2274;&#x2275;&#x2276;&#x2277;&#x2278;&#x2279;&#x227A;\',\n          \'&#x227B;&#x227C;&#x227D;&#x227E;&#x227F;&#x2280;&#x2281;&#x2282;&#x2283;&#x2284;&#x2285;&#x2286;\',\n          \'&#x2287;&#x2288;&#x2289;&#x228A;&#x228B;&#x228C;&#x228D;&#x228E;&#x228F;&#x2290;&#x2291;&#x2292;\',\n          \'&#x2293;&#x2294;&#x2295;&#x2296;&#x2297;&#x2298;&#x2299;&#x229A;&#x229B;&#x229C;&#x229D;&#x229E;\',\n          \'&#x229F;&#x22A0;&#x22A1;&#x22A2;&#x22A3;&#x22A5;&#x22A6;&#x22A7;&#x22A8;&#x22A9;&#x22AA;&#x22AB;\',\n          \'&#x22AC;&#x22AD;&#x22AE;&#x22AF;&#x22B0;&#x22B1;&#x22B2;&#x22B3;&#x22B4;&#x22B5;&#x22B6;&#x22B7;\',\n          \'&#x22B8;&#x22B9;&#x22BA;&#x22BB;&#x22BC;&#x22BD;&#x22C0;&#x22C1;&#x22C2;&#x22C3;&#x22C4;&#x22C5;\',\n          \'&#x22C6;&#x22C7;&#x22C8;&#x22C9;&#x22CA;&#x22CB;&#x22CC;&#x22CD;&#x22CE;&#x22CF;&#x22D0;&#x22D1;\',\n          \'&#x22D2;&#x22D3;&#x22D4;&#x22D5;&#x22D6;&#x22D7;&#x22D8;&#x22D9;&#x22DA;&#x22DB;&#x22DC;&#x22DD;\',\n          \'&#x22DE;&#x22DF;&#x22E0;&#x22E1;&#x22E2;&#x22E3;&#x22E4;&#x22E5;&#x22E6;&#x22E7;&#x22E8;&#x22E9;\',\n          \'&#x22EA;&#x22EB;&#x22EC;&#x22ED;&#x22EE;&#x22EF;&#x22F0;&#x22F1;&#x22F2;&#x22F3;&#x22F4;&#x22F5;\',\n          \'&#x22F6;&#x22F7;&#x22F8;&#x22F9;&#x22FA;&#x22FB;&#x22FC;&#x22FD;&#x22FE;&#x22FF;&#x2305;&#x2306;\',\n          \'&#x2308;&#x2309;&#x230A;&#x230B;&#x231C;&#x231D;&#x231E;&#x231F;&#x2322;&#x2323;&#x2329;&#x232A;\',\n          \'&#x233D;&#x233F;&#x23B0;&#x23B1;&#x23DC;&#x23DD;&#x23DE;&#x23DF;&#x23E0;&#x2502;&#x251C;&#x2524;\',\n          \'&#x252C;&#x2534;&#x2581;&#x2588;&#x2592;&#x25A0;&#x25A1;&#x25AD;&#x25B2;&#x25B3;&#x25B4;&#x25B5;\',\n          \'&#x25B6;&#x25B7;&#x25B8;&#x25B9;&#x25BC;&#x25BD;&#x25BE;&#x25BF;&#x25C0;&#x25C1;&#x25C2;&#x25C3;\',\n          \'&#x25C4;&#x25C5;&#x25CA;&#x25CB;&#x25E6;&#x25EB;&#x25EC;&#x25F8;&#x25F9;&#x25FA;&#x25FB;&#x25FC;\',\n          \'&#x25FD;&#x25FE;&#x25FF;&#x2605;&#x2606;&#x2772;&#x2773;&#x27D1;&#x27D2;&#x27D3;&#x27D4;&#x27D5;\',\n          \'&#x27D6;&#x27D7;&#x27D8;&#x27D9;&#x27DA;&#x27DB;&#x27DC;&#x27DD;&#x27DE;&#x27DF;&#x27E0;&#x27E1;\',\n          \'&#x27E2;&#x27E3;&#x27E4;&#x27E5;&#x27E6;&#x27E7;&#x27E8;&#x27E9;&#x27EA;&#x27EB;&#x27F0;&#x27F1;\',\n          \'&#x27F2;&#x27F3;&#x27F4;&#x27F5;&#x27F6;&#x27F7;&#x27F8;&#x27F9;&#x27FA;&#x27FB;&#x27FC;&#x27FD;\',\n          \'&#x27FE;&#x27FF;&#x2900;&#x2901;&#x2902;&#x2903;&#x2904;&#x2905;&#x2906;&#x2907;&#x2908;&#x2909;\',\n          \'&#x290A;&#x290B;&#x290C;&#x290D;&#x290E;&#x290F;&#x2910;&#x2911;&#x2912;&#x2913;&#x2914;&#x2915;\',\n          \'&#x2916;&#x2917;&#x2918;&#x2919;&#x291A;&#x291B;&#x291C;&#x291D;&#x291E;&#x291F;&#x2920;&#x2921;\',\n          \'&#x2922;&#x2923;&#x2924;&#x2925;&#x2926;&#x2927;&#x2928;&#x2929;&#x292A;&#x292B;&#x292C;&#x292D;\',\n          \'&#x292E;&#x292F;&#x2930;&#x2931;&#x2932;&#x2933;&#x2934;&#x2935;&#x2936;&#x2937;&#x2938;&#x2939;\',\n          \'&#x293A;&#x293B;&#x293C;&#x293D;&#x293E;&#x293F;&#x2940;&#x2941;&#x2942;&#x2943;&#x2944;&#x2945;\',\n          \'&#x2946;&#x2947;&#x2948;&#x2949;&#x294A;&#x294B;&#x294C;&#x294D;&#x294E;&#x294F;&#x2950;&#x2951;\',\n          \'&#x2952;&#x2953;&#x2954;&#x2955;&#x2956;&#x2957;&#x2958;&#x2959;&#x295A;&#x295B;&#x295C;&#x295D;\',\n          \'&#x295E;&#x295F;&#x2960;&#x2961;&#x2962;&#x2963;&#x2964;&#x2965;&#x2966;&#x2967;&#x2968;&#x2969;\',\n          \'&#x296A;&#x296B;&#x296C;&#x296D;&#x296E;&#x296F;&#x2970;&#x2971;&#x2972;&#x2973;&#x2974;&#x2975;\',\n          \'&#x2976;&#x2977;&#x2978;&#x2979;&#x297A;&#x297B;&#x297C;&#x297D;&#x297E;&#x297F;&#x2980;&#x2982;\',\n          \'&#x2983;&#x2984;&#x2985;&#x2986;&#x2987;&#x2988;&#x2989;&#x298A;&#x298B;&#x298C;&#x298D;&#x298E;\',\n          \'&#x298F;&#x2990;&#x2991;&#x2992;&#x2993;&#x2994;&#x2995;&#x2996;&#x2997;&#x2998;&#x2999;&#x299A;\',\n          \'&#x29B6;&#x29B7;&#x29B8;&#x29B9;&#x29C0;&#x29C1;&#x29C4;&#x29C5;&#x29C6;&#x29C7;&#x29C8;&#x29CE;\',\n          \'&#x29CF;&#x29D0;&#x29D1;&#x29D2;&#x29D3;&#x29D4;&#x29D5;&#x29D6;&#x29D7;&#x29D8;&#x29D9;&#x29DA;\',\n          \'&#x29DB;&#x29DF;&#x29E1;&#x29E2;&#x29E3;&#x29E4;&#x29E5;&#x29E6;&#x29EB;&#x29F4;&#x29F5;&#x29F6;\',\n          \'&#x29F7;&#x29F8;&#x29F9;&#x29FA;&#x29FB;&#x29FC;&#x29FD;&#x29FE;&#x29FF;&#x2A00;&#x2A01;&#x2A02;\',\n          \'&#x2A03;&#x2A04;&#x2A05;&#x2A06;&#x2A07;&#x2A08;&#x2A09;&#x2A0A;&#x2A0B;&#x2A0C;&#x2A0D;&#x2A0E;\',\n          \'&#x2A0F;&#x2A10;&#x2A11;&#x2A12;&#x2A13;&#x2A14;&#x2A15;&#x2A16;&#x2A17;&#x2A18;&#x2A19;&#x2A1A;\',\n          \'&#x2A1B;&#x2A1C;&#x2A1D;&#x2A1E;&#x2A1F;&#x2A20;&#x2A21;&#x2A22;&#x2A23;&#x2A24;&#x2A25;&#x2A26;\',\n          \'&#x2A27;&#x2A28;&#x2A29;&#x2A2A;&#x2A2B;&#x2A2C;&#x2A2D;&#x2A2E;&#x2A2F;&#x2A30;&#x2A31;&#x2A32;\',\n          \'&#x2A33;&#x2A34;&#x2A35;&#x2A36;&#x2A37;&#x2A38;&#x2A39;&#x2A3A;&#x2A3B;&#x2A3C;&#x2A3D;&#x2A3E;\',\n          \'&#x2A3F;&#x2A40;&#x2A41;&#x2A42;&#x2A43;&#x2A44;&#x2A45;&#x2A46;&#x2A47;&#x2A48;&#x2A49;&#x2A4A;\',\n          \'&#x2A4B;&#x2A4C;&#x2A4D;&#x2A4E;&#x2A4F;&#x2A50;&#x2A51;&#x2A52;&#x2A53;&#x2A54;&#x2A55;&#x2A56;\',\n          \'&#x2A57;&#x2A58;&#x2A59;&#x2A5A;&#x2A5B;&#x2A5C;&#x2A5D;&#x2A5E;&#x2A5F;&#x2A60;&#x2A61;&#x2A62;\',\n          \'&#x2A63;&#x2A64;&#x2A65;&#x2A66;&#x2A67;&#x2A68;&#x2A69;&#x2A6A;&#x2A6B;&#x2A6C;&#x2A6D;&#x2A6E;\',\n          \'&#x2A6F;&#x2A70;&#x2A71;&#x2A72;&#x2A73;&#x2A74;&#x2A75;&#x2A76;&#x2A77;&#x2A78;&#x2A79;&#x2A7A;\',\n          \'&#x2A7B;&#x2A7C;&#x2A7D;&#x2A7E;&#x2A7F;&#x2A80;&#x2A81;&#x2A82;&#x2A83;&#x2A84;&#x2A85;&#x2A86;\',\n          \'&#x2A87;&#x2A88;&#x2A89;&#x2A8A;&#x2A8B;&#x2A8C;&#x2A8D;&#x2A8E;&#x2A8F;&#x2A90;&#x2A91;&#x2A92;\',\n          \'&#x2A93;&#x2A94;&#x2A95;&#x2A96;&#x2A97;&#x2A98;&#x2A99;&#x2A9A;&#x2A9B;&#x2A9C;&#x2A9D;&#x2A9E;\',\n          \'&#x2A9F;&#x2AA0;&#x2AA1;&#x2AA2;&#x2AA3;&#x2AA4;&#x2AA5;&#x2AA6;&#x2AA7;&#x2AA8;&#x2AA9;&#x2AAA;\',\n          \'&#x2AAB;&#x2AAC;&#x2AAD;&#x2AAE;&#x2AAF;&#x2AB0;&#x2AB1;&#x2AB2;&#x2AB3;&#x2AB4;&#x2AB5;&#x2AB6;\',\n          \'&#x2AB7;&#x2AB8;&#x2AB9;&#x2ABA;&#x2ABB;&#x2ABC;&#x2ABD;&#x2ABE;&#x2ABF;&#x2AC0;&#x2AC1;&#x2AC2;\',\n          \'&#x2AC3;&#x2AC4;&#x2AC5;&#x2AC6;&#x2AC7;&#x2AC8;&#x2AC9;&#x2ACA;&#x2ACB;&#x2ACC;&#x2ACD;&#x2ACE;\',\n          \'&#x2ACF;&#x2AD0;&#x2AD1;&#x2AD2;&#x2AD3;&#x2AD4;&#x2AD5;&#x2AD6;&#x2AD7;&#x2AD8;&#x2AD9;&#x2ADA;\',\n          \'&#x2ADB;&#x2ADC;&#x2ADD;&#x2ADE;&#x2ADF;&#x2AE0;&#x2AE2;&#x2AE3;&#x2AE4;&#x2AE5;&#x2AE6;&#x2AE7;\',\n          \'&#x2AE8;&#x2AE9;&#x2AEA;&#x2AEB;&#x2AEC;&#x2AED;&#x2AEE;&#x2AEF;&#x2AF0;&#x2AF2;&#x2AF3;&#x2AF4;\',\n          \'&#x2AF5;&#x2AF6;&#x2AF7;&#x2AF8;&#x2AF9;&#x2AFA;&#x2AFB;&#x2AFC;&#x2AFD;&#x2AFE;&#x2AFF;&#x2B04;\',\n          \'&#x2B06;&#x2B07;&#x2B0C;&#x2B0D;&#x3014;&#x3015;&#x3016;&#x3017;&#x3018;&#x3019;&#xFF01;&#xFF06;\',\n          \'&#xFF08;&#xFF09;&#xFF0B;&#xFF0C;&#xFF0D;&#xFF0E;&#xFF0F;&#xFF1A;&#xFF1B;&#xFF1C;&#xFF1D;&#xFF1E;\',\n          \'&#xFF1F;&#xFF20;&#xFF3B;&#xFF3C;&#xFF3D;&#xFF3E;&#xFF3F;&#xFF5B;&#xFF5C;&#xFF5D;\')" />\n\n  \x3c!-- A string of \'-\'s repeated exactly as many times as the operators above --\x3e\n  <xsl:variable name="sMinuses">\n    <xsl:call-template name="SRepeatChar">\n      <xsl:with-param name="cchRequired" select="string-length($sOperators)" />\n      <xsl:with-param name="ch" select="\'-\'" />\n    </xsl:call-template>\n  </xsl:variable>\n\n  \x3c!-- Every single unicode character that is recognized by OMML as a number --\x3e\n  <xsl:variable name="sNumbers" select="\'0123456789\'" />\n\n  \x3c!-- A string of \'0\'s repeated exactly as many times as the list of numbers above --\x3e\n  <xsl:variable name="sZeros">\n    <xsl:call-template name="SRepeatChar">\n      <xsl:with-param name="cchRequired" select="string-length($sNumbers)" />\n      <xsl:with-param name="ch" select="\'0\'" />\n    </xsl:call-template>\n  </xsl:variable>\n\n  \x3c!-- %%Template: SReplace\n\n\t\tReplace all occurences of sOrig in sInput with sReplacement\n\t\tand return the resulting string. --\x3e\n  <xsl:template name="SReplace">\n    <xsl:param name="sInput" />\n    <xsl:param name="sOrig" />\n    <xsl:param name="sReplacement" />\n\n    <xsl:choose>\n      <xsl:when test="not(contains($sInput, $sOrig))">\n        <xsl:value-of select="$sInput" />\n      </xsl:when>\n      <xsl:otherwise>\n        <xsl:variable name="sBefore" select="substring-before($sInput, $sOrig)" />\n        <xsl:variable name="sAfter" select="substring-after($sInput, $sOrig)" />\n        <xsl:variable name="sAfterProcessed">\n          <xsl:call-template name="SReplace">\n            <xsl:with-param name="sInput" select="$sAfter" />\n            <xsl:with-param name="sOrig" select="$sOrig" />\n            <xsl:with-param name="sReplacement" select="$sReplacement" />\n          </xsl:call-template>\n        </xsl:variable>\n\n        <xsl:value-of select="concat($sBefore, concat($sReplacement, $sAfterProcessed))" />\n      </xsl:otherwise>\n    </xsl:choose>\n  </xsl:template>\n\n  \x3c!-- Templates --\x3e\n  <xsl:template match="/">\n    <mml:math>\n      <xsl:apply-templates select="*" />\n    </mml:math>\n  </xsl:template>\n\n  <xsl:template match="m:borderBox">\n\n    \x3c!-- Get Lowercase versions of properties --\x3e\n    <xsl:variable name="sLowerCaseHideTop" select="translate(m:borderBoxPr[last()]/m:hideTop[last()]/@m:val, \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\',\n\t\t                                                             \'abcdefghijklmnopqrstuvwxyz\')" />\n    <xsl:variable name="sLowerCaseHideBot" select="translate(m:borderBoxPr[last()]/m:hideBot[last()]/@m:val, \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\',\n\t\t                                                             \'abcdefghijklmnopqrstuvwxyz\')" />\n    <xsl:variable name="sLowerCaseHideLeft" select="translate(m:borderBoxPr[last()]/m:hideLeft[last()]/@m:val, \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\',\n\t\t                                                             \'abcdefghijklmnopqrstuvwxyz\')" />\n    <xsl:variable name="sLowerCaseHideRight" select="translate(m:borderBoxPr[last()]/m:hideRight[last()]/@m:val, \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\',\n\t\t                                                             \'abcdefghijklmnopqrstuvwxyz\')" />\n    <xsl:variable name="sLowerCaseStrikeH" select="translate(m:borderBoxPr[last()]/m:strikeH[last()]/@m:val, \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\',\n\t\t                                                             \'abcdefghijklmnopqrstuvwxyz\')" />\n    <xsl:variable name="sLowerCaseStrikeV" select="translate(m:borderBoxPr[last()]/m:strikeV[last()]/@m:val, \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\',\n\t\t                                                             \'abcdefghijklmnopqrstuvwxyz\')" />\n    <xsl:variable name="sLowerCaseStrikeBLTR" select="translate(m:borderBoxPr[last()]/m:strikeBLTR[last()]/@m:val, \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\',\n\t\t                                                             \'abcdefghijklmnopqrstuvwxyz\')" />\n    <xsl:variable name="sLowerCaseStrikeTLBR" select="translate(m:borderBoxPr[last()]/m:strikeTLBR[last()]/@m:val, \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\',\n\t\t                                                             \'abcdefghijklmnopqrstuvwxyz\')" />\n    <xsl:variable name="fHideTop">\n      <xsl:call-template name="ForceTrueStrVal">\n        <xsl:with-param name="str" select="$sLowerCaseHideTop" />\n      </xsl:call-template>\n    </xsl:variable>\n    <xsl:variable name="fHideBot">\n      <xsl:call-template name="ForceTrueStrVal">\n        <xsl:with-param name="str" select="$sLowerCaseHideBot" />\n      </xsl:call-template>\n    </xsl:variable>\n    <xsl:variable name="fHideLeft">\n      <xsl:call-template name="ForceTrueStrVal">\n        <xsl:with-param name="str" select="$sLowerCaseHideLeft" />\n      </xsl:call-template>\n    </xsl:variable>\n    <xsl:variable name="fHideRight">\n      <xsl:call-template name="ForceTrueStrVal">\n        <xsl:with-param name="str" select="$sLowerCaseHideRight" />\n      </xsl:call-template>\n    </xsl:variable>\n    <xsl:variable name="fStrikeH">\n      <xsl:call-template name="ForceTrueStrVal">\n        <xsl:with-param name="str" select="$sLowerCaseStrikeH" />\n      </xsl:call-template>\n    </xsl:variable>\n    <xsl:variable name="fStrikeV">\n      <xsl:call-template name="ForceTrueStrVal">\n        <xsl:with-param name="str" select="$sLowerCaseStrikeV" />\n      </xsl:call-template>\n    </xsl:variable>\n    <xsl:variable name="fStrikeBLTR">\n      <xsl:call-template name="ForceTrueStrVal">\n        <xsl:with-param name="str" select="$sLowerCaseStrikeBLTR" />\n      </xsl:call-template>\n    </xsl:variable>\n    <xsl:variable name="fStrikeTLBR">\n      <xsl:call-template name="ForceTrueStrVal">\n        <xsl:with-param name="str" select="$sLowerCaseStrikeTLBR" />\n      </xsl:call-template>\n    </xsl:variable>\n\n    <xsl:choose>\n      <xsl:when test="$fHideTop=1\n                      and $fHideBot=1\n                      and $fHideLeft=1\n                      and $fHideRight=1\n                      and $fStrikeH=0\n                      and $fStrikeV=0\n                      and $fStrikeBLTR=0\n                      and $fStrikeTLBR=0">\n        <mml:mrow>\n          <xsl:apply-templates select="m:e[1]" />\n        </mml:mrow>\n      </xsl:when>\n      <xsl:otherwise>\n        <mml:menclose>\n          <xsl:call-template name="CreateMencloseNotationAttrFromBorderBoxAttr">\n            <xsl:with-param name="fHideTop" select="$fHideTop" />\n            <xsl:with-param name="fHideBot" select="$fHideBot" />\n            <xsl:with-param name="fHideLeft" select="$fHideLeft" />\n            <xsl:with-param name="fHideRight" select="$fHideRight" />\n            <xsl:with-param name="fStrikeH" select="$fStrikeH" />\n            <xsl:with-param name="fStrikeV" select="$fStrikeV" />\n            <xsl:with-param name="fStrikeBLTR" select="$fStrikeBLTR" />\n            <xsl:with-param name="fStrikeTLBR" select="$fStrikeTLBR" />\n          </xsl:call-template>\n          <xsl:apply-templates select="m:e[1]" />\n        </mml:menclose>\n      </xsl:otherwise>\n    </xsl:choose>\n  </xsl:template>\n\n  <xsl:template match="*">\n    <xsl:apply-templates select="*" />\n  </xsl:template>\n\n  \x3c!--\n      { Non-combining, Upper-combining, Lower-combining }\n      {U+02D8, U+0306, U+032E}, // BREVE\n      {U+00B8, U+0312, U+0327}, // CEDILLA\n      {U+0060, U+0300, U+0316}, // GRAVE ACCENT\n      {U+002D, U+0305, U+0332}, // HYPHEN-MINUS/OVERLINE\n      {U+2212, U+0305, U+0332}, // MINUS SIGN/OVERLINE\n      {U+002E, U+0305, U+0323}, // FULL STOP/DOT ABOVE\n      {U+02D9, U+0307, U+0323}, // DOT ABOVE\n      {U+02DD, U+030B, U+02DD}, // DOUBLE ACUTE ACCENT\n      {U+00B4, U+0301, U+0317}, // ACUTE ACCENT\n      {U+007E, U+0303, U+0330}, // TILDE\n      {U+02DC, U+0303, U+0330}, // SMALL TILDE\n      {U+00A8, U+0308, U+0324}, // DIAERESIS\n      {U+02C7, U+030C, U+032C}, // CARON\n      {U+005E, U+0302, U+032D}, // CIRCUMFLEX ACCENT\n      {U+00AF, U+0305, ::::::}, // MACRON\n      {U+005F, ::::::, U+0332}, // LOW LINE\n      {U+2192, U+20D7, U+20EF}, // RIGHTWARDS ARROW\n      {U+27F6, U+20D7, U+20EF}, // LONG RIGHTWARDS ARROW\n      {U+2190, U+20D6, U+20EE}, // LEFT ARROW\n  --\x3e\n  <xsl:template name="ToNonCombining">\n    <xsl:param name="ch" />\n    <xsl:choose>\n      \x3c!-- BREVE --\x3e\n      <xsl:when test="$ch=\'&#x0306;\' or $ch=\'&#x032e;\'">&#x02D8;</xsl:when>\n      \x3c!-- CEDILLA --\x3e\n      <xsl:when test="$ch=\'&#x0312;\' or $ch=\'&#x0327;\'">&#x00B8;</xsl:when>\n      \x3c!-- GRAVE ACCENT --\x3e\n      <xsl:when test="$ch=\'&#x0300;\' or $ch=\'&#x0316;\'">&#x0060;</xsl:when>\n      \x3c!-- HYPHEN-MINUS/OVERLINE --\x3e\n      <xsl:when test="$ch=\'&#x0305;\' or $ch=\'&#x0332;\'">&#x002D;</xsl:when>\n      \x3c!-- MINUS SIGN/OVERLINE --\x3e\n      <xsl:when test="$ch=\'&#x0305;\' or $ch=\'&#x0332;\'">&#x2212;</xsl:when>\n      \x3c!-- FULL STOP/DOT ABOVE --\x3e\n      <xsl:when test="$ch=\'&#x0305;\' or $ch=\'&#x0323;\'">&#x002E;</xsl:when>\n      \x3c!-- DOT ABOVE --\x3e\n      <xsl:when test="$ch=\'&#x0307;\' or $ch=\'&#x0323;\'">&#x02D9;</xsl:when>\n      \x3c!-- DOUBLE ACUTE ACCENT --\x3e\n      <xsl:when test="$ch=\'&#x030B;\' or $ch=\'&#x02DD;\'">&#x02DD;</xsl:when>\n      \x3c!-- ACUTE ACCENT --\x3e\n      <xsl:when test="$ch=\'&#x0301;\' or $ch=\'&#x0317;\'">&#x00B4;</xsl:when>\n      \x3c!-- TILDE --\x3e\n      <xsl:when test="$ch=\'&#x0303;\' or $ch=\'&#x0330;\'">&#x007E;</xsl:when>\n      \x3c!-- SMALL TILDE --\x3e\n      <xsl:when test="$ch=\'&#x0303;\' or $ch=\'&#x0330;\'">&#x02DC;</xsl:when>\n      \x3c!-- DIAERESIS --\x3e\n      <xsl:when test="$ch=\'&#x0308;\' or $ch=\'&#x0324;\'">&#x00A8;</xsl:when>\n      \x3c!-- CARON --\x3e\n      <xsl:when test="$ch=\'&#x030C;\' or $ch=\'&#x032C;\'">&#x02C7;</xsl:when>\n      \x3c!-- CIRCUMFLEX ACCENT --\x3e\n      <xsl:when test="$ch=\'&#x0302;\' or $ch=\'&#x032D;\'">&#x005E;</xsl:when>\n      \x3c!-- MACRON --\x3e\n      <xsl:when test="$ch=\'&#x0305;\'                   ">&#x00AF;</xsl:when>\n      \x3c!-- LOW LINE --\x3e\n      <xsl:when test="                   $ch=\'&#x0332;\'">&#x005F;</xsl:when>\n      \x3c!-- RIGHTWARDS ARROW --\x3e\n      <xsl:when test="$ch=\'&#x20D7;\' or $ch=\'&#x20EF;\'">&#x2192;</xsl:when>\n      \x3c!-- LONG RIGHTWARDS ARROW --\x3e\n      <xsl:when test="$ch=\'&#x20D7;\' or $ch=\'&#x20EF;\'">&#x27F6;</xsl:when>\n      \x3c!-- LEFT ARROW --\x3e\n      <xsl:when test="$ch=\'&#x20D6;\' or $ch=\'&#x20EE;\'">&#x2190;</xsl:when>\n      <xsl:otherwise>\n        <xsl:value-of select="$ch"/>\n      </xsl:otherwise>\n    </xsl:choose>\n  </xsl:template>\n\n  <xsl:template match="m:acc">\n    <mml:mover>\n      <xsl:attribute name="accent">true</xsl:attribute>\n      <mml:mrow>\n        <xsl:apply-templates select="m:e[1]" />\n      </mml:mrow>\n      <xsl:variable name="chAcc">\n        <xsl:choose>\n          <xsl:when test="not(m:accPr[last()]/m:chr)">\n            <xsl:value-of select="\'&#x0302;\'" />\n          </xsl:when>\n          <xsl:otherwise>\n            <xsl:value-of select="substring(m:accPr/m:chr/@m:val,1,1)" />\n          </xsl:otherwise>\n        </xsl:choose>\n      </xsl:variable>\n      <xsl:variable name="chNonComb">\n        <xsl:call-template name="ToNonCombining">\n          <xsl:with-param name="ch" select="$chAcc" />\n        </xsl:call-template>\n      </xsl:variable>\n      <xsl:choose>\n        <xsl:when test="string-length($chAcc)=0">\n          <mml:mo/>\n        </xsl:when>\n        <xsl:otherwise>\n          <xsl:call-template name="ParseMt">\n            <xsl:with-param name="sToParse" select="$chNonComb" />\n            <xsl:with-param name="scr" select="m:e[1]/*/m:rPr[last()]/m:scr/@m:val" />\n            <xsl:with-param name="sty" select="m:e[1]/*/m:rPr[last()]/m:sty/@m:val" />\n            <xsl:with-param name="nor">\n              <xsl:choose>\n                <xsl:when test="count(m:e[1]/*/m:rPr[last()]/m:nor) = 0">0</xsl:when>\n                <xsl:otherwise>\n                  <xsl:call-template name="ForceFalseStrVal">\n                    <xsl:with-param name="str" select="translate(m:e[1]/*/m:rPr[last()]/m:nor/@m:val,\n                                                                     \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\',\n\t\t                                                                 \'abcdefghijklmnopqrstuvwxyz\')" />\n                  </xsl:call-template>\n                </xsl:otherwise>\n              </xsl:choose>\n            </xsl:with-param>\n          </xsl:call-template>\n        </xsl:otherwise>\n      </xsl:choose>\n    </mml:mover>\n  </xsl:template>\n\n  <xsl:template name="OutputScript">\n    <xsl:param name="ndCur" select="." />\n    <xsl:choose>\n      \x3c!-- Only output contents of $ndCur if $ndCur exists\n           and $ndCur has children --\x3e\n      <xsl:when test="count($ndCur/*) &gt; 0">\n        <mml:mrow>\n          <xsl:apply-templates select="$ndCur" />\n        </mml:mrow>\n      </xsl:when>\n      <xsl:otherwise>\n        <mml:none />\n      </xsl:otherwise>\n    </xsl:choose>\n  </xsl:template>\n\n  <xsl:template match="m:sPre">\n    <mml:mmultiscripts>\n      <mml:mrow>\n        <xsl:apply-templates select="m:e[1]" />\n      </mml:mrow>\n      <mml:mprescripts />\n      <xsl:call-template name="OutputScript">\n        <xsl:with-param name="ndCur" select="m:sub[1]"/>\n      </xsl:call-template>\n      <xsl:call-template name="OutputScript">\n        <xsl:with-param name="ndCur" select="m:sup[1]" />\n      </xsl:call-template>\n    </mml:mmultiscripts>\n  </xsl:template>\n\n  <xsl:template match="m:m">\n    <mml:mtable>\n      <xsl:call-template name="CreateMathMLMatrixAttr">\n        <xsl:with-param name="mcJc" select="m:mPr[last()]/m:mcs/m:mc/m:mcPr[last()]/m:mcJc/@m:val" />\n      </xsl:call-template>\n      <xsl:for-each select="m:mr">\n        <mml:mtr>\n          <xsl:for-each select="m:e">\n            <mml:mtd>\n              <xsl:apply-templates select="." />\n            </mml:mtd>\n          </xsl:for-each>\n        </mml:mtr>\n      </xsl:for-each>\n    </mml:mtable>\n  </xsl:template>\n\n  <xsl:template name="CreateMathMLMatrixAttr">\n    <xsl:param name="mcJc" />\n    <xsl:variable name="sLowerCaseMcjc" select="translate($mcJc, \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\',\n\t\t                                                             \'abcdefghijklmnopqrstuvwxyz\')" />\n    <xsl:choose>\n      <xsl:when test="$sLowerCaseMcjc=\'left\'">\n        <xsl:attribute name="columnalign">left</xsl:attribute>\n      </xsl:when>\n      <xsl:when test="$sLowerCaseMcjc=\'right\'">\n        <xsl:attribute name="columnalign">right</xsl:attribute>\n      </xsl:when>\n    </xsl:choose>\n  </xsl:template>\n\n  <xsl:template match="m:phant">\n    <xsl:variable name="sLowerCaseZeroWidVal" select="translate(m:phantPr[last()]/m:zeroWid[last()]/@m:val,\n\t\t                                                       \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\',\n\t\t                                                       \'abcdefghijklmnopqrstuvwxyz\')" />\n    <xsl:variable name="sLowerCaseZeroAscVal" select="translate(m:phantPr[last()]/m:zeroAsc[last()]/@m:val,\n\t\t                                                     \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\',\n\t\t                                                     \'abcdefghijklmnopqrstuvwxyz\')" />\n    <xsl:variable name="sLowerCaseZeroDescVal" select="translate(m:phantPr[last()]/m:zeroDesc[last()]/@m:val,\n\t\t                                                     \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\',\n\t\t                                                     \'abcdefghijklmnopqrstuvwxyz\')" />\n    <xsl:variable name="sLowerCaseShowVal" select="translate(m:phantPr[last()]/m:show[last()]/@m:val,\n\t\t                                                     \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\',\n\t\t                                                     \'abcdefghijklmnopqrstuvwxyz\')" />\n\n\n    \x3c!-- The following properties default to \'yes\' unless the last value equals \'no\' or there isn\'t any node for\n         the property --\x3e\n\n    <xsl:variable name="fZeroWid">\n      <xsl:choose>\n        <xsl:when test="count(m:phantPr[last()]/m:zeroWid[last()]) = 0">0</xsl:when>\n        <xsl:otherwise>\n          <xsl:call-template name="ForceFalseStrVal">\n            <xsl:with-param name="str" select="$sLowerCaseZeroWidVal" />\n          </xsl:call-template>\n        </xsl:otherwise>\n      </xsl:choose>\n    </xsl:variable>\n    <xsl:variable name="fZeroAsc">\n      <xsl:choose>\n        <xsl:when test="count(m:phantPr[last()]/m:zeroAsc[last()]) = 0">0</xsl:when>\n        <xsl:otherwise>\n          <xsl:call-template name="ForceFalseStrVal">\n            <xsl:with-param name="str" select="$sLowerCaseZeroAscVal" />\n          </xsl:call-template>\n        </xsl:otherwise>\n      </xsl:choose>\n    </xsl:variable>\n    <xsl:variable name="fZeroDesc">\n      <xsl:choose>\n        <xsl:when test="count(m:phantPr[last()]/m:zeroDesc[last()]) = 0">0</xsl:when>\n        <xsl:otherwise>\n          <xsl:call-template name="ForceFalseStrVal">\n            <xsl:with-param name="str" select="$sLowerCaseZeroDescVal" />\n          </xsl:call-template>\n        </xsl:otherwise>\n      </xsl:choose>\n    </xsl:variable>\n\n    \x3c!-- The show property defaults to \'on\' unless there exists a show property and its value is \'off\' --\x3e\n\n    <xsl:variable name="fShow">\n      <xsl:call-template name="ForceFalseStrVal">\n        <xsl:with-param name="str" select="$sLowerCaseShowVal" />\n      </xsl:call-template>\n    </xsl:variable>\n\n    <xsl:choose>\n      \x3c!-- Show the phantom contents, therefore, just use mpadded. --\x3e\n      <xsl:when test="$fShow = 1">\n        <xsl:element name="mml:mpadded">\n          <xsl:call-template name="CreateMpaddedAttributes">\n            <xsl:with-param name="fZeroWid" select="$fZeroWid" />\n            <xsl:with-param name="fZeroAsc" select="$fZeroAsc" />\n            <xsl:with-param name="fZeroDesc" select="$fZeroDesc" />\n          </xsl:call-template>\n          <mml:mrow>\n            <xsl:apply-templates select="m:e" />\n          </mml:mrow>\n        </xsl:element>\n      </xsl:when>\n      \x3c!-- Don\'t show phantom contents, but don\'t smash anything, therefore, just\n           use mphantom --\x3e\n      <xsl:when test="$fZeroWid=0 and $fZeroAsc=0 and $fZeroDesc=0">\n        <xsl:element name="mml:mphantom">\n          <mml:mrow>\n            <xsl:apply-templates select="m:e" />\n          </mml:mrow>\n        </xsl:element>\n      </xsl:when>\n      \x3c!-- Combination --\x3e\n      <xsl:otherwise>\n        <xsl:element name="mml:mphantom">\n          <xsl:element name="mml:mpadded">\n            <xsl:call-template name="CreateMpaddedAttributes">\n              <xsl:with-param name="fZeroWid" select="$fZeroWid" />\n              <xsl:with-param name="fZeroAsc" select="$fZeroAsc" />\n              <xsl:with-param name="fZeroDesc" select="$fZeroDesc" />\n            </xsl:call-template>\n            <mml:mrow>\n              <xsl:apply-templates select="m:e" />\n            </mml:mrow>\n          </xsl:element>\n        </xsl:element>\n      </xsl:otherwise>\n    </xsl:choose>\n  </xsl:template>\n\n  <xsl:template name="CreateMpaddedAttributes">\n    <xsl:param name="fZeroWid" />\n    <xsl:param name="fZeroAsc" />\n    <xsl:param name="fZeroDesc" />\n\n    <xsl:if test="$fZeroWid=1">\n      <xsl:attribute name="width">0in</xsl:attribute>\n    </xsl:if>\n    <xsl:if test="$fZeroAsc=1">\n      <xsl:attribute name="height">0in</xsl:attribute>\n    </xsl:if>\n    <xsl:if test="$fZeroDesc=1">\n      <xsl:attribute name="depth">0in</xsl:attribute>\n    </xsl:if>\n  </xsl:template>\n\n\n\n  <xsl:template match="m:rad">\n    <xsl:variable name="fDegHide">\n      <xsl:choose>\n        <xsl:when test="count(m:radPr[last()]/m:degHide)=0">0</xsl:when>\n        <xsl:otherwise>\n          <xsl:call-template name="ForceFalseStrVal">\n            <xsl:with-param name="str" select="translate(m:radPr[last()]/m:degHide/@m:val,\n\t\t                                                          \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\',\n\t\t                                                          \'abcdefghijklmnopqrstuvwxyz\')" />\n          </xsl:call-template>\n        </xsl:otherwise>\n      </xsl:choose>\n    </xsl:variable>\n    <xsl:choose>\n      <xsl:when test="$fDegHide=1">\n        <mml:msqrt>\n          <xsl:apply-templates select="m:e[1]" />\n        </mml:msqrt>\n      </xsl:when>\n      <xsl:otherwise>\n        <mml:mroot>\n          <mml:mrow>\n            <xsl:apply-templates select="m:e[1]" />\n          </mml:mrow>\n          <mml:mrow>\n            <xsl:apply-templates select="m:deg[1]" />\n          </mml:mrow>\n        </mml:mroot>\n      </xsl:otherwise>\n    </xsl:choose>\n  </xsl:template>\n\n  <xsl:template name="OutputNaryMo">\n    <xsl:param name="ndCur" select="." />\n    <xsl:param name="fGrow" select="0" />\n    <mml:mo>\n      <xsl:choose>\n        <xsl:when test="$fGrow=1">\n          <xsl:attribute name="stretchy">true</xsl:attribute>\n        </xsl:when>\n        <xsl:otherwise>\n          <xsl:attribute name="stretchy">false</xsl:attribute>\n        </xsl:otherwise>\n      </xsl:choose>\n      <xsl:choose>\n        <xsl:when test="not($ndCur/m:naryPr[last()]/m:chr/@m:val) or\n\t\t\t                            $ndCur/m:naryPr[last()]/m:chr/@m:val=\'\'">\n          <xsl:text>&#x222b;</xsl:text>\n        </xsl:when>\n        <xsl:otherwise>\n          <xsl:value-of select="$ndCur/m:naryPr[last()]/m:chr/@m:val" />\n        </xsl:otherwise>\n      </xsl:choose>\n    </mml:mo>\n  </xsl:template>\n\n  \x3c!-- %%Template match m:nary\n\t\tProcess an n-ary.\n\n\t\tDecides, based on which arguments are supplied, between\n\t\tusing an mo, msup, msub, or msubsup for the n-ary operator\n\t--\x3e\n  <xsl:template match="m:nary">\n    <xsl:variable name="sLowerCaseSubHide">\n      <xsl:choose>\n        <xsl:when test="count(m:naryPr[last()]/m:subHide) = 0">\n          <xsl:text>off</xsl:text>\n        </xsl:when>\n        <xsl:otherwise>\n          <xsl:value-of select="translate(m:naryPr[last()]/m:subHide/@m:val,\n\t                                  \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\',\n\t                                  \'abcdefghijklmnopqrstuvwxyz\')" />\n        </xsl:otherwise>\n      </xsl:choose>\n    </xsl:variable>\n\n    <xsl:variable name="sLowerCaseSupHide">\n      <xsl:choose>\n        <xsl:when test="count(m:naryPr[last()]/m:supHide) = 0">\n          <xsl:text>off</xsl:text>\n        </xsl:when>\n        <xsl:otherwise>\n          <xsl:value-of select="translate(m:naryPr[last()]/m:supHide/@m:val,\n\t                                  \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\',\n\t                                  \'abcdefghijklmnopqrstuvwxyz\')" />\n        </xsl:otherwise>\n      </xsl:choose>\n    </xsl:variable>\n\n    <xsl:variable name="sLowerCaseLimLoc">\n      <xsl:value-of select="translate(m:naryPr[last()]/m:limLoc/@m:val,\n\t                                  \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\',\n\t                                  \'abcdefghijklmnopqrstuvwxyz\')" />\n    </xsl:variable>\n\n    <xsl:variable name="sLowerGrow">\n      <xsl:choose>\n        <xsl:when test="count(m:naryPr[last()]/m:grow)=0">off</xsl:when>\n        <xsl:otherwise>\n          <xsl:value-of select="translate(m:naryPr[last()]/m:grow/@m:val,\n\t                                  \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\',\n\t                                  \'abcdefghijklmnopqrstuvwxyz\')" />\n        </xsl:otherwise>\n      </xsl:choose>\n    </xsl:variable>\n\n    <xsl:variable name="fLimLocSubSup">\n      <xsl:choose>\n        <xsl:when test="count(m:naryPr[last()]/m:limLoc)=0 or $sLowerCaseLimLoc=\'subsup\'">1</xsl:when>\n        <xsl:otherwise>0</xsl:otherwise>\n      </xsl:choose>\n    </xsl:variable>\n\n    <xsl:variable name="fGrow">\n      <xsl:call-template name="ForceFalseStrVal">\n        <xsl:with-param name="str" select="$sLowerGrow" />\n      </xsl:call-template>\n    </xsl:variable>\n\n    <xsl:variable name="fSupHide">\n      <xsl:call-template name="ForceFalseStrVal">\n        <xsl:with-param name="str" select="$sLowerCaseSupHide" />\n      </xsl:call-template>\n    </xsl:variable>\n\n    <xsl:variable name="fSubHide">\n      <xsl:call-template name="ForceFalseStrVal">\n        <xsl:with-param name="str" select="$sLowerCaseSubHide" />\n      </xsl:call-template>\n    </xsl:variable>\n\n    <mml:mrow>\n      <xsl:choose>\n        <xsl:when test="$fSupHide=1 and $fSubHide=1">\n          <xsl:call-template name="OutputNaryMo">\n            <xsl:with-param name="ndCur" select="." />\n            <xsl:with-param name="fGrow" select="$fGrow" />\n          </xsl:call-template>\n        </xsl:when>\n        <xsl:when test="$fSubHide=1">\n          <xsl:choose>\n            <xsl:when test="$fLimLocSubSup=1">\n              <mml:msup>\n                <xsl:call-template name="OutputNaryMo">\n                  <xsl:with-param name="ndCur" select="." />\n                  <xsl:with-param name="fGrow" select="$fGrow" />\n                </xsl:call-template>\n                <mml:mrow>\n                  <xsl:apply-templates select="m:sup[1]" />\n                </mml:mrow>\n              </mml:msup>\n            </xsl:when>\n            <xsl:otherwise>\n              <mml:mover>\n                <xsl:call-template name="OutputNaryMo">\n                  <xsl:with-param name="ndCur" select="." />\n                  <xsl:with-param name="fGrow" select="$fGrow" />\n                </xsl:call-template>\n                <mml:mrow>\n                  <xsl:apply-templates select="m:sup[1]" />\n                </mml:mrow>\n              </mml:mover>\n            </xsl:otherwise>\n          </xsl:choose>\n        </xsl:when>\n        <xsl:when test="$fSupHide=1">\n          <xsl:choose>\n            <xsl:when test="$fLimLocSubSup=1">\n              <mml:msub>\n                <xsl:call-template name="OutputNaryMo">\n                  <xsl:with-param name="ndCur" select="." />\n                  <xsl:with-param name="fGrow" select="$fGrow" />\n                </xsl:call-template>\n                <mml:mrow>\n                  <xsl:apply-templates select="m:sub[1]" />\n                </mml:mrow>\n              </mml:msub>\n            </xsl:when>\n            <xsl:otherwise>\n              <mml:munder>\n                <xsl:call-template name="OutputNaryMo">\n                  <xsl:with-param name="ndCur" select="." />\n                  <xsl:with-param name="fGrow" select="$fGrow" />\n                </xsl:call-template>\n                <mml:mrow>\n                  <xsl:apply-templates select="m:sub[1]" />\n                </mml:mrow>\n              </mml:munder>\n            </xsl:otherwise>\n          </xsl:choose>\n        </xsl:when>\n        <xsl:otherwise>\n          <xsl:choose>\n            <xsl:when test="$fLimLocSubSup=1">\n              <mml:msubsup>\n                <xsl:call-template name="OutputNaryMo">\n                  <xsl:with-param name="ndCur" select="." />\n                  <xsl:with-param name="fGrow" select="$fGrow" />\n                </xsl:call-template>\n                <mml:mrow>\n                  <xsl:apply-templates select="m:sub[1]" />\n                </mml:mrow>\n                <mml:mrow>\n                  <xsl:apply-templates select="m:sup[1]" />\n                </mml:mrow>\n              </mml:msubsup>\n            </xsl:when>\n            <xsl:otherwise>\n              <mml:munderover>\n                <xsl:call-template name="OutputNaryMo">\n                  <xsl:with-param name="ndCur" select="." />\n                  <xsl:with-param name="fGrow" select="$fGrow" />\n                </xsl:call-template>\n                <mml:mrow>\n                  <xsl:apply-templates select="m:sub[1]" />\n                </mml:mrow>\n                <mml:mrow>\n                  <xsl:apply-templates select="m:sup[1]" />\n                </mml:mrow>\n              </mml:munderover>\n            </xsl:otherwise>\n          </xsl:choose>\n        </xsl:otherwise>\n      </xsl:choose>\n      <mml:mrow>\n        <xsl:apply-templates select="m:e[1]" />\n      </mml:mrow>\n    </mml:mrow>\n  </xsl:template>\n\n  <xsl:template match="m:limLow">\n    <mml:munder>\n      <mml:mrow>\n        <xsl:apply-templates select="m:e[1]" />\n      </mml:mrow>\n      <mml:mrow>\n        <xsl:apply-templates select="m:lim[1]" />\n      </mml:mrow>\n    </mml:munder>\n  </xsl:template>\n\n  <xsl:template match="m:limUpp">\n    <mml:mover>\n      <mml:mrow>\n        <xsl:apply-templates select="m:e[1]" />\n      </mml:mrow>\n      <mml:mrow>\n        <xsl:apply-templates select="m:lim[1]" />\n      </mml:mrow>\n    </mml:mover>\n  </xsl:template>\n\n  <xsl:template match="m:sSub">\n    <mml:msub>\n      <mml:mrow>\n        <xsl:apply-templates select="m:e[1]" />\n      </mml:mrow>\n      <mml:mrow>\n        <xsl:apply-templates select="m:sub[1]" />\n      </mml:mrow>\n    </mml:msub>\n  </xsl:template>\n\n  <xsl:template match="m:sSup">\n    <mml:msup>\n      <mml:mrow>\n        <xsl:apply-templates select="m:e[1]" />\n      </mml:mrow>\n      <mml:mrow>\n        <xsl:apply-templates select="m:sup[1]" />\n      </mml:mrow>\n    </mml:msup>\n  </xsl:template>\n\n  <xsl:template match="m:sSubSup">\n    <mml:msubsup>\n      <mml:mrow>\n        <xsl:apply-templates select="m:e[1]" />\n      </mml:mrow>\n      <mml:mrow>\n        <xsl:apply-templates select="m:sub[1]" />\n      </mml:mrow>\n      <mml:mrow>\n        <xsl:apply-templates select="m:sup[1]" />\n      </mml:mrow>\n    </mml:msubsup>\n  </xsl:template>\n\n  <xsl:template match="m:groupChr">\n    <xsl:variable name="ndLastGroupChrPr" select="m:groupChrPr[last()]" />\n    <xsl:variable name="sLowerCasePos" select="translate($ndLastGroupChrPr/m:pos/@m:val,\n\t\t                                                     \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\',\n\t\t                                                     \'abcdefghijklmnopqrstuvwxyz\')" />\n\n    <xsl:variable name="sLowerCaseVertJc" select="translate($ndLastGroupChrPr/m:vertJc/@m:val,\n\t\t                                                     \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\',\n\t\t                                                     \'abcdefghijklmnopqrstuvwxyz\')" />\n    <xsl:variable name="ndLastChr" select="$ndLastGroupChrPr/m:chr" />\n\n    <xsl:variable name="chr">\n      <xsl:choose>\n        <xsl:when test="$ndLastChr and (not($ndLastChr/@m:val) or string-length($ndLastChr/@m:val) = 0)"></xsl:when>\n        <xsl:when test="string-length($ndLastChr/@m:val) &gt;= 1">\n          <xsl:value-of select="substring($ndLastChr/@m:val,1,1)" />\n        </xsl:when>\n        <xsl:otherwise>\n          <xsl:text>&#x023DF;</xsl:text>\n        </xsl:otherwise>\n      </xsl:choose>\n    </xsl:variable>\n    <xsl:choose>\n      <xsl:when test="$sLowerCasePos = \'top\'">\n        <xsl:choose>\n          <xsl:when test="$sLowerCaseVertJc = \'bot\'">\n            <mml:mover accent="false">\n              <mml:mrow>\n                <xsl:apply-templates select="m:e[1]" />\n              </mml:mrow>\n              <mml:mo>\n                <xsl:value-of select="$chr" />\n              </mml:mo>\n            </mml:mover>\n          </xsl:when>\n          <xsl:otherwise>\n            <mml:munder accentunder="false">\n              <mml:mo>\n                <xsl:value-of select="$chr" />\n              </mml:mo>\n              <mml:mrow>\n                <xsl:apply-templates select="m:e[1]" />\n              </mml:mrow>\n            </mml:munder>\n          </xsl:otherwise>\n        </xsl:choose>\n      </xsl:when>\n      <xsl:otherwise>\n        <xsl:choose>\n          <xsl:when test="$sLowerCaseVertJc = \'bot\'">\n            <mml:mover accent="false">\n              <mml:mo>\n                <xsl:value-of select="$chr" />\n              </mml:mo>\n              <mml:mrow>\n                <xsl:apply-templates select="m:e[1]" />\n              </mml:mrow>\n            </mml:mover>\n          </xsl:when>\n          <xsl:otherwise>\n            <mml:munder accentunder="false">\n              <mml:mrow>\n                <xsl:apply-templates select="m:e[1]" />\n              </mml:mrow>\n              <mml:mo>\n                <xsl:value-of select="$chr" />\n              </mml:mo>\n            </mml:munder>\n          </xsl:otherwise>\n        </xsl:choose>\n      </xsl:otherwise>\n    </xsl:choose>\n  </xsl:template>\n\n  <xsl:template name="fName">\n    <xsl:for-each select="m:fName/*">\n      <xsl:apply-templates select="." />\n    </xsl:for-each>\n  </xsl:template>\n\n  <xsl:template match="m:func">\n    <mml:mrow>\n      <mml:mrow>\n        <xsl:call-template name="fName" />\n      </mml:mrow>\n      <mml:mo>&#x02061;</mml:mo>\n      <mml:mrow>\n        <xsl:apply-templates select="m:e" />\n      </mml:mrow>\n    </mml:mrow>\n  </xsl:template>\n\n  \x3c!-- %%Template: match m:f\n\n\t\tm:f maps directly to mfrac.\n\t--\x3e\n  <xsl:template match="m:f">\n    <xsl:variable name="sLowerCaseType" select="translate(m:fPr[last()]/m:type/@m:val, \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\', \'abcdefghijklmnopqrstuvwxyz\')" />\n    <xsl:choose>\n      <xsl:when test="$sLowerCaseType=\'lin\'">\n        <mml:mrow>\n          <mml:mrow>\n            <xsl:apply-templates select="m:num[1]" />\n          </mml:mrow>\n          <mml:mo>/</mml:mo>\n          <mml:mrow>\n            <xsl:apply-templates select="m:den[1]" />\n          </mml:mrow>\n        </mml:mrow>\n      </xsl:when>\n      <xsl:otherwise>\n        <mml:mfrac>\n          <xsl:call-template name="CreateMathMLFracProp">\n            <xsl:with-param name="type" select="$sLowerCaseType" />\n          </xsl:call-template>\n          <mml:mrow>\n            <xsl:apply-templates select="m:num[1]" />\n          </mml:mrow>\n          <mml:mrow>\n            <xsl:apply-templates select="m:den[1]" />\n          </mml:mrow>\n        </mml:mfrac>\n      </xsl:otherwise>\n    </xsl:choose>\n\n  </xsl:template>\n\n\n  \x3c!-- %%Template: CreateMathMLFracProp\n\n\t\t\tMake fraction properties based on supplied parameters.\n\t\t\tOMML differentiates between a linear fraction and a skewed\n\t\t\tone. For MathML, we write both as bevelled.\n\t--\x3e\n  <xsl:template name="CreateMathMLFracProp">\n    <xsl:param name="type" />\n    <xsl:variable name="sLowerCaseType" select="translate($type, \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\', \'abcdefghijklmnopqrstuvwxyz\')" />\n\n    <xsl:if test="$sLowerCaseType=\'skw\' or $sLowerCaseType=\'lin\'">\n      <xsl:attribute name="bevelled">true</xsl:attribute>\n    </xsl:if>\n    <xsl:if test="$sLowerCaseType=\'nobar\'">\n      <xsl:attribute name="linethickness">0pt</xsl:attribute>\n    </xsl:if>\n    <xsl:choose>\n      <xsl:when test="sLowerCaseNumJc=\'right\'">\n        <xsl:attribute name="numalign">right</xsl:attribute>\n      </xsl:when>\n      <xsl:when test="sLowerCaseNumJc=\'left\'">\n        <xsl:attribute name="numalign">left</xsl:attribute>\n      </xsl:when>\n    </xsl:choose>\n    <xsl:choose>\n      <xsl:when test="sLowerCaseDenJc=\'right\'">\n        <xsl:attribute name="numalign">right</xsl:attribute>\n      </xsl:when>\n      <xsl:when test="sLowerCaseDenJc=\'left\'">\n        <xsl:attribute name="numalign">left</xsl:attribute>\n      </xsl:when>\n    </xsl:choose>\n  </xsl:template>\n\n  \x3c!-- %%Template: match m:e | m:den | m:num | m:lim | m:sup | m:sub\n\n\t\tThese element delinate parts of an expression (like the numerator).  --\x3e\n  <xsl:template match="m:e | m:den | m:num | m:lim | m:sup | m:sub">\n    <xsl:choose>\n\n      \x3c!-- If there is no scriptLevel specified, just call through --\x3e\n      <xsl:when test="not(m:argPr[last()]/m:scrLvl/@m:val)">\n        <xsl:apply-templates select="*" />\n      </xsl:when>\n\n      \x3c!-- Otherwise, create an mstyle and set the script level --\x3e\n      <xsl:otherwise>\n        <mml:mstyle>\n          <xsl:attribute name="scriptlevel">\n            <xsl:value-of select="m:argPr[last()]/m:scrLvl/@m:val" />\n          </xsl:attribute>\n          <xsl:apply-templates select="*" />\n        </mml:mstyle>\n      </xsl:otherwise>\n    </xsl:choose>\n  </xsl:template>\n\n  <xsl:template match="m:bar">\n    <xsl:variable name="sLowerCasePos" select="translate(m:barPr/m:pos/@m:val, \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\',\n\t\t                                                                       \'abcdefghijklmnopqrstuvwxyz\')" />\n\n    <xsl:variable name="fTop">\n\n      <xsl:choose>\n        <xsl:when test="$sLowerCasePos=\'top\'">1</xsl:when>\n        <xsl:otherwise>0</xsl:otherwise>\n      </xsl:choose>\n    </xsl:variable>\n    <xsl:choose>\n      <xsl:when test="$fTop=1">\n        <mml:mover accent="false">\n          <mml:mrow>\n            <xsl:apply-templates select="m:e[1]" />\n          </mml:mrow>\n          <mml:mo>\n            <xsl:text>&#x00AF;</xsl:text>\n          </mml:mo>\n        </mml:mover>\n      </xsl:when>\n      <xsl:otherwise>\n        <mml:munder underaccent="false">\n          <mml:mrow>\n            <xsl:apply-templates select="m:e[1]" />\n          </mml:mrow>\n          <mml:mo>\n            <xsl:text>&#x005F;</xsl:text>\n          </mml:mo>\n        </mml:munder>\n      </xsl:otherwise>\n    </xsl:choose>\n  </xsl:template>\n\n  \x3c!-- %%Template match m:d\n\n\t\tProcess a delimiter.\n\t--\x3e\n  <xsl:template match="m:d">\n    <mml:mfenced>\n      \x3c!-- open: default is \'(\' for both OMML and MathML --\x3e\n      <xsl:if test="m:dPr[1]/m:begChr/@m:val and not(m:dPr[1]/m:begChr/@m:val =\'(\')">\n        <xsl:attribute name="open">\n          <xsl:value-of select="m:dPr[1]/m:begChr/@m:val" />\n        </xsl:attribute>\n      </xsl:if>\n\n      \x3c!-- close: default is \')\' for both OMML and MathML --\x3e\n      <xsl:if test="m:dPr[1]/m:endChr/@m:val and not(m:dPr[1]/m:endChr/@m:val =\')\')">\n        <xsl:attribute name="close">\n          <xsl:value-of select="m:dPr[1]/m:endChr/@m:val" />\n        </xsl:attribute>\n      </xsl:if>\n\n      \x3c!-- separator: the default is \',\' for MathML, and \'|\' for OMML --\x3e\n      <xsl:choose>\n        \x3c!-- Matches MathML default. Write nothing --\x3e\n        <xsl:when test="m:dPr[1]/m:sepChr/@m:val = \',\'" />\n\n        \x3c!-- OMML default: | --\x3e\n        <xsl:when test="not(m:dPr[1]/m:sepChr/@m:val)">\n          <xsl:attribute name="separators">\n            <xsl:value-of select="\'|\'" />\n          </xsl:attribute>\n        </xsl:when>\n\n        <xsl:otherwise>\n          <xsl:attribute name="separators">\n            <xsl:value-of select="m:dPr[1]/m:sepChr/@m:val" />\n          </xsl:attribute>\n        </xsl:otherwise>\n      </xsl:choose>\n\n      \x3c!-- now write all the children. Put each one into an mrow\n\t\t\tjust in case it produces multiple runs, etc --\x3e\n      <xsl:for-each select="m:e">\n        <mml:mrow>\n          <xsl:apply-templates select="." />\n        </mml:mrow>\n      </xsl:for-each>\n    </mml:mfenced>\n  </xsl:template>\n\n  <xsl:template match="m:r">\n    <xsl:variable name="fNor">\n      <xsl:choose>\n        <xsl:when test="count(child::m:rPr[last()]/m:nor) = 0">0</xsl:when>\n        <xsl:otherwise>\n          <xsl:call-template name="ForceFalseStrVal">\n            <xsl:with-param name="str" select="translate(child::m:rPr[last()]/m:nor/@m:val, \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\',\n\t\t                                                                       \'abcdefghijklmnopqrstuvwxyz\')" />\n          </xsl:call-template>\n        </xsl:otherwise>\n      </xsl:choose>\n    </xsl:variable>\n\n    <xsl:choose>\n      <xsl:when test="$fNor=1">\n        <mml:mtext>\n          <xsl:variable name="sOutput" select="translate(.//m:t, \' \', \'&#xa0;\')" />\n          <xsl:value-of select="$sOutput" />\n        </mml:mtext>\n      </xsl:when>\n      <xsl:otherwise>\n        <xsl:for-each select=".//m:t">\n          <xsl:call-template name="ParseMt">\n            <xsl:with-param name="sToParse" select="text()" />\n            <xsl:with-param name="scr" select="../m:rPr[last()]/m:scr/@m:val" />\n            <xsl:with-param name="sty" select="../m:rPr[last()]/m:sty/@m:val" />\n            <xsl:with-param name="nor">0</xsl:with-param>\n          </xsl:call-template>\n        </xsl:for-each>\n      </xsl:otherwise>\n    </xsl:choose>\n  </xsl:template>\n\n\n  <xsl:template name="CreateTokenAttributes">\n    <xsl:param name="scr" />\n    <xsl:param name="sty" />\n    <xsl:param name="nor" />\n    <xsl:param name="nCharToPrint" />\n    <xsl:param name="sTokenType" />\n\n    <xsl:choose>\n      <xsl:when test="$nor=1">\n        <xsl:attribute name="mathvariant">normal</xsl:attribute>\n      </xsl:when>\n      <xsl:otherwise>\n        <xsl:variable name="mathvariant">\n          <xsl:choose>\n            \x3c!-- numbers don\'t care --\x3e\n            <xsl:when test="$sTokenType=\'mn\'" />\n\n            <xsl:when test="$scr=\'monospace\'">monospace</xsl:when>\n            <xsl:when test="$scr=\'sans-serif\' and $sty=\'i\'">sans-serif-italic</xsl:when>\n            <xsl:when test="$scr=\'sans-serif\' and $sty=\'b\'">bold-sans-serif</xsl:when>\n            <xsl:when test="$scr=\'sans-serif\' and $sty=\'bi\'">sans-serif-bold-italic</xsl:when>\n            <xsl:when test="$scr=\'sans-serif\'">sans-serif</xsl:when>\n            <xsl:when test="$scr=\'fraktur\' and ($sty=\'b\' or $sty=\'bi\')">bold-fraktur</xsl:when>\n            <xsl:when test="$scr=\'fraktur\'">fraktur</xsl:when>\n            <xsl:when test="$scr=\'double-struck\'">double-struck</xsl:when>\n            <xsl:when test="$scr=\'script\' and ($sty=\'b\' or $sty=\'bi\')">bold-script</xsl:when>\n            <xsl:when test="$scr=\'script\'">script</xsl:when>\n            <xsl:when test="($scr=\'roman\' or not($scr) or $scr=\'\') and $sty=\'b\'">bold</xsl:when>\n            <xsl:when test="($scr=\'roman\' or not($scr) or $scr=\'\') and $sty=\'i\'">italic</xsl:when>\n            <xsl:when test="($scr=\'roman\' or not($scr) or $scr=\'\') and $sty=\'p\'">normal</xsl:when>\n            <xsl:when test="($scr=\'roman\' or not($scr) or $scr=\'\') and $sty=\'bi\'">bold-italic</xsl:when>\n            <xsl:otherwise />\n          </xsl:choose>\n        </xsl:variable>\n        <xsl:variable name="fontweight">\n          <xsl:choose>\n            <xsl:when test="$sty=\'b\' or $sty=\'bi\'">bold</xsl:when>\n            <xsl:otherwise>normal</xsl:otherwise>\n          </xsl:choose>\n        </xsl:variable>\n        <xsl:variable name="fontstyle">\n          <xsl:choose>\n            <xsl:when test="$sty=\'p\' or $sty=\'b\'">normal</xsl:when>\n            <xsl:otherwise>italic</xsl:otherwise>\n          </xsl:choose>\n        </xsl:variable>\n\n        \x3c!-- Writing of attributes begins here --\x3e\n        <xsl:choose>\n          \x3c!-- Don\'t write mathvariant for operators unless they want to be normal --\x3e\n          <xsl:when test="$sTokenType=\'mo\' and $mathvariant!=\'normal\'" />\n\n          \x3c!-- A single character within an mi is already italics, don\'t write --\x3e\n          <xsl:when test="$sTokenType=\'mi\' and $nCharToPrint=1 and ($mathvariant=\'\' or $mathvariant=\'italic\')" />\n\n          <xsl:when test="$sTokenType=\'mi\' and $nCharToPrint &gt; 1 and ($mathvariant=\'\' or $mathvariant=\'italic\')">\n            <xsl:attribute name="mathvariant">\n              <xsl:value-of select="\'italic\'" />\n            </xsl:attribute>\n          </xsl:when>\n          <xsl:when test="$mathvariant!=\'italic\' and $mathvariant!=\'\'">\n            <xsl:attribute name="mathvariant">\n              <xsl:value-of select="$mathvariant" />\n            </xsl:attribute>\n          </xsl:when>\n          <xsl:otherwise>\n            <xsl:if test="not($sTokenType=\'mi\' and $nCharToPrint=1) and $fontstyle=\'italic\'">\n              <xsl:attribute name="fontstyle">italic</xsl:attribute>\n            </xsl:if>\n            <xsl:if test="$fontweight=\'bold\'">\n              <xsl:attribute name="fontweight">bold</xsl:attribute>\n            </xsl:if>\n          </xsl:otherwise>\n        </xsl:choose>\n      </xsl:otherwise>\n    </xsl:choose>\n  </xsl:template>\n\n  <xsl:template match="m:eqArr">\n    <mml:mtable>\n      <xsl:for-each select="m:e">\n        <mml:mtr>\n          <mml:mtd>\n            <xsl:choose>\n              <xsl:when test="m:argPr[last()]/m:scrLvl/@m:val!=\'0\' or\n\t\t\t\t\t            not(m:argPr[last()]/m:scrLvl/@m:val)  or\n\t\t\t\t\t            m:argPr[last()]/m:scrLvl/@m:val=\'\'">\n                <mml:mrow>\n                  <mml:maligngroup />\n                  <xsl:call-template name="CreateEqArrRow">\n                    <xsl:with-param name="align" select="1" />\n                    <xsl:with-param name="ndCur" select="*[1]" />\n                  </xsl:call-template>\n                </mml:mrow>\n              </xsl:when>\n              <xsl:otherwise>\n                <mml:mstyle>\n                  <xsl:attribute name="scriptlevel">\n                    <xsl:value-of select="m:argPr[last()]/m:scrLvl/@m:val" />\n                  </xsl:attribute>\n                  <mml:maligngroup />\n                  <xsl:call-template name="CreateEqArrRow">\n                    <xsl:with-param name="align" select="1" />\n                    <xsl:with-param name="ndCur" select="*[1]" />\n                  </xsl:call-template>\n                </mml:mstyle>\n              </xsl:otherwise>\n            </xsl:choose>\n          </mml:mtd>\n        </mml:mtr>\n      </xsl:for-each>\n    </mml:mtable>\n  </xsl:template>\n\n  <xsl:template name="CreateEqArrRow">\n    <xsl:param name="align" />\n    <xsl:param name="ndCur" />\n    <xsl:variable name="sAllMt">\n      <xsl:for-each select="$ndCur/m:t">\n        <xsl:value-of select="." />\n      </xsl:for-each>\n    </xsl:variable>\n    <xsl:choose>\n      <xsl:when test="$ndCur/self::m:r">\n        <xsl:call-template name="ParseEqArrMr">\n          <xsl:with-param name="sToParse" select="$sAllMt" />\n          <xsl:with-param name="scr" select="../m:rPr[last()]/m:scr/@m:val" />\n          <xsl:with-param name="sty" select="../m:rPr[last()]/m:sty/@m:val" />\n          <xsl:with-param name="nor">\n            <xsl:choose>\n              <xsl:when test="count($ndCur/m:rPr[last()]/m:nor) = 0">0</xsl:when>\n              <xsl:otherwise>\n                <xsl:call-template name="ForceFalseStrVal">\n                  <xsl:with-param name="str" select="translate($ndCur/m:rPr[last()]/m:nor/@m:val,\n                                                                     \'ABCDEFGHIJKLMNOPQRSTUVWXYZ\',\n\t\t                                                                 \'abcdefghijklmnopqrstuvwxyz\')" />\n                </xsl:call-template>\n              </xsl:otherwise>\n            </xsl:choose>\n          </xsl:with-param>\n          <xsl:with-param name="align" select="$align" />\n        </xsl:call-template>\n      </xsl:when>\n      <xsl:otherwise>\n        <xsl:apply-templates select="$ndCur" />\n      </xsl:otherwise>\n    </xsl:choose>\n    <xsl:if test="count($ndCur/following-sibling::*) &gt; 0">\n      <xsl:variable name="cAmp">\n        <xsl:call-template name="CountAmp">\n          <xsl:with-param name="sAllMt" select="$sAllMt" />\n          <xsl:with-param name="cAmp" select="0" />\n        </xsl:call-template>\n      </xsl:variable>\n      <xsl:call-template name="CreateEqArrRow">\n        <xsl:with-param name="align" select="($align+($cAmp mod 2)) mod 2" />\n        <xsl:with-param name="ndCur" select="$ndCur/following-sibling::*[1]" />\n      </xsl:call-template>\n    </xsl:if>\n  </xsl:template>\n\n  <xsl:template name="CountAmp">\n    <xsl:param name="sAllMt" />\n    <xsl:param name="cAmp" />\n    <xsl:choose>\n      <xsl:when test="string-length(substring-after($sAllMt, \'&amp;\')) &gt; 0 or\n\t\t\t                substring($sAllMt, string-length($sAllMt))=\'&#x0026;\'">\n        <xsl:call-template name="CountAmp">\n          <xsl:with-param name="sAllMt" select="substring-after($sAllMt, \'&#x0026;\')" />\n          <xsl:with-param name="cAmp" select="$cAmp+1" />\n        </xsl:call-template>\n      </xsl:when>\n      <xsl:otherwise>\n        <xsl:value-of select="$cAmp" />\n      </xsl:otherwise>\n    </xsl:choose>\n  </xsl:template>\n\n  \x3c!-- %%Template: ParseEqArrMr\n\n\t\t\tSimilar to ParseMt, but this one has to do more for an equation array.\n      In equation arrays &amp; is a special character which denotes alignment.\n\n      The &amp; in an equation works by alternating between meaning insert alignment spacing\n      and insert alignment mark.  For each equation in the equation array\n      there is an implied align space at the beginning of the equation.  Within each equation,\n      the first &amp; means alignmark, the second, align space, the third, alignmark, etc.\n\n      For this reason when parsing m:r\'s in equation arrays it is important to keep track of what\n      the next ampersand will mean.\n\n      $align=0 => Omml\'s align space, which is similar to MathML\'s maligngroup.\n      $align=1 => Omml\'s alignment mark, which is similar to MathML\'s malignmark.\n\t--\x3e\n  <xsl:template name="ParseEqArrMr">\n    <xsl:param name="sToParse" />\n    <xsl:param name="sty" />\n    <xsl:param name="scr" />\n    <xsl:param name="nor" />\n    <xsl:param name="align" />\n\n    <xsl:if test="string-length($sToParse) &gt; 0">\n      <xsl:choose>\n        <xsl:when test="substring($sToParse,1,1) = \'&amp;\'">\n          <xsl:choose>\n            <xsl:when test="$align=\'0\'">\n              <mml:maligngroup />\n            </xsl:when>\n            <xsl:when test="$align=\'1\'">\n              <mml:malignmark />\n            </xsl:when>\n          </xsl:choose>\n          <xsl:call-template name="ParseEqArrMr">\n            <xsl:with-param name="sToParse" select="substring($sToParse,2)" />\n            <xsl:with-param name="scr" select="$scr" />\n            <xsl:with-param name="sty" select="$sty" />\n            <xsl:with-param name="nor" select="$nor" />\n            <xsl:with-param name="align">\n              <xsl:choose>\n                <xsl:when test="$align=\'1\'">0</xsl:when>\n                <xsl:otherwise>1</xsl:otherwise>\n              </xsl:choose>\n            </xsl:with-param>\n          </xsl:call-template>\n        </xsl:when>\n        <xsl:otherwise>\n          <xsl:variable name="sRepNumWith0">\n            <xsl:call-template name="SReplaceNumWithZero">\n              <xsl:with-param name="sToParse" select="$sToParse" />\n            </xsl:call-template>\n          </xsl:variable>\n          <xsl:variable name="sRepOperWith-">\n            <xsl:call-template name="SReplaceOperWithMinus">\n              <xsl:with-param name="sToParse" select="$sRepNumWith0" />\n            </xsl:call-template>\n          </xsl:variable>\n\n          <xsl:variable name="iFirstOper" select="string-length($sRepOperWith-) - string-length(substring-after($sRepOperWith-, \'-\'))" />\n          <xsl:variable name="iFirstNum" select="string-length($sRepOperWith-) - string-length(substring-after($sRepOperWith-, \'0\'))" />\n          <xsl:variable name="iFirstAmp" select="string-length($sRepOperWith-) - string-length(substring-after($sRepOperWith-, \'&#x0026;\'))" />\n          <xsl:variable name="fNumAtPos1">\n            <xsl:choose>\n              <xsl:when test="substring($sRepOperWith-,1,1)=\'0\'">1</xsl:when>\n              <xsl:otherwise>0</xsl:otherwise>\n            </xsl:choose>\n          </xsl:variable>\n          <xsl:variable name="fOperAtPos1">\n            <xsl:choose>\n              <xsl:when test="substring($sRepOperWith-,1,1)=\'-\'">1</xsl:when>\n              <xsl:otherwise>0</xsl:otherwise>\n            </xsl:choose>\n          </xsl:variable>\n          <xsl:choose>\n\n            \x3c!-- Case I: The string begins with neither a number, nor an operator --\x3e\n            <xsl:when test="$fNumAtPos1=\'0\' and $fOperAtPos1=\'0\'">\n              <xsl:choose>\n                <xsl:when test="$nor = 0">\n                  <mml:mi>\n                    <xsl:call-template name="CreateTokenAttributes">\n                      <xsl:with-param name="scr" select="$scr" />\n                      <xsl:with-param name="sty" select="$sty" />\n                      <xsl:with-param name="nor" select="$nor" />\n                      <xsl:with-param name="nCharToPrint" select="1" />\n                      <xsl:with-param name="sTokenType" select="\'mi\'" />\n                    </xsl:call-template>\n                    <xsl:variable name="sOutput" select="translate(substring($sToParse, 1, 1), \' \', \'&#xa0;\')" />\n                    <xsl:value-of select="$sOutput" />\n                  </mml:mi>\n                </xsl:when>\n                <xsl:otherwise>\n                  <mml:mtext>\n                    <xsl:variable name="sOutput" select="translate(substring($sToParse, 1, 1), \' \', \'&#xa0;\')" />\n                    <xsl:value-of select="$sOutput" />\n                  </mml:mtext>\n                </xsl:otherwise>\n              </xsl:choose>\n              <xsl:call-template name="ParseEqArrMr">\n                <xsl:with-param name="sToParse" select="substring($sToParse, 2)" />\n                <xsl:with-param name="scr" select="$scr" />\n                <xsl:with-param name="sty" select="$sty" />\n                <xsl:with-param name="nor" select="$nor" />\n                <xsl:with-param name="align" select="$align" />\n              </xsl:call-template>\n            </xsl:when>\n\n            \x3c!-- Case II: There is an operator at position 1 --\x3e\n            <xsl:when test="$fOperAtPos1=\'1\'">\n              <xsl:choose>\n                <xsl:when test="$nor = 0">\n                  <mml:mo>\n                    <xsl:call-template name="CreateTokenAttributes">\n                      <xsl:with-param name="scr" />\n                      <xsl:with-param name="sty" />\n                      <xsl:with-param name="nor" select="$nor" />\n                      <xsl:with-param name="sTokenType" select="\'mo\'" />\n                    </xsl:call-template>\n                    <xsl:value-of select="substring($sToParse,1,1)" />\n                  </mml:mo>\n                </xsl:when>\n                <xsl:otherwise>\n                  <mml:mtext>\n                    <xsl:value-of select="substring($sToParse,1,1)" />\n                  </mml:mtext>\n                </xsl:otherwise>\n              </xsl:choose>\n              <xsl:call-template name="ParseEqArrMr">\n                <xsl:with-param name="sToParse" select="substring($sToParse, 2)" />\n                <xsl:with-param name="scr" select="$scr" />\n                <xsl:with-param name="sty" select="$sty" />\n                <xsl:with-param name="nor" select="$nor" />\n                <xsl:with-param name="align" select="$align" />\n              </xsl:call-template>\n            </xsl:when>\n\n            \x3c!-- Case III: There is a number at position 1 --\x3e\n            <xsl:otherwise>\n              <xsl:variable name="sConsecNum">\n                <xsl:call-template name="SNumStart">\n                  <xsl:with-param name="sToParse" select="$sToParse" />\n                  <xsl:with-param name="sPattern" select="$sRepNumWith0" />\n                </xsl:call-template>\n              </xsl:variable>\n              <xsl:choose>\n                <xsl:when test="$nor = 0">\n                  <mml:mn>\n                    <xsl:call-template name="CreateTokenAttributes">\n                      <xsl:with-param name="scr" />\n                      <xsl:with-param name="sty" select="\'p\'"/>\n                      <xsl:with-param name="nor" select="$nor" />\n                      <xsl:with-param name="sTokenType" select="\'mn\'" />\n                    </xsl:call-template>\n                    <xsl:value-of select="$sConsecNum" />\n                  </mml:mn>\n                </xsl:when>\n                <xsl:otherwise>\n                  <mml:mtext>\n                    <xsl:value-of select="$sConsecNum" />\n                  </mml:mtext>\n                </xsl:otherwise>\n              </xsl:choose>\n              <xsl:call-template name="ParseEqArrMr">\n                <xsl:with-param name="sToParse" select="substring-after($sToParse, $sConsecNum)" />\n                <xsl:with-param name="scr" select="$scr" />\n                <xsl:with-param name="sty" select="$sty" />\n                <xsl:with-param name="nor" select="$nor" />\n                <xsl:with-param name="align" select="$align" />\n              </xsl:call-template>\n            </xsl:otherwise>\n          </xsl:choose>\n        </xsl:otherwise>\n      </xsl:choose>\n    </xsl:if>\n  </xsl:template>\n\n  \x3c!-- %%Template: ParseMt\n\n\t\t\tProduce a run of text. Technically, OMML makes no distinction\n\t\t\tbetween numbers, operators, and other characters in a run. For\n\t\t\tMathML we need to break these into mi, mn, or mo elements.\n\n\t\t\tSee also ParseEqArrMr\n\t--\x3e\n  <xsl:template name="ParseMt">\n    <xsl:param name="sToParse" />\n    <xsl:param name="sty" />\n    <xsl:param name="scr" />\n    <xsl:param name="nor" />\n    <xsl:if test="string-length($sToParse) &gt; 0">\n      <xsl:variable name="sRepNumWith0">\n        <xsl:call-template name="SReplaceNumWithZero">\n          <xsl:with-param name="sToParse" select="$sToParse" />\n        </xsl:call-template>\n      </xsl:variable>\n      <xsl:variable name="sRepOperWith-">\n        <xsl:call-template name="SReplaceOperWithMinus">\n          <xsl:with-param name="sToParse" select="$sRepNumWith0" />\n        </xsl:call-template>\n      </xsl:variable>\n\n      <xsl:variable name="iFirstOper" select="string-length($sRepOperWith-) - string-length(substring-after($sRepOperWith-, \'-\'))" />\n      <xsl:variable name="iFirstNum" select="string-length($sRepOperWith-) - string-length(substring-after($sRepOperWith-, \'0\'))" />\n      <xsl:variable name="fNumAtPos1">\n        <xsl:choose>\n          <xsl:when test="substring($sRepOperWith-,1,1)=\'0\'">1</xsl:when>\n          <xsl:otherwise>0</xsl:otherwise>\n        </xsl:choose>\n      </xsl:variable>\n      <xsl:variable name="fOperAtPos1">\n        <xsl:choose>\n          <xsl:when test="substring($sRepOperWith-,1,1)=\'-\'">1</xsl:when>\n          <xsl:otherwise>0</xsl:otherwise>\n        </xsl:choose>\n      </xsl:variable>\n\n      <xsl:choose>\n\n        \x3c!-- Case I: The string begins with neither a number, nor an operator --\x3e\n        <xsl:when test="$fOperAtPos1=\'0\' and $fNumAtPos1=\'0\'">\n          <xsl:variable name="nCharToPrint">\n            <xsl:choose>\n              <xsl:when test="ancestor::m:fName">\n                <xsl:choose>\n                  <xsl:when test="($iFirstOper=$iFirstNum) and\n\t\t\t\t\t\t\t\t\t\t\t($iFirstOper=string-length($sToParse)) and\n\t\t\t\t\t\t\t                (substring($sRepOperWith-, string-length($sRepOperWith-))!=\'0\') and\n\t\t\t\t\t\t\t                (substring($sRepOperWith-, string-length($sRepOperWith-))!=\'-\')">\n                    <xsl:value-of select="string-length($sToParse)" />\n                  </xsl:when>\n                  <xsl:when test="$iFirstOper &lt; $iFirstNum">\n                    <xsl:value-of select="$iFirstOper - 1" />\n                  </xsl:when>\n                  <xsl:otherwise>\n                    <xsl:value-of select="$iFirstNum - 1" />\n                  </xsl:otherwise>\n                </xsl:choose>\n              </xsl:when>\n              <xsl:otherwise>1</xsl:otherwise>\n            </xsl:choose>\n          </xsl:variable>\n\n          <mml:mi>\n            <xsl:call-template name="CreateTokenAttributes">\n              <xsl:with-param name="scr" select="$scr" />\n              <xsl:with-param name="sty" select="$sty" />\n              <xsl:with-param name="nor" select="$nor" />\n              <xsl:with-param name="nCharToPrint" select="$nCharToPrint" />\n              <xsl:with-param name="sTokenType" select="\'mi\'" />\n            </xsl:call-template>\n            <xsl:variable name="sWrite" select="translate(substring($sToParse, 1, $nCharToPrint), \' \', \'&#xa0;\')" />\n            <xsl:value-of select="$sWrite" />\n          </mml:mi>\n          <xsl:call-template name="ParseMt">\n            <xsl:with-param name="sToParse" select="substring($sToParse, $nCharToPrint+1)" />\n            <xsl:with-param name="scr" select="$scr" />\n            <xsl:with-param name="sty" select="$sty" />\n            <xsl:with-param name="nor" select="$nor" />\n          </xsl:call-template>\n        </xsl:when>\n\n        \x3c!-- Case II: There is an operator at position 1 --\x3e\n        <xsl:when test="$fOperAtPos1=\'1\'">\n          <mml:mo>\n            <xsl:call-template name="CreateTokenAttributes">\n              <xsl:with-param name="scr" />\n              <xsl:with-param name="sty" />\n              <xsl:with-param name="nor" select="$nor" />\n              <xsl:with-param name="sTokenType" select="\'mo\'" />\n            </xsl:call-template>\n            <xsl:value-of select="substring($sToParse,1,1)" />\n          </mml:mo>\n          <xsl:call-template name="ParseMt">\n            <xsl:with-param name="sToParse" select="substring($sToParse, 2)" />\n            <xsl:with-param name="scr" select="$scr" />\n            <xsl:with-param name="sty" select="$sty" />\n            <xsl:with-param name="nor" select="$nor" />\n          </xsl:call-template>\n        </xsl:when>\n\n        \x3c!-- Case III: There is a number at position 1 --\x3e\n        <xsl:otherwise>\n          <xsl:variable name="sConsecNum">\n            <xsl:call-template name="SNumStart">\n              <xsl:with-param name="sToParse" select="$sToParse" />\n              <xsl:with-param name="sPattern" select="$sRepNumWith0" />\n            </xsl:call-template>\n          </xsl:variable>\n          <mml:mn>\n            <xsl:call-template name="CreateTokenAttributes">\n              <xsl:with-param name="scr" select="$scr" />\n              <xsl:with-param name="sty" select="\'p\'" />\n              <xsl:with-param name="nor" select="$nor" />\n              <xsl:with-param name="sTokenType" select="\'mn\'" />\n            </xsl:call-template>\n            <xsl:value-of select="$sConsecNum" />\n          </mml:mn>\n          <xsl:call-template name="ParseMt">\n            <xsl:with-param name="sToParse" select="substring-after($sToParse, $sConsecNum)" />\n            <xsl:with-param name="scr" select="$scr" />\n            <xsl:with-param name="sty" select="$sty" />\n            <xsl:with-param name="nor" select="$nor" />\n          </xsl:call-template>\n        </xsl:otherwise>\n      </xsl:choose>\n    </xsl:if>\n  </xsl:template>\n\n  \x3c!-- %%Template: SNumStart\n\n\t\tReturn the longest substring of sToParse starting from the\n\t\tstart of sToParse that is a number. In addition, it takes the\n\t\tpattern string, which is sToParse with all of its numbers\n\t\treplaced with a 0. sPattern should be the same length\n\t\tas sToParse\n\t--\x3e\n  <xsl:template name="SNumStart">\n    <xsl:param name="sToParse" select="\'\'" />\n    \x3c!-- if we don\'t get anything, take the string itself --\x3e\n    <xsl:param name="sPattern" select="\'$sToParse\'" />\n\n\n    <xsl:choose>\n      \x3c!-- the pattern says this is a number, recurse with the rest --\x3e\n      <xsl:when test="substring($sPattern, 1, 1) = \'0\'">\n        <xsl:call-template name="SNumStart">\n          <xsl:with-param name="sToParse" select="$sToParse" />\n          <xsl:with-param name="sPattern" select="substring($sPattern, 2)" />\n        </xsl:call-template>\n      </xsl:when>\n\n      \x3c!-- the pattern says we\'ve run out of numbers. Take as many\n\t\t\t\tcharacters from sToParse as we shaved off sPattern --\x3e\n      <xsl:otherwise>\n        <xsl:value-of select="substring($sToParse, 1, string-length($sToParse) - string-length($sPattern))" />\n      </xsl:otherwise>\n    </xsl:choose>\n  </xsl:template>\n\n  \x3c!-- %%Template SRepeatCharAcc\n\n\t\t\tThe core of SRepeatChar with an accumulator. The current\n\t\t\tstring is in param $acc, and we will double and recurse,\n\t\t\tif we\'re less than half of the required length or else just\n\t\t\tadd the right amount of characters to the accumulator and\n\t\t\treturn\n\t--\x3e\n  <xsl:template name="SRepeatCharAcc">\n    <xsl:param name="cchRequired" select="1" />\n    <xsl:param name="ch" select="\'-\'" />\n    <xsl:param name="acc" select="$ch" />\n\n    <xsl:variable name="cchAcc" select="string-length($acc)" />\n    <xsl:choose>\n      <xsl:when test="(2 * $cchAcc) &lt; $cchRequired">\n        <xsl:call-template name="SRepeatCharAcc">\n          <xsl:with-param name="cchRequired" select="$cchRequired" />\n          <xsl:with-param name="ch" select="$ch" />\n          <xsl:with-param name="acc" select="concat($acc, $acc)" />\n        </xsl:call-template>\n      </xsl:when>\n\n      <xsl:otherwise>\n        <xsl:value-of select="concat($acc, substring($acc, 1, $cchRequired - $cchAcc))" />\n      </xsl:otherwise>\n    </xsl:choose>\n  </xsl:template>\n\n\n  \x3c!-- %%Template SRepeatChar\n\n\t\t\tGenerates a string nchRequired long by repeating the given character ch\n\t--\x3e\n  <xsl:template name="SRepeatChar">\n    <xsl:param name="cchRequired" select="1" />\n    <xsl:param name="ch" select="\'-\'" />\n\n    <xsl:call-template name="SRepeatCharAcc">\n      <xsl:with-param name="cchRequired" select="$cchRequired" />\n      <xsl:with-param name="ch" select="$ch" />\n      <xsl:with-param name="acc" select="$ch" />\n    </xsl:call-template>\n  </xsl:template>\n\n  \x3c!-- %%Template SReplaceOperWithMinus\n\n\t\tGo through the given string and replace every instance\n\t\tof an operator with a minus \'-\'. This helps quickly identify\n\t\tthe first instance of an operator.\n\t--\x3e\n  <xsl:template name="SReplaceOperWithMinus">\n    <xsl:param name="sToParse" select="\'\'" />\n\n    <xsl:value-of select="translate($sToParse, $sOperators, $sMinuses)" />\n  </xsl:template>\n\n  \x3c!-- %%Template SReplaceNumWithZero\n\n\t\tGo through the given string and replace every instance\n\t\tof an number with a zero \'0\'. This helps quickly identify\n\t\tthe first occurence of a number.\n\n\t\tConsiders the \'.\' and \',\' part of a number iff they are sandwiched\n\t\tbetween two other numbers. 0.3 will be recognized as a number,\n\t\tx.3 will not be. Since these characters can also be an operator, this\n\t\tshould be called before SReplaceOperWithMinus.\n\t--\x3e\n  <xsl:template name="SReplaceNumWithZero">\n    <xsl:param name="sToParse" select="\'\'" />\n\n    \x3c!-- First do a simple replace. Numbers will all be come 0\'s.\n\t\t\tAfter this point, the pattern involving the . or , that\n\t\t\twe are looking for will become 0.0 or 0,0 --\x3e\n    <xsl:variable name="sSimpleReplace" select="translate($sToParse, $sNumbers, $sZeros)" />\n\n    \x3c!-- And then, replace 0.0 with just 000. This means that the . will\n\t\t\tbecome part of the number --\x3e\n    <xsl:variable name="sReplacePeriod">\n      <xsl:call-template name="SReplace">\n        <xsl:with-param name="sInput" select="$sSimpleReplace" />\n        <xsl:with-param name="sOrig" select="\'0.0\'" />\n        <xsl:with-param name="sReplacement" select="\'000\'" />\n      </xsl:call-template>\n    </xsl:variable>\n\n    \x3c!-- And then, replace 0,0 with just 000. This means that the , will\n\t\t\tbecome part of the number --\x3e\n    <xsl:call-template name="SReplace">\n      <xsl:with-param name="sInput" select="$sReplacePeriod" />\n      <xsl:with-param name="sOrig" select="\'0,0\'" />\n      <xsl:with-param name="sReplacement" select="\'000\'" />\n    </xsl:call-template>\n  </xsl:template>\n\n  \x3c!-- Template to translate Word\'s borderBox properties into the menclose notation attribute\n       The initial call to this SHOULD NOT pass an sAttribute.  Subsequent calls to\n       CreateMencloseNotationAttrFromBorderBoxAttr by CreateMencloseNotationAttrFromBorderBoxAttr will\n       update the sAttribute as appropriate.\n\n       CreateMencloseNotationAttrFromBorderBoxAttr looks at each attribute (fHideTop, fHideBot, etc.) one at a time\n       in the order they are listed and passes a modified sAttribute to CreateMencloseNotationAttrFromBorderBoxAttr.\n       Each successive call to CreateMencloseNotationAttrFromBorderBoxAttr knows which attribute to look at because\n       the previous call should have omitted passing the attribute it just analyzed.  This is why as you read lower\n       and lower in the template that each call to CreateMencloseNotationAttrFromBorderBoxAttr has fewer and fewer attributes.\n       --\x3e\n  <xsl:template name="CreateMencloseNotationAttrFromBorderBoxAttr">\n    <xsl:param name="fHideTop" />\n    <xsl:param name="fHideBot" />\n    <xsl:param name="fHideLeft" />\n    <xsl:param name="fHideRight" />\n    <xsl:param name="fStrikeH" />\n    <xsl:param name="fStrikeV" />\n    <xsl:param name="fStrikeBLTR" />\n    <xsl:param name="fStrikeTLBR" />\n    <xsl:param name="sAttribute" />\n\n    <xsl:choose>\n      <xsl:when test="string-length($sAttribute) = 0">\n        <xsl:choose>\n          <xsl:when test="string-length($fHideTop) &gt; 0\n                      and string-length($fHideBot) &gt; 0\n                      and string-length($fHideLeft) &gt; 0\n                      and string-length($fHideRight) &gt; 0">\n\n            <xsl:choose>\n              <xsl:when test="$fHideTop = 0\n                              and $fHideBot = 0\n                              and $fHideLeft = 0\n                              and $fHideRight = 0">\n                \x3c!-- We can use \'box\' instead of top, bot, left, and right.  Therefore,\n                  replace sAttribute with \'box\' and begin analyzing params fStrikeH\n                  and below. --\x3e\n                <xsl:call-template name="CreateMencloseNotationAttrFromBorderBoxAttr">\n                  <xsl:with-param name="fStrikeH" select="$fStrikeH" />\n                  <xsl:with-param name="fStrikeV" select="$fStrikeV" />\n                  <xsl:with-param name="fStrikeBLTR" select="$fStrikeBLTR" />\n                  <xsl:with-param name="fStrikeTLBR" select="$fStrikeTLBR" />\n                  <xsl:with-param name="sAttribute">\n                    <xsl:text>box</xsl:text>\n                  </xsl:with-param>\n                </xsl:call-template>\n              </xsl:when>\n              <xsl:otherwise>\n                \x3c!-- Can\'t use \'box\', theremore, must analyze all attributes --\x3e\n                <xsl:call-template name="CreateMencloseNotationAttrFromBorderBoxAttr">\n                  <xsl:with-param name="fHideTop" select="$fHideTop" />\n                  <xsl:with-param name="fHideBot" select="$fHideBot" />\n                  <xsl:with-param name="fHideLeft" select="$fHideLeft" />\n                  <xsl:with-param name="fHideRight" select="$fHideRight" />\n                  <xsl:with-param name="fStrikeH" select="$fStrikeH" />\n                  <xsl:with-param name="fStrikeV" select="$fStrikeV" />\n                  <xsl:with-param name="fStrikeBLTR" select="$fStrikeBLTR" />\n                  <xsl:with-param name="fStrikeTLBR" select="$fStrikeTLBR" />\n                  <xsl:with-param name="sAttribute">\n                    \x3c!-- Assume using all four (left right top bottom).  Subsequent calls\n                         will remove the sides which aren\'t to be includes. --\x3e\n                    <xsl:text>left right top bottom</xsl:text>\n                  </xsl:with-param>\n                </xsl:call-template>\n              </xsl:otherwise>\n            </xsl:choose>\n          </xsl:when>\n        </xsl:choose>\n      </xsl:when>\n      <xsl:otherwise>\n        <xsl:choose>\n          <xsl:when test="string-length($fHideTop) &gt; 0">\n            <xsl:choose>\n              <xsl:when test="$fHideTop=1">\n                <xsl:call-template name="CreateMencloseNotationAttrFromBorderBoxAttr">\n                  <xsl:with-param name="fHideBot" select="$fHideBot" />\n                  <xsl:with-param name="fHideLeft" select="$fHideLeft" />\n                  <xsl:with-param name="fHideRight" select="$fHideRight" />\n                  <xsl:with-param name="fStrikeH" select="$fStrikeH" />\n                  <xsl:with-param name="fStrikeV" select="$fStrikeV" />\n                  <xsl:with-param name="fStrikeBLTR" select="$fStrikeBLTR" />\n                  <xsl:with-param name="fStrikeTLBR" select="$fStrikeTLBR" />\n                  <xsl:with-param name="sAttribute">\n                    <xsl:call-template name="SReplace">\n                      <xsl:with-param name="sInput" select="$sAttribute" />\n                      <xsl:with-param name="sOrig" select="\'top\'" />\n                      <xsl:with-param name="sReplacement" select="\'\'" />\n                    </xsl:call-template>\n                  </xsl:with-param>\n                </xsl:call-template>\n              </xsl:when>\n              <xsl:otherwise>\n                <xsl:call-template name="CreateMencloseNotationAttrFromBorderBoxAttr">\n                  <xsl:with-param name="fHideBot" select="$fHideBot" />\n                  <xsl:with-param name="fHideLeft" select="$fHideLeft" />\n                  <xsl:with-param name="fHideRight" select="$fHideRight" />\n                  <xsl:with-param name="fStrikeH" select="$fStrikeH" />\n                  <xsl:with-param name="fStrikeV" select="$fStrikeV" />\n                  <xsl:with-param name="fStrikeBLTR" select="$fStrikeBLTR" />\n                  <xsl:with-param name="fStrikeTLBR" select="$fStrikeTLBR" />\n                  <xsl:with-param name="sAttribute" select="$sAttribute" />\n                </xsl:call-template>\n              </xsl:otherwise>\n            </xsl:choose>\n          </xsl:when>\n          <xsl:when test="string-length($fHideBot) &gt; 0">\n            <xsl:choose>\n              <xsl:when test="$fHideBot=1">\n                <xsl:call-template name="CreateMencloseNotationAttrFromBorderBoxAttr">\n                  <xsl:with-param name="fHideLeft" select="$fHideLeft" />\n                  <xsl:with-param name="fHideRight" select="$fHideRight" />\n                  <xsl:with-param name="fStrikeH" select="$fStrikeH" />\n                  <xsl:with-param name="fStrikeV" select="$fStrikeV" />\n                  <xsl:with-param name="fStrikeBLTR" select="$fStrikeBLTR" />\n                  <xsl:with-param name="fStrikeTLBR" select="$fStrikeTLBR" />\n                  <xsl:with-param name="sAttribute">\n                    <xsl:call-template name="SReplace">\n                      <xsl:with-param name="sInput" select="$sAttribute" />\n                      <xsl:with-param name="sOrig" select="\'bottom\'" />\n                      <xsl:with-param name="sReplacement" select="\'\'" />\n                    </xsl:call-template>\n                  </xsl:with-param>\n                </xsl:call-template>\n              </xsl:when>\n              <xsl:otherwise>\n                <xsl:call-template name="CreateMencloseNotationAttrFromBorderBoxAttr">\n                  <xsl:with-param name="fHideLeft" select="$fHideLeft" />\n                  <xsl:with-param name="fHideRight" select="$fHideRight" />\n                  <xsl:with-param name="fStrikeH" select="$fStrikeH" />\n                  <xsl:with-param name="fStrikeV" select="$fStrikeV" />\n                  <xsl:with-param name="fStrikeBLTR" select="$fStrikeBLTR" />\n                  <xsl:with-param name="fStrikeTLBR" select="$fStrikeTLBR" />\n                  <xsl:with-param name="sAttribute" select="$sAttribute" />\n                </xsl:call-template>\n              </xsl:otherwise>\n            </xsl:choose>\n          </xsl:when>\n          <xsl:when test="string-length($fHideLeft) &gt; 0">\n            <xsl:choose>\n              <xsl:when test="$fHideLeft=1">\n                <xsl:call-template name="CreateMencloseNotationAttrFromBorderBoxAttr">\n                  <xsl:with-param name="fHideRight" select="$fHideRight" />\n                  <xsl:with-param name="fStrikeH" select="$fStrikeH" />\n                  <xsl:with-param name="fStrikeV" select="$fStrikeV" />\n                  <xsl:with-param name="fStrikeBLTR" select="$fStrikeBLTR" />\n                  <xsl:with-param name="fStrikeTLBR" select="$fStrikeTLBR" />\n                  <xsl:with-param name="sAttribute">\n                    <xsl:call-template name="SReplace">\n                      <xsl:with-param name="sInput" select="$sAttribute" />\n                      <xsl:with-param name="sOrig" select="\'left\'" />\n                      <xsl:with-param name="sReplacement" select="\'\'" />\n                    </xsl:call-template>\n                  </xsl:with-param>\n                </xsl:call-template>\n              </xsl:when>\n              <xsl:otherwise>\n                <xsl:call-template name="CreateMencloseNotationAttrFromBorderBoxAttr">\n                  <xsl:with-param name="fHideRight" select="$fHideRight" />\n                  <xsl:with-param name="fStrikeH" select="$fStrikeH" />\n                  <xsl:with-param name="fStrikeV" select="$fStrikeV" />\n                  <xsl:with-param name="fStrikeBLTR" select="$fStrikeBLTR" />\n                  <xsl:with-param name="fStrikeTLBR" select="$fStrikeTLBR" />\n                  <xsl:with-param name="sAttribute" select="$sAttribute" />\n                </xsl:call-template>\n              </xsl:otherwise>\n            </xsl:choose>\n          </xsl:when>\n          <xsl:when test="string-length($fHideRight) &gt; 0">\n            <xsl:choose>\n              <xsl:when test="$fHideRight=1">\n                <xsl:call-template name="CreateMencloseNotationAttrFromBorderBoxAttr">\n                  <xsl:with-param name="fStrikeH" select="$fStrikeH" />\n                  <xsl:with-param name="fStrikeV" select="$fStrikeV" />\n                  <xsl:with-param name="fStrikeBLTR" select="$fStrikeBLTR" />\n                  <xsl:with-param name="fStrikeTLBR" select="$fStrikeTLBR" />\n                  <xsl:with-param name="sAttribute">\n                    <xsl:call-template name="SReplace">\n                      <xsl:with-param name="sInput" select="$sAttribute" />\n                      <xsl:with-param name="sOrig" select="\'right\'" />\n                      <xsl:with-param name="sReplacement" select="\'\'" />\n                    </xsl:call-template>\n                  </xsl:with-param>\n                </xsl:call-template>\n              </xsl:when>\n              <xsl:otherwise>\n                <xsl:call-template name="CreateMencloseNotationAttrFromBorderBoxAttr">\n                  <xsl:with-param name="fStrikeH" select="$fStrikeH" />\n                  <xsl:with-param name="fStrikeV" select="$fStrikeV" />\n                  <xsl:with-param name="fStrikeBLTR" select="$fStrikeBLTR" />\n                  <xsl:with-param name="fStrikeTLBR" select="$fStrikeTLBR" />\n                  <xsl:with-param name="sAttribute" select="$sAttribute" />\n                </xsl:call-template>\n              </xsl:otherwise>\n            </xsl:choose>\n          </xsl:when>\n          <xsl:when test="string-length($fStrikeH) &gt; 0">\n            <xsl:choose>\n              <xsl:when test="$fStrikeH=1">\n                <xsl:call-template name="CreateMencloseNotationAttrFromBorderBoxAttr">\n                  <xsl:with-param name="fStrikeV" select="$fStrikeV" />\n                  <xsl:with-param name="fStrikeBLTR" select="$fStrikeBLTR" />\n                  <xsl:with-param name="fStrikeTLBR" select="$fStrikeTLBR" />\n                  <xsl:with-param name="sAttribute" select="concat($sAttribute, \' horizontalstrike\')" />\n                </xsl:call-template>\n              </xsl:when>\n              <xsl:otherwise>\n                <xsl:call-template name="CreateMencloseNotationAttrFromBorderBoxAttr">\n                  <xsl:with-param name="fStrikeV" select="$fStrikeV" />\n                  <xsl:with-param name="fStrikeBLTR" select="$fStrikeBLTR" />\n                  <xsl:with-param name="fStrikeTLBR" select="$fStrikeTLBR" />\n                  <xsl:with-param name="sAttribute" select="$sAttribute" />\n                </xsl:call-template>\n              </xsl:otherwise>\n            </xsl:choose>\n          </xsl:when>\n          <xsl:when test="string-length($fStrikeV) &gt; 0">\n            <xsl:choose>\n              <xsl:when test="$fStrikeV=1">\n                <xsl:call-template name="CreateMencloseNotationAttrFromBorderBoxAttr">\n                  <xsl:with-param name="fStrikeBLTR" select="$fStrikeBLTR" />\n                  <xsl:with-param name="fStrikeTLBR" select="$fStrikeTLBR" />\n                  <xsl:with-param name="sAttribute" select="concat($sAttribute, \' verticalstrike\')" />\n                </xsl:call-template>\n              </xsl:when>\n              <xsl:otherwise>\n                <xsl:call-template name="CreateMencloseNotationAttrFromBorderBoxAttr">\n                  <xsl:with-param name="fStrikeBLTR" select="$fStrikeBLTR" />\n                  <xsl:with-param name="fStrikeTLBR" select="$fStrikeTLBR" />\n                  <xsl:with-param name="sAttribute" select="$sAttribute" />\n                </xsl:call-template>\n              </xsl:otherwise>\n            </xsl:choose>\n          </xsl:when>\n          <xsl:when test="string-length($fStrikeBLTR) &gt; 0">\n            <xsl:choose>\n              <xsl:when test="$fStrikeBLTR=1">\n                <xsl:call-template name="CreateMencloseNotationAttrFromBorderBoxAttr">\n                  <xsl:with-param name="fStrikeTLBR" select="$fStrikeTLBR" />\n                  <xsl:with-param name="sAttribute" select="concat($sAttribute, \' updiagonalstrike\')" />\n                </xsl:call-template>\n              </xsl:when>\n              <xsl:otherwise>\n                <xsl:call-template name="CreateMencloseNotationAttrFromBorderBoxAttr">\n                  <xsl:with-param name="fStrikeTLBR" select="$fStrikeTLBR" />\n                  <xsl:with-param name="sAttribute" select="$sAttribute" />\n                </xsl:call-template>\n              </xsl:otherwise>\n            </xsl:choose>\n          </xsl:when>\n          <xsl:when test="string-length($fStrikeTLBR) &gt; 0">\n            <xsl:choose>\n              <xsl:when test="$fStrikeTLBR=1">\n                <xsl:call-template name="CreateMencloseNotationAttrFromBorderBoxAttr">\n                  <xsl:with-param name="sAttribute" select="concat($sAttribute, \' downdiagonalstrike\')" />\n                </xsl:call-template>\n              </xsl:when>\n              <xsl:otherwise>\n                <xsl:call-template name="CreateMencloseNotationAttrFromBorderBoxAttr">\n                  <xsl:with-param name="sAttribute" select="$sAttribute" />\n                </xsl:call-template>\n              </xsl:otherwise>\n            </xsl:choose>\n          </xsl:when>\n          <xsl:otherwise>\n            <xsl:attribute name="notation">\n              <xsl:value-of select="normalize-space($sAttribute)" />\n            </xsl:attribute>\n          </xsl:otherwise>\n        </xsl:choose>\n      </xsl:otherwise>\n    </xsl:choose>\n  </xsl:template>\n\n  \x3c!-- Tristate (true, false, neither) from string value --\x3e\n  <xsl:template name="TFromStrVal">\n    <xsl:param name="str" />\n    <xsl:choose>\n      <xsl:when test="$str = \'on\' or $str = \'1\' or $str = \'true\'">1</xsl:when>\n      <xsl:when test="$str = \'off\' or $str = \'0\' or $str = \'false\'">0</xsl:when>\n      <xsl:otherwise>-1</xsl:otherwise>\n    </xsl:choose>\n  </xsl:template>\n\n  \x3c!-- Return 0 iff $str is explicitly set to a false value.\n       Return true otherwise --\x3e\n  <xsl:template name="ForceFalseStrVal">\n    <xsl:param name="str" />\n    <xsl:variable name="tValue">\n      <xsl:call-template name="TFromStrVal">\n        <xsl:with-param name="str" select="$str"/>\n      </xsl:call-template>\n    </xsl:variable>\n    <xsl:choose>\n      <xsl:when test="$tValue = \'0\'">0</xsl:when>\n      <xsl:otherwise>1</xsl:otherwise>\n    </xsl:choose>\n  </xsl:template>\n\n  \x3c!-- Return 1 iff $str is explicitly set to a true value.\n       Return false otherwise --\x3e\n  <xsl:template name="ForceTrueStrVal">\n    <xsl:param name="str" />\n    <xsl:variable name="tValue">\n      <xsl:call-template name="TFromStrVal">\n        <xsl:with-param name="str" select="$str"/>\n      </xsl:call-template>\n    </xsl:variable>\n    <xsl:choose>\n      <xsl:when test="$tValue = \'1\'">1</xsl:when>\n      <xsl:otherwise>0</xsl:otherwise>\n    </xsl:choose>\n  </xsl:template>\n</xsl:stylesheet>\n'.trim(),(new DOMParser).parseFromString(le,"application/xml"));function xe(t,n,e,a){var r,l;void 0===e&&(e=!0),void 0===a&&(a=!1),t.currentParagraph=n;var o=fn("p");t.addClass(o,"p");var x,y,p=n.properties;An(t,o,p),o.style.position="relative",p.numPr&&dn(o,function(t,n,e){var a=n.numbering,r=e.numId;if(!r)return console.warn("renderNumbering: numId is empty"),null;if(!a)return console.warn("renderNumbering: numbering is empty"),null;var l=a.nums[r];if(!l)return console.warn("renderNumbering: num is empty"),null;var o=a.abstractNums[l.abstractNumId].lvls;l.lvlOverride&&(o=(0,s.Cl)((0,s.Cl)({},o),l.lvlOverride.lvls));var x=o[e.ilvl];if(!x)return console.warn("renderNumbering: lvl is empty"),null;var y=e.ilvl,p=a.numData[r];if(p[y])for(var f in p[y]+=1,p)parseInt(f)>parseInt(y)&&(p[f]=0);else p[y]=x.start;for(var c=fn("span"),i=x.lvlText,d=parseInt(y),h=[],m=0;m<=d;m++){var w=p[m];if(w){var g=ae(o[m].numFmt,w);x.isLgl&&(g=String(w)),h.push(g)}}for(m=0;m<h.length;m++){var u=h[m];i=i.replace("%".concat(m+1),u)}if(An(n,t,x.pPr),An(n,c,x.rPr),"bullet"!==x.numFmt||n.renderOptions.bulletUseFont)c.innerText=i;else{var T="&bull;",v=i.charCodeAt(0).toString(16).padStart(4,"0");"f06e"===v?T="&#9632;":"f075"===v?T="&#9670;":"f0d8"===v&&(T="&#9658;"),c.innerHTML=T}return"space"===x.suff?c.innerHTML+=" ":"tab"===x.suff&&(c.innerHTML+="&emsp;"),c}(o,t,p.numPr)),p.tabs&&p.tabs.length&&dn(o,Hn(0,p.tabs[0],!0));try{for(var f=(0,s.Ju)(n.children),c=f.next();!c.done;c=f.next()){var i=c.value;i instanceof Kt?(i.fldChar,dn(o,ee(t,i,n))):i instanceof nt?dn(o,Xn(0,i)):i instanceof _t?dn(o,Vn(t,i,n)):i instanceof en?dn(o,(x=void 0,y=void 0,x=i.element,(y=new XSLTProcessor).importStylesheet(oe),y.transformToFragment(x,document))):console.warn("unknow pargraph type",i)}}catch(t){r={error:t}}finally{try{c&&!c.done&&(l=f.return)&&l.call(f)}finally{if(r)throw r.error}}return""===o.innerHTML&&e&&(o.innerHTML="&nbsp;"),o}function ye(t,n){var e,a,r=fn("div");try{for(var l=(0,s.Ju)(n.children),o=l.next();!o.done;o=l.next()){var x=o.value;x instanceof sn?dn(r,xe(t,x,!0,!0)):x instanceof mt?dn(r,Rn(t,x)):console.warn("unknown child",x)}}catch(t){e={error:t}}finally{try{o&&!o.done&&(a=l.return)&&a.call(l)}finally{if(e)throw e.error}}return r}function pe(t,n,e,s){var a=fn("section");a.style.position="relative",n.backgroundColor&&(a.style.background=n.backgroundColor),s.page&&(s.pageMarginBottom&&(a.style.marginBottom=s.pageMarginBottom+"px"),s.pageShadow&&(a.style.boxShadow="0 0 8px rgba(0, 0, 0, 0.5)"),s.pageBackground&&(a.style.background=s.pageBackground));var r=e.properties,l=r.pageSize;l&&(s.ignoreWidth||(a.style.width=l.width),s.ignoreHeight||(a.style.height=l.height)),s.padding?a.style.padding=s.padding:(p=r.pageMargin)&&(a.style.paddingLeft=p.left||"0",a.style.paddingRight=p.right||"0",a.style.paddingTop=p.top||"0",a.style.paddingBottom=p.bottom||"0"),r.cols&&r.cols.num&&r.cols.num>1&&(a.style.columnCount=""+r.cols.num,r.cols.space&&(a.style.columnGap=r.cols.space)),t.currentPage++;var o="auto";if(r.pageSize&&r.pageSize.width&&(o=r.pageSize.width),r.headers&&s.page&&s.renderHeader){var x=r.headers,y=null;x.even&&t.currentPage%2==0?y=ye(t,x.even):x.default?y=ye(t,x.default):console.warn("can not find header",t.currentPage,r.headers),y&&(y.style.position="absolute",(p=r.pageMargin)&&p.header&&(y.style.top=p.header,y.style.width=o),a.appendChild(y))}if(r.footers&&s.page&&s.renderFooter){var p,f=r.footers,c=null;f.even&&t.currentPage%2==0?c=ye(t,f.even):f.default?c=ye(t,f.default):console.warn("can not find footer",t.currentPage,r.footers),c&&(c.style.position="absolute",(p=r.pageMargin)&&p.footer&&(c.style.bottom=p.footer,c.style.width=o),a.appendChild(c))}return a}function fe(t,n,e,s,a,r,l,o){var x=0===a.children.length;if(dn(a,o),!x&&function(t,n,e){if(t.breakPage)return t.breakPage=!1,!0;var s=e.getBoundingClientRect();return s.top+s.height>n.bottom||s.left>n.right}(t,r,o)){var y=o.cloneNode(!0);!function(t,n){t&&n&&t.removeChild(n)}(a,o);var p=pe(t,n,l,e);return dn(s,p),dn(p,y),{sectionEl:p,sectionEnd:r=ce(l,p)}}return{sectionEl:a,sectionEnd:r}}function ce(t,n){var e=n.getBoundingClientRect(),s=t.properties.pageMargin,a=e.top+e.height;(null==s?void 0:s.bottom)&&(a-=parseInt(s.bottom.replace("px",""),10));var r=e.left+e.width;return(null==s?void 0:s.right)&&(r-=parseInt(s.right.replace("px",""),10)),{bottom:a,right:r}}function ie(t,n,e){var s=n.properties,a=s.pageSize;if(e.zoomFitWidth&&!e.ignoreWidth){var r=null==a?void 0:a.width;if(t&&r){var l=parseInt(r.replace("px",""),10);if(s.pageMargin){var o=s.pageMargin;l+=o.left?parseInt(o.left.replace("px",""),10):0,l+=o.right?parseInt(o.right.replace("px",""),10):0}return t/l}}return 1}function de(t,n,e,a,r,l,o){setTimeout((function(){var x,y,p=ce(l,r);try{for(var f=(0,s.Ju)(l.children),c=f.next();!c.done;c=f.next()){var i=c.value;if(i instanceof sn){var d=xe(t,i),h=fe(t,n,a,e,r,p,l,d);r=h.sectionEl,p=h.sectionEnd}else if(i instanceof mt){var m=Rn(t,i);h=fe(t,n,a,e,r,p,l,m),r=h.sectionEl,p=h.sectionEnd}else console.warn("unknown child",i)}}catch(t){x={error:t}}finally{try{c&&!c.done&&(y=f.return)&&y.call(f)}finally{if(x)throw x.error}}o&&(r.style.marginBottom="0")}),0)}var he=function(){function t(){this.start=1,this.lvlText="%1.",this.isLgl=!1,this.lvlJc="start",this.suff="space"}return t.fromXML=function(n,e){var l,x,y=new t;y.ilvl=e.getAttribute("w:ilvl");try{for(var p=(0,s.Ju)(e.children),f=p.next();!f.done;f=p.next()){var c=f.value,i=c.tagName;switch(i){case"w:start":y.start=r(c);break;case"w:numFmt":y.numFmt=a(c);break;case"w:lvlText":y.lvlText=a(c);break;case"w:lvlJc":y.lvlJc=a(c);break;case"w:legacy":case"w:pStyle":break;case"w:pPr":y.pPr=sn.parseParagraphPr(n,c);break;case"w:rPr":y.rPr=Kt.parseRunPr(n,c);break;case"w:isLgl":y.isLgl=o(c);break;default:console.warn("Lvl: Unknown tag ",i,c)}}}catch(t){l={error:t}}finally{try{f&&!f.done&&(x=p.return)&&x.call(p)}finally{if(l)throw l.error}}return y},t}(),me=function(){function t(){this.lvls={}}return t.fromXML=function(n,e){var r,l,o=new t;o.abstractNumId=e.getAttribute("w:abstractNumId")||"",o.multiLevelType=e.getAttribute("w:multiLevelType");var x=e.getElementsByTagName("w:lvl");try{for(var y=(0,s.Ju)(x),p=y.next();!p.done;p=y.next()){var f=p.value,c=f.getAttribute("w:ilvl")||"";o.lvls[c]=he.fromXML(n,f)}}catch(t){r={error:t}}finally{try{p&&!p.done&&(l=y.return)&&l.call(y)}finally{if(r)throw r.error}}var i=e.getElementsByTagName("w:multiLevelType").item(0);return i&&(o.multiLevelType=a(i)),o},t}(),we=function(){function t(){this.lvlOverride={lvls:{}}}return t.fromXML=function(n,e){var l,o,x=new t;x.numId=e.getAttribute("w:numId")||"";var y=e.getElementsByTagName("w:abstractNumId").item(0);y&&(x.abstractNumId=a(y));var p=e.getElementsByTagName("w:lvlOverride").item(0);if(p)try{for(var f=(0,s.Ju)(p.children),c=f.next();!c.done;c=f.next()){var i=c.value,d=i.tagName;switch(d){case"w:lvl":var h=i.getAttribute("w:lvlId")||"";x.lvlOverride.lvls[h]=he.fromXML(n,i);break;case"w:startOverride":var m=i.getAttribute("w:lvlId")||"";x.lvlOverride.lvls[m]&&(x.lvlOverride.lvls[m].start=r(i));break;default:console.warn("Num: Unknown tag ",d,i)}}}catch(t){l={error:t}}finally{try{c&&!c.done&&(o=f.return)&&o.call(f)}finally{if(l)throw l.error}}return x},t}(),ge=function(){function t(){this.abstractNums={},this.nums={},this.numData={}}return t.fromXML=function(n,e){var a,r,l,o,x=new t;try{for(var y=(0,s.Ju)(e.getElementsByTagName("w:abstractNum")),p=y.next();!p.done;p=y.next()){var f=p.value,c=me.fromXML(n,f);x.abstractNums[c.abstractNumId]=c}}catch(t){a={error:t}}finally{try{p&&!p.done&&(r=y.return)&&r.call(y)}finally{if(a)throw a.error}}try{for(var i=(0,s.Ju)(e.getElementsByTagName("w:num")),d=i.next();!d.done;d=i.next()){var h=d.value,m=we.fromXML(n,h);x.nums[m.numId]=m,x.numData[m.numId]={}}}catch(t){l={error:t}}finally{try{d&&!d.done&&(o=i.return)&&o.call(i)}finally{if(l)throw l.error}}return x},t}();function ue(t,n,e){var s=n?tt(t,n):{},a=e?tt(t,e):{};return JSON.stringify(s)===JSON.stringify(a)}function Te(t,n){var e=t.getElementsByTagName("w:t")[0],s=n.getElementsByTagName("w:t")[0];if(e&&s){var a=s.textContent||"";e.textContent+=a||""}}function ve(t){var n,e,a=t.tagName,r=t.children,l=!1,o=!1;try{for(var x=(0,s.Ju)(r),y=x.next();!y.done;y=x.next()){var p=y.value;if("w:t"===p.tagName&&(l=!0,o="preserve"===p.getAttribute("xml:space")))break;if("w:tab"===p.tagName)return!1}}catch(t){n={error:t}}finally{try{y&&!y.done&&(e=x.return)&&e.call(x)}finally{if(n)throw n.error}}return"w:r"===a&&l&&!o}function Ae(t,n){var e,a,r,l,o=[],x=null;try{for(var y=(0,s.Ju)(n.children),p=y.next();!p.done;p=y.next()){var f=p.value,c=f.tagName;ve(f)?x&&ue(t,x.getElementsByTagName("w:rPr")[0],f.getElementsByTagName("w:rPr")[0])?Te(x,f):(x=f,o.push(f)):"w:proofErr"!==c&&(x=null,o.push(f))}}catch(t){e={error:t}}finally{try{p&&!p.done&&(a=y.return)&&a.call(y)}finally{if(e)throw e.error}}n.innerHTML="";try{for(var i=(0,s.Ju)(o),d=i.next();!d.done;d=i.next()){var h=d.value;n.appendChild(h)}}catch(t){r={error:t}}finally{try{d&&!d.done&&(l=i.return)&&l.call(i)}finally{if(r)throw r.error}}}function be(t,n){var e,a,r=n.getElementsByTagName("w:p");try{for(var l=(0,s.Ju)(r),o=l.next();!o.done;o=l.next())Ae(t,o.value)}catch(t){e={error:t}}finally{try{o&&!o.done&&(a=l.return)&&a.call(l)}finally{if(e)throw e.error}}}var ke=function(){function t(){this.children=[]}return t.fromXML=function(n,e){var a,r,l=new t,o=[];l.children=o;var x=e,y=e.firstElementChild;!y||"w:hdr"!==y.tagName&&"w:ftr"!==y.tagName||(x=y);try{for(var p=(0,s.Ju)(Bt(x)),f=p.next();!f.done;f=p.next()){var c=f.value,i=c.tagName;switch(i){case"w:p":var d=sn.fromXML(n,c);o.push(d);break;case"w:tbl":var h=Ft(n,c);o.push(h);break;default:console.warn("Header.fromXML Unknown key",i,c)}}}catch(t){a={error:t}}finally{try{f&&!f.done&&(r=p.return)&&r.call(p)}finally{if(a)throw a.error}}return l},t}();function Re(t,n,e){var s=n.getAttribute("w:type"),a=n.getAttribute("r:id");if(s&&a){var r=t.getDocumentRels(a);if(r){var l=t.getXML("/word/"+r.target);if(l)return{headerType:s,header:ke.fromXML(t,l)}}}return null}var Le=function(){function t(){this.properties={},this.children=[]}return t.prototype.addChild=function(t){this.children.push(t)},t.parsePr=function(t,n,e){var a,r,l={headers:{},footers:{}};try{for(var o=(0,s.Ju)(n.children),x=o.next();!x.done;x=o.next()){var p=x.value;switch(p.tagName){case"w:pgSz":l.pageSize={width:u(p,"w:w"),height:u(p,"w:h")};break;case"w:pgMar":l.pageMargin={left:u(p,"w:left"),right:u(p,"w:right"),top:u(p,"w:top"),bottom:u(p,"w:bottom"),header:u(p,"w:header"),footer:u(p,"w:footer"),gutter:u(p,"w:gutter")};break;case"w:headerReference":var f=Re(t,p);f&&(l.headers[f.headerType]=f.header);break;case"w:footerReference":var c=Re(t,p);c&&(l.footers[c.headerType]=c.header);break;case"w:cols":var i={},d=y(p,"w:num",1);i.num=d;var h=u(p,"w:space");h&&(i.space=h),l.cols=i}}}catch(t){a={error:t}}finally{try{x&&!x.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return l},t}(),Ce=function(){function t(){this.sections=[],this.currentSection=new Le,this.sections.push(this.currentSection)}return t.prototype.addChild=function(t){this.currentSection.addChild(t)},t.prototype.addSection=function(t){this.currentSection.properties=t,this.currentSection=new Le,this.sections.push(this.currentSection)},t.fromXML=function(n,e){var a,r,l=new t;try{for(var o=(0,s.Ju)(Bt(e)),x=o.next();!x.done;x=o.next()){var y=x.value,p=y.tagName;switch(p){case"w:p":var f=sn.fromXML(n,y);l.addChild(f);break;case"w:tbl":var c=Ft(n,y);l.addChild(c);break;case"w:bookmarkStart":case"w:bookmarkEnd":break;case"w:sectPr":l.addSection(Le.parsePr(n,y,l));break;default:console.warn("Body.fromXML Unknown key",p,y)}}}catch(t){a={error:t}}finally{try{x&&!x.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return l.sections=l.sections.filter((function(t){return t.children.length>0})),l},t}(),je=function(){function t(){}return t.fromXML=function(n,e){var a,r,l,o,x=new t,y=e.getElementsByTagName("w:body").item(0);y&&(x.body=Ce.fromXML(n,y));var p=e.getElementsByTagName("w:background").item(0);if(p){var f={};try{for(var c=(0,s.Ju)(p.attributes),i=c.next();!i.done;i=c.next())switch(i.value.name){case"w:color":f.color=b(n,p,"w:color");break;case"w:themeColor":f.themeColor=b(n,p,"w:themeColor");break;case"w:themeShade":f.themeShade=b(n,p,"w:themeShade");break;case"w:themeTint":f.themeTint=b(n,p,"w:themeTint");break;default:console.log("unknown background",p)}}catch(t){a={error:t}}finally{try{i&&!i.done&&(r=c.return)&&r.call(c)}finally{if(a)throw a.error}}try{for(var d=(0,s.Ju)(p.children),h=d.next();!h.done;h=d.next())"v:background"===h.value.tagName||console.log("unknown background",p)}catch(t){l={error:t}}finally{try{h&&!h.done&&(o=d.return)&&o.call(d)}finally{if(l)throw l.error}}x.backgroundColor=function(t){if(t.color)return t.color;if(t.themeColor){var n=t.themeColor;if(t.themeTint){var e=new G(n),s=parseInt(t.themeTint,16);e.tint(s/256)}else t.themeShade&&(e=new G(n),s=parseInt(t.themeShade,16),e.lumMod(s/256))}return"#FFFFF"}(f)}return x},t}(),Be=Uint8Array,Oe=Uint16Array,De=Uint32Array,Fe=new Be([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),Se=new Be([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),qe=new Be([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),Ee=function(t,n){for(var e=new Oe(31),s=0;s<31;++s)e[s]=n+=1<<t[s-1];var a=new De(e[30]);for(s=1;s<30;++s)for(var r=e[s];r<e[s+1];++r)a[r]=r-e[s]<<5|s;return[e,a]},Pe=Ee(Fe,2),$e=Pe[0],Me=Pe[1];$e[28]=258,Me[258]=28;for(var Ne=Ee(Se,0),Ie=Ne[0],He=Ne[1],ze=new Oe(32768),Ge=0;Ge<32768;++Ge){var Ve=(43690&Ge)>>>1|(21845&Ge)<<1;Ve=(61680&(Ve=(52428&Ve)>>>2|(13107&Ve)<<2))>>>4|(3855&Ve)<<4,ze[Ge]=((65280&Ve)>>>8|(255&Ve)<<8)>>>1}var Xe=function(t,n,e){for(var s=t.length,a=0,r=new Oe(n);a<s;++a)t[a]&&++r[t[a]-1];var l,o=new Oe(n);for(a=0;a<n;++a)o[a]=o[a-1]+r[a-1]<<1;if(e){l=new Oe(1<<n);var x=15-n;for(a=0;a<s;++a)if(t[a])for(var y=a<<4|t[a],p=n-t[a],f=o[t[a]-1]++<<p,c=f|(1<<p)-1;f<=c;++f)l[ze[f]>>>x]=y}else for(l=new Oe(s),a=0;a<s;++a)t[a]&&(l[a]=ze[o[t[a]-1]++]>>>15-t[a]);return l},Je=new Be(288);for(Ge=0;Ge<144;++Ge)Je[Ge]=8;for(Ge=144;Ge<256;++Ge)Je[Ge]=9;for(Ge=256;Ge<280;++Ge)Je[Ge]=7;for(Ge=280;Ge<288;++Ge)Je[Ge]=8;var Ue=new Be(32);for(Ge=0;Ge<32;++Ge)Ue[Ge]=5;var We=Xe(Je,9,0),Ze=Xe(Je,9,1),Ye=Xe(Ue,5,0),Ke=Xe(Ue,5,1),_e=function(t){for(var n=t[0],e=1;e<t.length;++e)t[e]>n&&(n=t[e]);return n},Qe=function(t,n,e){var s=n/8|0;return(t[s]|t[s+1]<<8)>>(7&n)&e},ts=function(t,n){var e=n/8|0;return(t[e]|t[e+1]<<8|t[e+2]<<16)>>(7&n)},ns=function(t){return(t+7)/8|0},es=function(t,n,e){(null==n||n<0)&&(n=0),(null==e||e>t.length)&&(e=t.length);var s=new(2==t.BYTES_PER_ELEMENT?Oe:4==t.BYTES_PER_ELEMENT?De:Be)(e-n);return s.set(t.subarray(n,e)),s},ss=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],as=function(t,n,e){var s=new Error(n||ss[t]);if(s.code=t,Error.captureStackTrace&&Error.captureStackTrace(s,as),!e)throw s;return s},rs=function(t,n,e){e<<=7&n;var s=n/8|0;t[s]|=e,t[s+1]|=e>>>8},ls=function(t,n,e){e<<=7&n;var s=n/8|0;t[s]|=e,t[s+1]|=e>>>8,t[s+2]|=e>>>16},os=function(t,n){for(var e=[],s=0;s<t.length;++s)t[s]&&e.push({s:s,f:t[s]});var a=e.length,r=e.slice();if(!a)return[ds,0];if(1==a){var l=new Be(e[0].s+1);return l[e[0].s]=1,[l,1]}e.sort((function(t,n){return t.f-n.f})),e.push({s:-1,f:25001});var o=e[0],x=e[1],y=0,p=1,f=2;for(e[0]={s:-1,f:o.f+x.f,l:o,r:x};p!=a-1;)o=e[e[y].f<e[f].f?y++:f++],x=e[y!=p&&e[y].f<e[f].f?y++:f++],e[p++]={s:-1,f:o.f+x.f,l:o,r:x};var c=r[0].s;for(s=1;s<a;++s)r[s].s>c&&(c=r[s].s);var i=new Oe(c+1),d=xs(e[p-1],i,0);if(d>n){s=0;var h=0,m=d-n,w=1<<m;for(r.sort((function(t,n){return i[n.s]-i[t.s]||t.f-n.f}));s<a;++s){var g=r[s].s;if(!(i[g]>n))break;h+=w-(1<<d-i[g]),i[g]=n}for(h>>>=m;h>0;){var u=r[s].s;i[u]<n?h-=1<<n-i[u]++-1:++s}for(;s>=0&&h;--s){var T=r[s].s;i[T]==n&&(--i[T],++h)}d=n}return[new Be(i),d]},xs=function(t,n,e){return-1==t.s?Math.max(xs(t.l,n,e+1),xs(t.r,n,e+1)):n[t.s]=e},ys=function(t){for(var n=t.length;n&&!t[--n];);for(var e=new Oe(++n),s=0,a=t[0],r=1,l=function(t){e[s++]=t},o=1;o<=n;++o)if(t[o]==a&&o!=n)++r;else{if(!a&&r>2){for(;r>138;r-=138)l(32754);r>2&&(l(r>10?r-11<<5|28690:r-3<<5|12305),r=0)}else if(r>3){for(l(a),--r;r>6;r-=6)l(8304);r>2&&(l(r-3<<5|8208),r=0)}for(;r--;)l(a);r=1,a=t[o]}return[e.subarray(0,s),n]},ps=function(t,n){for(var e=0,s=0;s<n.length;++s)e+=t[s]*n[s];return e},fs=function(t,n,e){var s=e.length,a=ns(n+2);t[a]=255&s,t[a+1]=s>>>8,t[a+2]=255^t[a],t[a+3]=255^t[a+1];for(var r=0;r<s;++r)t[a+r+4]=e[r];return 8*(a+4+s)},cs=function(t,n,e,s,a,r,l,o,x,y,p){rs(n,p++,e),++a[256];for(var f=os(a,15),c=f[0],i=f[1],d=os(r,15),h=d[0],m=d[1],w=ys(c),g=w[0],u=w[1],T=ys(h),v=T[0],A=T[1],b=new Oe(19),k=0;k<g.length;++k)b[31&g[k]]++;for(k=0;k<v.length;++k)b[31&v[k]]++;for(var R=os(b,7),L=R[0],C=R[1],j=19;j>4&&!L[qe[j-1]];--j);var B,O,D,F,S=y+5<<3,q=ps(a,Je)+ps(r,Ue)+l,E=ps(a,c)+ps(r,h)+l+14+3*j+ps(b,L)+(2*b[16]+3*b[17]+7*b[18]);if(S<=q&&S<=E)return fs(n,p,t.subarray(x,x+y));if(rs(n,p,1+(E<q)),p+=2,E<q){B=Xe(c,i,0),O=c,D=Xe(h,m,0),F=h;var P=Xe(L,C,0);for(rs(n,p,u-257),rs(n,p+5,A-1),rs(n,p+10,j-4),p+=14,k=0;k<j;++k)rs(n,p+3*k,L[qe[k]]);p+=3*j;for(var $=[g,v],M=0;M<2;++M){var N=$[M];for(k=0;k<N.length;++k){var I=31&N[k];rs(n,p,P[I]),p+=L[I],I>15&&(rs(n,p,N[k]>>>5&127),p+=N[k]>>>12)}}}else B=We,O=Je,D=Ye,F=Ue;for(k=0;k<o;++k)if(s[k]>255){I=s[k]>>>18&31,ls(n,p,B[I+257]),p+=O[I+257],I>7&&(rs(n,p,s[k]>>>23&31),p+=Fe[I]);var H=31&s[k];ls(n,p,D[H]),p+=F[H],H>3&&(ls(n,p,s[k]>>>5&8191),p+=Se[H])}else ls(n,p,B[s[k]]),p+=O[s[k]];return ls(n,p,B[256]),p+O[256]},is=new De([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),ds=new Be(0),hs=function(){for(var t=new Int32Array(256),n=0;n<256;++n){for(var e=n,s=9;--s;)e=(1&e&&-306674912)^e>>>1;t[n]=e}return t}(),ms=function(){var t=-1;return{p:function(n){for(var e=t,s=0;s<n.length;++s)e=hs[255&e^n[s]]^e>>>8;t=e},d:function(){return~t}}},ws=function(t,n,e,s,a){return function(t,n,e,s,a,r){var l=t.length,o=new Be(s+l+5*(1+Math.ceil(l/7e3))+a),x=o.subarray(s,o.length-a),y=0;if(!n||l<8)for(var p=0;p<=l;p+=65535){var f=p+65535;f>=l&&(x[y>>3]=r),y=fs(x,y+1,t.subarray(p,f))}else{for(var c=is[n-1],i=c>>>13,d=8191&c,h=(1<<e)-1,m=new Oe(32768),w=new Oe(h+1),g=Math.ceil(e/3),u=2*g,T=function(n){return(t[n]^t[n+1]<<g^t[n+2]<<u)&h},v=new De(25e3),A=new Oe(288),b=new Oe(32),k=0,R=0,L=(p=0,0),C=0,j=0;p<l;++p){var B=T(p),O=32767&p,D=w[B];if(m[O]=D,w[B]=O,C<=p){var F=l-p;if((k>7e3||L>24576)&&F>423){y=cs(t,x,0,v,A,b,R,L,j,p-j,y),L=k=R=0,j=p;for(var S=0;S<286;++S)A[S]=0;for(S=0;S<30;++S)b[S]=0}var q=2,E=0,P=d,$=O-D&32767;if(F>2&&B==T(p-$))for(var M=Math.min(i,F)-1,N=Math.min(32767,p),I=Math.min(258,F);$<=N&&--P&&O!=D;){if(t[p+q]==t[p+q-$]){for(var H=0;H<I&&t[p+H]==t[p+H-$];++H);if(H>q){if(q=H,E=$,H>M)break;var z=Math.min($,H-2),G=0;for(S=0;S<z;++S){var V=p-$+S+32768&32767,X=V-m[V]+32768&32767;X>G&&(G=X,D=V)}}}$+=(O=D)-(D=m[O])+32768&32767}if(E){v[L++]=268435456|Me[q]<<18|He[E];var J=31&Me[q],U=31&He[E];R+=Fe[J]+Se[U],++A[257+J],++b[U],C=p+q,++k}else v[L++]=t[p],++A[t[p]]}}y=cs(t,x,r,v,A,b,R,L,j,p-j,y),!r&&7&y&&(y=fs(x,y+1,ds))}return es(o,0,s+ns(y)+a)}(t,null==n.level?6:n.level,null==n.mem?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(t.length)))):12+n.mem,e,s,!a)},gs=function(t,n){var e={};for(var s in t)e[s]=t[s];for(var s in n)e[s]=n[s];return e},us=function(t,n){return t[n]|t[n+1]<<8},Ts=function(t,n){return(t[n]|t[n+1]<<8|t[n+2]<<16|t[n+3]<<24)>>>0},vs=function(t,n){return Ts(t,n)+4294967296*Ts(t,n+4)},As=function(t,n,e){for(;e;++n)t[n]=e,e>>>=8};function bs(t,n){return ws(t,n||{},0,0)}function ks(t,n){return function(t,n,e){var s=t.length;if(!s||e&&e.f&&!e.l)return n||new Be(0);var a=!n||e,r=!e||e.i;e||(e={}),n||(n=new Be(3*s));var l=function(t){var e=n.length;if(t>e){var s=new Be(Math.max(2*e,t));s.set(n),n=s}},o=e.f||0,x=e.p||0,y=e.b||0,p=e.l,f=e.d,c=e.m,i=e.n,d=8*s;do{if(!p){o=Qe(t,x,1);var h=Qe(t,x+1,3);if(x+=3,!h){var m=t[(C=ns(x)+4)-4]|t[C-3]<<8,w=C+m;if(w>s){r&&as(0);break}a&&l(y+m),n.set(t.subarray(C,w),y),e.b=y+=m,e.p=x=8*w,e.f=o;continue}if(1==h)p=Ze,f=Ke,c=9,i=5;else if(2==h){var g=Qe(t,x,31)+257,u=Qe(t,x+10,15)+4,T=g+Qe(t,x+5,31)+1;x+=14;for(var v=new Be(T),A=new Be(19),b=0;b<u;++b)A[qe[b]]=Qe(t,x+3*b,7);x+=3*u;var k=_e(A),R=(1<<k)-1,L=Xe(A,k,1);for(b=0;b<T;){var C,j=L[Qe(t,x,R)];if(x+=15&j,(C=j>>>4)<16)v[b++]=C;else{var B=0,O=0;for(16==C?(O=3+Qe(t,x,3),x+=2,B=v[b-1]):17==C?(O=3+Qe(t,x,7),x+=3):18==C&&(O=11+Qe(t,x,127),x+=7);O--;)v[b++]=B}}var D=v.subarray(0,g),F=v.subarray(g);c=_e(D),i=_e(F),p=Xe(D,c,1),f=Xe(F,i,1)}else as(1);if(x>d){r&&as(0);break}}a&&l(y+131072);for(var S=(1<<c)-1,q=(1<<i)-1,E=x;;E=x){var P=(B=p[ts(t,x)&S])>>>4;if((x+=15&B)>d){r&&as(0);break}if(B||as(2),P<256)n[y++]=P;else{if(256==P){E=x,p=null;break}var $=P-254;if(P>264){var M=Fe[b=P-257];$=Qe(t,x,(1<<M)-1)+$e[b],x+=M}var N=f[ts(t,x)&q],I=N>>>4;if(N||as(3),x+=15&N,F=Ie[I],I>3&&(M=Se[I],F+=ts(t,x)&(1<<M)-1,x+=M),x>d){r&&as(0);break}a&&l(y+131072);for(var H=y+$;y<H;y+=4)n[y]=n[y-F],n[y+1]=n[y+1-F],n[y+2]=n[y+2-F],n[y+3]=n[y+3-F];y=H}}e.l=p,e.p=E,e.b=y,e.f=o,p&&(o=1,e.m=c,e.d=f,e.n=i)}while(!o);return y==n.length?n:es(n,0,y)}(t,n)}var Rs=function(t,n,e,s){for(var a in t){var r=t[a],l=n+a,o=s;Array.isArray(r)&&(o=gs(s,r[1]),r=r[0]),r instanceof Be?e[l]=[r,o]:(e[l+="/"]=[new Be(0),o],Rs(r,l,e,s))}},Ls="undefined"!=typeof TextEncoder&&new TextEncoder,Cs="undefined"!=typeof TextDecoder&&new TextDecoder;try{Cs.decode(ds,{stream:!0})}catch(t){}function js(t,n){if(n){for(var e=new Be(t.length),s=0;s<t.length;++s)e[s]=t.charCodeAt(s);return e}if(Ls)return Ls.encode(t);var a=t.length,r=new Be(t.length+(t.length>>1)),l=0,o=function(t){r[l++]=t};for(s=0;s<a;++s){if(l+5>r.length){var x=new Be(l+8+(a-s<<1));x.set(r),r=x}var y=t.charCodeAt(s);y<128||n?o(y):y<2048?(o(192|y>>6),o(128|63&y)):y>55295&&y<57344?(o(240|(y=65536+(1047552&y)|1023&t.charCodeAt(++s))>>18),o(128|y>>12&63),o(128|y>>6&63),o(128|63&y)):(o(224|y>>12),o(128|y>>6&63),o(128|63&y))}return es(r,0,l)}function Bs(t,n){if(n){for(var e="",s=0;s<t.length;s+=16384)e+=String.fromCharCode.apply(null,t.subarray(s,s+16384));return e}if(Cs)return Cs.decode(t);var a=function(t){for(var n="",e=0;;){var s=t[e++],a=(s>127)+(s>223)+(s>239);if(e+a>t.length)return[n,es(t,e-1)];a?3==a?(s=((15&s)<<18|(63&t[e++])<<12|(63&t[e++])<<6|63&t[e++])-65536,n+=String.fromCharCode(55296|s>>10,56320|1023&s)):n+=1&a?String.fromCharCode((31&s)<<6|63&t[e++]):String.fromCharCode((15&s)<<12|(63&t[e++])<<6|63&t[e++]):n+=String.fromCharCode(s)}}(t),r=a[0];return a[1].length&&as(8),r}var Os=function(t,n){return n+30+us(t,n+26)+us(t,n+28)},Ds=function(t,n,e){var s=us(t,n+28),a=Bs(t.subarray(n+46,n+46+s),!(2048&us(t,n+8))),r=n+46+s,l=Ts(t,n+20),o=e&&4294967295==l?Fs(t,r):[l,Ts(t,n+24),Ts(t,n+42)],x=o[0],y=o[1],p=o[2];return[us(t,n+10),x,y,a,r+us(t,n+30)+us(t,n+32),p]},Fs=function(t,n){for(;1!=us(t,n);n+=4+us(t,n+2));return[vs(t,n+12),vs(t,n+4),vs(t,n+20)]},Ss=function(t){var n=0;if(t)for(var e in t){var s=t[e].length;s>65535&&as(9),n+=s+4}return n},qs=function(t,n,e,s,a,r,l,o){var x=s.length,y=e.extra,p=o&&o.length,f=Ss(y);As(t,n,null!=l?33639248:67324752),n+=4,null!=l&&(t[n++]=20,t[n++]=e.os),t[n]=20,n+=2,t[n++]=e.flag<<1|(r<0&&8),t[n++]=a&&8,t[n++]=255&e.compression,t[n++]=e.compression>>8;var c=new Date(null==e.mtime?Date.now():e.mtime),i=c.getFullYear()-1980;if((i<0||i>119)&&as(10),As(t,n,i<<25|c.getMonth()+1<<21|c.getDate()<<16|c.getHours()<<11|c.getMinutes()<<5|c.getSeconds()>>>1),n+=4,-1!=r&&(As(t,n,e.crc),As(t,n+4,r<0?-r-2:r),As(t,n+8,e.size)),As(t,n+12,x),As(t,n+14,f),n+=16,null!=l&&(As(t,n,p),As(t,n+6,e.attrs),As(t,n+10,l),n+=14),t.set(s,n),n+=x,f)for(var d in y){var h=y[d],m=h.length;As(t,n,+d),As(t,n+2,m),t.set(h,n+4),n+=4+m}return p&&(t.set(o,n),n+=p),n};function Es(t,n){n||(n={});var e={},s=[];Rs(t,"",e,n);var a=0,r=0;for(var l in e){var o=e[l],x=o[0],y=o[1],p=0==y.level?0:8,f=(b=js(l)).length,c=y.comment,i=c&&js(c),d=i&&i.length,h=Ss(y.extra);f>65535&&as(11);var m=p?bs(x,y):x,w=m.length,g=ms();g.p(x),s.push(gs(y,{size:x.length,crc:g.d(),c:m,f:b,m:i,u:f!=l.length||i&&c.length!=d,o:a,compression:p})),a+=30+f+h+w,r+=76+2*(f+h)+(d||0)+w}for(var u=new Be(r+22),T=a,v=r-a,A=0;A<s.length;++A){var b=s[A];qs(u,b.o,b,b.f,b.u,b.c.length);var k=30+b.f.length+Ss(b.extra);u.set(b.c,b.o+k),qs(u,a,b,b.f,b.u,b.c.length,b.o,b.m),a+=16+k+(b.m?b.m.length:0)}return function(t,n,e,s,a){As(t,n,101010256),As(t,n+8,e),As(t,n+10,e),As(t,n+12,s),As(t,n+16,a)}(u,a,s.length,v,T),u}"function"==typeof queueMicrotask?queueMicrotask:"function"==typeof setTimeout&&setTimeout;var Ps=function(){function t(){}return t.prototype.load=function(t){this.zip=function(t,n){for(var e={},s=t.length-22;101010256!=Ts(t,s);--s)(!s||t.length-s>65558)&&as(13);var a=us(t,s+8);if(!a)return{};var r=Ts(t,s+16),l=4294967295==r||65535==a;if(l){var o=Ts(t,s-12);(l=101075792==Ts(t,o))&&(a=Ts(t,o+32),r=Ts(t,o+48))}for(var x=n,y=0;y<a;++y){var p=Ds(t,r,l),f=p[0],c=p[1],i=p[2],d=p[3],h=p[4],m=p[5],w=Os(t,m);r=h,x&&!x({name:d,size:c,originalSize:i,compression:f})||(f?8==f?e[d]=ks(t.subarray(w,w+c),new Be(i)):as(14,"unknown compression type "+f):e[d]=es(t,w,w+c))}return e}(new Uint8Array(t))},t.prototype.getXML=function(t){var n=this.getFileByType(t,"string"),e=(new DOMParser).parseFromString(n,"application/xml"),s=e.getElementsByTagName("parsererror").item(0);if(s)throw new Error(s.textContent||"can't parse xml");return e},t.prototype.getFileByType=function(t,n){t=t.startsWith("/")?t.slice(1):t;var e=this.zip[t];if(e){if("string"===n)return Bs(e);if("blob"===n)return new Blob([e]);if("uint8array"===n)return e}return console.warn("getFileByType",t,"not found"),null},t.prototype.saveFile=function(t,n){"string"==typeof n&&(n=js(n)),this.zip[t]=n},t.prototype.fileExists=function(t){return(t=t.startsWith("/")?t.slice(1):t)in this.zip},t.prototype.generateZip=function(t){return this.zip["word/document.xml"]=js(t),new Blob([Es(this.zip)])},t}();function $s(t,n,e){var s=n.textContent||"";n.textContent=Ms(t,s,e)}function Ms(t,n,e){var s=t.renderOptions.evalVar;if(n.startsWith("{{")){var a=s(n=n.replace(/^{{/g,"").replace(/}}$/g,""),e);return null!=a?String(a):(console.warn("var error: [",n,"] not found in data"),"")}return n}var Ns=1;function Is(t,n,e,a){return void 0===a&&(a=!1),(0,s.sH)(this,void 0,void 0,(function(){var r,l,o,x,y,p,f;return(0,s.YH)(this,(function(s){switch(s.label){case 0:return n.getAttribute("downloaded")?[2]:(r=n.getAttribute("descr")||"",l=Ms(t,r,e),n.setAttribute("descrVar",l),a&&l?(o=n.parentElement.parentElement,(x=o.getElementsByTagName("a:blip").item(0))?(y="rIdn".concat(Ns),x.setAttribute("r:embed",y),[4,fetch(l)]):[3,3]):[3,4]);case 1:return[4,s.sent().arrayBuffer()];case 2:p=s.sent(),t.saveNewImage(y,new Uint8Array(p)),n.setAttribute("downloaded","true"),Ns++,s.label=3;case 3:(f=ht.fromXML(t,o))&&f.blipFill&&f.blipFill.blip&&f.blipFill.blip.embled,s.label=4;case 4:return[2]}}))}))}function Hs(t,n,e){return void 0===e&&(e=!1),(0,s.sH)(this,void 0,void 0,(function(){var a,r,l,o,x,y,p,f,c,i,d,h,m,w,g,u,T,v,A,b,k,R,L,C,j,B,O,D,F,S,q,E,P,$,M,N,I,H,z;return(0,s.YH)(this,(function(G){switch(G.label){case 0:a=t.renderOptions.evalVar,r=t.renderOptions.data,l=n.parentNode,o=n.getElementsByTagName("w:tc"),x=!1,y=[];try{for(p=(0,s.Ju)(o),f=p.next();!f.done;f=p.next()){c=f.value,b=c.getElementsByTagName("w:t");try{for(E=void 0,i=(0,s.Ju)(b),d=i.next();!d.done;d=i.next())C=d.value,(h=C.textContent||"").startsWith("{{#")&&(m=/{{#([^\}]+)}}/.exec(h))&&m.length>0&&(x=!0,w=m[1],g=a(w,r),Array.isArray(g)&&(y=g),C.textContent=C.textContent.replace("{{#".concat(w,"}}"),"")),-1!==h.indexOf("{{/}}")&&(C.textContent=C.textContent.replace("{{/}}",""))}catch(t){E={error:t}}finally{try{d&&!d.done&&(P=i.return)&&P.call(i)}finally{if(E)throw E.error}}}}catch(t){S={error:t}}finally{try{f&&!f.done&&(q=p.return)&&q.call(p)}finally{if(S)throw S.error}}if(!x)return[3,16];G.label=1;case 1:G.trys.push([1,13,14,15]),u=(0,s.Ju)(y),T=u.next(),G.label=2;case 2:if(T.done)return[3,12];v=T.value,A=function(t){var n,e,a,r,l,o=t.cloneNode(!0);zs(o);var x=[].slice.call(o.getElementsByTagName("w:p"));try{for(var y=(0,s.Ju)(x),p=y.next();!p.done;p=y.next())zs(p.value)}catch(t){n={error:t}}finally{try{p&&!p.done&&(e=y.return)&&e.call(y)}finally{if(n)throw n.error}}var f=[].slice.call(o.getElementsByTagName("w:cnfStyle"));try{for(var c=(0,s.Ju)(f),i=c.next();!i.done;i=c.next()){var d=i.value;null===(l=d.parentElement)||void 0===l||l.removeChild(d)}}catch(t){a={error:t}}finally{try{i&&!i.done&&(r=c.return)&&r.call(c)}finally{if(a)throw a.error}}return o}(n),b=A.getElementsByTagName("w:t"),k=function(t,n,e){t&&Object.isFrozen(t)&&(t=function(t,n){void 0===n&&(n=!0);var e=t&&t.__super?Object.create(t.__super,{__super:{value:t.__super,writable:!1,enumerable:!1}}):Object.create(Object.prototype);return n&&t&&Object.keys(t).forEach((function(n){return e[n]=t[n]})),e}(t));var a=t?Object.create(t,(0,s.Cl)((0,s.Cl)({},e),{__super:{value:t,writable:!1,enumerable:!1}})):Object.create(Object.prototype,e);return n&&function(t){var n=typeof t;return t&&"string"!==n&&"number"!==n&&"boolean"!==n&&"function"!==n&&!Array.isArray(t)}(n)&&Object.keys(n).forEach((function(t){return a[t]=n[t]})),a}(r,v);try{for(N=void 0,R=(0,s.Ju)(b),L=R.next();!L.done;L=R.next())C=L.value,$s(t,C,k)}catch(t){N={error:t}}finally{try{L&&!L.done&&(I=R.return)&&I.call(R)}finally{if(N)throw N.error}}G.label=3;case 3:G.trys.push([3,8,9,10]),H=void 0,j=(0,s.Ju)(A.getElementsByTagName("pic:cNvPr")),B=j.next(),G.label=4;case 4:return B.done?[3,7]:(O=B.value,[4,Is(t,O,k,e)]);case 5:G.sent(),G.label=6;case 6:return B=j.next(),[3,4];case 7:return[3,10];case 8:return D=G.sent(),H={error:D},[3,10];case 9:try{B&&!B.done&&(z=j.return)&&z.call(j)}finally{if(H)throw H.error}return[7];case 10:l.insertBefore(A,n),G.label=11;case 11:return T=u.next(),[3,2];case 12:return[3,15];case 13:return F=G.sent(),$={error:F},[3,15];case 14:try{T&&!T.done&&(M=u.return)&&M.call(u)}finally{if($)throw $.error}return[7];case 15:l.removeChild(n),G.label=16;case 16:return[2]}}))}))}function zs(t){for(;t.attributes.length>0;)t.removeAttributeNode(t.attributes[0])}function Gs(t,n,e){return void 0===e&&(e=!1),(0,s.sH)(this,void 0,void 0,(function(){var a,r,l,o,x,y,p;return(0,s.YH)(this,(function(f){switch(f.label){case 0:a=[].slice.call(n.getElementsByTagName("w:tr")),f.label=1;case 1:f.trys.push([1,6,7,8]),r=(0,s.Ju)(a),l=r.next(),f.label=2;case 2:return l.done?[3,5]:(o=l.value,[4,Hs(t,o,e)]);case 3:f.sent(),f.label=4;case 4:return l=r.next(),[3,2];case 5:return[3,8];case 6:return x=f.sent(),y={error:x},[3,8];case 7:try{l&&!l.done&&(p=r.return)&&p.call(r)}finally{if(y)throw y.error}return[7];case 8:return[2]}}))}))}function Vs(t,n){return(0,s.sH)(this,void 0,void 0,(function(){var e,a,r,l,o,x;return(0,s.YH)(this,(function(y){switch(y.label){case 0:y.trys.push([0,5,6,7]),e=(0,s.Ju)(n.getElementsByTagName("pic:cNvPr")),a=e.next(),y.label=1;case 1:return a.done?[3,4]:(r=a.value,[4,Is(t,r,t.renderOptions.data,!0)]);case 2:y.sent(),y.label=3;case 3:return a=e.next(),[3,1];case 4:return[3,7];case 5:return l=y.sent(),o={error:l},[3,7];case 6:try{a&&!a.done&&(x=e.return)&&x.call(e)}finally{if(o)throw o.error}return[7];case 7:return[2]}}))}))}function Xs(t,n,e){return void 0===e&&(e=!1),(0,s.sH)(this,void 0,void 0,(function(){return(0,s.YH)(this,(function(s){switch(s.label){case 0:return[4,Gs(t,n,e)];case 1:return s.sent(),e?[4,Vs(t,n)]:[3,3];case 2:s.sent(),s.label=3;case 3:return[2]}}))}))}var Js=function(){function t(){this.children=[]}return t.prototype.addChild=function(t){this.children.push(t)},t.fromXML=function(n,e){var a,r,l=new t;try{for(var o=(0,s.Ju)(e.children),x=o.next();!x.done;x=o.next()){var y=x.value,p=y.tagName;switch(p){case"w:p":var f=sn.fromXML(n,y);l.addChild(f);break;case"w:tbl":var c=Ft(n,y);l.addChild(c);break;default:console.warn("Note.fromXML unknown tag",p,y)}}}catch(t){a={error:t}}finally{try{x&&!x.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return l},t}();function Us(t,n){var e,a,r={},l=[].slice.call(n.getElementsByTagName("w:footnote"));try{for(var o=(0,s.Ju)(l),x=o.next();!x.done;x=o.next()){var y=x.value,p=Js.fromXML(t,y);r[y.getAttribute("w:id")]=p}}catch(t){e={error:t}}finally{try{x&&!x.done&&(a=o.return)&&a.call(o)}finally{if(e)throw e.error}}return r}function Ws(t,n){var e,a,r={},l=[].slice.call(n.getElementsByTagName("w:endnote"));try{for(var o=(0,s.Ju)(l),x=o.next();!x.done;x=o.next()){var y=x.value,p=Js.fromXML(t,y);r[y.getAttribute("w:id")]=p}}catch(t){e={error:t}}finally{try{x&&!x.done&&(a=o.return)&&a.call(o)}finally{if(e)throw e.error}}return r}function Zs(t,n,e,a,r){var l,o,x=r.children,y=fn("div"),p=fn("a"),f=e+"-"+a;p.name=f,p.id=f,n.appendChild(y);try{for(var c=(0,s.Ju)(x),i=c.next();!i.done;i=c.next()){var d=i.value;d instanceof sn?dn(y,xe(t,d)):d instanceof mt?dn(y,Rn(t,d)):console.warn("unknown child",d)}}catch(t){l={error:t}}finally{try{i&&!i.done&&(o=c.return)&&o.call(c)}finally{if(l)throw l.error}}}function Ys(t){if(!t)return!1;for(var n in t)if("0"!==n&&"-1"!==n)return!0;return!1}function Ks(t){return new Promise((function(n){var e=function(){t&&void 0!==t.naturalWidth&&0!==t.naturalWidth&&t.complete?n():setTimeout(e,500)};e()}))}function _s(t){var n,e;null===(n=t.contentWindow)||void 0===n||n.print(),null===(e=t.parentNode)||void 0===e||e.removeChild(t)}function Qs(t){var n,e,a={};try{for(var r=(0,s.Ju)(t.attributes),l=r.next();!l.done;l=r.next()){var o=l.value,x=o.name.replace("w:",""),y=o.value;"light1"===y?y="lt1":"light2"===y?y="lt2":"dark1"===y?y="dk1":"dark2"===y&&(y="dk2"),a[x]=y}}catch(t){n={error:t}}finally{try{l&&!l.done&&(e=r.return)&&e.call(r)}finally{if(n)throw n.error}}return a.bg1||(a.bg1="lt1"),a.bg2||(a.bg2="lt2"),a.tx1||(a.tx1="dk1"),a}var ta=function(){function t(){this.clrSchemeMapping={},this.autoHyphenation=!1}return t.parse=function(n,e){var a,r,l=new t,x=e;e.firstElementChild&&"w:settings"===e.firstElementChild.tagName&&(x=e.getElementsByTagName("w:settings").item(0));try{for(var y=(0,s.Ju)(Array.from(x.children)),p=y.next();!p.done;p=y.next()){var f=p.value;switch(f.tagName){case"w:clrSchemeMapping":l.clrSchemeMapping=Qs(f);break;case"w:autoHyphenation":l.autoHyphenation=o(f,!1)}}}catch(t){a={error:t}}finally{try{p&&!p.done&&(r=y.return)&&r.call(y)}finally{if(a)throw a.error}}return l},t}();function na(t,n,e){var a,r;void 0===e&&(e={});var l=e.offset||0;try{for(var o=(0,s.Ju)(n.entries()),x=o.next();!x.done;x=o.next()){var y=(0,s.zs)(x.value,2),p=y[0],f=y[1];if(e.mask){if(f!==(e.mask[p]&t[p+l]))return!1}else if(f!==t[p+l])return!1}}catch(t){a={error:t}}finally{try{x&&!x.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return!0}function ea(t,n,e){return void 0===e&&(e={}),na(t,function(t){return(0,s.fX)([],(0,s.zs)(t),!1).map((function(t){return t.charCodeAt(0)}))}(n),e)}var sa={classPrefix:"docx-viewer",page:!1,pageWrap:!0,bulletUseFont:!0,ignoreHeight:!0,ignoreWidth:!1,minLineHeight:1,enableVar:!1,debug:!1,pageWrapPadding:20,pageMarginBottom:20,pageShadow:!0,pageBackground:"#FFFFFF",pageWrapBackground:"#ECECEC",printWaitTime:100,zoomFitWidth:!1,renderHeader:!0,renderFooter:!0,data:{},evalVar:function(t,n){return function(t,n,e){void 0===e&&(e=void 0);var s=function(e){return String.prototype.split.call(n,e).filter(Boolean).reduce((function(t,n){return null!=t?t[n]:t}),t)},a=s(/[,[\]]+?/)||s(/[,[\].]+?/);return void 0===a||a===t?e:a}(n,t)}},aa=function(){function t(n,e,a){void 0===a&&(a=new Ps),this.themes=[],this.styleIdMap={},this.styleIdNum=0,this.wrapClassName="docx-viewer-wrapper",this.footNotes={},this.endNotes={},this.inited=!1,this.breakPage=!1,this.DOCUMENT_RELS="/word/_rels/document.xml.rels",a.load(n),this.id=t.globalId++,this.parser=a,this.renderOptions=(0,s.Cl)((0,s.Cl)({},sa),e),this.renderOptions.page&&(this.renderOptions.ignoreHeight=!1,this.renderOptions.ignoreWidth=!1)}return t.prototype.init=function(){this.inited||(this.initContentType(),this.initRelation(),this.initSettings(),this.initTheme(),this.initFontTable(),this.initStyle(),this.initNumbering(),this.initNotes(),this.inited=!0)},t.prototype.initTheme=function(){var t,n;try{for(var e=(0,s.Ju)(this.conentTypes.overrides),a=e.next();!a.done;a=e.next()){var r=a.value;if(r.partName.startsWith("/word/theme")){var l=this.parser.getXML(r.partName);this.themes.push(xn(l))}}}catch(n){t={error:n}}finally{try{a&&!a.done&&(n=e.return)&&n.call(e)}finally{if(t)throw t.error}}},t.prototype.initStyle=function(){var t,n;try{for(var e=(0,s.Ju)(this.conentTypes.overrides),a=e.next();!a.done;a=e.next())a.value.partName.startsWith("/word/styles.xml")&&(this.styles=ln(this,this.parser.getXML("/word/styles.xml")))}catch(n){t={error:n}}finally{try{a&&!a.done&&(n=e.return)&&n.call(e)}finally{if(t)throw t.error}}},t.prototype.initSettings=function(){var t,n;try{for(var e=(0,s.Ju)(this.conentTypes.overrides),a=e.next();!a.done;a=e.next())a.value.partName.startsWith("/word/settings.xml")&&(this.settings=ta.parse(this,this.parser.getXML("/word/settings.xml")))}catch(n){t={error:n}}finally{try{a&&!a.done&&(n=e.return)&&n.call(e)}finally{if(t)throw t.error}}},t.prototype.initFontTable=function(){var t,n;try{for(var e=(0,s.Ju)(this.conentTypes.overrides),a=e.next();!a.done;a=e.next())a.value.partName.startsWith("/word/fontTable.xml")&&(this.fontTable=i.fromXML(this,this.parser.getXML("/word/fontTable.xml")))}catch(n){t={error:n}}finally{try{a&&!a.done&&(n=e.return)&&n.call(e)}finally{if(t)throw t.error}}},t.prototype.initRelation=function(){var t={};this.parser.fileExists("/_rels/.rels")&&(t=h(this.parser.getXML("/_rels/.rels"),"root")),this.relationships=t;var n={};this.parser.fileExists(this.DOCUMENT_RELS)&&(n=h(this.parser.getXML(this.DOCUMENT_RELS),"word")),this.documentRels=n;var e={};this.parser.fileExists("/word/_rels/fontTable.xml.rels")&&(e=h(this.parser.getXML("/word/_rels/fontTable.xml.rels"),"word")),this.fontTableRels=e},t.prototype.initContentType=function(){var t=this.parser.getXML("[Content_Types].xml");this.conentTypes=function(t){var n,e,a={overrides:[]},r=[].slice.call(t.getElementsByTagName("Override"));try{for(var l=(0,s.Ju)(r),o=l.next();!o.done;o=l.next()){var x=o.value;a.overrides.push({partName:x.getAttribute("PartName"),contentType:x.getAttribute("ContentType")})}}catch(t){n={error:t}}finally{try{o&&!o.done&&(e=l.return)&&e.call(l)}finally{if(n)throw n.error}}return a}(t)},t.prototype.initNumbering=function(){var t,n;try{for(var e=(0,s.Ju)(this.conentTypes.overrides),a=e.next();!a.done;a=e.next()){var r=a.value;if(r.partName.startsWith("/word/numbering")){var l=this.parser.getXML(r.partName);this.numbering=ge.fromXML(this,l)}}}catch(n){t={error:n}}finally{try{a&&!a.done&&(n=e.return)&&n.call(e)}finally{if(t)throw t.error}}},t.prototype.initNotes=function(){var t,n;try{for(var e=(0,s.Ju)(this.conentTypes.overrides),a=e.next();!a.done;a=e.next()){var r=a.value;if(r.partName.startsWith("/word/footnotes.xml")){var l=this.parser.getXML(r.partName);this.footNotes=Us(this,l)}r.partName.startsWith("/word/endnotes.xml")&&(l=this.parser.getXML(r.partName),this.endNotes=Ws(this,l))}}catch(n){t={error:n}}finally{try{a&&!a.done&&(n=e.return)&&n.call(e)}finally{if(t)throw t.error}}},t.prototype.getRelationship=function(t){return t&&this.relationships?this.relationships[t]:null},t.prototype.getDocumentRels=function(t){return t&&this.documentRels?this.documentRels[t]:null},t.prototype.getFontTableRels=function(t){return t&&this.fontTableRels?this.fontTableRels[t]:null},t.prototype.replaceText=function(t){var n=this;if(!1===this.renderOptions.enableVar)return t;var e=this.renderOptions.data;return-1!==t.indexOf("{{")&&(t=t.replace(/{{([^{}]+)}}/g,(function(t,s){var a=n.renderOptions.evalVar(s,e);return void 0===a?"":String(a)}))),t},t.prototype.loadWordRelXML=function(t){var n=t.target;return"word"===t.part&&(n="word/"+n),this.getXML(n)},t.prototype.loadImage=function(t){var n=t.target;"word"===t.part&&(n="word/"+n);var e=this.parser.getFileByType(n,"blob");return e?URL.createObjectURL(e):null},t.prototype.saveNewImage=function(t,n){if(this.parser.fileExists(this.DOCUMENT_RELS)){var e=this.parser.getXML(this.DOCUMENT_RELS),s=e.getElementsByTagName("Relationship").item(0).cloneNode(!0);s.setAttributeNS(null,"Id",t),s.setAttributeNS(null,"Type","http://schemas.openxmlformats.org/officeDocument/2006/relationships/image");var a="",r=na(o=n,[137,80,78,71,13,10,26,10])?{ext:"png",mime:"image/png"}:na(o,[255,216,255])?{ext:"jpg",mime:"image/jpeg"}:na(o,[71,73,70])?{ext:"gif",mime:"image/gif"}:na(o,[66,77])?{ext:"bmp",mime:"image/bmp"}:na(o,[197,208,211,198])?{ext:"eps",mime:"application/eps"}:ea(o,"8BPS")?{ext:"psd",mime:"image/vnd.adobe.photoshop"}:ea(o,"%PDF")?{ext:"pdf",mime:"application/pdf"}:null;r&&(a="."+r.ext);var l="media/image"+t+a;s.setAttributeNS(null,"Target",l),e.getElementsByTagName("Relationships")[0].appendChild(s),this.parser.saveFile(this.DOCUMENT_RELS.replace(/^\//,""),re(e)),this.parser.saveFile("word/"+l,n)}var o},t.prototype.loadFont=function(t,n){var e=this.getFontTableRels(t);if(!e)return null;var s=e.target;"word"===e.part&&(s="word/"+s);var a=this.parser.getFileByType(s,"uint8array");return a?URL.createObjectURL(new Blob([f(a,n)])):null},t.prototype.getXML=function(t){return this.parser.getXML(t)},t.prototype.getStyleIdDisplayName=function(t){return t.match(/^[a-zA-Z]+[a-zA-Z0-9\-\_]*$/)?this.getClassPrefix()+"-"+t:(t in this.styleIdMap||(this.styleIdMap[t]=this.genClassName()),this.styleIdMap[t])},t.prototype.genClassName=function(){return"docx-classname-"+this.styleIdNum++},t.prototype.appendStyle=function(t){void 0===t&&(t="");var n=fn("style");n.textContent=t,this.rootElement.appendChild(n)},t.prototype.getStyleClassName=function(t){var n=this.styles.styleMap[t];if(!n)return[];var e=[this.getStyleIdDisplayName(t)];return n.basedOn&&e.unshift(this.getStyleIdDisplayName(n.basedOn)),e},t.prototype.getStyle=function(t){return this.styles.styleMap[t]},t.prototype.getClassPrefix=function(){return"".concat(this.renderOptions.classPrefix,"-").concat(this.id)},t.prototype.getThemeColor=function(t){var n,e;if(this.settings.clrSchemeMapping&&(t=this.settings.clrSchemeMapping[t]||t),this.themes&&this.themes.length>0){var s=null===(e=null===(n=this.themes[0].themeElements)||void 0===n?void 0:n.clrScheme)||void 0===e?void 0:e.colors;return(null==s?void 0:s[t])||(console.warn("unknown theme color: "+t),(null==s?void 0:s.accent1)||"")}return""},t.prototype.addClass=function(t,n){t.classList.add("".concat(this.getClassPrefix(),"-").concat(n))},t.prototype.updateVariable=function(){this.rootElement&&!1!==this.renderOptions.enableVar&&function(t){for(var n=t.rootElement.querySelectorAll(".".concat(te)),e=0;e<n.length;e++){var s=n[e],a=s.dataset.originText||"";s.textContent=t.replaceText(a)}}(this)},t.prototype.download=function(t){return void 0===t&&(t="document.docx"),(0,s.sH)(this,void 0,void 0,(function(){var n,e,a;return(0,s.YH)(this,(function(s){switch(s.label){case 0:return n=this.getXML("word/document.xml"),this.renderOptions.enableVar?(be(this,n),[4,Xs(this,n,!0)]):[3,2];case 1:for(s.sent(),e=n.getElementsByTagName("w:t"),a=0;a<e.length;a++)$s(this,e[a],this.renderOptions.data);s.label=2;case 2:return function(t,n){void 0===n&&(n="file.txt");var e=URL.createObjectURL(t),s=document.createElement("a");s.href=e,s.download=n,document.body.appendChild(s),s.dispatchEvent(new MouseEvent("click",{bubbles:!0,cancelable:!0,view:window})),document.body.removeChild(s)}(this.parser.generateZip(re(n)),t),[2]}}))}))},t.prototype.print=function(){return(0,s.sH)(this,void 0,void 0,(function(){var t,n;return(0,s.YH)(this,(function(e){switch(e.label){case 0:return(t=document.createElement("iframe")).style.position="absolute",t.style.top="-10000px",document.body.appendChild(t),(n=t.contentDocument)?(n.write('<style>\n      html, body { margin:0; padding:0 }\n      @page { size: auto; margin: 0mm; }\n      </style>\n      <div id="print"></div>'),[4,this.render(n.getElementById("print"),(0,s.Cl)({pageWrap:!1,pageShadow:!1,pageMarginBottom:0,pageWrapPadding:void 0,zoom:1},this.renderOptions.printOptions))]):(console.warn("printDocument is null"),[2,null]);case 1:return e.sent(),setTimeout((function(){t.focus(),function(t){var n=t.contentDocument.getElementsByTagName("img");n.length>0?function(t){var n=this,e=t.map((function(t){return(0,s.sH)(n,void 0,void 0,(function(){return(0,s.YH)(this,(function(n){switch(n.label){case 0:return t.src&&t.src!==window.location.href?[4,Ks(t)]:[3,2];case 1:n.sent(),n.label=2;case 2:return[2]}}))}))}));return Promise.all(e)}(Array.from(n)).then((function(){return _s(t)})):_s(t)}(t)}),this.renderOptions.printWaitTime||100),window.focus(),[2]}}))}))},t.prototype.render=function(t,n){return void 0===n&&(n={}),(0,s.sH)(this,void 0,void 0,(function(){var e,a,r,l,o;return(0,s.YH)(this,(function(x){switch(x.label){case 0:return this.init(),this.currentPage=0,e=(0,s.Cl)((0,s.Cl)({},this.renderOptions),n),(a=e.debug)&&console.log("init",this),this.rootElement=t,t.innerHTML="",r=this.getXML("word/document.xml"),a&&console.log("documentData",r),e.enableVar?(be(this,r),[4,Xs(this,r)]):[3,2];case 1:x.sent(),x.label=2;case 2:return l=je.fromXML(this,r),a&&console.log("document",l),o=function(t,n,e,a){var r=fn("article");return function(t,n,e,a,r,l){var o,x,y,p,f=l.page||!1,c=t.getBoundingClientRect().width-2*(l.pageWrapPadding||0),i=[],d=0,h=r.sections,m=h.length,w=!1;try{for(var g=(0,s.Ju)(h),u=g.next();!u.done;u=g.next()){var T=u.value;i.push(ie(c,T,l)),n.currentSection=T;var v=pe(n,a,T,l);if(dn(e,v),(d+=1)===m&&(w=!0),f)de(n,a,e,l,v,T,w);else try{for(var A=(y=void 0,(0,s.Ju)(T.children)),b=A.next();!b.done;b=A.next()){var k=b.value;k instanceof sn?dn(v,xe(n,k)):k instanceof mt?dn(v,Rn(n,k)):console.warn("unknown child",k)}}catch(t){y={error:t}}finally{try{b&&!b.done&&(p=A.return)&&p.call(A)}finally{if(y)throw y.error}}}}catch(t){o={error:t}}finally{try{u&&!u.done&&(x=g.return)&&x.call(g)}finally{if(o)throw o.error}}setTimeout((function(){if(l.zoom)e.style.transformOrigin="0 0",e.style.transform="scale(".concat(l.zoom,")");else if(l.page&&l.zoomFitWidth&&!l.ignoreWidth){var t=Math.min.apply(Math,(0,s.fX)([],(0,s.zs)(i),!1));e.style.transformOrigin="0 0",e.style.transform="scale(".concat(t,")")}}),0)}(t,n,r,e,e.body,a),r}(t,this,l,e),t.classList.add(this.getClassPrefix()),e.page&&e.pageWrap&&(t.classList.add(this.wrapClassName),t.style.padding="".concat(e.pageWrapPadding||0,"pt"),t.style.background=e.pageWrapBackground||"#ECECEC"),dn(t,(y=this,p=fn("style"),f=function(t){var n,e=t.styles.defaultStyle,s="";(null==e?void 0:e.pPr)&&(s=yn(e.pPr.cssStyle));var a="";(null==e?void 0:e.rPr)&&(a=yn(e.rPr.cssStyle));var r=(null===(n=t.settings)||void 0===n?void 0:n.autoHyphenation)?"hyphens: auto;":"",l=t.getClassPrefix();return"\n\n\n  /** docDefaults **/\n  .".concat(l," {\n    --docx-theme-font-minorHAnsi: Calibri,  Helvetica, Arial, 'Helvetica Neue';\n    --docx-theme-font-minorEastAsia: 'PingFang SC', 'Microsoft YaHei', 'Hiragino Sans GB', 'STHeiti',\n    'Microsoft YaHei';\n  }\n\n  .").concat(l," p {\n    margin: 0;\n    padding: 0;\n    line-height: 1.5;\n    ").concat(r,"\n  }\n\n  .").concat(l," .justify {\n    text-align-last: left;\n  }\n\n  .").concat(l," table {\n    border-spacing: 0;\n  }\n\n  .").concat(l," .").concat(l,"-p {\n    ").concat(s,"\n  }\n\n  .").concat(l," .").concat(l,"-r {\n    overflow-wrap: break-word;\n    ").concat(a,"\n  }\n  ")}(y),c=function(t){var n=t.styles.styleMap,e=t.getClassPrefix(),s="";for(var a in n){var r=t.getStyleIdDisplayName(a),l=n[a],o=l.pPr,x="";if(o){var y=yn(o.cssStyle);x="\n      .".concat(e," .").concat(r," {\n        ").concat(y,"\n      }\n      ")}var p="";if(l.rPr){var f=yn(l.rPr.cssStyle);p="\n      .".concat(e," .").concat(r," > .").concat(e,"-r {\n        ").concat(f,"\n      }\n      ")}var c=gn(e,r,l),i=vn(e,r,l.tblStylePr);s+="\n    ".concat(x,"\n    ").concat(p,"\n    ").concat(c,"\n    ").concat(i,"\n    ")}return s}(y),p.textContent="\n  ".concat(f,"\n\n  ").concat(c,"\n  "),p)),dn(t,function(t){var n,e;if(!t)return null;var a=t.fonts;if(!a||!a.length)return null;var r=fn("style"),l="/** embedded fonts **/";try{for(var o=(0,s.Ju)(t.fonts),x=o.next();!x.done;x=o.next()){var y=x.value,p=y.name.replace(/['\\]/g,""),f=y.url;p&&f&&(l+="\n      @font-face {\n        font-family: '".concat(p,"';\n        src: url('").concat(f,"');\n      }\n      "))}}catch(t){n={error:t}}finally{try{x&&!x.done&&(e=o.return)&&e.call(o)}finally{if(n)throw n.error}}return r.innerHTML=l,r}(this.fontTable)),dn(t,o),dn(t,function(t){var n=fn("div");if(Ys(t.footNotes))for(var e in t.footNotes)Zs(t,n,"footnote",e,t.footNotes[e]);if(Ys(t.endNotes))for(var e in t.endNotes||{})Zs(t,n,"endnote",e,t.endNotes[e]);return n.children.length?n:null}(this)),[2]}var y,p,f,c}))}))},t.globalId=0,t}(),ra={Word:aa}}}]);