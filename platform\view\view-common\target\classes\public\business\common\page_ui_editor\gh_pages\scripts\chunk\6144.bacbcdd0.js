(self.webpackChunktansci_boot_ui_editor=self.webpackChunktansci_boot_ui_editor||[]).push([[6144],{36144:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return l},markdown:function(){return u}});var n=r(31635),s=r(96540),o=r(42922),i=r.n(o),a=r(20306),c=i()();function u(e,t){return t&&c.set(t),c.render(e)}c.use(a.html5Media);var l=function(e){function t(t){var r=e.call(this,t)||this;return r.htmlRef=r.htmlRef.bind(r),r}return(0,n.C6)(t,e),t.prototype.htmlRef=function(e){this.dom=e,e&&this._render()},t.prototype.componentDidUpdate=function(e){this.props.content!==e.content&&this._render()},t.prototype._render=function(){return(0,n.sH)(this,void 0,void 0,(function(){var e,t,r;return(0,n.YH)(this,(function(n){return e=this.props,t=e.content,r=e.options,this.dom.innerHTML=u(t,r),"function"==typeof renderMathInElement&&renderMathInElement(this.dom,{delimiters:[{left:"$$",right:"$$",display:!0},{left:"$",right:"$",display:!1}]}),[2]}))}))},t.prototype.render=function(){return s.createElement("div",{"data-testid":"markdown-body",className:"markdown-body",ref:this.htmlRef})},t.defaultProps={content:"",options:{linkify:!0}},t}(s.Component)},42833:function(e,t,r){"use strict";function n(e){return Array.prototype.slice.call(arguments,1).forEach((function(t){t&&Object.keys(t).forEach((function(r){e[r]=t[r]}))})),e}function s(e){return Object.prototype.toString.call(e)}function o(e){return"[object Function]"===s(e)}function i(e){return e.replace(/[.?*+^$[\]\\(){}|-]/g,"\\$&")}var a={fuzzyLink:!0,fuzzyEmail:!0,fuzzyIP:!1},c={"http:":{validate:function(e,t,r){var n=e.slice(t);return r.re.http||(r.re.http=new RegExp("^\\/\\/"+r.re.src_auth+r.re.src_host_port_strict+r.re.src_path,"i")),r.re.http.test(n)?n.match(r.re.http)[0].length:0}},"https:":"http:","ftp:":"http:","//":{validate:function(e,t,r){var n=e.slice(t);return r.re.no_http||(r.re.no_http=new RegExp("^"+r.re.src_auth+"(?:localhost|(?:(?:"+r.re.src_domain+")\\.)+"+r.re.src_domain_root+")"+r.re.src_port+r.re.src_host_terminator+r.re.src_path,"i")),r.re.no_http.test(n)?t>=3&&":"===e[t-3]||t>=3&&"/"===e[t-3]?0:n.match(r.re.no_http)[0].length:0}},"mailto:":{validate:function(e,t,r){var n=e.slice(t);return r.re.mailto||(r.re.mailto=new RegExp("^"+r.re.src_email_name+"@"+r.re.src_host_strict,"i")),r.re.mailto.test(n)?n.match(r.re.mailto)[0].length:0}}},u="a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]",l="biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф".split("|");function p(e){var t=e.re=r(45260)(e.__opts__),n=e.__tlds__.slice();function a(e){return e.replace("%TLDS%",t.src_tlds)}e.onCompile(),e.__tlds_replaced__||n.push(u),n.push(t.src_xn),t.src_tlds=n.join("|"),t.email_fuzzy=RegExp(a(t.tpl_email_fuzzy),"i"),t.link_fuzzy=RegExp(a(t.tpl_link_fuzzy),"i"),t.link_no_ip_fuzzy=RegExp(a(t.tpl_link_no_ip_fuzzy),"i"),t.host_fuzzy_test=RegExp(a(t.tpl_host_fuzzy_test),"i");var c=[];function l(e,t){throw new Error('(LinkifyIt) Invalid schema "'+e+'": '+t)}e.__compiled__={},Object.keys(e.__schemas__).forEach((function(t){var r=e.__schemas__[t];if(null!==r){var n={validate:null,link:null};if(e.__compiled__[t]=n,"[object Object]"===s(r))return"[object RegExp]"!==s(r.validate)?o(r.validate)?n.validate=r.validate:l(t,r):n.validate=function(e){return function(t,r){var n=t.slice(r);return e.test(n)?n.match(e)[0].length:0}}(r.validate),void(o(r.normalize)?n.normalize=r.normalize:r.normalize?l(t,r):n.normalize=function(e,t){t.normalize(e)});!function(e){return"[object String]"===s(e)}(r)?l(t,r):c.push(t)}})),c.forEach((function(t){e.__compiled__[e.__schemas__[t]]&&(e.__compiled__[t].validate=e.__compiled__[e.__schemas__[t]].validate,e.__compiled__[t].normalize=e.__compiled__[e.__schemas__[t]].normalize)})),e.__compiled__[""]={validate:null,normalize:function(e,t){t.normalize(e)}};var p=Object.keys(e.__compiled__).filter((function(t){return t.length>0&&e.__compiled__[t]})).map(i).join("|");e.re.schema_test=RegExp("(^|(?!_)(?:[><｜]|"+t.src_ZPCc+"))("+p+")","i"),e.re.schema_search=RegExp("(^|(?!_)(?:[><｜]|"+t.src_ZPCc+"))("+p+")","ig"),e.re.pretest=RegExp("("+e.re.schema_test.source+")|("+e.re.host_fuzzy_test.source+")|@","i"),function(e){e.__index__=-1,e.__text_cache__=""}(e)}function h(e,t){var r=e.__index__,n=e.__last_index__,s=e.__text_cache__.slice(r,n);this.schema=e.__schema__.toLowerCase(),this.index=r+t,this.lastIndex=n+t,this.raw=s,this.text=s,this.url=s}function f(e,t){var r=new h(e,t);return e.__compiled__[r.schema].normalize(r,e),r}function d(e,t){if(!(this instanceof d))return new d(e,t);var r;t||(r=e,Object.keys(r||{}).reduce((function(e,t){return e||a.hasOwnProperty(t)}),!1)&&(t=e,e={})),this.__opts__=n({},a,t),this.__index__=-1,this.__last_index__=-1,this.__schema__="",this.__text_cache__="",this.__schemas__=n({},c,e),this.__compiled__={},this.__tlds__=l,this.__tlds_replaced__=!1,this.re={},p(this)}d.prototype.add=function(e,t){return this.__schemas__[e]=t,p(this),this},d.prototype.set=function(e){return this.__opts__=n(this.__opts__,e),this},d.prototype.test=function(e){if(this.__text_cache__=e,this.__index__=-1,!e.length)return!1;var t,r,n,s,o,i,a,c;if(this.re.schema_test.test(e))for((a=this.re.schema_search).lastIndex=0;null!==(t=a.exec(e));)if(s=this.testSchemaAt(e,t[2],a.lastIndex)){this.__schema__=t[2],this.__index__=t.index+t[1].length,this.__last_index__=t.index+t[0].length+s;break}return this.__opts__.fuzzyLink&&this.__compiled__["http:"]&&(c=e.search(this.re.host_fuzzy_test))>=0&&(this.__index__<0||c<this.__index__)&&null!==(r=e.match(this.__opts__.fuzzyIP?this.re.link_fuzzy:this.re.link_no_ip_fuzzy))&&(o=r.index+r[1].length,(this.__index__<0||o<this.__index__)&&(this.__schema__="",this.__index__=o,this.__last_index__=r.index+r[0].length)),this.__opts__.fuzzyEmail&&this.__compiled__["mailto:"]&&e.indexOf("@")>=0&&null!==(n=e.match(this.re.email_fuzzy))&&(o=n.index+n[1].length,i=n.index+n[0].length,(this.__index__<0||o<this.__index__||o===this.__index__&&i>this.__last_index__)&&(this.__schema__="mailto:",this.__index__=o,this.__last_index__=i)),this.__index__>=0},d.prototype.pretest=function(e){return this.re.pretest.test(e)},d.prototype.testSchemaAt=function(e,t,r){return this.__compiled__[t.toLowerCase()]?this.__compiled__[t.toLowerCase()].validate(e,r,this):0},d.prototype.match=function(e){var t=0,r=[];this.__index__>=0&&this.__text_cache__===e&&(r.push(f(this,t)),t=this.__last_index__);for(var n=t?e.slice(t):e;this.test(n);)r.push(f(this,t)),n=n.slice(this.__last_index__),t+=this.__last_index__;return r.length?r:null},d.prototype.tlds=function(e,t){return e=Array.isArray(e)?e:[e],t?(this.__tlds__=this.__tlds__.concat(e).sort().filter((function(e,t,r){return e!==r[t-1]})).reverse(),p(this),this):(this.__tlds__=e.slice(),this.__tlds_replaced__=!0,p(this),this)},d.prototype.normalize=function(e){e.schema||(e.url="http://"+e.url),"mailto:"!==e.schema||/^mailto:/i.test(e.url)||(e.url="mailto:"+e.url)},d.prototype.onCompile=function(){},e.exports=d},45260:function(e,t,r){"use strict";e.exports=function(e){var t={};t.src_Any=r(76027).source,t.src_Cc=r(50592).source,t.src_Z=r(23978).source,t.src_P=r(2828).source,t.src_ZPCc=[t.src_Z,t.src_P,t.src_Cc].join("|"),t.src_ZCc=[t.src_Z,t.src_Cc].join("|");return t.src_pseudo_letter="(?:(?![><｜]|"+t.src_ZPCc+")"+t.src_Any+")",t.src_ip4="(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)",t.src_auth="(?:(?:(?!"+t.src_ZCc+"|[@/\\[\\]()]).)+@)?",t.src_port="(?::(?:6(?:[0-4]\\d{3}|5(?:[0-4]\\d{2}|5(?:[0-2]\\d|3[0-5])))|[1-5]?\\d{1,4}))?",t.src_host_terminator="(?=$|[><｜]|"+t.src_ZPCc+")(?!-|_|:\\d|\\.-|\\.(?!$|"+t.src_ZPCc+"))",t.src_path="(?:[/?#](?:(?!"+t.src_ZCc+"|[><｜]|[()[\\]{}.,\"'?!\\-;]).|\\[(?:(?!"+t.src_ZCc+"|\\]).)*\\]|\\((?:(?!"+t.src_ZCc+"|[)]).)*\\)|\\{(?:(?!"+t.src_ZCc+'|[}]).)*\\}|\\"(?:(?!'+t.src_ZCc+'|["]).)+\\"|\\\'(?:(?!'+t.src_ZCc+"|[']).)+\\'|\\'(?="+t.src_pseudo_letter+"|[-]).|\\.{2,}[a-zA-Z0-9%/&]|\\.(?!"+t.src_ZCc+"|[.]).|"+(e&&e["---"]?"\\-(?!--(?:[^-]|$))(?:-*)|":"\\-+|")+",(?!"+t.src_ZCc+").|;(?!"+t.src_ZCc+").|\\!+(?!"+t.src_ZCc+"|[!]).|\\?(?!"+t.src_ZCc+"|[?]).)+|\\/)?",t.src_email_name='[\\-;:&=\\+\\$,\\.a-zA-Z0-9_][\\-;:&=\\+\\$,\\"\\.a-zA-Z0-9_]*',t.src_xn="xn--[a-z0-9\\-]{1,59}",t.src_domain_root="(?:"+t.src_xn+"|"+t.src_pseudo_letter+"{1,63})",t.src_domain="(?:"+t.src_xn+"|(?:"+t.src_pseudo_letter+")|(?:"+t.src_pseudo_letter+"(?:-|"+t.src_pseudo_letter+"){0,61}"+t.src_pseudo_letter+"))",t.src_host="(?:(?:(?:(?:"+t.src_domain+")\\.)*"+t.src_domain+"))",t.tpl_host_fuzzy="(?:"+t.src_ip4+"|(?:(?:(?:"+t.src_domain+")\\.)+(?:%TLDS%)))",t.tpl_host_no_ip_fuzzy="(?:(?:(?:"+t.src_domain+")\\.)+(?:%TLDS%))",t.src_host_strict=t.src_host+t.src_host_terminator,t.tpl_host_fuzzy_strict=t.tpl_host_fuzzy+t.src_host_terminator,t.src_host_port_strict=t.src_host+t.src_port+t.src_host_terminator,t.tpl_host_port_fuzzy_strict=t.tpl_host_fuzzy+t.src_port+t.src_host_terminator,t.tpl_host_port_no_ip_fuzzy_strict=t.tpl_host_no_ip_fuzzy+t.src_port+t.src_host_terminator,t.tpl_host_fuzzy_test="localhost|www\\.|\\.\\d{1,3}\\.|(?:\\.(?:%TLDS%)(?:"+t.src_ZPCc+"|>|$))",t.tpl_email_fuzzy='(^|[><｜]|"|\\(|'+t.src_ZCc+")("+t.src_email_name+"@"+t.tpl_host_fuzzy_strict+")",t.tpl_link_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+t.src_ZPCc+"))((?![$+<=>^`|｜])"+t.tpl_host_port_fuzzy_strict+t.src_path+")",t.tpl_link_no_ip_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+t.src_ZPCc+"))((?![$+<=>^`|｜])"+t.tpl_host_port_no_ip_fuzzy_strict+t.src_path+")",t}},20306:function(e){"use strict";const t=["aac","m4a","mp3","oga","ogg","wav"],r=["mp4","m4v","ogv","webm","mpg","mpeg"];let n={en:{"html5 video not supported":"Your browser does not support playing HTML5 video.","html5 audio not supported":"Your browser does not support playing HTML5 audio.","html5 media fallback link":'You can <a href="%s" download>download the file</a> instead.',"html5 media description":"Here is a description of the content: %s"}},s=function(e,t,r){if(n[e]&&n[e][t]||(e="en"),!n[e])return"";let s=n[e][t]||"";if(r)for(let e of r)s=s.replace("%s",e);return s};function o(e){const n=e.match(/\.([^/.]+)$/);if(null===n)return"image";const s=n[1];return-1!=t.indexOf(s.toLowerCase())?"audio":-1!=r.indexOf(s.toLowerCase())?"video":"image"}e.exports={html5Media:function(e,t={}){t.messages&&(n=t.messages),t.translateFn&&(s=t.translateFn);const r=void 0!==t.videoAttrs?t.videoAttrs:'controls class="html5-video-player"',i=void 0!==t.audioAttrs?t.audioAttrs:'controls class="html5-audio-player"';e.inline.ruler.at("image",((t,r)=>function(e,t,r){let n,s,i,a,c,u,l,p,h,f,d,m,g,_="",k=e.pos,b=e.posMax;if(33!==e.src.charCodeAt(e.pos)||91!==e.src.charCodeAt(e.pos+1))return!1;if(u=e.pos+2,c=e.md.helpers.parseLinkLabel(e,e.pos+1,!1),c<0)return!1;if(l=c+1,l<b&&40===e.src.charCodeAt(l)){for(l++;l<b&&(s=e.src.charCodeAt(l),r.utils.isSpace(s)||10===s);l++);if(l>=b)return!1;for(g=l,h=e.md.helpers.parseLinkDestination(e.src,l,e.posMax),h.ok&&(_=e.md.normalizeLink(h.str),e.md.validateLink(_)?l=h.pos:_=""),g=l;l<b&&(s=e.src.charCodeAt(l),r.utils.isSpace(s)||10===s);l++);if(h=e.md.helpers.parseLinkTitle(e.src,l,e.posMax),l<b&&g!==l&&h.ok)for(f=h.str,l=h.pos;l<b&&(s=e.src.charCodeAt(l),r.utils.isSpace(s)||10===s);l++);else f="";if(l>=b||41!==e.src.charCodeAt(l))return e.pos=k,!1;l++}else{if(void 0===e.env.references)return!1;if(l<b&&91===e.src.charCodeAt(l)?(g=l+1,l=e.md.helpers.parseLinkLabel(e,l),l>=0?a=e.src.slice(g,l++):l=c+1):l=c+1,a||(a=e.src.slice(u,c)),p=e.env.references[r.utils.normalizeReference(a)],!p)return e.pos=k,!1;_=p.href,f=p.title}if(e.pos=l,e.posMax=b,t)return!0;i=e.src.slice(u,c),e.md.inline.parse(i,e.md,e.env,m=[]);const v=o(_),C="image"==v?"img":v;return d=e.push(v,C,0),d.attrs=n=[["src",_]],"image"==v&&n.push(["alt",""]),d.children=m,d.content=i,f&&n.push(["title",f]),e.pos=l,e.posMax=b,!0}(t,r,e))),e.renderer.rules.video=e.renderer.rules.audio=(t,n,o,a)=>(o.html5Media={videoAttrs:r,audioAttrs:i},function(e,t,r,n,o){const i=e[t],a=i.type;if("video"!==a&&"audio"!==a)return"";let c=r.html5Media[`${a}Attrs`].trim();c&&(c=" "+c);const u=i.attrs[i.attrIndex("src")][1];return`<${a} src="${u}"${-1!=i.attrIndex("title")?` title="${o.utils.escapeHtml(i.attrs[i.attrIndex("title")][1])}"`:""}${c}>\n${s(n.language,`html5 ${a} not supported`)+"\n"+s(n.language,"html5 media fallback link",[u])}${i.content?"\n"+s(n.language,"html5 media description",[o.utils.escapeHtml(i.content)]):""}\n</${a}>`}(t,n,o,a,e))},messages:n,guessMediaType:o}},42922:function(e,t,r){"use strict";e.exports=r(91246)},68359:function(e,t,r){"use strict";e.exports=r(24357)},71358:function(e){"use strict";e.exports=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","section","source","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"]},98938:function(e){"use strict";var t="<[A-Za-z][A-Za-z0-9\\-]*(?:\\s+[a-zA-Z_:][a-zA-Z0-9:._-]*(?:\\s*=\\s*(?:[^\"'=<>`\\x00-\\x20]+|'[^']*'|\"[^\"]*\"))?)*\\s*\\/?>",r="<\\/[A-Za-z][A-Za-z0-9\\-]*\\s*>",n=new RegExp("^(?:"+t+"|"+r+"|\x3c!----\x3e|\x3c!--(?:-?[^>-])(?:-?[^-])*--\x3e|<[?][\\s\\S]*?[?]>|<![A-Z]+\\s+[^>]*>|<!\\[CDATA\\[[\\s\\S]*?\\]\\]>)"),s=new RegExp("^(?:"+t+"|"+r+")");e.exports.l=n,e.exports.p=s},49963:function(e,t,r){"use strict";var n=Object.prototype.hasOwnProperty;function s(e,t){return n.call(e,t)}function o(e){return!(e>=55296&&e<=57343||e>=64976&&e<=65007||!(65535&~e&&65534!=(65535&e))||e>=0&&e<=8||11===e||e>=14&&e<=31||e>=127&&e<=159||e>1114111)}function i(e){if(e>65535){var t=55296+((e-=65536)>>10),r=56320+(1023&e);return String.fromCharCode(t,r)}return String.fromCharCode(e)}var a=/\\([!"#$%&'()*+,\-.\/:;<=>?@[\\\]^_`{|}~])/g,c=new RegExp(a.source+"|"+/&([a-z#][a-z0-9]{1,31});/gi.source,"gi"),u=/^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))/i,l=r(68359),p=/[&<>"]/,h=/[&<>"]/g,f={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};function d(e){return f[e]}var m=/[.?*+^$[\]\\(){}|-]/g,g=r(2828);t.lib={},t.lib.mdurl=r(86781),t.lib.ucmicro=r(39295),t.assign=function(e){return Array.prototype.slice.call(arguments,1).forEach((function(t){if(t){if("object"!=typeof t)throw new TypeError(t+"must be object");Object.keys(t).forEach((function(r){e[r]=t[r]}))}})),e},t.isString=function(e){return"[object String]"===function(e){return Object.prototype.toString.call(e)}(e)},t.has=s,t.unescapeMd=function(e){return e.indexOf("\\")<0?e:e.replace(a,"$1")},t.unescapeAll=function(e){return e.indexOf("\\")<0&&e.indexOf("&")<0?e:e.replace(c,(function(e,t,r){return t||function(e,t){var r=0;return s(l,t)?l[t]:35===t.charCodeAt(0)&&u.test(t)&&o(r="x"===t[1].toLowerCase()?parseInt(t.slice(2),16):parseInt(t.slice(1),10))?i(r):e}(e,r)}))},t.isValidEntityCode=o,t.fromCodePoint=i,t.escapeHtml=function(e){return p.test(e)?e.replace(h,d):e},t.arrayReplaceAt=function(e,t,r){return[].concat(e.slice(0,t),r,e.slice(t+1))},t.isSpace=function(e){switch(e){case 9:case 32:return!0}return!1},t.isWhiteSpace=function(e){if(e>=8192&&e<=8202)return!0;switch(e){case 9:case 10:case 11:case 12:case 13:case 32:case 160:case 5760:case 8239:case 8287:case 12288:return!0}return!1},t.isMdAsciiPunct=function(e){switch(e){case 33:case 34:case 35:case 36:case 37:case 38:case 39:case 40:case 41:case 42:case 43:case 44:case 45:case 46:case 47:case 58:case 59:case 60:case 61:case 62:case 63:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 124:case 125:case 126:return!0;default:return!1}},t.isPunctChar=function(e){return g.test(e)},t.escapeRE=function(e){return e.replace(m,"\\$&")},t.normalizeReference=function(e){return e=e.trim().replace(/\s+/g," "),"Ṿ"==="ẞ".toLowerCase()&&(e=e.replace(/ẞ/g,"ß")),e.toLowerCase().toUpperCase()}},83592:function(e,t,r){"use strict";t.parseLinkLabel=r(31947),t.parseLinkDestination=r(58949),t.parseLinkTitle=r(27311)},58949:function(e,t,r){"use strict";var n=r(49963).unescapeAll;e.exports=function(e,t,r){var s,o,i=t,a={ok:!1,pos:0,lines:0,str:""};if(60===e.charCodeAt(t)){for(t++;t<r;){if(10===(s=e.charCodeAt(t)))return a;if(60===s)return a;if(62===s)return a.pos=t+1,a.str=n(e.slice(i+1,t)),a.ok=!0,a;92===s&&t+1<r?t+=2:t++}return a}for(o=0;t<r&&32!==(s=e.charCodeAt(t))&&!(s<32||127===s);)if(92===s&&t+1<r){if(32===e.charCodeAt(t+1))break;t+=2}else{if(40===s&&++o>32)return a;if(41===s){if(0===o)break;o--}t++}return i===t||0!==o||(a.str=n(e.slice(i,t)),a.lines=0,a.pos=t,a.ok=!0),a}},31947:function(e){"use strict";e.exports=function(e,t,r){var n,s,o,i,a=-1,c=e.posMax,u=e.pos;for(e.pos=t+1,n=1;e.pos<c;){if(93===(o=e.src.charCodeAt(e.pos))&&0==--n){s=!0;break}if(i=e.pos,e.md.inline.skipToken(e),91===o)if(i===e.pos-1)n++;else if(r)return e.pos=u,-1}return s&&(a=e.pos),e.pos=u,a}},27311:function(e,t,r){"use strict";var n=r(49963).unescapeAll;e.exports=function(e,t,r){var s,o,i=0,a=t,c={ok:!1,pos:0,lines:0,str:""};if(t>=r)return c;if(34!==(o=e.charCodeAt(t))&&39!==o&&40!==o)return c;for(t++,40===o&&(o=41);t<r;){if((s=e.charCodeAt(t))===o)return c.pos=t+1,c.lines=i,c.str=n(e.slice(a+1,t)),c.ok=!0,c;if(40===s&&41===o)return c;10===s?i++:92===s&&t+1<r&&(t++,10===e.charCodeAt(t)&&i++),t++}return c}},91246:function(e,t,r){"use strict";var n=r(49963),s=r(83592),o=r(14847),i=r(6321),a=r(31525),c=r(13171),u=r(42833),l=r(86781),p=r(48379),h={default:r(25092),zero:r(24719),commonmark:r(30073)},f=/^(vbscript|javascript|file|data):/,d=/^data:image\/(gif|png|jpeg|webp);/;function m(e){var t=e.trim().toLowerCase();return!f.test(t)||!!d.test(t)}var g=["http:","https:","mailto:"];function _(e){var t=l.parse(e,!0);if(t.hostname&&(!t.protocol||g.indexOf(t.protocol)>=0))try{t.hostname=p.toASCII(t.hostname)}catch(e){}return l.encode(l.format(t))}function k(e){var t=l.parse(e,!0);if(t.hostname&&(!t.protocol||g.indexOf(t.protocol)>=0))try{t.hostname=p.toUnicode(t.hostname)}catch(e){}return l.decode(l.format(t),l.decode.defaultChars+"%")}function b(e,t){if(!(this instanceof b))return new b(e,t);t||n.isString(e)||(t=e||{},e="default"),this.inline=new c,this.block=new a,this.core=new i,this.renderer=new o,this.linkify=new u,this.validateLink=m,this.normalizeLink=_,this.normalizeLinkText=k,this.utils=n,this.helpers=n.assign({},s),this.options={},this.configure(e),t&&this.set(t)}b.prototype.set=function(e){return n.assign(this.options,e),this},b.prototype.configure=function(e){var t,r=this;if(n.isString(e)&&!(e=h[t=e]))throw new Error('Wrong `markdown-it` preset "'+t+'", check name');if(!e)throw new Error("Wrong `markdown-it` preset, can't be empty");return e.options&&r.set(e.options),e.components&&Object.keys(e.components).forEach((function(t){e.components[t].rules&&r[t].ruler.enableOnly(e.components[t].rules),e.components[t].rules2&&r[t].ruler2.enableOnly(e.components[t].rules2)})),this},b.prototype.enable=function(e,t){var r=[];Array.isArray(e)||(e=[e]),["core","block","inline"].forEach((function(t){r=r.concat(this[t].ruler.enable(e,!0))}),this),r=r.concat(this.inline.ruler2.enable(e,!0));var n=e.filter((function(e){return r.indexOf(e)<0}));if(n.length&&!t)throw new Error("MarkdownIt. Failed to enable unknown rule(s): "+n);return this},b.prototype.disable=function(e,t){var r=[];Array.isArray(e)||(e=[e]),["core","block","inline"].forEach((function(t){r=r.concat(this[t].ruler.disable(e,!0))}),this),r=r.concat(this.inline.ruler2.disable(e,!0));var n=e.filter((function(e){return r.indexOf(e)<0}));if(n.length&&!t)throw new Error("MarkdownIt. Failed to disable unknown rule(s): "+n);return this},b.prototype.use=function(e){var t=[this].concat(Array.prototype.slice.call(arguments,1));return e.apply(e,t),this},b.prototype.parse=function(e,t){if("string"!=typeof e)throw new Error("Input data should be a String");var r=new this.core.State(e,this,t);return this.core.process(r),r.tokens},b.prototype.render=function(e,t){return t=t||{},this.renderer.render(this.parse(e,t),this.options,t)},b.prototype.parseInline=function(e,t){var r=new this.core.State(e,this,t);return r.inlineMode=!0,this.core.process(r),r.tokens},b.prototype.renderInline=function(e,t){return t=t||{},this.renderer.render(this.parseInline(e,t),this.options,t)},e.exports=b},31525:function(e,t,r){"use strict";var n=r(12378),s=[["table",r(24752),["paragraph","reference"]],["code",r(15711)],["fence",r(52373),["paragraph","reference","blockquote","list"]],["blockquote",r(82941),["paragraph","reference","blockquote","list"]],["hr",r(88e3),["paragraph","reference","blockquote","list"]],["list",r(36686),["paragraph","reference","blockquote"]],["reference",r(86897)],["html_block",r(81857),["paragraph","reference","blockquote"]],["heading",r(50634),["paragraph","reference","blockquote"]],["lheading",r(39648)],["paragraph",r(87046)]];function o(){this.ruler=new n;for(var e=0;e<s.length;e++)this.ruler.push(s[e][0],s[e][1],{alt:(s[e][2]||[]).slice()})}o.prototype.tokenize=function(e,t,r){for(var n,s=this.ruler.getRules(""),o=s.length,i=t,a=!1,c=e.md.options.maxNesting;i<r&&(e.line=i=e.skipEmptyLines(i),!(i>=r))&&!(e.sCount[i]<e.blkIndent);){if(e.level>=c){e.line=r;break}for(n=0;n<o&&!s[n](e,i,r,!1);n++);e.tight=!a,e.isEmpty(e.line-1)&&(a=!0),(i=e.line)<r&&e.isEmpty(i)&&(a=!0,i++,e.line=i)}},o.prototype.parse=function(e,t,r,n){var s;e&&(s=new this.State(e,t,r,n),this.tokenize(s,s.line,s.lineMax))},o.prototype.State=r(7759),e.exports=o},6321:function(e,t,r){"use strict";var n=r(12378),s=[["normalize",r(803)],["block",r(73437)],["inline",r(3547)],["linkify",r(986)],["replacements",r(10203)],["smartquotes",r(65260)]];function o(){this.ruler=new n;for(var e=0;e<s.length;e++)this.ruler.push(s[e][0],s[e][1])}o.prototype.process=function(e){var t,r,n;for(t=0,r=(n=this.ruler.getRules("")).length;t<r;t++)n[t](e)},o.prototype.State=r(1839),e.exports=o},13171:function(e,t,r){"use strict";var n=r(12378),s=[["text",r(82015)],["newline",r(22534)],["escape",r(21231)],["backticks",r(26757)],["strikethrough",r(97141).q],["emphasis",r(93898).q],["link",r(56552)],["image",r(23707)],["autolink",r(86955)],["html_inline",r(30961)],["entity",r(18103)]],o=[["balance_pairs",r(65940)],["strikethrough",r(97141).g],["emphasis",r(93898).g],["text_collapse",r(77729)]];function i(){var e;for(this.ruler=new n,e=0;e<s.length;e++)this.ruler.push(s[e][0],s[e][1]);for(this.ruler2=new n,e=0;e<o.length;e++)this.ruler2.push(o[e][0],o[e][1])}i.prototype.skipToken=function(e){var t,r,n=e.pos,s=this.ruler.getRules(""),o=s.length,i=e.md.options.maxNesting,a=e.cache;if(void 0===a[n]){if(e.level<i)for(r=0;r<o&&(e.level++,t=s[r](e,!0),e.level--,!t);r++);else e.pos=e.posMax;t||e.pos++,a[n]=e.pos}else e.pos=a[n]},i.prototype.tokenize=function(e){for(var t,r,n=this.ruler.getRules(""),s=n.length,o=e.posMax,i=e.md.options.maxNesting;e.pos<o;){if(e.level<i)for(r=0;r<s&&!(t=n[r](e,!1));r++);if(t){if(e.pos>=o)break}else e.pending+=e.src[e.pos++]}e.pending&&e.pushPending()},i.prototype.parse=function(e,t,r,n){var s,o,i,a=new this.State(e,t,r,n);for(this.tokenize(a),i=(o=this.ruler2.getRules("")).length,s=0;s<i;s++)o[s](a)},i.prototype.State=r(60979),e.exports=i},30073:function(e){"use strict";e.exports={options:{html:!0,xhtmlOut:!0,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline"]},block:{rules:["blockquote","code","fence","heading","hr","html_block","lheading","list","reference","paragraph"]},inline:{rules:["autolink","backticks","emphasis","entity","escape","html_inline","image","link","newline","text"],rules2:["balance_pairs","emphasis","text_collapse"]}}}},25092:function(e){"use strict";e.exports={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:100},components:{core:{},block:{},inline:{}}}},24719:function(e){"use strict";e.exports={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline"]},block:{rules:["paragraph"]},inline:{rules:["text"],rules2:["balance_pairs","text_collapse"]}}}},14847:function(e,t,r){"use strict";var n=r(49963).assign,s=r(49963).unescapeAll,o=r(49963).escapeHtml,i={};function a(){this.rules=n({},i)}i.code_inline=function(e,t,r,n,s){var i=e[t];return"<code"+s.renderAttrs(i)+">"+o(e[t].content)+"</code>"},i.code_block=function(e,t,r,n,s){var i=e[t];return"<pre"+s.renderAttrs(i)+"><code>"+o(e[t].content)+"</code></pre>\n"},i.fence=function(e,t,r,n,i){var a,c,u,l,p,h=e[t],f=h.info?s(h.info).trim():"",d="",m="";return f&&(d=(u=f.split(/(\s+)/g))[0],m=u.slice(2).join("")),0===(a=r.highlight&&r.highlight(h.content,d,m)||o(h.content)).indexOf("<pre")?a+"\n":f?(c=h.attrIndex("class"),l=h.attrs?h.attrs.slice():[],c<0?l.push(["class",r.langPrefix+d]):(l[c]=l[c].slice(),l[c][1]+=" "+r.langPrefix+d),p={attrs:l},"<pre><code"+i.renderAttrs(p)+">"+a+"</code></pre>\n"):"<pre><code"+i.renderAttrs(h)+">"+a+"</code></pre>\n"},i.image=function(e,t,r,n,s){var o=e[t];return o.attrs[o.attrIndex("alt")][1]=s.renderInlineAsText(o.children,r,n),s.renderToken(e,t,r)},i.hardbreak=function(e,t,r){return r.xhtmlOut?"<br />\n":"<br>\n"},i.softbreak=function(e,t,r){return r.breaks?r.xhtmlOut?"<br />\n":"<br>\n":"\n"},i.text=function(e,t){return o(e[t].content)},i.html_block=function(e,t){return e[t].content},i.html_inline=function(e,t){return e[t].content},a.prototype.renderAttrs=function(e){var t,r,n;if(!e.attrs)return"";for(n="",t=0,r=e.attrs.length;t<r;t++)n+=" "+o(e.attrs[t][0])+'="'+o(e.attrs[t][1])+'"';return n},a.prototype.renderToken=function(e,t,r){var n,s="",o=!1,i=e[t];return i.hidden?"":(i.block&&-1!==i.nesting&&t&&e[t-1].hidden&&(s+="\n"),s+=(-1===i.nesting?"</":"<")+i.tag,s+=this.renderAttrs(i),0===i.nesting&&r.xhtmlOut&&(s+=" /"),i.block&&(o=!0,1===i.nesting&&t+1<e.length&&("inline"===(n=e[t+1]).type||n.hidden||-1===n.nesting&&n.tag===i.tag)&&(o=!1)),s+=o?">\n":">")},a.prototype.renderInline=function(e,t,r){for(var n,s="",o=this.rules,i=0,a=e.length;i<a;i++)void 0!==o[n=e[i].type]?s+=o[n](e,i,t,r,this):s+=this.renderToken(e,i,t);return s},a.prototype.renderInlineAsText=function(e,t,r){for(var n="",s=0,o=e.length;s<o;s++)"text"===e[s].type?n+=e[s].content:"image"===e[s].type?n+=this.renderInlineAsText(e[s].children,t,r):"softbreak"===e[s].type&&(n+="\n");return n},a.prototype.render=function(e,t,r){var n,s,o,i="",a=this.rules;for(n=0,s=e.length;n<s;n++)"inline"===(o=e[n].type)?i+=this.renderInline(e[n].children,t,r):void 0!==a[o]?i+=a[e[n].type](e,n,t,r,this):i+=this.renderToken(e,n,t,r);return i},e.exports=a},12378:function(e){"use strict";function t(){this.__rules__=[],this.__cache__=null}t.prototype.__find__=function(e){for(var t=0;t<this.__rules__.length;t++)if(this.__rules__[t].name===e)return t;return-1},t.prototype.__compile__=function(){var e=this,t=[""];e.__rules__.forEach((function(e){e.enabled&&e.alt.forEach((function(e){t.indexOf(e)<0&&t.push(e)}))})),e.__cache__={},t.forEach((function(t){e.__cache__[t]=[],e.__rules__.forEach((function(r){r.enabled&&(t&&r.alt.indexOf(t)<0||e.__cache__[t].push(r.fn))}))}))},t.prototype.at=function(e,t,r){var n=this.__find__(e),s=r||{};if(-1===n)throw new Error("Parser rule not found: "+e);this.__rules__[n].fn=t,this.__rules__[n].alt=s.alt||[],this.__cache__=null},t.prototype.before=function(e,t,r,n){var s=this.__find__(e),o=n||{};if(-1===s)throw new Error("Parser rule not found: "+e);this.__rules__.splice(s,0,{name:t,enabled:!0,fn:r,alt:o.alt||[]}),this.__cache__=null},t.prototype.after=function(e,t,r,n){var s=this.__find__(e),o=n||{};if(-1===s)throw new Error("Parser rule not found: "+e);this.__rules__.splice(s+1,0,{name:t,enabled:!0,fn:r,alt:o.alt||[]}),this.__cache__=null},t.prototype.push=function(e,t,r){var n=r||{};this.__rules__.push({name:e,enabled:!0,fn:t,alt:n.alt||[]}),this.__cache__=null},t.prototype.enable=function(e,t){Array.isArray(e)||(e=[e]);var r=[];return e.forEach((function(e){var n=this.__find__(e);if(n<0){if(t)return;throw new Error("Rules manager: invalid rule name "+e)}this.__rules__[n].enabled=!0,r.push(e)}),this),this.__cache__=null,r},t.prototype.enableOnly=function(e,t){Array.isArray(e)||(e=[e]),this.__rules__.forEach((function(e){e.enabled=!1})),this.enable(e,t)},t.prototype.disable=function(e,t){Array.isArray(e)||(e=[e]);var r=[];return e.forEach((function(e){var n=this.__find__(e);if(n<0){if(t)return;throw new Error("Rules manager: invalid rule name "+e)}this.__rules__[n].enabled=!1,r.push(e)}),this),this.__cache__=null,r},t.prototype.getRules=function(e){return null===this.__cache__&&this.__compile__(),this.__cache__[e]||[]},e.exports=t},82941:function(e,t,r){"use strict";var n=r(49963).isSpace;e.exports=function(e,t,r,s){var o,i,a,c,u,l,p,h,f,d,m,g,_,k,b,v,C,y,x,A,w=e.lineMax,D=e.bMarks[t]+e.tShift[t],E=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;if(62!==e.src.charCodeAt(D++))return!1;if(s)return!0;for(c=f=e.sCount[t]+1,32===e.src.charCodeAt(D)?(D++,c++,f++,o=!1,v=!0):9===e.src.charCodeAt(D)?(v=!0,(e.bsCount[t]+f)%4==3?(D++,c++,f++,o=!1):o=!0):v=!1,d=[e.bMarks[t]],e.bMarks[t]=D;D<E&&(i=e.src.charCodeAt(D),n(i));)9===i?f+=4-(f+e.bsCount[t]+(o?1:0))%4:f++,D++;for(m=[e.bsCount[t]],e.bsCount[t]=e.sCount[t]+1+(v?1:0),l=D>=E,k=[e.sCount[t]],e.sCount[t]=f-c,b=[e.tShift[t]],e.tShift[t]=D-e.bMarks[t],y=e.md.block.ruler.getRules("blockquote"),_=e.parentType,e.parentType="blockquote",h=t+1;h<r&&(A=e.sCount[h]<e.blkIndent,!((D=e.bMarks[h]+e.tShift[h])>=(E=e.eMarks[h])));h++)if(62!==e.src.charCodeAt(D++)||A){if(l)break;for(C=!1,a=0,u=y.length;a<u;a++)if(y[a](e,h,r,!0)){C=!0;break}if(C){e.lineMax=h,0!==e.blkIndent&&(d.push(e.bMarks[h]),m.push(e.bsCount[h]),b.push(e.tShift[h]),k.push(e.sCount[h]),e.sCount[h]-=e.blkIndent);break}d.push(e.bMarks[h]),m.push(e.bsCount[h]),b.push(e.tShift[h]),k.push(e.sCount[h]),e.sCount[h]=-1}else{for(c=f=e.sCount[h]+1,32===e.src.charCodeAt(D)?(D++,c++,f++,o=!1,v=!0):9===e.src.charCodeAt(D)?(v=!0,(e.bsCount[h]+f)%4==3?(D++,c++,f++,o=!1):o=!0):v=!1,d.push(e.bMarks[h]),e.bMarks[h]=D;D<E&&(i=e.src.charCodeAt(D),n(i));)9===i?f+=4-(f+e.bsCount[h]+(o?1:0))%4:f++,D++;l=D>=E,m.push(e.bsCount[h]),e.bsCount[h]=e.sCount[h]+1+(v?1:0),k.push(e.sCount[h]),e.sCount[h]=f-c,b.push(e.tShift[h]),e.tShift[h]=D-e.bMarks[h]}for(g=e.blkIndent,e.blkIndent=0,(x=e.push("blockquote_open","blockquote",1)).markup=">",x.map=p=[t,0],e.md.block.tokenize(e,t,h),(x=e.push("blockquote_close","blockquote",-1)).markup=">",e.lineMax=w,e.parentType=_,p[1]=e.line,a=0;a<b.length;a++)e.bMarks[a+t]=d[a],e.tShift[a+t]=b[a],e.sCount[a+t]=k[a],e.bsCount[a+t]=m[a];return e.blkIndent=g,!0}},15711:function(e){"use strict";e.exports=function(e,t,r){var n,s,o;if(e.sCount[t]-e.blkIndent<4)return!1;for(s=n=t+1;n<r;)if(e.isEmpty(n))n++;else{if(!(e.sCount[n]-e.blkIndent>=4))break;s=++n}return e.line=s,(o=e.push("code_block","code",0)).content=e.getLines(t,s,4+e.blkIndent,!1)+"\n",o.map=[t,e.line],!0}},52373:function(e){"use strict";e.exports=function(e,t,r,n){var s,o,i,a,c,u,l,p=!1,h=e.bMarks[t]+e.tShift[t],f=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;if(h+3>f)return!1;if(126!==(s=e.src.charCodeAt(h))&&96!==s)return!1;if(c=h,(o=(h=e.skipChars(h,s))-c)<3)return!1;if(l=e.src.slice(c,h),i=e.src.slice(h,f),96===s&&i.indexOf(String.fromCharCode(s))>=0)return!1;if(n)return!0;for(a=t;!(++a>=r||(h=c=e.bMarks[a]+e.tShift[a])<(f=e.eMarks[a])&&e.sCount[a]<e.blkIndent);)if(e.src.charCodeAt(h)===s&&!(e.sCount[a]-e.blkIndent>=4||(h=e.skipChars(h,s))-c<o||(h=e.skipSpaces(h))<f)){p=!0;break}return o=e.sCount[t],e.line=a+(p?1:0),(u=e.push("fence","code",0)).info=i,u.content=e.getLines(t+1,a,o,!0),u.markup=l,u.map=[t,e.line],!0}},50634:function(e,t,r){"use strict";var n=r(49963).isSpace;e.exports=function(e,t,r,s){var o,i,a,c,u=e.bMarks[t]+e.tShift[t],l=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;if(35!==(o=e.src.charCodeAt(u))||u>=l)return!1;for(i=1,o=e.src.charCodeAt(++u);35===o&&u<l&&i<=6;)i++,o=e.src.charCodeAt(++u);return!(i>6||u<l&&!n(o)||(s||(l=e.skipSpacesBack(l,u),(a=e.skipCharsBack(l,35,u))>u&&n(e.src.charCodeAt(a-1))&&(l=a),e.line=t+1,(c=e.push("heading_open","h"+String(i),1)).markup="########".slice(0,i),c.map=[t,e.line],(c=e.push("inline","",0)).content=e.src.slice(u,l).trim(),c.map=[t,e.line],c.children=[],(c=e.push("heading_close","h"+String(i),-1)).markup="########".slice(0,i)),0))}},88e3:function(e,t,r){"use strict";var n=r(49963).isSpace;e.exports=function(e,t,r,s){var o,i,a,c,u=e.bMarks[t]+e.tShift[t],l=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;if(42!==(o=e.src.charCodeAt(u++))&&45!==o&&95!==o)return!1;for(i=1;u<l;){if((a=e.src.charCodeAt(u++))!==o&&!n(a))return!1;a===o&&i++}return!(i<3||(s||(e.line=t+1,(c=e.push("hr","hr",0)).map=[t,e.line],c.markup=Array(i+1).join(String.fromCharCode(o))),0))}},81857:function(e,t,r){"use strict";var n=r(71358),s=r(98938).p,o=[[/^<(script|pre|style|textarea)(?=(\s|>|$))/i,/<\/(script|pre|style|textarea)>/i,!0],[/^<!--/,/-->/,!0],[/^<\?/,/\?>/,!0],[/^<![A-Z]/,/>/,!0],[/^<!\[CDATA\[/,/\]\]>/,!0],[new RegExp("^</?("+n.join("|")+")(?=(\\s|/?>|$))","i"),/^$/,!0],[new RegExp(s.source+"\\s*$"),/^$/,!1]];e.exports=function(e,t,r,n){var s,i,a,c,u=e.bMarks[t]+e.tShift[t],l=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;if(!e.md.options.html)return!1;if(60!==e.src.charCodeAt(u))return!1;for(c=e.src.slice(u,l),s=0;s<o.length&&!o[s][0].test(c);s++);if(s===o.length)return!1;if(n)return o[s][2];if(i=t+1,!o[s][1].test(c))for(;i<r&&!(e.sCount[i]<e.blkIndent);i++)if(u=e.bMarks[i]+e.tShift[i],l=e.eMarks[i],c=e.src.slice(u,l),o[s][1].test(c)){0!==c.length&&i++;break}return e.line=i,(a=e.push("html_block","",0)).map=[t,i],a.content=e.getLines(t,i,e.blkIndent,!0),!0}},39648:function(e){"use strict";e.exports=function(e,t,r){var n,s,o,i,a,c,u,l,p,h,f=t+1,d=e.md.block.ruler.getRules("paragraph");if(e.sCount[t]-e.blkIndent>=4)return!1;for(h=e.parentType,e.parentType="paragraph";f<r&&!e.isEmpty(f);f++)if(!(e.sCount[f]-e.blkIndent>3)){if(e.sCount[f]>=e.blkIndent&&(c=e.bMarks[f]+e.tShift[f])<(u=e.eMarks[f])&&(45===(p=e.src.charCodeAt(c))||61===p)&&(c=e.skipChars(c,p),(c=e.skipSpaces(c))>=u)){l=61===p?1:2;break}if(!(e.sCount[f]<0)){for(s=!1,o=0,i=d.length;o<i;o++)if(d[o](e,f,r,!0)){s=!0;break}if(s)break}}return!!l&&(n=e.getLines(t,f,e.blkIndent,!1).trim(),e.line=f+1,(a=e.push("heading_open","h"+String(l),1)).markup=String.fromCharCode(p),a.map=[t,e.line],(a=e.push("inline","",0)).content=n,a.map=[t,e.line-1],a.children=[],(a=e.push("heading_close","h"+String(l),-1)).markup=String.fromCharCode(p),e.parentType=h,!0)}},36686:function(e,t,r){"use strict";var n=r(49963).isSpace;function s(e,t){var r,s,o,i;return s=e.bMarks[t]+e.tShift[t],o=e.eMarks[t],42!==(r=e.src.charCodeAt(s++))&&45!==r&&43!==r||s<o&&(i=e.src.charCodeAt(s),!n(i))?-1:s}function o(e,t){var r,s=e.bMarks[t]+e.tShift[t],o=s,i=e.eMarks[t];if(o+1>=i)return-1;if((r=e.src.charCodeAt(o++))<48||r>57)return-1;for(;;){if(o>=i)return-1;if(!((r=e.src.charCodeAt(o++))>=48&&r<=57)){if(41===r||46===r)break;return-1}if(o-s>=10)return-1}return o<i&&(r=e.src.charCodeAt(o),!n(r))?-1:o}e.exports=function(e,t,r,n){var i,a,c,u,l,p,h,f,d,m,g,_,k,b,v,C,y,x,A,w,D,E,q,S,F,L,z,T,I=!1,M=!0;if(e.sCount[t]-e.blkIndent>=4)return!1;if(e.listIndent>=0&&e.sCount[t]-e.listIndent>=4&&e.sCount[t]<e.blkIndent)return!1;if(n&&"paragraph"===e.parentType&&e.sCount[t]>=e.blkIndent&&(I=!0),(q=o(e,t))>=0){if(h=!0,F=e.bMarks[t]+e.tShift[t],k=Number(e.src.slice(F,q-1)),I&&1!==k)return!1}else{if(!((q=s(e,t))>=0))return!1;h=!1}if(I&&e.skipSpaces(q)>=e.eMarks[t])return!1;if(_=e.src.charCodeAt(q-1),n)return!0;for(g=e.tokens.length,h?(T=e.push("ordered_list_open","ol",1),1!==k&&(T.attrs=[["start",k]])):T=e.push("bullet_list_open","ul",1),T.map=m=[t,0],T.markup=String.fromCharCode(_),v=t,S=!1,z=e.md.block.ruler.getRules("list"),x=e.parentType,e.parentType="list";v<r;){for(E=q,b=e.eMarks[v],p=C=e.sCount[v]+q-(e.bMarks[t]+e.tShift[t]);E<b;){if(9===(i=e.src.charCodeAt(E)))C+=4-(C+e.bsCount[v])%4;else{if(32!==i)break;C++}E++}if((l=(a=E)>=b?1:C-p)>4&&(l=1),u=p+l,(T=e.push("list_item_open","li",1)).markup=String.fromCharCode(_),T.map=f=[t,0],h&&(T.info=e.src.slice(F,q-1)),D=e.tight,w=e.tShift[t],A=e.sCount[t],y=e.listIndent,e.listIndent=e.blkIndent,e.blkIndent=u,e.tight=!0,e.tShift[t]=a-e.bMarks[t],e.sCount[t]=C,a>=b&&e.isEmpty(t+1)?e.line=Math.min(e.line+2,r):e.md.block.tokenize(e,t,r,!0),e.tight&&!S||(M=!1),S=e.line-t>1&&e.isEmpty(e.line-1),e.blkIndent=e.listIndent,e.listIndent=y,e.tShift[t]=w,e.sCount[t]=A,e.tight=D,(T=e.push("list_item_close","li",-1)).markup=String.fromCharCode(_),v=t=e.line,f[1]=v,a=e.bMarks[t],v>=r)break;if(e.sCount[v]<e.blkIndent)break;if(e.sCount[t]-e.blkIndent>=4)break;for(L=!1,c=0,d=z.length;c<d;c++)if(z[c](e,v,r,!0)){L=!0;break}if(L)break;if(h){if((q=o(e,v))<0)break;F=e.bMarks[v]+e.tShift[v]}else if((q=s(e,v))<0)break;if(_!==e.src.charCodeAt(q-1))break}return(T=h?e.push("ordered_list_close","ol",-1):e.push("bullet_list_close","ul",-1)).markup=String.fromCharCode(_),m[1]=v,e.line=v,e.parentType=x,M&&function(e,t){var r,n,s=e.level+2;for(r=t+2,n=e.tokens.length-2;r<n;r++)e.tokens[r].level===s&&"paragraph_open"===e.tokens[r].type&&(e.tokens[r+2].hidden=!0,e.tokens[r].hidden=!0,r+=2)}(e,g),!0}},87046:function(e){"use strict";e.exports=function(e,t){var r,n,s,o,i,a,c=t+1,u=e.md.block.ruler.getRules("paragraph"),l=e.lineMax;for(a=e.parentType,e.parentType="paragraph";c<l&&!e.isEmpty(c);c++)if(!(e.sCount[c]-e.blkIndent>3||e.sCount[c]<0)){for(n=!1,s=0,o=u.length;s<o;s++)if(u[s](e,c,l,!0)){n=!0;break}if(n)break}return r=e.getLines(t,c,e.blkIndent,!1).trim(),e.line=c,(i=e.push("paragraph_open","p",1)).map=[t,e.line],(i=e.push("inline","",0)).content=r,i.map=[t,e.line],i.children=[],i=e.push("paragraph_close","p",-1),e.parentType=a,!0}},86897:function(e,t,r){"use strict";var n=r(49963).normalizeReference,s=r(49963).isSpace;e.exports=function(e,t,r,o){var i,a,c,u,l,p,h,f,d,m,g,_,k,b,v,C,y=0,x=e.bMarks[t]+e.tShift[t],A=e.eMarks[t],w=t+1;if(e.sCount[t]-e.blkIndent>=4)return!1;if(91!==e.src.charCodeAt(x))return!1;for(;++x<A;)if(93===e.src.charCodeAt(x)&&92!==e.src.charCodeAt(x-1)){if(x+1===A)return!1;if(58!==e.src.charCodeAt(x+1))return!1;break}for(u=e.lineMax,v=e.md.block.ruler.getRules("reference"),m=e.parentType,e.parentType="reference";w<u&&!e.isEmpty(w);w++)if(!(e.sCount[w]-e.blkIndent>3||e.sCount[w]<0)){for(b=!1,p=0,h=v.length;p<h;p++)if(v[p](e,w,u,!0)){b=!0;break}if(b)break}for(A=(k=e.getLines(t,w,e.blkIndent,!1).trim()).length,x=1;x<A;x++){if(91===(i=k.charCodeAt(x)))return!1;if(93===i){d=x;break}(10===i||92===i&&++x<A&&10===k.charCodeAt(x))&&y++}if(d<0||58!==k.charCodeAt(d+1))return!1;for(x=d+2;x<A;x++)if(10===(i=k.charCodeAt(x)))y++;else if(!s(i))break;if(!(g=e.md.helpers.parseLinkDestination(k,x,A)).ok)return!1;if(l=e.md.normalizeLink(g.str),!e.md.validateLink(l))return!1;for(a=x=g.pos,c=y+=g.lines,_=x;x<A;x++)if(10===(i=k.charCodeAt(x)))y++;else if(!s(i))break;for(g=e.md.helpers.parseLinkTitle(k,x,A),x<A&&_!==x&&g.ok?(C=g.str,x=g.pos,y+=g.lines):(C="",x=a,y=c);x<A&&(i=k.charCodeAt(x),s(i));)x++;if(x<A&&10!==k.charCodeAt(x)&&C)for(C="",x=a,y=c;x<A&&(i=k.charCodeAt(x),s(i));)x++;return!(x<A&&10!==k.charCodeAt(x)||!(f=n(k.slice(1,d)))||(o||(void 0===e.env.references&&(e.env.references={}),void 0===e.env.references[f]&&(e.env.references[f]={title:C,href:l}),e.parentType=m,e.line=t+y+1),0))}},7759:function(e,t,r){"use strict";var n=r(5099),s=r(49963).isSpace;function o(e,t,r,n){var o,i,a,c,u,l,p,h;for(this.src=e,this.md=t,this.env=r,this.tokens=n,this.bMarks=[],this.eMarks=[],this.tShift=[],this.sCount=[],this.bsCount=[],this.blkIndent=0,this.line=0,this.lineMax=0,this.tight=!1,this.ddIndent=-1,this.listIndent=-1,this.parentType="root",this.level=0,this.result="",h=!1,a=c=l=p=0,u=(i=this.src).length;c<u;c++){if(o=i.charCodeAt(c),!h){if(s(o)){l++,9===o?p+=4-p%4:p++;continue}h=!0}10!==o&&c!==u-1||(10!==o&&c++,this.bMarks.push(a),this.eMarks.push(c),this.tShift.push(l),this.sCount.push(p),this.bsCount.push(0),h=!1,l=0,p=0,a=c+1)}this.bMarks.push(i.length),this.eMarks.push(i.length),this.tShift.push(0),this.sCount.push(0),this.bsCount.push(0),this.lineMax=this.bMarks.length-1}o.prototype.push=function(e,t,r){var s=new n(e,t,r);return s.block=!0,r<0&&this.level--,s.level=this.level,r>0&&this.level++,this.tokens.push(s),s},o.prototype.isEmpty=function(e){return this.bMarks[e]+this.tShift[e]>=this.eMarks[e]},o.prototype.skipEmptyLines=function(e){for(var t=this.lineMax;e<t&&!(this.bMarks[e]+this.tShift[e]<this.eMarks[e]);e++);return e},o.prototype.skipSpaces=function(e){for(var t,r=this.src.length;e<r&&(t=this.src.charCodeAt(e),s(t));e++);return e},o.prototype.skipSpacesBack=function(e,t){if(e<=t)return e;for(;e>t;)if(!s(this.src.charCodeAt(--e)))return e+1;return e},o.prototype.skipChars=function(e,t){for(var r=this.src.length;e<r&&this.src.charCodeAt(e)===t;e++);return e},o.prototype.skipCharsBack=function(e,t,r){if(e<=r)return e;for(;e>r;)if(t!==this.src.charCodeAt(--e))return e+1;return e},o.prototype.getLines=function(e,t,r,n){var o,i,a,c,u,l,p,h=e;if(e>=t)return"";for(l=new Array(t-e),o=0;h<t;h++,o++){for(i=0,p=c=this.bMarks[h],u=h+1<t||n?this.eMarks[h]+1:this.eMarks[h];c<u&&i<r;){if(a=this.src.charCodeAt(c),s(a))9===a?i+=4-(i+this.bsCount[h])%4:i++;else{if(!(c-p<this.tShift[h]))break;i++}c++}l[o]=i>r?new Array(i-r+1).join(" ")+this.src.slice(c,u):this.src.slice(c,u)}return l.join("")},o.prototype.Token=n,e.exports=o},24752:function(e,t,r){"use strict";var n=r(49963).isSpace;function s(e,t){var r=e.bMarks[t]+e.tShift[t],n=e.eMarks[t];return e.src.substr(r,n-r)}function o(e){var t,r=[],n=0,s=e.length,o=!1,i=0,a="";for(t=e.charCodeAt(n);n<s;)124===t&&(o?(a+=e.substring(i,n-1),i=n):(r.push(a+e.substring(i,n)),a="",i=n+1)),o=92===t,n++,t=e.charCodeAt(n);return r.push(a+e.substring(i)),r}e.exports=function(e,t,r,i){var a,c,u,l,p,h,f,d,m,g,_,k,b,v,C,y,x,A;if(t+2>r)return!1;if(h=t+1,e.sCount[h]<e.blkIndent)return!1;if(e.sCount[h]-e.blkIndent>=4)return!1;if((u=e.bMarks[h]+e.tShift[h])>=e.eMarks[h])return!1;if(124!==(x=e.src.charCodeAt(u++))&&45!==x&&58!==x)return!1;if(u>=e.eMarks[h])return!1;if(124!==(A=e.src.charCodeAt(u++))&&45!==A&&58!==A&&!n(A))return!1;if(45===x&&n(A))return!1;for(;u<e.eMarks[h];){if(124!==(a=e.src.charCodeAt(u))&&45!==a&&58!==a&&!n(a))return!1;u++}for(f=(c=s(e,t+1)).split("|"),g=[],l=0;l<f.length;l++){if(!(_=f[l].trim())){if(0===l||l===f.length-1)continue;return!1}if(!/^:?-+:?$/.test(_))return!1;58===_.charCodeAt(_.length-1)?g.push(58===_.charCodeAt(0)?"center":"right"):58===_.charCodeAt(0)?g.push("left"):g.push("")}if(-1===(c=s(e,t).trim()).indexOf("|"))return!1;if(e.sCount[t]-e.blkIndent>=4)return!1;if((f=o(c)).length&&""===f[0]&&f.shift(),f.length&&""===f[f.length-1]&&f.pop(),0===(d=f.length)||d!==g.length)return!1;if(i)return!0;for(v=e.parentType,e.parentType="table",y=e.md.block.ruler.getRules("blockquote"),(m=e.push("table_open","table",1)).map=k=[t,0],(m=e.push("thead_open","thead",1)).map=[t,t+1],(m=e.push("tr_open","tr",1)).map=[t,t+1],l=0;l<f.length;l++)m=e.push("th_open","th",1),g[l]&&(m.attrs=[["style","text-align:"+g[l]]]),(m=e.push("inline","",0)).content=f[l].trim(),m.children=[],m=e.push("th_close","th",-1);for(m=e.push("tr_close","tr",-1),m=e.push("thead_close","thead",-1),h=t+2;h<r&&!(e.sCount[h]<e.blkIndent);h++){for(C=!1,l=0,p=y.length;l<p;l++)if(y[l](e,h,r,!0)){C=!0;break}if(C)break;if(!(c=s(e,h).trim()))break;if(e.sCount[h]-e.blkIndent>=4)break;for((f=o(c)).length&&""===f[0]&&f.shift(),f.length&&""===f[f.length-1]&&f.pop(),h===t+2&&((m=e.push("tbody_open","tbody",1)).map=b=[t+2,0]),(m=e.push("tr_open","tr",1)).map=[h,h+1],l=0;l<d;l++)m=e.push("td_open","td",1),g[l]&&(m.attrs=[["style","text-align:"+g[l]]]),(m=e.push("inline","",0)).content=f[l]?f[l].trim():"",m.children=[],m=e.push("td_close","td",-1);m=e.push("tr_close","tr",-1)}return b&&(m=e.push("tbody_close","tbody",-1),b[1]=h),m=e.push("table_close","table",-1),k[1]=h,e.parentType=v,e.line=h,!0}},73437:function(e){"use strict";e.exports=function(e){var t;e.inlineMode?((t=new e.Token("inline","",0)).content=e.src,t.map=[0,1],t.children=[],e.tokens.push(t)):e.md.block.parse(e.src,e.md,e.env,e.tokens)}},3547:function(e){"use strict";e.exports=function(e){var t,r,n,s=e.tokens;for(r=0,n=s.length;r<n;r++)"inline"===(t=s[r]).type&&e.md.inline.parse(t.content,e.md,e.env,t.children)}},986:function(e,t,r){"use strict";var n=r(49963).arrayReplaceAt;function s(e){return/^<\/a\s*>/i.test(e)}e.exports=function(e){var t,r,o,i,a,c,u,l,p,h,f,d,m,g,_,k,b,v,C=e.tokens;if(e.md.options.linkify)for(r=0,o=C.length;r<o;r++)if("inline"===C[r].type&&e.md.linkify.pretest(C[r].content))for(m=0,t=(i=C[r].children).length-1;t>=0;t--)if("link_close"!==(c=i[t]).type){if("html_inline"===c.type&&(v=c.content,/^<a[>\s]/i.test(v)&&m>0&&m--,s(c.content)&&m++),!(m>0)&&"text"===c.type&&e.md.linkify.test(c.content)){for(p=c.content,b=e.md.linkify.match(p),u=[],d=c.level,f=0,l=0;l<b.length;l++)g=b[l].url,_=e.md.normalizeLink(g),e.md.validateLink(_)&&(k=b[l].text,k=b[l].schema?"mailto:"!==b[l].schema||/^mailto:/i.test(k)?e.md.normalizeLinkText(k):e.md.normalizeLinkText("mailto:"+k).replace(/^mailto:/,""):e.md.normalizeLinkText("http://"+k).replace(/^http:\/\//,""),(h=b[l].index)>f&&((a=new e.Token("text","",0)).content=p.slice(f,h),a.level=d,u.push(a)),(a=new e.Token("link_open","a",1)).attrs=[["href",_]],a.level=d++,a.markup="linkify",a.info="auto",u.push(a),(a=new e.Token("text","",0)).content=k,a.level=d,u.push(a),(a=new e.Token("link_close","a",-1)).level=--d,a.markup="linkify",a.info="auto",u.push(a),f=b[l].lastIndex);f<p.length&&((a=new e.Token("text","",0)).content=p.slice(f),a.level=d,u.push(a)),C[r].children=i=n(i,t,u)}}else for(t--;i[t].level!==c.level&&"link_open"!==i[t].type;)t--}},803:function(e){"use strict";var t=/\r\n?|\n/g,r=/\0/g;e.exports=function(e){var n;n=(n=e.src.replace(t,"\n")).replace(r,"�"),e.src=n}},10203:function(e){"use strict";var t=/\+-|\.\.|\?\?\?\?|!!!!|,,|--/,r=/\((c|tm|r|p)\)/i,n=/\((c|tm|r|p)\)/gi,s={c:"©",r:"®",p:"§",tm:"™"};function o(e,t){return s[t.toLowerCase()]}function i(e){var t,r,s=0;for(t=e.length-1;t>=0;t--)"text"!==(r=e[t]).type||s||(r.content=r.content.replace(n,o)),"link_open"===r.type&&"auto"===r.info&&s--,"link_close"===r.type&&"auto"===r.info&&s++}function a(e){var r,n,s=0;for(r=e.length-1;r>=0;r--)"text"!==(n=e[r]).type||s||t.test(n.content)&&(n.content=n.content.replace(/\+-/g,"±").replace(/\.{2,}/g,"…").replace(/([?!])…/g,"$1..").replace(/([?!]){4,}/g,"$1$1$1").replace(/,{2,}/g,",").replace(/(^|[^-])---(?=[^-]|$)/gm,"$1—").replace(/(^|\s)--(?=\s|$)/gm,"$1–").replace(/(^|[^-\s])--(?=[^-\s]|$)/gm,"$1–")),"link_open"===n.type&&"auto"===n.info&&s--,"link_close"===n.type&&"auto"===n.info&&s++}e.exports=function(e){var n;if(e.md.options.typographer)for(n=e.tokens.length-1;n>=0;n--)"inline"===e.tokens[n].type&&(r.test(e.tokens[n].content)&&i(e.tokens[n].children),t.test(e.tokens[n].content)&&a(e.tokens[n].children))}},65260:function(e,t,r){"use strict";var n=r(49963).isWhiteSpace,s=r(49963).isPunctChar,o=r(49963).isMdAsciiPunct,i=/['"]/,a=/['"]/g;function c(e,t,r){return e.substr(0,t)+r+e.substr(t+1)}function u(e,t){var r,i,u,l,p,h,f,d,m,g,_,k,b,v,C,y,x,A,w,D,E;for(w=[],r=0;r<e.length;r++){for(i=e[r],f=e[r].level,x=w.length-1;x>=0&&!(w[x].level<=f);x--);if(w.length=x+1,"text"===i.type){p=0,h=(u=i.content).length;e:for(;p<h&&(a.lastIndex=p,l=a.exec(u));){if(C=y=!0,p=l.index+1,A="'"===l[0],m=32,l.index-1>=0)m=u.charCodeAt(l.index-1);else for(x=r-1;x>=0&&"softbreak"!==e[x].type&&"hardbreak"!==e[x].type;x--)if(e[x].content){m=e[x].content.charCodeAt(e[x].content.length-1);break}if(g=32,p<h)g=u.charCodeAt(p);else for(x=r+1;x<e.length&&"softbreak"!==e[x].type&&"hardbreak"!==e[x].type;x++)if(e[x].content){g=e[x].content.charCodeAt(0);break}if(_=o(m)||s(String.fromCharCode(m)),k=o(g)||s(String.fromCharCode(g)),b=n(m),(v=n(g))?C=!1:k&&(b||_||(C=!1)),b?y=!1:_&&(v||k||(y=!1)),34===g&&'"'===l[0]&&m>=48&&m<=57&&(y=C=!1),C&&y&&(C=_,y=k),C||y){if(y)for(x=w.length-1;x>=0&&(d=w[x],!(w[x].level<f));x--)if(d.single===A&&w[x].level===f){d=w[x],A?(D=t.md.options.quotes[2],E=t.md.options.quotes[3]):(D=t.md.options.quotes[0],E=t.md.options.quotes[1]),i.content=c(i.content,l.index,E),e[d.token].content=c(e[d.token].content,d.pos,D),p+=E.length-1,d.token===r&&(p+=D.length-1),h=(u=i.content).length,w.length=x;continue e}C?w.push({token:r,pos:l.index,single:A,level:f}):y&&A&&(i.content=c(i.content,l.index,"’"))}else A&&(i.content=c(i.content,l.index,"’"))}}}}e.exports=function(e){var t;if(e.md.options.typographer)for(t=e.tokens.length-1;t>=0;t--)"inline"===e.tokens[t].type&&i.test(e.tokens[t].content)&&u(e.tokens[t].children,e)}},1839:function(e,t,r){"use strict";var n=r(5099);function s(e,t,r){this.src=e,this.env=r,this.tokens=[],this.inlineMode=!1,this.md=t}s.prototype.Token=n,e.exports=s},86955:function(e){"use strict";var t=/^([a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)$/,r=/^([a-zA-Z][a-zA-Z0-9+.\-]{1,31}):([^<>\x00-\x20]*)$/;e.exports=function(e,n){var s,o,i,a,c,u,l=e.pos;if(60!==e.src.charCodeAt(l))return!1;for(c=e.pos,u=e.posMax;;){if(++l>=u)return!1;if(60===(a=e.src.charCodeAt(l)))return!1;if(62===a)break}return s=e.src.slice(c+1,l),r.test(s)?(o=e.md.normalizeLink(s),!!e.md.validateLink(o)&&(n||((i=e.push("link_open","a",1)).attrs=[["href",o]],i.markup="autolink",i.info="auto",(i=e.push("text","",0)).content=e.md.normalizeLinkText(s),(i=e.push("link_close","a",-1)).markup="autolink",i.info="auto"),e.pos+=s.length+2,!0)):!!t.test(s)&&(o=e.md.normalizeLink("mailto:"+s),!!e.md.validateLink(o)&&(n||((i=e.push("link_open","a",1)).attrs=[["href",o]],i.markup="autolink",i.info="auto",(i=e.push("text","",0)).content=e.md.normalizeLinkText(s),(i=e.push("link_close","a",-1)).markup="autolink",i.info="auto"),e.pos+=s.length+2,!0))}},26757:function(e){"use strict";e.exports=function(e,t){var r,n,s,o,i,a,c,u,l=e.pos;if(96!==e.src.charCodeAt(l))return!1;for(r=l,l++,n=e.posMax;l<n&&96===e.src.charCodeAt(l);)l++;if(c=(s=e.src.slice(r,l)).length,e.backticksScanned&&(e.backticks[c]||0)<=r)return t||(e.pending+=s),e.pos+=c,!0;for(i=a=l;-1!==(i=e.src.indexOf("`",a));){for(a=i+1;a<n&&96===e.src.charCodeAt(a);)a++;if((u=a-i)===c)return t||((o=e.push("code_inline","code",0)).markup=s,o.content=e.src.slice(l,i).replace(/\n/g," ").replace(/^ (.+) $/,"$1")),e.pos=a,!0;e.backticks[u]=i}return e.backticksScanned=!0,t||(e.pending+=s),e.pos+=c,!0}},65940:function(e){"use strict";function t(e,t){var r,n,s,o,i,a,c,u,l={},p=t.length;if(p){var h=0,f=-2,d=[];for(r=0;r<p;r++)if(s=t[r],d.push(0),t[h].marker===s.marker&&f===s.token-1||(h=r),f=s.token,s.length=s.length||0,s.close){for(l.hasOwnProperty(s.marker)||(l[s.marker]=[-1,-1,-1,-1,-1,-1]),i=l[s.marker][(s.open?3:0)+s.length%3],a=n=h-d[h]-1;n>i;n-=d[n]+1)if((o=t[n]).marker===s.marker&&o.open&&o.end<0&&(c=!1,(o.close||s.open)&&(o.length+s.length)%3==0&&(o.length%3==0&&s.length%3==0||(c=!0)),!c)){u=n>0&&!t[n-1].open?d[n-1]+1:0,d[r]=r-n+u,d[n]=u,s.open=!1,o.end=r,o.close=!1,a=-1,f=-2;break}-1!==a&&(l[s.marker][(s.open?3:0)+(s.length||0)%3]=a)}}}e.exports=function(e){var r,n=e.tokens_meta,s=e.tokens_meta.length;for(t(0,e.delimiters),r=0;r<s;r++)n[r]&&n[r].delimiters&&t(0,n[r].delimiters)}},93898:function(e){"use strict";function t(e,t){var r,n,s,o,i,a;for(r=t.length-1;r>=0;r--)95!==(n=t[r]).marker&&42!==n.marker||-1!==n.end&&(s=t[n.end],a=r>0&&t[r-1].end===n.end+1&&t[r-1].marker===n.marker&&t[r-1].token===n.token-1&&t[n.end+1].token===s.token+1,i=String.fromCharCode(n.marker),(o=e.tokens[n.token]).type=a?"strong_open":"em_open",o.tag=a?"strong":"em",o.nesting=1,o.markup=a?i+i:i,o.content="",(o=e.tokens[s.token]).type=a?"strong_close":"em_close",o.tag=a?"strong":"em",o.nesting=-1,o.markup=a?i+i:i,o.content="",a&&(e.tokens[t[r-1].token].content="",e.tokens[t[n.end+1].token].content="",r--))}e.exports.q=function(e,t){var r,n,s=e.pos,o=e.src.charCodeAt(s);if(t)return!1;if(95!==o&&42!==o)return!1;for(n=e.scanDelims(e.pos,42===o),r=0;r<n.length;r++)e.push("text","",0).content=String.fromCharCode(o),e.delimiters.push({marker:o,length:n.length,token:e.tokens.length-1,end:-1,open:n.can_open,close:n.can_close});return e.pos+=n.length,!0},e.exports.g=function(e){var r,n=e.tokens_meta,s=e.tokens_meta.length;for(t(e,e.delimiters),r=0;r<s;r++)n[r]&&n[r].delimiters&&t(e,n[r].delimiters)}},18103:function(e,t,r){"use strict";var n=r(68359),s=r(49963).has,o=r(49963).isValidEntityCode,i=r(49963).fromCodePoint,a=/^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i,c=/^&([a-z][a-z0-9]{1,31});/i;e.exports=function(e,t){var r,u,l=e.pos,p=e.posMax;if(38!==e.src.charCodeAt(l))return!1;if(l+1<p)if(35===e.src.charCodeAt(l+1)){if(u=e.src.slice(l).match(a))return t||(r="x"===u[1][0].toLowerCase()?parseInt(u[1].slice(1),16):parseInt(u[1],10),e.pending+=o(r)?i(r):i(65533)),e.pos+=u[0].length,!0}else if((u=e.src.slice(l).match(c))&&s(n,u[1]))return t||(e.pending+=n[u[1]]),e.pos+=u[0].length,!0;return t||(e.pending+="&"),e.pos++,!0}},21231:function(e,t,r){"use strict";for(var n=r(49963).isSpace,s=[],o=0;o<256;o++)s.push(0);"\\!\"#$%&'()*+,./:;<=>?@[]^_`{|}~-".split("").forEach((function(e){s[e.charCodeAt(0)]=1})),e.exports=function(e,t){var r,o=e.pos,i=e.posMax;if(92!==e.src.charCodeAt(o))return!1;if(++o<i){if((r=e.src.charCodeAt(o))<256&&0!==s[r])return t||(e.pending+=e.src[o]),e.pos+=2,!0;if(10===r){for(t||e.push("hardbreak","br",0),o++;o<i&&(r=e.src.charCodeAt(o),n(r));)o++;return e.pos=o,!0}}return t||(e.pending+="\\"),e.pos++,!0}},30961:function(e,t,r){"use strict";var n=r(98938).l;e.exports=function(e,t){var r,s,o,i=e.pos;return!(!e.md.options.html||(o=e.posMax,60!==e.src.charCodeAt(i)||i+2>=o||33!==(r=e.src.charCodeAt(i+1))&&63!==r&&47!==r&&!function(e){var t=32|e;return t>=97&&t<=122}(r)||!(s=e.src.slice(i).match(n))||(t||(e.push("html_inline","",0).content=e.src.slice(i,i+s[0].length)),e.pos+=s[0].length,0)))}},23707:function(e,t,r){"use strict";var n=r(49963).normalizeReference,s=r(49963).isSpace;e.exports=function(e,t){var r,o,i,a,c,u,l,p,h,f,d,m,g,_="",k=e.pos,b=e.posMax;if(33!==e.src.charCodeAt(e.pos))return!1;if(91!==e.src.charCodeAt(e.pos+1))return!1;if(u=e.pos+2,(c=e.md.helpers.parseLinkLabel(e,e.pos+1,!1))<0)return!1;if((l=c+1)<b&&40===e.src.charCodeAt(l)){for(l++;l<b&&(o=e.src.charCodeAt(l),s(o)||10===o);l++);if(l>=b)return!1;for(g=l,(h=e.md.helpers.parseLinkDestination(e.src,l,e.posMax)).ok&&(_=e.md.normalizeLink(h.str),e.md.validateLink(_)?l=h.pos:_=""),g=l;l<b&&(o=e.src.charCodeAt(l),s(o)||10===o);l++);if(h=e.md.helpers.parseLinkTitle(e.src,l,e.posMax),l<b&&g!==l&&h.ok)for(f=h.str,l=h.pos;l<b&&(o=e.src.charCodeAt(l),s(o)||10===o);l++);else f="";if(l>=b||41!==e.src.charCodeAt(l))return e.pos=k,!1;l++}else{if(void 0===e.env.references)return!1;if(l<b&&91===e.src.charCodeAt(l)?(g=l+1,(l=e.md.helpers.parseLinkLabel(e,l))>=0?a=e.src.slice(g,l++):l=c+1):l=c+1,a||(a=e.src.slice(u,c)),!(p=e.env.references[n(a)]))return e.pos=k,!1;_=p.href,f=p.title}return t||(i=e.src.slice(u,c),e.md.inline.parse(i,e.md,e.env,m=[]),(d=e.push("image","img",0)).attrs=r=[["src",_],["alt",""]],d.children=m,d.content=i,f&&r.push(["title",f])),e.pos=l,e.posMax=b,!0}},56552:function(e,t,r){"use strict";var n=r(49963).normalizeReference,s=r(49963).isSpace;e.exports=function(e,t){var r,o,i,a,c,u,l,p,h="",f="",d=e.pos,m=e.posMax,g=e.pos,_=!0;if(91!==e.src.charCodeAt(e.pos))return!1;if(c=e.pos+1,(a=e.md.helpers.parseLinkLabel(e,e.pos,!0))<0)return!1;if((u=a+1)<m&&40===e.src.charCodeAt(u)){for(_=!1,u++;u<m&&(o=e.src.charCodeAt(u),s(o)||10===o);u++);if(u>=m)return!1;if(g=u,(l=e.md.helpers.parseLinkDestination(e.src,u,e.posMax)).ok){for(h=e.md.normalizeLink(l.str),e.md.validateLink(h)?u=l.pos:h="",g=u;u<m&&(o=e.src.charCodeAt(u),s(o)||10===o);u++);if(l=e.md.helpers.parseLinkTitle(e.src,u,e.posMax),u<m&&g!==u&&l.ok)for(f=l.str,u=l.pos;u<m&&(o=e.src.charCodeAt(u),s(o)||10===o);u++);}(u>=m||41!==e.src.charCodeAt(u))&&(_=!0),u++}if(_){if(void 0===e.env.references)return!1;if(u<m&&91===e.src.charCodeAt(u)?(g=u+1,(u=e.md.helpers.parseLinkLabel(e,u))>=0?i=e.src.slice(g,u++):u=a+1):u=a+1,i||(i=e.src.slice(c,a)),!(p=e.env.references[n(i)]))return e.pos=d,!1;h=p.href,f=p.title}return t||(e.pos=c,e.posMax=a,e.push("link_open","a",1).attrs=r=[["href",h]],f&&r.push(["title",f]),e.md.inline.tokenize(e),e.push("link_close","a",-1)),e.pos=u,e.posMax=m,!0}},22534:function(e,t,r){"use strict";var n=r(49963).isSpace;e.exports=function(e,t){var r,s,o,i=e.pos;if(10!==e.src.charCodeAt(i))return!1;if(r=e.pending.length-1,s=e.posMax,!t)if(r>=0&&32===e.pending.charCodeAt(r))if(r>=1&&32===e.pending.charCodeAt(r-1)){for(o=r-1;o>=1&&32===e.pending.charCodeAt(o-1);)o--;e.pending=e.pending.slice(0,o),e.push("hardbreak","br",0)}else e.pending=e.pending.slice(0,-1),e.push("softbreak","br",0);else e.push("softbreak","br",0);for(i++;i<s&&n(e.src.charCodeAt(i));)i++;return e.pos=i,!0}},60979:function(e,t,r){"use strict";var n=r(5099),s=r(49963).isWhiteSpace,o=r(49963).isPunctChar,i=r(49963).isMdAsciiPunct;function a(e,t,r,n){this.src=e,this.env=r,this.md=t,this.tokens=n,this.tokens_meta=Array(n.length),this.pos=0,this.posMax=this.src.length,this.level=0,this.pending="",this.pendingLevel=0,this.cache={},this.delimiters=[],this._prev_delimiters=[],this.backticks={},this.backticksScanned=!1}a.prototype.pushPending=function(){var e=new n("text","",0);return e.content=this.pending,e.level=this.pendingLevel,this.tokens.push(e),this.pending="",e},a.prototype.push=function(e,t,r){this.pending&&this.pushPending();var s=new n(e,t,r),o=null;return r<0&&(this.level--,this.delimiters=this._prev_delimiters.pop()),s.level=this.level,r>0&&(this.level++,this._prev_delimiters.push(this.delimiters),this.delimiters=[],o={delimiters:this.delimiters}),this.pendingLevel=this.level,this.tokens.push(s),this.tokens_meta.push(o),s},a.prototype.scanDelims=function(e,t){var r,n,a,c,u,l,p,h,f,d=e,m=!0,g=!0,_=this.posMax,k=this.src.charCodeAt(e);for(r=e>0?this.src.charCodeAt(e-1):32;d<_&&this.src.charCodeAt(d)===k;)d++;return a=d-e,n=d<_?this.src.charCodeAt(d):32,p=i(r)||o(String.fromCharCode(r)),f=i(n)||o(String.fromCharCode(n)),l=s(r),(h=s(n))?m=!1:f&&(l||p||(m=!1)),l?g=!1:p&&(h||f||(g=!1)),t?(c=m,u=g):(c=m&&(!g||p),u=g&&(!m||f)),{can_open:c,can_close:u,length:a}},a.prototype.Token=n,e.exports=a},97141:function(e){"use strict";function t(e,t){var r,n,s,o,i,a=[],c=t.length;for(r=0;r<c;r++)126===(s=t[r]).marker&&-1!==s.end&&(o=t[s.end],(i=e.tokens[s.token]).type="s_open",i.tag="s",i.nesting=1,i.markup="~~",i.content="",(i=e.tokens[o.token]).type="s_close",i.tag="s",i.nesting=-1,i.markup="~~",i.content="","text"===e.tokens[o.token-1].type&&"~"===e.tokens[o.token-1].content&&a.push(o.token-1));for(;a.length;){for(n=(r=a.pop())+1;n<e.tokens.length&&"s_close"===e.tokens[n].type;)n++;r!==--n&&(i=e.tokens[n],e.tokens[n]=e.tokens[r],e.tokens[r]=i)}}e.exports.q=function(e,t){var r,n,s,o,i=e.pos,a=e.src.charCodeAt(i);if(t)return!1;if(126!==a)return!1;if(s=(n=e.scanDelims(e.pos,!0)).length,o=String.fromCharCode(a),s<2)return!1;for(s%2&&(e.push("text","",0).content=o,s--),r=0;r<s;r+=2)e.push("text","",0).content=o+o,e.delimiters.push({marker:a,length:0,token:e.tokens.length-1,end:-1,open:n.can_open,close:n.can_close});return e.pos+=n.length,!0},e.exports.g=function(e){var r,n=e.tokens_meta,s=e.tokens_meta.length;for(t(e,e.delimiters),r=0;r<s;r++)n[r]&&n[r].delimiters&&t(e,n[r].delimiters)}},82015:function(e){"use strict";function t(e){switch(e){case 10:case 33:case 35:case 36:case 37:case 38:case 42:case 43:case 45:case 58:case 60:case 61:case 62:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 125:case 126:return!0;default:return!1}}e.exports=function(e,r){for(var n=e.pos;n<e.posMax&&!t(e.src.charCodeAt(n));)n++;return n!==e.pos&&(r||(e.pending+=e.src.slice(e.pos,n)),e.pos=n,!0)}},77729:function(e){"use strict";e.exports=function(e){var t,r,n=0,s=e.tokens,o=e.tokens.length;for(t=r=0;t<o;t++)s[t].nesting<0&&n--,s[t].level=n,s[t].nesting>0&&n++,"text"===s[t].type&&t+1<o&&"text"===s[t+1].type?s[t+1].content=s[t].content+s[t+1].content:(t!==r&&(s[r]=s[t]),r++);t!==r&&(s.length=r)}},5099:function(e){"use strict";function t(e,t,r){this.type=e,this.tag=t,this.attrs=null,this.map=null,this.nesting=r,this.level=0,this.children=null,this.content="",this.markup="",this.info="",this.meta=null,this.block=!1,this.hidden=!1}t.prototype.attrIndex=function(e){var t,r,n;if(!this.attrs)return-1;for(r=0,n=(t=this.attrs).length;r<n;r++)if(t[r][0]===e)return r;return-1},t.prototype.attrPush=function(e){this.attrs?this.attrs.push(e):this.attrs=[e]},t.prototype.attrSet=function(e,t){var r=this.attrIndex(e),n=[e,t];r<0?this.attrPush(n):this.attrs[r]=n},t.prototype.attrGet=function(e){var t=this.attrIndex(e),r=null;return t>=0&&(r=this.attrs[t][1]),r},t.prototype.attrJoin=function(e,t){var r=this.attrIndex(e);r<0?this.attrPush([e,t]):this.attrs[r][1]=this.attrs[r][1]+" "+t},e.exports=t},23527:function(e){"use strict";var t={};function r(e,n){var s;return"string"!=typeof n&&(n=r.defaultChars),s=function(e){var r,n,s=t[e];if(s)return s;for(s=t[e]=[],r=0;r<128;r++)n=String.fromCharCode(r),s.push(n);for(r=0;r<e.length;r++)s[n=e.charCodeAt(r)]="%"+("0"+n.toString(16).toUpperCase()).slice(-2);return s}(n),e.replace(/(%[a-f0-9]{2})+/gi,(function(e){var t,r,n,o,i,a,c,u="";for(t=0,r=e.length;t<r;t+=3)(n=parseInt(e.slice(t+1,t+3),16))<128?u+=s[n]:192==(224&n)&&t+3<r&&128==(192&(o=parseInt(e.slice(t+4,t+6),16)))?(u+=(c=n<<6&1984|63&o)<128?"��":String.fromCharCode(c),t+=3):224==(240&n)&&t+6<r&&(o=parseInt(e.slice(t+4,t+6),16),i=parseInt(e.slice(t+7,t+9),16),128==(192&o)&&128==(192&i))?(u+=(c=n<<12&61440|o<<6&4032|63&i)<2048||c>=55296&&c<=57343?"���":String.fromCharCode(c),t+=6):240==(248&n)&&t+9<r&&(o=parseInt(e.slice(t+4,t+6),16),i=parseInt(e.slice(t+7,t+9),16),a=parseInt(e.slice(t+10,t+12),16),128==(192&o)&&128==(192&i)&&128==(192&a))?((c=n<<18&1835008|o<<12&258048|i<<6&4032|63&a)<65536||c>1114111?u+="����":(c-=65536,u+=String.fromCharCode(55296+(c>>10),56320+(1023&c))),t+=9):u+="�";return u}))}r.defaultChars=";/?:@&=+$,#",r.componentChars="",e.exports=r},43331:function(e){"use strict";var t={};function r(e,n,s){var o,i,a,c,u,l="";for("string"!=typeof n&&(s=n,n=r.defaultChars),void 0===s&&(s=!0),u=function(e){var r,n,s=t[e];if(s)return s;for(s=t[e]=[],r=0;r<128;r++)n=String.fromCharCode(r),/^[0-9a-z]$/i.test(n)?s.push(n):s.push("%"+("0"+r.toString(16).toUpperCase()).slice(-2));for(r=0;r<e.length;r++)s[e.charCodeAt(r)]=e[r];return s}(n),o=0,i=e.length;o<i;o++)if(a=e.charCodeAt(o),s&&37===a&&o+2<i&&/^[0-9a-f]{2}$/i.test(e.slice(o+1,o+3)))l+=e.slice(o,o+3),o+=2;else if(a<128)l+=u[a];else if(a>=55296&&a<=57343){if(a>=55296&&a<=56319&&o+1<i&&(c=e.charCodeAt(o+1))>=56320&&c<=57343){l+=encodeURIComponent(e[o]+e[o+1]),o++;continue}l+="%EF%BF%BD"}else l+=encodeURIComponent(e[o]);return l}r.defaultChars=";/?:@&=+$,-_.!~*'()#",r.componentChars="-_.!~*'()",e.exports=r},86998:function(e){"use strict";e.exports=function(e){var t="";return t+=e.protocol||"",t+=e.slashes?"//":"",t+=e.auth?e.auth+"@":"",e.hostname&&-1!==e.hostname.indexOf(":")?t+="["+e.hostname+"]":t+=e.hostname||"",t+=e.port?":"+e.port:"",t+=e.pathname||"",(t+=e.search||"")+(e.hash||"")}},86781:function(e,t,r){"use strict";e.exports.encode=r(43331),e.exports.decode=r(23527),e.exports.format=r(86998),e.exports.parse=r(2613)},2613:function(e){"use strict";function t(){this.protocol=null,this.slashes=null,this.auth=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.pathname=null}var r=/^([a-z0-9.+-]+:)/i,n=/:[0-9]*$/,s=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,o=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r","\n","\t"]),i=["'"].concat(o),a=["%","/","?",";","#"].concat(i),c=["/","?","#"],u=/^[+a-z0-9A-Z_-]{0,63}$/,l=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,p={javascript:!0,"javascript:":!0},h={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0};t.prototype.parse=function(e,t){var n,o,i,f,d,m=e;if(m=m.trim(),!t&&1===e.split("#").length){var g=s.exec(m);if(g)return this.pathname=g[1],g[2]&&(this.search=g[2]),this}var _=r.exec(m);if(_&&(i=(_=_[0]).toLowerCase(),this.protocol=_,m=m.substr(_.length)),(t||_||m.match(/^\/\/[^@\/]+@[^@\/]+/))&&(!(d="//"===m.substr(0,2))||_&&p[_]||(m=m.substr(2),this.slashes=!0)),!p[_]&&(d||_&&!h[_])){var k,b,v=-1;for(n=0;n<c.length;n++)-1!==(f=m.indexOf(c[n]))&&(-1===v||f<v)&&(v=f);for(-1!==(b=-1===v?m.lastIndexOf("@"):m.lastIndexOf("@",v))&&(k=m.slice(0,b),m=m.slice(b+1),this.auth=k),v=-1,n=0;n<a.length;n++)-1!==(f=m.indexOf(a[n]))&&(-1===v||f<v)&&(v=f);-1===v&&(v=m.length),":"===m[v-1]&&v--;var C=m.slice(0,v);m=m.slice(v),this.parseHost(C),this.hostname=this.hostname||"";var y="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!y){var x=this.hostname.split(/\./);for(n=0,o=x.length;n<o;n++){var A=x[n];if(A&&!A.match(u)){for(var w="",D=0,E=A.length;D<E;D++)A.charCodeAt(D)>127?w+="x":w+=A[D];if(!w.match(u)){var q=x.slice(0,n),S=x.slice(n+1),F=A.match(l);F&&(q.push(F[1]),S.unshift(F[2])),S.length&&(m=S.join(".")+m),this.hostname=q.join(".");break}}}}this.hostname.length>255&&(this.hostname=""),y&&(this.hostname=this.hostname.substr(1,this.hostname.length-2))}var L=m.indexOf("#");-1!==L&&(this.hash=m.substr(L),m=m.slice(0,L));var z=m.indexOf("?");return-1!==z&&(this.search=m.substr(z),m=m.slice(0,z)),m&&(this.pathname=m),h[i]&&this.hostname&&!this.pathname&&(this.pathname=""),this},t.prototype.parseHost=function(e){var t=n.exec(e);t&&(":"!==(t=t[0])&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)},e.exports=function(e,r){if(e&&e instanceof t)return e;var n=new t;return n.parse(e,r),n}},48379:function(e,t,r){"use strict";r.r(t),r.d(t,{decode:function(){return _},encode:function(){return k},toASCII:function(){return v},toUnicode:function(){return b},ucs2decode:function(){return f},ucs2encode:function(){return d}});const n=2147483647,s=36,o=/^xn--/,i=/[^\0-\x7F]/,a=/[\x2E\u3002\uFF0E\uFF61]/g,c={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},u=Math.floor,l=String.fromCharCode;function p(e){throw new RangeError(c[e])}function h(e,t){const r=e.split("@");let n="";r.length>1&&(n=r[0]+"@",e=r[1]);const s=function(e,t){const r=[];let n=e.length;for(;n--;)r[n]=t(e[n]);return r}((e=e.replace(a,".")).split("."),t).join(".");return n+s}function f(e){const t=[];let r=0;const n=e.length;for(;r<n;){const s=e.charCodeAt(r++);if(s>=55296&&s<=56319&&r<n){const n=e.charCodeAt(r++);56320==(64512&n)?t.push(((1023&s)<<10)+(1023&n)+65536):(t.push(s),r--)}else t.push(s)}return t}const d=e=>String.fromCodePoint(...e),m=function(e,t){return e+22+75*(e<26)-((0!=t)<<5)},g=function(e,t,r){let n=0;for(e=r?u(e/700):e>>1,e+=u(e/t);e>455;n+=s)e=u(e/35);return u(n+36*e/(e+38))},_=function(e){const t=[],r=e.length;let o=0,i=128,a=72,c=e.lastIndexOf("-");c<0&&(c=0);for(let r=0;r<c;++r)e.charCodeAt(r)>=128&&p("not-basic"),t.push(e.charCodeAt(r));for(let h=c>0?c+1:0;h<r;){const c=o;for(let t=1,i=s;;i+=s){h>=r&&p("invalid-input");const c=(l=e.charCodeAt(h++))>=48&&l<58?l-48+26:l>=65&&l<91?l-65:l>=97&&l<123?l-97:s;c>=s&&p("invalid-input"),c>u((n-o)/t)&&p("overflow"),o+=c*t;const f=i<=a?1:i>=a+26?26:i-a;if(c<f)break;const d=s-f;t>u(n/d)&&p("overflow"),t*=d}const f=t.length+1;a=g(o-c,f,0==c),u(o/f)>n-i&&p("overflow"),i+=u(o/f),o%=f,t.splice(o++,0,i)}var l;return String.fromCodePoint(...t)},k=function(e){const t=[],r=(e=f(e)).length;let o=128,i=0,a=72;for(const r of e)r<128&&t.push(l(r));const c=t.length;let h=c;for(c&&t.push("-");h<r;){let r=n;for(const t of e)t>=o&&t<r&&(r=t);const f=h+1;r-o>u((n-i)/f)&&p("overflow"),i+=(r-o)*f,o=r;for(const r of e)if(r<o&&++i>n&&p("overflow"),r===o){let e=i;for(let r=s;;r+=s){const n=r<=a?1:r>=a+26?26:r-a;if(e<n)break;const o=e-n,i=s-n;t.push(l(m(n+o%i,0))),e=u(o/i)}t.push(l(m(e,0))),a=g(i,f,h===c),i=0,++h}++i,++o}return t.join("")},b=function(e){return h(e,(function(e){return o.test(e)?_(e.slice(4).toLowerCase()):e}))},v=function(e){return h(e,(function(e){return i.test(e)?"xn--"+k(e):e}))},C={version:"2.3.1",ucs2:{decode:f,encode:d},decode:_,encode:k,toASCII:v,toUnicode:b};t.default=C},50592:function(e){e.exports=/[\0-\x1F\x7F-\x9F]/},2675:function(e){e.exports=/[\xAD\u0600-\u0605\u061C\u06DD\u070F\u08E2\u180E\u200B-\u200F\u202A-\u202E\u2060-\u2064\u2066-\u206F\uFEFF\uFFF9-\uFFFB]|\uD804[\uDCBD\uDCCD]|\uD82F[\uDCA0-\uDCA3]|\uD834[\uDD73-\uDD7A]|\uDB40[\uDC01\uDC20-\uDC7F]/},2828:function(e){e.exports=/[!-#%-\*,-\/:;\?@\[-\]_\{\}\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061E\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166D\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4E\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]|\uD800[\uDD00-\uDD02\uDF9F\uDFD0]|\uD801\uDD6F|\uD802[\uDC57\uDD1F\uDD3F\uDE50-\uDE58\uDE7F\uDEF0-\uDEF6\uDF39-\uDF3F\uDF99-\uDF9C]|\uD803[\uDF55-\uDF59]|\uD804[\uDC47-\uDC4D\uDCBB\uDCBC\uDCBE-\uDCC1\uDD40-\uDD43\uDD74\uDD75\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDDF\uDE38-\uDE3D\uDEA9]|\uD805[\uDC4B-\uDC4F\uDC5B\uDC5D\uDCC6\uDDC1-\uDDD7\uDE41-\uDE43\uDE60-\uDE6C\uDF3C-\uDF3E]|\uD806[\uDC3B\uDE3F-\uDE46\uDE9A-\uDE9C\uDE9E-\uDEA2]|\uD807[\uDC41-\uDC45\uDC70\uDC71\uDEF7\uDEF8]|\uD809[\uDC70-\uDC74]|\uD81A[\uDE6E\uDE6F\uDEF5\uDF37-\uDF3B\uDF44]|\uD81B[\uDE97-\uDE9A]|\uD82F\uDC9F|\uD836[\uDE87-\uDE8B]|\uD83A[\uDD5E\uDD5F]/},23978:function(e){e.exports=/[ \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]/},39295:function(e,t,r){"use strict";t.Any=r(76027),t.Cc=r(50592),t.Cf=r(2675),t.P=r(2828),t.Z=r(23978)},76027:function(e){e.exports=/[\0-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/},24357:function(e){"use strict";e.exports=JSON.parse('{"Aacute":"Á","aacute":"á","Abreve":"Ă","abreve":"ă","ac":"∾","acd":"∿","acE":"∾̳","Acirc":"Â","acirc":"â","acute":"´","Acy":"А","acy":"а","AElig":"Æ","aelig":"æ","af":"⁡","Afr":"𝔄","afr":"𝔞","Agrave":"À","agrave":"à","alefsym":"ℵ","aleph":"ℵ","Alpha":"Α","alpha":"α","Amacr":"Ā","amacr":"ā","amalg":"⨿","amp":"&","AMP":"&","andand":"⩕","And":"⩓","and":"∧","andd":"⩜","andslope":"⩘","andv":"⩚","ang":"∠","ange":"⦤","angle":"∠","angmsdaa":"⦨","angmsdab":"⦩","angmsdac":"⦪","angmsdad":"⦫","angmsdae":"⦬","angmsdaf":"⦭","angmsdag":"⦮","angmsdah":"⦯","angmsd":"∡","angrt":"∟","angrtvb":"⊾","angrtvbd":"⦝","angsph":"∢","angst":"Å","angzarr":"⍼","Aogon":"Ą","aogon":"ą","Aopf":"𝔸","aopf":"𝕒","apacir":"⩯","ap":"≈","apE":"⩰","ape":"≊","apid":"≋","apos":"\'","ApplyFunction":"⁡","approx":"≈","approxeq":"≊","Aring":"Å","aring":"å","Ascr":"𝒜","ascr":"𝒶","Assign":"≔","ast":"*","asymp":"≈","asympeq":"≍","Atilde":"Ã","atilde":"ã","Auml":"Ä","auml":"ä","awconint":"∳","awint":"⨑","backcong":"≌","backepsilon":"϶","backprime":"‵","backsim":"∽","backsimeq":"⋍","Backslash":"∖","Barv":"⫧","barvee":"⊽","barwed":"⌅","Barwed":"⌆","barwedge":"⌅","bbrk":"⎵","bbrktbrk":"⎶","bcong":"≌","Bcy":"Б","bcy":"б","bdquo":"„","becaus":"∵","because":"∵","Because":"∵","bemptyv":"⦰","bepsi":"϶","bernou":"ℬ","Bernoullis":"ℬ","Beta":"Β","beta":"β","beth":"ℶ","between":"≬","Bfr":"𝔅","bfr":"𝔟","bigcap":"⋂","bigcirc":"◯","bigcup":"⋃","bigodot":"⨀","bigoplus":"⨁","bigotimes":"⨂","bigsqcup":"⨆","bigstar":"★","bigtriangledown":"▽","bigtriangleup":"△","biguplus":"⨄","bigvee":"⋁","bigwedge":"⋀","bkarow":"⤍","blacklozenge":"⧫","blacksquare":"▪","blacktriangle":"▴","blacktriangledown":"▾","blacktriangleleft":"◂","blacktriangleright":"▸","blank":"␣","blk12":"▒","blk14":"░","blk34":"▓","block":"█","bne":"=⃥","bnequiv":"≡⃥","bNot":"⫭","bnot":"⌐","Bopf":"𝔹","bopf":"𝕓","bot":"⊥","bottom":"⊥","bowtie":"⋈","boxbox":"⧉","boxdl":"┐","boxdL":"╕","boxDl":"╖","boxDL":"╗","boxdr":"┌","boxdR":"╒","boxDr":"╓","boxDR":"╔","boxh":"─","boxH":"═","boxhd":"┬","boxHd":"╤","boxhD":"╥","boxHD":"╦","boxhu":"┴","boxHu":"╧","boxhU":"╨","boxHU":"╩","boxminus":"⊟","boxplus":"⊞","boxtimes":"⊠","boxul":"┘","boxuL":"╛","boxUl":"╜","boxUL":"╝","boxur":"└","boxuR":"╘","boxUr":"╙","boxUR":"╚","boxv":"│","boxV":"║","boxvh":"┼","boxvH":"╪","boxVh":"╫","boxVH":"╬","boxvl":"┤","boxvL":"╡","boxVl":"╢","boxVL":"╣","boxvr":"├","boxvR":"╞","boxVr":"╟","boxVR":"╠","bprime":"‵","breve":"˘","Breve":"˘","brvbar":"¦","bscr":"𝒷","Bscr":"ℬ","bsemi":"⁏","bsim":"∽","bsime":"⋍","bsolb":"⧅","bsol":"\\\\","bsolhsub":"⟈","bull":"•","bullet":"•","bump":"≎","bumpE":"⪮","bumpe":"≏","Bumpeq":"≎","bumpeq":"≏","Cacute":"Ć","cacute":"ć","capand":"⩄","capbrcup":"⩉","capcap":"⩋","cap":"∩","Cap":"⋒","capcup":"⩇","capdot":"⩀","CapitalDifferentialD":"ⅅ","caps":"∩︀","caret":"⁁","caron":"ˇ","Cayleys":"ℭ","ccaps":"⩍","Ccaron":"Č","ccaron":"č","Ccedil":"Ç","ccedil":"ç","Ccirc":"Ĉ","ccirc":"ĉ","Cconint":"∰","ccups":"⩌","ccupssm":"⩐","Cdot":"Ċ","cdot":"ċ","cedil":"¸","Cedilla":"¸","cemptyv":"⦲","cent":"¢","centerdot":"·","CenterDot":"·","cfr":"𝔠","Cfr":"ℭ","CHcy":"Ч","chcy":"ч","check":"✓","checkmark":"✓","Chi":"Χ","chi":"χ","circ":"ˆ","circeq":"≗","circlearrowleft":"↺","circlearrowright":"↻","circledast":"⊛","circledcirc":"⊚","circleddash":"⊝","CircleDot":"⊙","circledR":"®","circledS":"Ⓢ","CircleMinus":"⊖","CirclePlus":"⊕","CircleTimes":"⊗","cir":"○","cirE":"⧃","cire":"≗","cirfnint":"⨐","cirmid":"⫯","cirscir":"⧂","ClockwiseContourIntegral":"∲","CloseCurlyDoubleQuote":"”","CloseCurlyQuote":"’","clubs":"♣","clubsuit":"♣","colon":":","Colon":"∷","Colone":"⩴","colone":"≔","coloneq":"≔","comma":",","commat":"@","comp":"∁","compfn":"∘","complement":"∁","complexes":"ℂ","cong":"≅","congdot":"⩭","Congruent":"≡","conint":"∮","Conint":"∯","ContourIntegral":"∮","copf":"𝕔","Copf":"ℂ","coprod":"∐","Coproduct":"∐","copy":"©","COPY":"©","copysr":"℗","CounterClockwiseContourIntegral":"∳","crarr":"↵","cross":"✗","Cross":"⨯","Cscr":"𝒞","cscr":"𝒸","csub":"⫏","csube":"⫑","csup":"⫐","csupe":"⫒","ctdot":"⋯","cudarrl":"⤸","cudarrr":"⤵","cuepr":"⋞","cuesc":"⋟","cularr":"↶","cularrp":"⤽","cupbrcap":"⩈","cupcap":"⩆","CupCap":"≍","cup":"∪","Cup":"⋓","cupcup":"⩊","cupdot":"⊍","cupor":"⩅","cups":"∪︀","curarr":"↷","curarrm":"⤼","curlyeqprec":"⋞","curlyeqsucc":"⋟","curlyvee":"⋎","curlywedge":"⋏","curren":"¤","curvearrowleft":"↶","curvearrowright":"↷","cuvee":"⋎","cuwed":"⋏","cwconint":"∲","cwint":"∱","cylcty":"⌭","dagger":"†","Dagger":"‡","daleth":"ℸ","darr":"↓","Darr":"↡","dArr":"⇓","dash":"‐","Dashv":"⫤","dashv":"⊣","dbkarow":"⤏","dblac":"˝","Dcaron":"Ď","dcaron":"ď","Dcy":"Д","dcy":"д","ddagger":"‡","ddarr":"⇊","DD":"ⅅ","dd":"ⅆ","DDotrahd":"⤑","ddotseq":"⩷","deg":"°","Del":"∇","Delta":"Δ","delta":"δ","demptyv":"⦱","dfisht":"⥿","Dfr":"𝔇","dfr":"𝔡","dHar":"⥥","dharl":"⇃","dharr":"⇂","DiacriticalAcute":"´","DiacriticalDot":"˙","DiacriticalDoubleAcute":"˝","DiacriticalGrave":"`","DiacriticalTilde":"˜","diam":"⋄","diamond":"⋄","Diamond":"⋄","diamondsuit":"♦","diams":"♦","die":"¨","DifferentialD":"ⅆ","digamma":"ϝ","disin":"⋲","div":"÷","divide":"÷","divideontimes":"⋇","divonx":"⋇","DJcy":"Ђ","djcy":"ђ","dlcorn":"⌞","dlcrop":"⌍","dollar":"$","Dopf":"𝔻","dopf":"𝕕","Dot":"¨","dot":"˙","DotDot":"⃜","doteq":"≐","doteqdot":"≑","DotEqual":"≐","dotminus":"∸","dotplus":"∔","dotsquare":"⊡","doublebarwedge":"⌆","DoubleContourIntegral":"∯","DoubleDot":"¨","DoubleDownArrow":"⇓","DoubleLeftArrow":"⇐","DoubleLeftRightArrow":"⇔","DoubleLeftTee":"⫤","DoubleLongLeftArrow":"⟸","DoubleLongLeftRightArrow":"⟺","DoubleLongRightArrow":"⟹","DoubleRightArrow":"⇒","DoubleRightTee":"⊨","DoubleUpArrow":"⇑","DoubleUpDownArrow":"⇕","DoubleVerticalBar":"∥","DownArrowBar":"⤓","downarrow":"↓","DownArrow":"↓","Downarrow":"⇓","DownArrowUpArrow":"⇵","DownBreve":"̑","downdownarrows":"⇊","downharpoonleft":"⇃","downharpoonright":"⇂","DownLeftRightVector":"⥐","DownLeftTeeVector":"⥞","DownLeftVectorBar":"⥖","DownLeftVector":"↽","DownRightTeeVector":"⥟","DownRightVectorBar":"⥗","DownRightVector":"⇁","DownTeeArrow":"↧","DownTee":"⊤","drbkarow":"⤐","drcorn":"⌟","drcrop":"⌌","Dscr":"𝒟","dscr":"𝒹","DScy":"Ѕ","dscy":"ѕ","dsol":"⧶","Dstrok":"Đ","dstrok":"đ","dtdot":"⋱","dtri":"▿","dtrif":"▾","duarr":"⇵","duhar":"⥯","dwangle":"⦦","DZcy":"Џ","dzcy":"џ","dzigrarr":"⟿","Eacute":"É","eacute":"é","easter":"⩮","Ecaron":"Ě","ecaron":"ě","Ecirc":"Ê","ecirc":"ê","ecir":"≖","ecolon":"≕","Ecy":"Э","ecy":"э","eDDot":"⩷","Edot":"Ė","edot":"ė","eDot":"≑","ee":"ⅇ","efDot":"≒","Efr":"𝔈","efr":"𝔢","eg":"⪚","Egrave":"È","egrave":"è","egs":"⪖","egsdot":"⪘","el":"⪙","Element":"∈","elinters":"⏧","ell":"ℓ","els":"⪕","elsdot":"⪗","Emacr":"Ē","emacr":"ē","empty":"∅","emptyset":"∅","EmptySmallSquare":"◻","emptyv":"∅","EmptyVerySmallSquare":"▫","emsp13":" ","emsp14":" ","emsp":" ","ENG":"Ŋ","eng":"ŋ","ensp":" ","Eogon":"Ę","eogon":"ę","Eopf":"𝔼","eopf":"𝕖","epar":"⋕","eparsl":"⧣","eplus":"⩱","epsi":"ε","Epsilon":"Ε","epsilon":"ε","epsiv":"ϵ","eqcirc":"≖","eqcolon":"≕","eqsim":"≂","eqslantgtr":"⪖","eqslantless":"⪕","Equal":"⩵","equals":"=","EqualTilde":"≂","equest":"≟","Equilibrium":"⇌","equiv":"≡","equivDD":"⩸","eqvparsl":"⧥","erarr":"⥱","erDot":"≓","escr":"ℯ","Escr":"ℰ","esdot":"≐","Esim":"⩳","esim":"≂","Eta":"Η","eta":"η","ETH":"Ð","eth":"ð","Euml":"Ë","euml":"ë","euro":"€","excl":"!","exist":"∃","Exists":"∃","expectation":"ℰ","exponentiale":"ⅇ","ExponentialE":"ⅇ","fallingdotseq":"≒","Fcy":"Ф","fcy":"ф","female":"♀","ffilig":"ﬃ","fflig":"ﬀ","ffllig":"ﬄ","Ffr":"𝔉","ffr":"𝔣","filig":"ﬁ","FilledSmallSquare":"◼","FilledVerySmallSquare":"▪","fjlig":"fj","flat":"♭","fllig":"ﬂ","fltns":"▱","fnof":"ƒ","Fopf":"𝔽","fopf":"𝕗","forall":"∀","ForAll":"∀","fork":"⋔","forkv":"⫙","Fouriertrf":"ℱ","fpartint":"⨍","frac12":"½","frac13":"⅓","frac14":"¼","frac15":"⅕","frac16":"⅙","frac18":"⅛","frac23":"⅔","frac25":"⅖","frac34":"¾","frac35":"⅗","frac38":"⅜","frac45":"⅘","frac56":"⅚","frac58":"⅝","frac78":"⅞","frasl":"⁄","frown":"⌢","fscr":"𝒻","Fscr":"ℱ","gacute":"ǵ","Gamma":"Γ","gamma":"γ","Gammad":"Ϝ","gammad":"ϝ","gap":"⪆","Gbreve":"Ğ","gbreve":"ğ","Gcedil":"Ģ","Gcirc":"Ĝ","gcirc":"ĝ","Gcy":"Г","gcy":"г","Gdot":"Ġ","gdot":"ġ","ge":"≥","gE":"≧","gEl":"⪌","gel":"⋛","geq":"≥","geqq":"≧","geqslant":"⩾","gescc":"⪩","ges":"⩾","gesdot":"⪀","gesdoto":"⪂","gesdotol":"⪄","gesl":"⋛︀","gesles":"⪔","Gfr":"𝔊","gfr":"𝔤","gg":"≫","Gg":"⋙","ggg":"⋙","gimel":"ℷ","GJcy":"Ѓ","gjcy":"ѓ","gla":"⪥","gl":"≷","glE":"⪒","glj":"⪤","gnap":"⪊","gnapprox":"⪊","gne":"⪈","gnE":"≩","gneq":"⪈","gneqq":"≩","gnsim":"⋧","Gopf":"𝔾","gopf":"𝕘","grave":"`","GreaterEqual":"≥","GreaterEqualLess":"⋛","GreaterFullEqual":"≧","GreaterGreater":"⪢","GreaterLess":"≷","GreaterSlantEqual":"⩾","GreaterTilde":"≳","Gscr":"𝒢","gscr":"ℊ","gsim":"≳","gsime":"⪎","gsiml":"⪐","gtcc":"⪧","gtcir":"⩺","gt":">","GT":">","Gt":"≫","gtdot":"⋗","gtlPar":"⦕","gtquest":"⩼","gtrapprox":"⪆","gtrarr":"⥸","gtrdot":"⋗","gtreqless":"⋛","gtreqqless":"⪌","gtrless":"≷","gtrsim":"≳","gvertneqq":"≩︀","gvnE":"≩︀","Hacek":"ˇ","hairsp":" ","half":"½","hamilt":"ℋ","HARDcy":"Ъ","hardcy":"ъ","harrcir":"⥈","harr":"↔","hArr":"⇔","harrw":"↭","Hat":"^","hbar":"ℏ","Hcirc":"Ĥ","hcirc":"ĥ","hearts":"♥","heartsuit":"♥","hellip":"…","hercon":"⊹","hfr":"𝔥","Hfr":"ℌ","HilbertSpace":"ℋ","hksearow":"⤥","hkswarow":"⤦","hoarr":"⇿","homtht":"∻","hookleftarrow":"↩","hookrightarrow":"↪","hopf":"𝕙","Hopf":"ℍ","horbar":"―","HorizontalLine":"─","hscr":"𝒽","Hscr":"ℋ","hslash":"ℏ","Hstrok":"Ħ","hstrok":"ħ","HumpDownHump":"≎","HumpEqual":"≏","hybull":"⁃","hyphen":"‐","Iacute":"Í","iacute":"í","ic":"⁣","Icirc":"Î","icirc":"î","Icy":"И","icy":"и","Idot":"İ","IEcy":"Е","iecy":"е","iexcl":"¡","iff":"⇔","ifr":"𝔦","Ifr":"ℑ","Igrave":"Ì","igrave":"ì","ii":"ⅈ","iiiint":"⨌","iiint":"∭","iinfin":"⧜","iiota":"℩","IJlig":"Ĳ","ijlig":"ĳ","Imacr":"Ī","imacr":"ī","image":"ℑ","ImaginaryI":"ⅈ","imagline":"ℐ","imagpart":"ℑ","imath":"ı","Im":"ℑ","imof":"⊷","imped":"Ƶ","Implies":"⇒","incare":"℅","in":"∈","infin":"∞","infintie":"⧝","inodot":"ı","intcal":"⊺","int":"∫","Int":"∬","integers":"ℤ","Integral":"∫","intercal":"⊺","Intersection":"⋂","intlarhk":"⨗","intprod":"⨼","InvisibleComma":"⁣","InvisibleTimes":"⁢","IOcy":"Ё","iocy":"ё","Iogon":"Į","iogon":"į","Iopf":"𝕀","iopf":"𝕚","Iota":"Ι","iota":"ι","iprod":"⨼","iquest":"¿","iscr":"𝒾","Iscr":"ℐ","isin":"∈","isindot":"⋵","isinE":"⋹","isins":"⋴","isinsv":"⋳","isinv":"∈","it":"⁢","Itilde":"Ĩ","itilde":"ĩ","Iukcy":"І","iukcy":"і","Iuml":"Ï","iuml":"ï","Jcirc":"Ĵ","jcirc":"ĵ","Jcy":"Й","jcy":"й","Jfr":"𝔍","jfr":"𝔧","jmath":"ȷ","Jopf":"𝕁","jopf":"𝕛","Jscr":"𝒥","jscr":"𝒿","Jsercy":"Ј","jsercy":"ј","Jukcy":"Є","jukcy":"є","Kappa":"Κ","kappa":"κ","kappav":"ϰ","Kcedil":"Ķ","kcedil":"ķ","Kcy":"К","kcy":"к","Kfr":"𝔎","kfr":"𝔨","kgreen":"ĸ","KHcy":"Х","khcy":"х","KJcy":"Ќ","kjcy":"ќ","Kopf":"𝕂","kopf":"𝕜","Kscr":"𝒦","kscr":"𝓀","lAarr":"⇚","Lacute":"Ĺ","lacute":"ĺ","laemptyv":"⦴","lagran":"ℒ","Lambda":"Λ","lambda":"λ","lang":"⟨","Lang":"⟪","langd":"⦑","langle":"⟨","lap":"⪅","Laplacetrf":"ℒ","laquo":"«","larrb":"⇤","larrbfs":"⤟","larr":"←","Larr":"↞","lArr":"⇐","larrfs":"⤝","larrhk":"↩","larrlp":"↫","larrpl":"⤹","larrsim":"⥳","larrtl":"↢","latail":"⤙","lAtail":"⤛","lat":"⪫","late":"⪭","lates":"⪭︀","lbarr":"⤌","lBarr":"⤎","lbbrk":"❲","lbrace":"{","lbrack":"[","lbrke":"⦋","lbrksld":"⦏","lbrkslu":"⦍","Lcaron":"Ľ","lcaron":"ľ","Lcedil":"Ļ","lcedil":"ļ","lceil":"⌈","lcub":"{","Lcy":"Л","lcy":"л","ldca":"⤶","ldquo":"“","ldquor":"„","ldrdhar":"⥧","ldrushar":"⥋","ldsh":"↲","le":"≤","lE":"≦","LeftAngleBracket":"⟨","LeftArrowBar":"⇤","leftarrow":"←","LeftArrow":"←","Leftarrow":"⇐","LeftArrowRightArrow":"⇆","leftarrowtail":"↢","LeftCeiling":"⌈","LeftDoubleBracket":"⟦","LeftDownTeeVector":"⥡","LeftDownVectorBar":"⥙","LeftDownVector":"⇃","LeftFloor":"⌊","leftharpoondown":"↽","leftharpoonup":"↼","leftleftarrows":"⇇","leftrightarrow":"↔","LeftRightArrow":"↔","Leftrightarrow":"⇔","leftrightarrows":"⇆","leftrightharpoons":"⇋","leftrightsquigarrow":"↭","LeftRightVector":"⥎","LeftTeeArrow":"↤","LeftTee":"⊣","LeftTeeVector":"⥚","leftthreetimes":"⋋","LeftTriangleBar":"⧏","LeftTriangle":"⊲","LeftTriangleEqual":"⊴","LeftUpDownVector":"⥑","LeftUpTeeVector":"⥠","LeftUpVectorBar":"⥘","LeftUpVector":"↿","LeftVectorBar":"⥒","LeftVector":"↼","lEg":"⪋","leg":"⋚","leq":"≤","leqq":"≦","leqslant":"⩽","lescc":"⪨","les":"⩽","lesdot":"⩿","lesdoto":"⪁","lesdotor":"⪃","lesg":"⋚︀","lesges":"⪓","lessapprox":"⪅","lessdot":"⋖","lesseqgtr":"⋚","lesseqqgtr":"⪋","LessEqualGreater":"⋚","LessFullEqual":"≦","LessGreater":"≶","lessgtr":"≶","LessLess":"⪡","lesssim":"≲","LessSlantEqual":"⩽","LessTilde":"≲","lfisht":"⥼","lfloor":"⌊","Lfr":"𝔏","lfr":"𝔩","lg":"≶","lgE":"⪑","lHar":"⥢","lhard":"↽","lharu":"↼","lharul":"⥪","lhblk":"▄","LJcy":"Љ","ljcy":"љ","llarr":"⇇","ll":"≪","Ll":"⋘","llcorner":"⌞","Lleftarrow":"⇚","llhard":"⥫","lltri":"◺","Lmidot":"Ŀ","lmidot":"ŀ","lmoustache":"⎰","lmoust":"⎰","lnap":"⪉","lnapprox":"⪉","lne":"⪇","lnE":"≨","lneq":"⪇","lneqq":"≨","lnsim":"⋦","loang":"⟬","loarr":"⇽","lobrk":"⟦","longleftarrow":"⟵","LongLeftArrow":"⟵","Longleftarrow":"⟸","longleftrightarrow":"⟷","LongLeftRightArrow":"⟷","Longleftrightarrow":"⟺","longmapsto":"⟼","longrightarrow":"⟶","LongRightArrow":"⟶","Longrightarrow":"⟹","looparrowleft":"↫","looparrowright":"↬","lopar":"⦅","Lopf":"𝕃","lopf":"𝕝","loplus":"⨭","lotimes":"⨴","lowast":"∗","lowbar":"_","LowerLeftArrow":"↙","LowerRightArrow":"↘","loz":"◊","lozenge":"◊","lozf":"⧫","lpar":"(","lparlt":"⦓","lrarr":"⇆","lrcorner":"⌟","lrhar":"⇋","lrhard":"⥭","lrm":"‎","lrtri":"⊿","lsaquo":"‹","lscr":"𝓁","Lscr":"ℒ","lsh":"↰","Lsh":"↰","lsim":"≲","lsime":"⪍","lsimg":"⪏","lsqb":"[","lsquo":"‘","lsquor":"‚","Lstrok":"Ł","lstrok":"ł","ltcc":"⪦","ltcir":"⩹","lt":"<","LT":"<","Lt":"≪","ltdot":"⋖","lthree":"⋋","ltimes":"⋉","ltlarr":"⥶","ltquest":"⩻","ltri":"◃","ltrie":"⊴","ltrif":"◂","ltrPar":"⦖","lurdshar":"⥊","luruhar":"⥦","lvertneqq":"≨︀","lvnE":"≨︀","macr":"¯","male":"♂","malt":"✠","maltese":"✠","Map":"⤅","map":"↦","mapsto":"↦","mapstodown":"↧","mapstoleft":"↤","mapstoup":"↥","marker":"▮","mcomma":"⨩","Mcy":"М","mcy":"м","mdash":"—","mDDot":"∺","measuredangle":"∡","MediumSpace":" ","Mellintrf":"ℳ","Mfr":"𝔐","mfr":"𝔪","mho":"℧","micro":"µ","midast":"*","midcir":"⫰","mid":"∣","middot":"·","minusb":"⊟","minus":"−","minusd":"∸","minusdu":"⨪","MinusPlus":"∓","mlcp":"⫛","mldr":"…","mnplus":"∓","models":"⊧","Mopf":"𝕄","mopf":"𝕞","mp":"∓","mscr":"𝓂","Mscr":"ℳ","mstpos":"∾","Mu":"Μ","mu":"μ","multimap":"⊸","mumap":"⊸","nabla":"∇","Nacute":"Ń","nacute":"ń","nang":"∠⃒","nap":"≉","napE":"⩰̸","napid":"≋̸","napos":"ŉ","napprox":"≉","natural":"♮","naturals":"ℕ","natur":"♮","nbsp":" ","nbump":"≎̸","nbumpe":"≏̸","ncap":"⩃","Ncaron":"Ň","ncaron":"ň","Ncedil":"Ņ","ncedil":"ņ","ncong":"≇","ncongdot":"⩭̸","ncup":"⩂","Ncy":"Н","ncy":"н","ndash":"–","nearhk":"⤤","nearr":"↗","neArr":"⇗","nearrow":"↗","ne":"≠","nedot":"≐̸","NegativeMediumSpace":"​","NegativeThickSpace":"​","NegativeThinSpace":"​","NegativeVeryThinSpace":"​","nequiv":"≢","nesear":"⤨","nesim":"≂̸","NestedGreaterGreater":"≫","NestedLessLess":"≪","NewLine":"\\n","nexist":"∄","nexists":"∄","Nfr":"𝔑","nfr":"𝔫","ngE":"≧̸","nge":"≱","ngeq":"≱","ngeqq":"≧̸","ngeqslant":"⩾̸","nges":"⩾̸","nGg":"⋙̸","ngsim":"≵","nGt":"≫⃒","ngt":"≯","ngtr":"≯","nGtv":"≫̸","nharr":"↮","nhArr":"⇎","nhpar":"⫲","ni":"∋","nis":"⋼","nisd":"⋺","niv":"∋","NJcy":"Њ","njcy":"њ","nlarr":"↚","nlArr":"⇍","nldr":"‥","nlE":"≦̸","nle":"≰","nleftarrow":"↚","nLeftarrow":"⇍","nleftrightarrow":"↮","nLeftrightarrow":"⇎","nleq":"≰","nleqq":"≦̸","nleqslant":"⩽̸","nles":"⩽̸","nless":"≮","nLl":"⋘̸","nlsim":"≴","nLt":"≪⃒","nlt":"≮","nltri":"⋪","nltrie":"⋬","nLtv":"≪̸","nmid":"∤","NoBreak":"⁠","NonBreakingSpace":" ","nopf":"𝕟","Nopf":"ℕ","Not":"⫬","not":"¬","NotCongruent":"≢","NotCupCap":"≭","NotDoubleVerticalBar":"∦","NotElement":"∉","NotEqual":"≠","NotEqualTilde":"≂̸","NotExists":"∄","NotGreater":"≯","NotGreaterEqual":"≱","NotGreaterFullEqual":"≧̸","NotGreaterGreater":"≫̸","NotGreaterLess":"≹","NotGreaterSlantEqual":"⩾̸","NotGreaterTilde":"≵","NotHumpDownHump":"≎̸","NotHumpEqual":"≏̸","notin":"∉","notindot":"⋵̸","notinE":"⋹̸","notinva":"∉","notinvb":"⋷","notinvc":"⋶","NotLeftTriangleBar":"⧏̸","NotLeftTriangle":"⋪","NotLeftTriangleEqual":"⋬","NotLess":"≮","NotLessEqual":"≰","NotLessGreater":"≸","NotLessLess":"≪̸","NotLessSlantEqual":"⩽̸","NotLessTilde":"≴","NotNestedGreaterGreater":"⪢̸","NotNestedLessLess":"⪡̸","notni":"∌","notniva":"∌","notnivb":"⋾","notnivc":"⋽","NotPrecedes":"⊀","NotPrecedesEqual":"⪯̸","NotPrecedesSlantEqual":"⋠","NotReverseElement":"∌","NotRightTriangleBar":"⧐̸","NotRightTriangle":"⋫","NotRightTriangleEqual":"⋭","NotSquareSubset":"⊏̸","NotSquareSubsetEqual":"⋢","NotSquareSuperset":"⊐̸","NotSquareSupersetEqual":"⋣","NotSubset":"⊂⃒","NotSubsetEqual":"⊈","NotSucceeds":"⊁","NotSucceedsEqual":"⪰̸","NotSucceedsSlantEqual":"⋡","NotSucceedsTilde":"≿̸","NotSuperset":"⊃⃒","NotSupersetEqual":"⊉","NotTilde":"≁","NotTildeEqual":"≄","NotTildeFullEqual":"≇","NotTildeTilde":"≉","NotVerticalBar":"∤","nparallel":"∦","npar":"∦","nparsl":"⫽⃥","npart":"∂̸","npolint":"⨔","npr":"⊀","nprcue":"⋠","nprec":"⊀","npreceq":"⪯̸","npre":"⪯̸","nrarrc":"⤳̸","nrarr":"↛","nrArr":"⇏","nrarrw":"↝̸","nrightarrow":"↛","nRightarrow":"⇏","nrtri":"⋫","nrtrie":"⋭","nsc":"⊁","nsccue":"⋡","nsce":"⪰̸","Nscr":"𝒩","nscr":"𝓃","nshortmid":"∤","nshortparallel":"∦","nsim":"≁","nsime":"≄","nsimeq":"≄","nsmid":"∤","nspar":"∦","nsqsube":"⋢","nsqsupe":"⋣","nsub":"⊄","nsubE":"⫅̸","nsube":"⊈","nsubset":"⊂⃒","nsubseteq":"⊈","nsubseteqq":"⫅̸","nsucc":"⊁","nsucceq":"⪰̸","nsup":"⊅","nsupE":"⫆̸","nsupe":"⊉","nsupset":"⊃⃒","nsupseteq":"⊉","nsupseteqq":"⫆̸","ntgl":"≹","Ntilde":"Ñ","ntilde":"ñ","ntlg":"≸","ntriangleleft":"⋪","ntrianglelefteq":"⋬","ntriangleright":"⋫","ntrianglerighteq":"⋭","Nu":"Ν","nu":"ν","num":"#","numero":"№","numsp":" ","nvap":"≍⃒","nvdash":"⊬","nvDash":"⊭","nVdash":"⊮","nVDash":"⊯","nvge":"≥⃒","nvgt":">⃒","nvHarr":"⤄","nvinfin":"⧞","nvlArr":"⤂","nvle":"≤⃒","nvlt":"<⃒","nvltrie":"⊴⃒","nvrArr":"⤃","nvrtrie":"⊵⃒","nvsim":"∼⃒","nwarhk":"⤣","nwarr":"↖","nwArr":"⇖","nwarrow":"↖","nwnear":"⤧","Oacute":"Ó","oacute":"ó","oast":"⊛","Ocirc":"Ô","ocirc":"ô","ocir":"⊚","Ocy":"О","ocy":"о","odash":"⊝","Odblac":"Ő","odblac":"ő","odiv":"⨸","odot":"⊙","odsold":"⦼","OElig":"Œ","oelig":"œ","ofcir":"⦿","Ofr":"𝔒","ofr":"𝔬","ogon":"˛","Ograve":"Ò","ograve":"ò","ogt":"⧁","ohbar":"⦵","ohm":"Ω","oint":"∮","olarr":"↺","olcir":"⦾","olcross":"⦻","oline":"‾","olt":"⧀","Omacr":"Ō","omacr":"ō","Omega":"Ω","omega":"ω","Omicron":"Ο","omicron":"ο","omid":"⦶","ominus":"⊖","Oopf":"𝕆","oopf":"𝕠","opar":"⦷","OpenCurlyDoubleQuote":"“","OpenCurlyQuote":"‘","operp":"⦹","oplus":"⊕","orarr":"↻","Or":"⩔","or":"∨","ord":"⩝","order":"ℴ","orderof":"ℴ","ordf":"ª","ordm":"º","origof":"⊶","oror":"⩖","orslope":"⩗","orv":"⩛","oS":"Ⓢ","Oscr":"𝒪","oscr":"ℴ","Oslash":"Ø","oslash":"ø","osol":"⊘","Otilde":"Õ","otilde":"õ","otimesas":"⨶","Otimes":"⨷","otimes":"⊗","Ouml":"Ö","ouml":"ö","ovbar":"⌽","OverBar":"‾","OverBrace":"⏞","OverBracket":"⎴","OverParenthesis":"⏜","para":"¶","parallel":"∥","par":"∥","parsim":"⫳","parsl":"⫽","part":"∂","PartialD":"∂","Pcy":"П","pcy":"п","percnt":"%","period":".","permil":"‰","perp":"⊥","pertenk":"‱","Pfr":"𝔓","pfr":"𝔭","Phi":"Φ","phi":"φ","phiv":"ϕ","phmmat":"ℳ","phone":"☎","Pi":"Π","pi":"π","pitchfork":"⋔","piv":"ϖ","planck":"ℏ","planckh":"ℎ","plankv":"ℏ","plusacir":"⨣","plusb":"⊞","pluscir":"⨢","plus":"+","plusdo":"∔","plusdu":"⨥","pluse":"⩲","PlusMinus":"±","plusmn":"±","plussim":"⨦","plustwo":"⨧","pm":"±","Poincareplane":"ℌ","pointint":"⨕","popf":"𝕡","Popf":"ℙ","pound":"£","prap":"⪷","Pr":"⪻","pr":"≺","prcue":"≼","precapprox":"⪷","prec":"≺","preccurlyeq":"≼","Precedes":"≺","PrecedesEqual":"⪯","PrecedesSlantEqual":"≼","PrecedesTilde":"≾","preceq":"⪯","precnapprox":"⪹","precneqq":"⪵","precnsim":"⋨","pre":"⪯","prE":"⪳","precsim":"≾","prime":"′","Prime":"″","primes":"ℙ","prnap":"⪹","prnE":"⪵","prnsim":"⋨","prod":"∏","Product":"∏","profalar":"⌮","profline":"⌒","profsurf":"⌓","prop":"∝","Proportional":"∝","Proportion":"∷","propto":"∝","prsim":"≾","prurel":"⊰","Pscr":"𝒫","pscr":"𝓅","Psi":"Ψ","psi":"ψ","puncsp":" ","Qfr":"𝔔","qfr":"𝔮","qint":"⨌","qopf":"𝕢","Qopf":"ℚ","qprime":"⁗","Qscr":"𝒬","qscr":"𝓆","quaternions":"ℍ","quatint":"⨖","quest":"?","questeq":"≟","quot":"\\"","QUOT":"\\"","rAarr":"⇛","race":"∽̱","Racute":"Ŕ","racute":"ŕ","radic":"√","raemptyv":"⦳","rang":"⟩","Rang":"⟫","rangd":"⦒","range":"⦥","rangle":"⟩","raquo":"»","rarrap":"⥵","rarrb":"⇥","rarrbfs":"⤠","rarrc":"⤳","rarr":"→","Rarr":"↠","rArr":"⇒","rarrfs":"⤞","rarrhk":"↪","rarrlp":"↬","rarrpl":"⥅","rarrsim":"⥴","Rarrtl":"⤖","rarrtl":"↣","rarrw":"↝","ratail":"⤚","rAtail":"⤜","ratio":"∶","rationals":"ℚ","rbarr":"⤍","rBarr":"⤏","RBarr":"⤐","rbbrk":"❳","rbrace":"}","rbrack":"]","rbrke":"⦌","rbrksld":"⦎","rbrkslu":"⦐","Rcaron":"Ř","rcaron":"ř","Rcedil":"Ŗ","rcedil":"ŗ","rceil":"⌉","rcub":"}","Rcy":"Р","rcy":"р","rdca":"⤷","rdldhar":"⥩","rdquo":"”","rdquor":"”","rdsh":"↳","real":"ℜ","realine":"ℛ","realpart":"ℜ","reals":"ℝ","Re":"ℜ","rect":"▭","reg":"®","REG":"®","ReverseElement":"∋","ReverseEquilibrium":"⇋","ReverseUpEquilibrium":"⥯","rfisht":"⥽","rfloor":"⌋","rfr":"𝔯","Rfr":"ℜ","rHar":"⥤","rhard":"⇁","rharu":"⇀","rharul":"⥬","Rho":"Ρ","rho":"ρ","rhov":"ϱ","RightAngleBracket":"⟩","RightArrowBar":"⇥","rightarrow":"→","RightArrow":"→","Rightarrow":"⇒","RightArrowLeftArrow":"⇄","rightarrowtail":"↣","RightCeiling":"⌉","RightDoubleBracket":"⟧","RightDownTeeVector":"⥝","RightDownVectorBar":"⥕","RightDownVector":"⇂","RightFloor":"⌋","rightharpoondown":"⇁","rightharpoonup":"⇀","rightleftarrows":"⇄","rightleftharpoons":"⇌","rightrightarrows":"⇉","rightsquigarrow":"↝","RightTeeArrow":"↦","RightTee":"⊢","RightTeeVector":"⥛","rightthreetimes":"⋌","RightTriangleBar":"⧐","RightTriangle":"⊳","RightTriangleEqual":"⊵","RightUpDownVector":"⥏","RightUpTeeVector":"⥜","RightUpVectorBar":"⥔","RightUpVector":"↾","RightVectorBar":"⥓","RightVector":"⇀","ring":"˚","risingdotseq":"≓","rlarr":"⇄","rlhar":"⇌","rlm":"‏","rmoustache":"⎱","rmoust":"⎱","rnmid":"⫮","roang":"⟭","roarr":"⇾","robrk":"⟧","ropar":"⦆","ropf":"𝕣","Ropf":"ℝ","roplus":"⨮","rotimes":"⨵","RoundImplies":"⥰","rpar":")","rpargt":"⦔","rppolint":"⨒","rrarr":"⇉","Rrightarrow":"⇛","rsaquo":"›","rscr":"𝓇","Rscr":"ℛ","rsh":"↱","Rsh":"↱","rsqb":"]","rsquo":"’","rsquor":"’","rthree":"⋌","rtimes":"⋊","rtri":"▹","rtrie":"⊵","rtrif":"▸","rtriltri":"⧎","RuleDelayed":"⧴","ruluhar":"⥨","rx":"℞","Sacute":"Ś","sacute":"ś","sbquo":"‚","scap":"⪸","Scaron":"Š","scaron":"š","Sc":"⪼","sc":"≻","sccue":"≽","sce":"⪰","scE":"⪴","Scedil":"Ş","scedil":"ş","Scirc":"Ŝ","scirc":"ŝ","scnap":"⪺","scnE":"⪶","scnsim":"⋩","scpolint":"⨓","scsim":"≿","Scy":"С","scy":"с","sdotb":"⊡","sdot":"⋅","sdote":"⩦","searhk":"⤥","searr":"↘","seArr":"⇘","searrow":"↘","sect":"§","semi":";","seswar":"⤩","setminus":"∖","setmn":"∖","sext":"✶","Sfr":"𝔖","sfr":"𝔰","sfrown":"⌢","sharp":"♯","SHCHcy":"Щ","shchcy":"щ","SHcy":"Ш","shcy":"ш","ShortDownArrow":"↓","ShortLeftArrow":"←","shortmid":"∣","shortparallel":"∥","ShortRightArrow":"→","ShortUpArrow":"↑","shy":"­","Sigma":"Σ","sigma":"σ","sigmaf":"ς","sigmav":"ς","sim":"∼","simdot":"⩪","sime":"≃","simeq":"≃","simg":"⪞","simgE":"⪠","siml":"⪝","simlE":"⪟","simne":"≆","simplus":"⨤","simrarr":"⥲","slarr":"←","SmallCircle":"∘","smallsetminus":"∖","smashp":"⨳","smeparsl":"⧤","smid":"∣","smile":"⌣","smt":"⪪","smte":"⪬","smtes":"⪬︀","SOFTcy":"Ь","softcy":"ь","solbar":"⌿","solb":"⧄","sol":"/","Sopf":"𝕊","sopf":"𝕤","spades":"♠","spadesuit":"♠","spar":"∥","sqcap":"⊓","sqcaps":"⊓︀","sqcup":"⊔","sqcups":"⊔︀","Sqrt":"√","sqsub":"⊏","sqsube":"⊑","sqsubset":"⊏","sqsubseteq":"⊑","sqsup":"⊐","sqsupe":"⊒","sqsupset":"⊐","sqsupseteq":"⊒","square":"□","Square":"□","SquareIntersection":"⊓","SquareSubset":"⊏","SquareSubsetEqual":"⊑","SquareSuperset":"⊐","SquareSupersetEqual":"⊒","SquareUnion":"⊔","squarf":"▪","squ":"□","squf":"▪","srarr":"→","Sscr":"𝒮","sscr":"𝓈","ssetmn":"∖","ssmile":"⌣","sstarf":"⋆","Star":"⋆","star":"☆","starf":"★","straightepsilon":"ϵ","straightphi":"ϕ","strns":"¯","sub":"⊂","Sub":"⋐","subdot":"⪽","subE":"⫅","sube":"⊆","subedot":"⫃","submult":"⫁","subnE":"⫋","subne":"⊊","subplus":"⪿","subrarr":"⥹","subset":"⊂","Subset":"⋐","subseteq":"⊆","subseteqq":"⫅","SubsetEqual":"⊆","subsetneq":"⊊","subsetneqq":"⫋","subsim":"⫇","subsub":"⫕","subsup":"⫓","succapprox":"⪸","succ":"≻","succcurlyeq":"≽","Succeeds":"≻","SucceedsEqual":"⪰","SucceedsSlantEqual":"≽","SucceedsTilde":"≿","succeq":"⪰","succnapprox":"⪺","succneqq":"⪶","succnsim":"⋩","succsim":"≿","SuchThat":"∋","sum":"∑","Sum":"∑","sung":"♪","sup1":"¹","sup2":"²","sup3":"³","sup":"⊃","Sup":"⋑","supdot":"⪾","supdsub":"⫘","supE":"⫆","supe":"⊇","supedot":"⫄","Superset":"⊃","SupersetEqual":"⊇","suphsol":"⟉","suphsub":"⫗","suplarr":"⥻","supmult":"⫂","supnE":"⫌","supne":"⊋","supplus":"⫀","supset":"⊃","Supset":"⋑","supseteq":"⊇","supseteqq":"⫆","supsetneq":"⊋","supsetneqq":"⫌","supsim":"⫈","supsub":"⫔","supsup":"⫖","swarhk":"⤦","swarr":"↙","swArr":"⇙","swarrow":"↙","swnwar":"⤪","szlig":"ß","Tab":"\\t","target":"⌖","Tau":"Τ","tau":"τ","tbrk":"⎴","Tcaron":"Ť","tcaron":"ť","Tcedil":"Ţ","tcedil":"ţ","Tcy":"Т","tcy":"т","tdot":"⃛","telrec":"⌕","Tfr":"𝔗","tfr":"𝔱","there4":"∴","therefore":"∴","Therefore":"∴","Theta":"Θ","theta":"θ","thetasym":"ϑ","thetav":"ϑ","thickapprox":"≈","thicksim":"∼","ThickSpace":"  ","ThinSpace":" ","thinsp":" ","thkap":"≈","thksim":"∼","THORN":"Þ","thorn":"þ","tilde":"˜","Tilde":"∼","TildeEqual":"≃","TildeFullEqual":"≅","TildeTilde":"≈","timesbar":"⨱","timesb":"⊠","times":"×","timesd":"⨰","tint":"∭","toea":"⤨","topbot":"⌶","topcir":"⫱","top":"⊤","Topf":"𝕋","topf":"𝕥","topfork":"⫚","tosa":"⤩","tprime":"‴","trade":"™","TRADE":"™","triangle":"▵","triangledown":"▿","triangleleft":"◃","trianglelefteq":"⊴","triangleq":"≜","triangleright":"▹","trianglerighteq":"⊵","tridot":"◬","trie":"≜","triminus":"⨺","TripleDot":"⃛","triplus":"⨹","trisb":"⧍","tritime":"⨻","trpezium":"⏢","Tscr":"𝒯","tscr":"𝓉","TScy":"Ц","tscy":"ц","TSHcy":"Ћ","tshcy":"ћ","Tstrok":"Ŧ","tstrok":"ŧ","twixt":"≬","twoheadleftarrow":"↞","twoheadrightarrow":"↠","Uacute":"Ú","uacute":"ú","uarr":"↑","Uarr":"↟","uArr":"⇑","Uarrocir":"⥉","Ubrcy":"Ў","ubrcy":"ў","Ubreve":"Ŭ","ubreve":"ŭ","Ucirc":"Û","ucirc":"û","Ucy":"У","ucy":"у","udarr":"⇅","Udblac":"Ű","udblac":"ű","udhar":"⥮","ufisht":"⥾","Ufr":"𝔘","ufr":"𝔲","Ugrave":"Ù","ugrave":"ù","uHar":"⥣","uharl":"↿","uharr":"↾","uhblk":"▀","ulcorn":"⌜","ulcorner":"⌜","ulcrop":"⌏","ultri":"◸","Umacr":"Ū","umacr":"ū","uml":"¨","UnderBar":"_","UnderBrace":"⏟","UnderBracket":"⎵","UnderParenthesis":"⏝","Union":"⋃","UnionPlus":"⊎","Uogon":"Ų","uogon":"ų","Uopf":"𝕌","uopf":"𝕦","UpArrowBar":"⤒","uparrow":"↑","UpArrow":"↑","Uparrow":"⇑","UpArrowDownArrow":"⇅","updownarrow":"↕","UpDownArrow":"↕","Updownarrow":"⇕","UpEquilibrium":"⥮","upharpoonleft":"↿","upharpoonright":"↾","uplus":"⊎","UpperLeftArrow":"↖","UpperRightArrow":"↗","upsi":"υ","Upsi":"ϒ","upsih":"ϒ","Upsilon":"Υ","upsilon":"υ","UpTeeArrow":"↥","UpTee":"⊥","upuparrows":"⇈","urcorn":"⌝","urcorner":"⌝","urcrop":"⌎","Uring":"Ů","uring":"ů","urtri":"◹","Uscr":"𝒰","uscr":"𝓊","utdot":"⋰","Utilde":"Ũ","utilde":"ũ","utri":"▵","utrif":"▴","uuarr":"⇈","Uuml":"Ü","uuml":"ü","uwangle":"⦧","vangrt":"⦜","varepsilon":"ϵ","varkappa":"ϰ","varnothing":"∅","varphi":"ϕ","varpi":"ϖ","varpropto":"∝","varr":"↕","vArr":"⇕","varrho":"ϱ","varsigma":"ς","varsubsetneq":"⊊︀","varsubsetneqq":"⫋︀","varsupsetneq":"⊋︀","varsupsetneqq":"⫌︀","vartheta":"ϑ","vartriangleleft":"⊲","vartriangleright":"⊳","vBar":"⫨","Vbar":"⫫","vBarv":"⫩","Vcy":"В","vcy":"в","vdash":"⊢","vDash":"⊨","Vdash":"⊩","VDash":"⊫","Vdashl":"⫦","veebar":"⊻","vee":"∨","Vee":"⋁","veeeq":"≚","vellip":"⋮","verbar":"|","Verbar":"‖","vert":"|","Vert":"‖","VerticalBar":"∣","VerticalLine":"|","VerticalSeparator":"❘","VerticalTilde":"≀","VeryThinSpace":" ","Vfr":"𝔙","vfr":"𝔳","vltri":"⊲","vnsub":"⊂⃒","vnsup":"⊃⃒","Vopf":"𝕍","vopf":"𝕧","vprop":"∝","vrtri":"⊳","Vscr":"𝒱","vscr":"𝓋","vsubnE":"⫋︀","vsubne":"⊊︀","vsupnE":"⫌︀","vsupne":"⊋︀","Vvdash":"⊪","vzigzag":"⦚","Wcirc":"Ŵ","wcirc":"ŵ","wedbar":"⩟","wedge":"∧","Wedge":"⋀","wedgeq":"≙","weierp":"℘","Wfr":"𝔚","wfr":"𝔴","Wopf":"𝕎","wopf":"𝕨","wp":"℘","wr":"≀","wreath":"≀","Wscr":"𝒲","wscr":"𝓌","xcap":"⋂","xcirc":"◯","xcup":"⋃","xdtri":"▽","Xfr":"𝔛","xfr":"𝔵","xharr":"⟷","xhArr":"⟺","Xi":"Ξ","xi":"ξ","xlarr":"⟵","xlArr":"⟸","xmap":"⟼","xnis":"⋻","xodot":"⨀","Xopf":"𝕏","xopf":"𝕩","xoplus":"⨁","xotime":"⨂","xrarr":"⟶","xrArr":"⟹","Xscr":"𝒳","xscr":"𝓍","xsqcup":"⨆","xuplus":"⨄","xutri":"△","xvee":"⋁","xwedge":"⋀","Yacute":"Ý","yacute":"ý","YAcy":"Я","yacy":"я","Ycirc":"Ŷ","ycirc":"ŷ","Ycy":"Ы","ycy":"ы","yen":"¥","Yfr":"𝔜","yfr":"𝔶","YIcy":"Ї","yicy":"ї","Yopf":"𝕐","yopf":"𝕪","Yscr":"𝒴","yscr":"𝓎","YUcy":"Ю","yucy":"ю","yuml":"ÿ","Yuml":"Ÿ","Zacute":"Ź","zacute":"ź","Zcaron":"Ž","zcaron":"ž","Zcy":"З","zcy":"з","Zdot":"Ż","zdot":"ż","zeetrf":"ℨ","ZeroWidthSpace":"​","Zeta":"Ζ","zeta":"ζ","zfr":"𝔷","Zfr":"ℨ","ZHcy":"Ж","zhcy":"ж","zigrarr":"⇝","zopf":"𝕫","Zopf":"ℤ","Zscr":"𝒵","zscr":"𝓏","zwj":"‍","zwnj":"‌"}')}}]);