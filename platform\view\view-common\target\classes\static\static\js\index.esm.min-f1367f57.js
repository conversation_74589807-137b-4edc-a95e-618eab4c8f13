import{ck as ue,cp as _e,aS as ce,d as de,H as T,bo as A,Z as fe,a0 as O,cq as le,L as j,cr as Q,n as ve,cs as he,W as V,ct as ge,a9 as pe,cu as ze,u as be}from"./index-bb2cbf17.js";const we=(e,t,r)=>e=ue({},_e(t,r),e),Be=(e,t)=>(e.backgroundColor="rgba(0,0,0,0)",we(e,ce,t)),Pe=(e,t)=>{if(!e)return;const r=e.getOption();r.dataset=null,e.setOption(t)};var W=null;function ye(e){return W||(W=(window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){return setTimeout(t,16)}).bind(window)),W(e)}var D=null;function Oe(e){D||(D=(window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||function(t){clearTimeout(t)}).bind(window)),D(e)}function me(e){var t=document.createElement("style");return t.styleSheet?t.styleSheet.cssText=e:t.appendChild(document.createTextNode(e)),(document.querySelector("head")||document.body).appendChild(t),t}function H(e,t){t===void 0&&(t={});var r=document.createElement(e);return Object.keys(t).forEach(function(i){r[i]=t[i]}),r}function Y(e,t,r){var i=window.getComputedStyle(e,r||null)||{display:"none"};return i[t]}function N(e){if(!document.documentElement.contains(e))return{detached:!0,rendered:!1};for(var t=e;t!==document;){if(Y(t,"display")==="none")return{detached:!1,rendered:!1};t=t.parentNode}return{detached:!1,rendered:!0}}var Ee='.resize-triggers{visibility:hidden;opacity:0;pointer-events:none}.resize-contract-trigger,.resize-contract-trigger:before,.resize-expand-trigger,.resize-triggers{content:"";position:absolute;top:0;left:0;height:100%;width:100%;overflow:hidden}.resize-contract-trigger,.resize-expand-trigger{background:#eee;overflow:auto}.resize-contract-trigger:before{width:200%;height:200%}',U=0,M=null;function xe(e,t){e.__resize_mutation_handler__||(e.__resize_mutation_handler__=Se.bind(e));var r=e.__resize_listeners__;if(!r){if(e.__resize_listeners__=[],window.ResizeObserver){var i=e.offsetWidth,a=e.offsetHeight,n=new ResizeObserver(function(){!e.__resize_observer_triggered__&&(e.__resize_observer_triggered__=!0,e.offsetWidth===i&&e.offsetHeight===a)||R(e)}),c=N(e),z=c.detached,l=c.rendered;e.__resize_observer_triggered__=z===!1&&l===!1,e.__resize_observer__=n,n.observe(e)}else if(e.attachEvent&&e.addEventListener)e.__resize_legacy_resize_handler__=function(){R(e)},e.attachEvent("onresize",e.__resize_legacy_resize_handler__),document.addEventListener("DOMSubtreeModified",e.__resize_mutation_handler__);else if(U||(M=me(Ee)),Te(e),e.__resize_rendered__=N(e).rendered,window.MutationObserver){var v=new MutationObserver(e.__resize_mutation_handler__);v.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0}),e.__resize_mutation_observer__=v}}e.__resize_listeners__.push(t),U++}function Ce(e,t){var r=e.__resize_listeners__;if(r){if(t&&r.splice(r.indexOf(t),1),!r.length||!t){if(e.detachEvent&&e.removeEventListener){e.detachEvent("onresize",e.__resize_legacy_resize_handler__),document.removeEventListener("DOMSubtreeModified",e.__resize_mutation_handler__);return}e.__resize_observer__?(e.__resize_observer__.unobserve(e),e.__resize_observer__.disconnect(),e.__resize_observer__=null):(e.__resize_mutation_observer__&&(e.__resize_mutation_observer__.disconnect(),e.__resize_mutation_observer__=null),e.removeEventListener("scroll",q),e.removeChild(e.__resize_triggers__.triggers),e.__resize_triggers__=null),e.__resize_listeners__=null}!--U&&M&&M.parentNode.removeChild(M)}}function Le(e){var t=e.__resize_last__,r=t.width,i=t.height,a=e.offsetWidth,n=e.offsetHeight;return a!==r||n!==i?{width:a,height:n}:null}function Se(){var e=N(this),t=e.rendered,r=e.detached;t!==this.__resize_rendered__&&(!r&&this.__resize_triggers__&&(B(this),this.addEventListener("scroll",q,!0)),this.__resize_rendered__=t,R(this))}function q(){var e=this;B(this),this.__resize_raf__&&Oe(this.__resize_raf__),this.__resize_raf__=ye(function(){var t=Le(e);t&&(e.__resize_last__=t,R(e))})}function R(e){!e||!e.__resize_listeners__||e.__resize_listeners__.forEach(function(t){t.call(e,e)})}function Te(e){var t=Y(e,"position");(!t||t==="static")&&(e.style.position="relative"),e.__resize_old_position__=t,e.__resize_last__={};var r=H("div",{className:"resize-triggers"}),i=H("div",{className:"resize-expand-trigger"}),a=H("div"),n=H("div",{className:"resize-contract-trigger"});i.appendChild(a),r.appendChild(i),r.appendChild(n),e.appendChild(r),e.__resize_triggers__={triggers:r,expand:i,expandChild:a,contract:n},B(e),e.addEventListener("scroll",q,!0),e.__resize_last__={width:e.offsetWidth,height:e.offsetHeight}}function B(e){var t=e.__resize_triggers__,r=t.expand,i=t.expandChild,a=t.contract,n=a.scrollWidth,c=a.scrollHeight,z=r.offsetWidth,l=r.offsetHeight,v=r.scrollWidth,f=r.scrollHeight;a.scrollLeft=n,a.scrollTop=c,i.style.width=z+1+"px",i.style.height=l+1+"px",r.scrollLeft=v,r.scrollTop=f}var p=function(){return p=Object.assign||function(e){for(var t,r=1,i=arguments.length;r<i;r++)for(var a in t=arguments[r])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},p.apply(this,arguments)};var Ae=["getWidth","getHeight","getDom","getOption","resize","dispatchAction","convertToPixel","convertFromPixel","containPixel","getDataURL","getConnectedDataURL","appendData","clear","isDisposed","dispose"];function je(e){return t=Object.create(null),Ae.forEach(function(r){t[r]=function(i){return function(){for(var a=[],n=0;n<arguments.length;n++)a[n]=arguments[n];if(!e.value)throw new Error("ECharts is not initialized yet.");return e.value[i].apply(e.value,a)}}(r)}),t;var t}var He={autoresize:[Boolean,Object]},ke=/^on[^a-z]/,Me=function(e){return ke.test(e)};function k(e,t){var r=be(e);return r&&typeof r=="object"&&"value"in r?r.value||t:r||t}var Re="ecLoadingOptions",Fe={loading:Boolean,loadingOptions:Object},m=null,ee="x-vue-echarts",X=[],E=[];(function(e,t){if(e&&typeof document!="undefined"){var r,i=t.prepend===!0?"prepend":"append",a=t.singleTag===!0,n=typeof t.container=="string"?document.querySelector(t.container):document.getElementsByTagName("head")[0];if(a){var c=X.indexOf(n);c===-1&&(c=X.push(n)-1,E[c]={}),r=E[c]&&E[c][i]?E[c][i]:E[c][i]=z()}else r=z();e.charCodeAt(0)===65279&&(e=e.substring(1)),r.styleSheet?r.styleSheet.cssText+=e:r.appendChild(document.createTextNode(e))}function z(){var l=document.createElement("style");if(l.setAttribute("type","text/css"),t.attributes)for(var v=Object.keys(t.attributes),f=0;f<v.length;f++)l.setAttribute(v[f],t.attributes[v[f]]);var x=i==="prepend"?"afterbegin":"beforeend";return n.insertAdjacentElement(x,l),l}})(`x-vue-echarts{display:block;width:100%;height:100%;min-width:0}x-vue-echarts>div{width:100%;height:100%}
`,{});var We=function(){if(m!=null)return m;if(typeof HTMLElement=="undefined"||typeof customElements=="undefined")return m=!1;try{new Function("tag",`class EChartsElement extends HTMLElement {
  __dispose = null;

  disconnectedCallback() {
    if (this.__dispose) {
      this.__dispose();
      this.__dispose = null;
    }
  }
}

if (customElements.get(tag) == null) {
  customElements.define(tag, EChartsElement);
}
`)(ee)}catch(e){return m=!1}return m=!0}(),De="ecTheme",Ne="ecInitOptions",Ue="ecUpdateOptions",Ie=de({name:"echarts",props:p(p({option:Object,theme:{type:[Object,String]},initOptions:Object,updateOptions:Object,group:String,manualUpdate:Boolean},He),Fe),emits:{},inheritAttrs:!1,setup:function(e,t){var r=t.attrs,i=T(),a=T(),n=T(),c=T(),z=A(De,null),l=A(Ne,null),v=A(Ue,null),f=fe(e),x=f.autoresize,te=f.manualUpdate,re=f.loading,ne=f.loadingOptions,ie=O(function(){return c.value||e.option||null}),P=O(function(){return e.theme||k(z,{})}),I=O(function(){return e.initOptions||k(l,{})}),$=O(function(){return e.updateOptions||k(v,{})}),ae=O(function(){return function(d){var s={};for(var _ in d)Me(_)||(s[_]=d[_]);return s}(r)}),se=le().proxy.$listeners;function C(d){if(a.value){var s=n.value=ge(a.value,P.value,I.value);e.group&&(s.group=e.group);var _=se;_||(_={},Object.keys(r).filter(function(u){return u.indexOf("on")===0&&u.length>2}).forEach(function(u){var o=u.charAt(2).toLowerCase()+u.slice(3);o.substring(o.length-4)==="Once"&&(o="~".concat(o.substring(0,o.length-4))),_[o]=r[u]})),Object.keys(_).forEach(function(u){var o=_[u];if(o){var h=u.toLowerCase();h.charAt(0)==="~"&&(h=h.substring(1),o.__once__=!0);var b=s;if(h.indexOf("zr:")===0&&(b=s.getZr(),h=h.substring(3)),o.__once__){delete o.__once__;var S=o;o=function(){for(var y=[],w=0;w<arguments.length;w++)y[w]=arguments[w];S.apply(void 0,y),b.off(h,o)}}b.on(h,o)}}),x.value?pe(function(){s&&!s.isDisposed()&&s.resize(),g()}):g()}function g(){var u=d||ie.value;u&&s.setOption(u,$.value)}}function F(){n.value&&(n.value.dispose(),n.value=void 0)}var L=null;j(te,function(d){typeof L=="function"&&(L(),L=null),d||(L=j(function(){return e.option},function(s,_){s&&(n.value?n.value.setOption(s,p({notMerge:s!==_},$.value)):C())},{deep:!0}))},{immediate:!0}),j([P,I],function(){F(),C()},{deep:!0}),Q(function(){e.group&&n.value&&(n.value.group=e.group)});var oe=je(n);return function(d,s,_){var g=A(Re,{}),u=O(function(){return p(p({},k(g,{})),_==null?void 0:_.value)});Q(function(){var o=d.value;o&&(s.value?o.showLoading(u.value):o.hideLoading())})}(n,re,ne),function(d,s,_){var g=null;j([_,d,s],function(u,o,h){var b=u[0],S=u[1],y=u[2];if(b&&S&&y){var w=y===!0?{}:y,Z=w.throttle,G=Z===void 0?100:Z,J=w.onResize,K=function(){S.resize(),J==null||J()};g=G?ze(K,G):K,xe(b,g)}h(function(){b&&g&&Ce(b,g)})})}(n,x,a),ve(function(){C()}),he(function(){We&&i.value?i.value.__dispose=F:F()}),p({chart:n,root:i,inner:a,setOption:function(d,s){e.manualUpdate&&(c.value=d),n.value?n.value.setOption(d,s||{}):C(d)},nonEventAttrs:ae},oe)},render:function(){var e=p({},this.nonEventAttrs);return e.ref="root",e.class=e.class?["echarts"].concat(e.class):"echarts",V(ee,e,[V("div",{ref:"inner"})])}});export{Ie as B,Be as e,we as m,Pe as s};
