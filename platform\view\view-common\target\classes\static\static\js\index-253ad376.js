var _t=Object.defineProperty,mt=Object.defineProperties;var Ct=Object.getOwnPropertyDescriptors;var qe=Object.getOwnPropertySymbols;var yt=Object.prototype.hasOwnProperty,St=Object.prototype.propertyIsEnumerable;var je=(e,o,s)=>o in e?_t(e,o,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[o]=s,P=(e,o)=>{for(var s in o||(o={}))yt.call(o,s)&&je(e,s,o[s]);if(qe)for(var s of qe(o))St.call(o,s)&&je(e,s,o[s]);return e},fe=(e,o)=>mt(e,Ct(o));var ue=(e,o,s)=>new Promise((d,t)=>{var g=u=>{try{v(s.next(u))}catch(i){t(i)}},l=u=>{try{v(s.throw(u))}catch(i){t(i)}},v=u=>u.done?d(u.value):Promise.resolve(u.value).then(g,l);v((s=s.apply(e,o)).next())});import{n as Ge,a1 as nt,cH as Tt,aE as we,cI as G,b9 as Oe,a7 as xt,d as Z,b8 as _e,m as me,a0 as $,o as E,c as B,b as Ke,F as ae,s as ie,ao as ne,an as q,u as r,a5 as Le,q as R,j as ee,aw as at,E as X,w as b,G as de,aq as Be,au as Ee,av as Xe,ar as rt,a6 as J,$ as lt,p as Fe,a8 as Je,L as le,ap as ct,r as H,cJ as wt,Z as Ie,l as K,cK as Ze,A as De,B as Me,e as k,g as Et,f as he,t as se,aQ as bt,cv as N,cw as F,cr as kt,cL as Lt,aD as It,cM as Dt,a9 as Mt,cN as $t,C as Rt,ac as Qe,Q as Ue,R as He,bd as Ot,N as Ut,cO as Ht,b4 as it,O as At,b1 as Pt,S as Te,aC as Ae,bk as Yt}from"./index-bb2cbf17.js";import{u as ze,d as Bt}from"./index-288f9b11.js";import{u as te,f as V,g as D,h as xe,a as Xt}from"./chartEditStore-55fbe93c.js";import{e as Nt,S as Gt}from"./index-0ec04aee.js";import{a as Kt}from"./useKeyboard.hook-d93304b3.js";import{a as Ft,b as et,l as zt,g as Wt}from"./plugin-3ef0fcec.js";import{l as be}from"./lodash-d17632fd.js";import{C as Vt}from"./index-17dc177f.js";import"./querySelectorAll-a1c431d2.js";import{l as tt}from"./listen-92ff7612.js";import{u as ut,C as dt}from"./chartLayoutStore-88198b25.js";import{i as pe}from"./icon-f36697ff.js";import{G as qt}from"./index.vue_vue_type_script_setup_true_lang-825e1dc4.js";import{F as ot}from"./fileTypeEnum-21359a08.js";import{u as pt,a as jt}from"./useSyncUpdate.hook-2ef81e1e.js";import"./index-56351f34.js";import"./SettingItem-7fe1cfec.js";/* empty css                                                                      */import"./SettingItemBox-500aaf18.js";import"./CollapseItem.vue_vue_type_script_setup_true_lang-9b6d8958.js";import"./index.esm.min-f1367f57.js";import"./http-36f53bd1.js";const ge=te(),Jt=e=>{let o=()=>{};Ge(()=>ue(void 0,null,function*(){ge.setEditCanvas(V.EDIT_LAYOUT_DOM,document.getElementById("go-chart-edit-layout")),ge.setEditCanvas(V.EDIT_CONTENT_DOM,document.getElementById("go-chart-edit-content")),yield e(),o=ge.listenerScale()})),nt(()=>{ge.setEditCanvas(V.EDIT_LAYOUT_DOM,null),ge.setEditCanvas(V.EDIT_CONTENT_DOM,null),o()})},y=te(),{onClickOutSide:Zt}=ze(),Qt=e=>ue(void 0,null,function*(){e.preventDefault();try{Ft();const o=e.dataTransfer.getData(Tt.DRAG_KEY);if(!o){et();return}y.setEditCanvas(V.IS_CREATE,!1);const s=we(o);if(s.disabled)return;let d=yield Nt(s);s.redirectComponent&&(s.dataset&&(d.option.dataset=s.dataset),d.chartConfig.title=s.title,d.chartConfig.chartFrame=s.chartFrame),G(d,e.offsetX-d.attr.w/2,e.offsetY-d.attr.h/2),y.addComponentList(d,!1,!0),y.setTargetSelectChart(d.id),et()}catch(o){zt(),window.$message.warning("图表正在研发中, 敬请期待...")}}),st=e=>{e.preventDefault(),e.stopPropagation(),e.dataTransfer&&(e.dataTransfer.dropEffect="copy")},ft=(e,o)=>{if(o){y.setTargetSelectChart(o.id);return}y.setTargetSelectChart(void 0)},eo=(e,o)=>{var i;if(e.which==2||(i=window.$KeyboardActive)!=null&&i.space)return;ft();const s=e.offsetX,d=e.offsetY,t=e.screenX,g=e.screenY,l=y.getEditCanvas.scale;y.setMousePosition(void 0,void 0,s,d);const v=be.throttle(p=>{y.setTargetSelectChart(),y.setEditCanvas(V.IS_SELECT,!0);const a=s+p.screenX-t,n=d+p.screenY-g;y.setMousePosition(a,n);const C={x1:0,y1:0,x2:0,y2:0};a>s&&n>d?(C.x1=s,C.y1=d,C.x2=Math.round(s+(p.screenX-t)/l),C.y2=Math.round(d+(p.screenY-g)/l)):a>s&&n<d?(C.x1=s,C.y1=Math.round(d-(g-p.screenY)/l),C.x2=Math.round(s+(p.screenX-t)/l),C.y2=d):a<s&&n>d?(C.x1=Math.round(s-(t-p.screenX)/l),C.y1=d,C.x2=s,C.y2=Math.round(d+(p.screenY-g)/l)):(C.x1=Math.round(s-(t-p.screenX)/l),C.y1=Math.round(d-(g-p.screenY)/l),C.x2=s,C.y2=d),y.getComponentList.forEach(c=>{if(!y.getTargetChart.selectId.includes(c.id)){const{x:h,y:S,w:x,h:w}=c.attr,T={x1:h,y1:S,x2:h+x,y2:S+w};T.x1-C.x1>=0&&T.y1-C.y1>=0&&T.x2-C.x2<=0&&T.y2-C.y2<=0&&!c.status.lock&&!c.status.hide&&y.setTargetSelectChart(c.id,!0)}})},30),u=()=>{v.cancel(),y.setEditCanvas(V.IS_SELECT,!1),y.setMousePosition(0,0,0,0),document.removeEventListener("mousemove",v),document.removeEventListener("mouseup",u)};document.addEventListener("mousemove",v),document.addEventListener("mouseup",u)},gt=()=>({mouseClickHandle:(t,g)=>{var l;if(t.preventDefault(),t.stopPropagation(),!g.status.lock&&(l=window.$KeyboardActive)!=null&&l.ctrl)if(y.targetChart.selectId.includes(g.id)){const v=y.targetChart.selectId.filter(u=>u!==g.id);y.setTargetSelectChart(v)}else y.setTargetSelectChart(g.id,!0)},mousedownHandle:(t,g)=>{var S;if(t.preventDefault(),t.stopPropagation(),g.status.lock||(Zt(),t.buttons===Oe.LEFT&&((S=window.$KeyboardActive)!=null&&S.ctrl)))return;const l=y.getTargetChart.selectId;if(t.buttons===Oe.RIGHT&&l.length>1&&l.includes(g.id)||(y.setTargetSelectChart(g.id),t.buttons===Oe.RIGHT))return;const v=y.getEditCanvas.scale,u=y.getEditCanvasConfig.width,i=y.getEditCanvasConfig.height,p=new Map;y.getTargetChart.selectId.forEach(x=>{const w=y.fetchTargetIndex(x);if(w!==-1){const{x:T,y:f,w:L,h:I}=xt(y.getComponentList[w]).attr;p.set(x,{x:T,y:f,w:L,h:I})}});const a=t.screenX,n=t.screenY;let C=[];y.getTargetChart.selectId.forEach(x=>{if(!p.has(x))return;const w=y.fetchTargetIndex(x);C.push(be.cloneDeep(y.getComponentList[w]))}),y.setMousePosition(void 0,void 0,a,n);const c=be.throttle(x=>{y.setEditCanvas(V.IS_DRAG,!0),y.setMousePosition(x.screenX,x.screenY);let w=(x.screenX-a)/v,T=(x.screenY-n)/v;y.getTargetChart.selectId.forEach(f=>{if(!p.has(f))return;const L=y.fetchTargetIndex(f),{x:I,y:oe,w:Y,h:m}=p.get(f),O=y.getComponentList[L];let _=Math.round(I+w),M=Math.round(oe+T);const U=50;_=_<-Y+U?-Y+U:_,M=M<-m+U?-m+U:M,_=_>u-U?u-U:_,M=M>i-U?i-U:M,O&&(O.attr=Object.assign(O.attr,{x:_,y:M}))})},20),h=()=>{try{y.setMousePosition(0,0,0,0),y.setEditCanvas(V.IS_DRAG,!1),C.length&&(y.getTargetChart.selectId.forEach(x=>{if(!p.has(x))return;const w=y.fetchTargetIndex(x),T=y.getComponentList[w];C.forEach(f=>{f.id===x&&(f.attr=Object.assign(f.attr,{offsetX:T.attr.x-f.attr.x,offsetY:T.attr.y-f.attr.y}))})}),y.moveComponentList(C)),document.removeEventListener("mousemove",c),document.removeEventListener("mouseup",h)}catch(x){console.log(x)}};document.addEventListener("mousemove",c),document.addEventListener("mouseup",h)},mouseenterHandle:(t,g)=>{t.preventDefault(),t.stopPropagation(),y.getEditCanvas.isSelect||y.setTargetHoverChart(g.id)},mouseleaveHandle:(t,g)=>{t.preventDefault(),t.stopPropagation(),y.setEditCanvas(V.IS_DRAG,!1),y.setTargetHoverChart(void 0)}}),to=(e,o,s)=>{e.stopPropagation(),e.preventDefault(),y.setEditCanvas(V.IS_DRAG,!0);const d=y.getEditCanvas.scale,t=s.x,g=s.y,l=s.w,v=s.h,u=e.screenX,i=e.screenY;y.setMousePosition(u,i);const p=be.throttle(n=>{y.setMousePosition(n.screenX,n.screenY);let C=Math.round((n.screenX-u)/d),c=Math.round((n.screenY-i)/d);const h=/t/.test(o),S=/b/.test(o),x=/l/.test(o),w=/r/.test(o),T=v+(h?-c:S?c:0),f=l+(x?-C:w?C:0);s.h=T>0?T:0,s.w=f>0?f:0,s.x=t+(x?C:0),s.y=g+(h?c:0)},50),a=()=>{y.setEditCanvas(V.IS_DRAG,!1),y.setMousePosition(0,0,0,0),document.removeEventListener("mousemove",p),document.removeEventListener("mouseup",a)};document.addEventListener("mousemove",p),document.addEventListener("mouseup",a)},ke=(e,o)=>e?{zIndex:o+1,left:`${e.x}px`,top:`${e.y}px`}:{},ce=(e,o)=>e?{width:`${o?o*e.w:e.w}px`,height:`${o?o*e.h:e.h}px`}:{},oo=(e,o,s,d)=>{const{w:t,h:g}=s,l=/t/.test(e),v=/b/.test(e),u=/l/.test(e),i=/r/.test(e);let p=0,a=0;return e.length===2?(p=u?0:t,a=l?0:g):((l||v)&&(p=t/2,a=l?0:g),(u||i)&&(p=u?0:t,a=Math.floor(g/2))),{left:`${p}px`,top:`${a}px`,cursor:d[o]+"-resize"}},so=["onMousedown"],no=Z({__name:"index",props:{item:{type:Object,required:!0},hiddenPoint:{type:Boolean,required:!1}},setup(e){_e(a=>({"2d925dfa":l.value}));const o=e,s=me(),d=te(),t=["t","r","b","l","lt","rt","lb","rb"],g=["n","e","s","w","nw","ne","sw","se"],l=$(()=>s.getAppTheme),v=$(()=>d.getEditCanvas[V.IS_DRAG]||o.item.status.lock?!1:o.item.id===d.getTargetChart.hoverId),u=$(()=>{const a=o.item.id;return o.item.status.lock?!1:d.getTargetChart.selectId.find(n=>n===a)}),i=$(()=>o.item.status.lock),p=$(()=>o.item.status.hide);return(a,n)=>(E(),B("div",{class:ne(["go-shape-box",{lock:i.value,hide:p.value}])},[Ke(a.$slots,"default",{},void 0,!0),e.hiddenPoint?Le("",!0):(E(!0),B(ae,{key:0},ie(u.value?t:[],(C,c)=>(E(),B("div",{class:ne(`shape-point ${C}`),key:c,style:q(r(oo)(C,c,e.item.attr,g)),onMousedown:h=>r(to)(h,C,e.item.attr)},null,46,so))),128)),R("div",{class:"shape-modal",style:q(r(ce)(e.item.attr))},[R("div",{class:ne(["shape-modal-select",{active:u.value}])},null,2),R("div",{class:ne(["shape-modal-change",{selectActive:u.value,hoverActive:v.value}])},null,2)],4)],2))}});const Ne=ee(no,[["__scopeId","data-v-25c3c11c"]]),ao={class:"go-edit-group-box"},ro=Z({__name:"index",props:{groupData:{type:Object,required:!0},groupIndex:{type:Number,required:!0}},setup(e){const o=te(),{handleContextMenu:s}=ze(),{mouseenterHandle:d,mouseleaveHandle:t,mousedownHandle:g,mouseClickHandle:l}=gt(),v=(p,a,n)=>{const C=c=>a.filter(h=>c.includes(h.key));if(o.getTargetChart.selectId.length>1)return C([J.GROUP,J.DELETE]);{const c=[];n.status.lock?c.push(J.LOCK):c.push(J.UNLOCK),n.status.hide?c.push(J.HIDE):c.push(J.SHOW);const h=[J.UN_GROUP];return[...C(h),Bt(),...p.filter(S=>!c.includes(S.key))]}},u=$(()=>at(o.getEditCanvasConfig.chartCustomThemeColorInfo)[o.getEditCanvasConfig.chartThemeColor]),i=$(()=>o.getEditCanvasConfig.chartThemeSetting);return(p,a)=>(E(),B("div",ao,[(E(),X(r(Ne),{key:e.groupData.id,"data-id":e.groupData.id,index:e.groupIndex,item:e.groupData,hiddenPoint:!0,class:ne(r(Be)(e.groupData.styles.animations)),style:q(P(P(P(P(P({},r(ke)(e.groupData.attr,e.groupIndex)),r(ce)(e.groupData.attr)),r(Ee)(e.groupData.styles)),r(Xe)(e.groupData.styles)),r(rt)(e.groupData.styles))),onClick:a[0]||(a[0]=n=>r(l)(n,e.groupData)),onMousedown:a[1]||(a[1]=n=>r(g)(n,e.groupData)),onMouseenter:a[2]||(a[2]=n=>r(d)(n,e.groupData)),onMouseleave:a[3]||(a[3]=n=>r(t)(n,e.groupData)),onContextmenu:a[4]||(a[4]=n=>r(s)(n,e.groupData,v))},{default:b(()=>[(E(!0),B(ae,null,ie(e.groupData.groupList,n=>(E(),X(r(Ne),{key:n.id,"data-id":n.id,index:e.groupIndex,item:n,hiddenPoint:!0,style:q(P({},r(ke)(n.attr,e.groupIndex)))},{default:b(()=>[(E(),X(de(n.chartConfig.chartKey),{class:ne(["edit-content-chart",r(Be)(n.styles.animations)]),chartConfig:n,themeSetting:i.value,themeColor:u.value,style:q(P(P(P({},r(ce)(n.attr)),r(Ee)(n.styles)),r(Xe)(n.styles)))},null,8,["class","chartConfig","themeSetting","themeColor","style"]))]),_:2},1032,["data-id","index","item","style"]))),128))]),_:1},8,["data-id","index","item","class","style"]))]))}}),lo={class:"go-edit-align-line"},co=Z({__name:"index",setup(e){_e(c=>({"2a25621e":l.value}));const o=me(),s=te(),d=lt(),t=Fe({lineArr:["rowt","rowc","rowb","coll","colc","colr"],select:new Map,sorptioned:{x:!1,y:!1}}),g=c=>c?{left:`${c.x?c.x:0}px`,top:`${c.y?c.y:0}px`}:{},l=$(()=>o.getAppTheme),v=$(()=>d.getChartAlignRange),u=$(()=>s.getEditCanvas[V.IS_DRAG]),i=(c,h)=>Math.abs(c-h)<=v.value,p=$(()=>s.getTargetChart.selectId),a=$(()=>s.getComponentList[s.fetchTargetIndex()]),n=$(()=>{var c;return((c=a.value)==null?void 0:c.attr)||{}}),C=$(()=>({id:"0",attr:{w:Je(s.getEditCanvasConfig.width),h:Je(s.getEditCanvasConfig.height),x:0,y:0,offsetX:0,offsetY:0,zIndex:0}}));return le(()=>s.getMousePosition,ct(()=>{try{if(!u.value||p.value.length!==1)return;const c=n.value.w,h=n.value.h,S=n.value.x,x=S+c/2,w=S+c,T=[S,x,w],f=n.value.y,L=f+h/2,I=f+h,oe=[f,L,I];t.select.clear(),t.sorptioned.y=!1;const Y=s.getComponentList.map(m=>({id:m.id,attr:m.attr}));Y.push(C.value),t.lineArr.forEach(m=>{Y.forEach(O=>{if(p.value[0]===O.id)return;const _=O.attr.w,M=O.attr.h,U=O.attr.x,W=U+_/2,A=U+_,Ce=[U,W,A],z=O.attr.y,j=z+M/2,Q=z+M,Ve=[z,j,Q];m.includes("rowt")&&(i(f,z)&&(t.select.set(m,{y:z}),G(a.value,S,z)),i(f,j)&&(t.select.set(m,{y:j}),G(a.value,S,j)),i(f,Q)&&(t.select.set(m,{y:Q}),G(a.value,S,Q))),m.includes("rowc")&&(i(L,z)&&(t.select.set(m,{y:z}),G(a.value,S,z-h/2)),i(L,j)&&(t.select.set(m,{y:j}),G(a.value,S,j-h/2)),i(L,Q)&&(t.select.set(m,{y:Q}),G(a.value,S,Q-h/2))),m.includes("rowb")&&(i(I,z)&&(t.select.set(m,{y:z}),G(a.value,S,z-h)),i(I,j)&&(t.select.set(m,{y:j}),G(a.value,S,j-h)),i(I,Q)&&(t.select.set(m,{y:Q}),G(a.value,S,Q-h))),m.includes("coll")&&(i(S,U)&&(t.select.set(m,{x:U}),G(a.value,U,f)),i(S,W)&&(t.select.set(m,{x:W}),G(a.value,W,f)),i(S,A)&&(t.select.set(m,{x:A}),G(a.value,A,f))),m.includes("colc")&&(i(x,U)&&(t.select.set(m,{x:U}),G(a.value,U-c/2,f)),i(x,W)&&(t.select.set(m,{x:W}),G(a.value,W-c/2,f)),i(x,A)&&(t.select.set(m,{x:A}),G(a.value,A-c/2,f))),m.includes("colr")&&(i(w,U)&&(t.select.set(m,{x:U}),G(a.value,U-c,f)),i(w,W)&&(t.select.set(m,{x:W}),G(a.value,W-c,f)),i(w,A)&&(t.select.set(m,{x:A}),G(a.value,A-c,f)))})})}catch(c){console.log(c)}},200),{deep:!0}),le(()=>u.value,c=>{c||(t.select.clear(),t.sorptioned.y=!1)}),(c,h)=>(E(),B("div",lo,[(E(!0),B(ae,null,ie(t.lineArr,S=>(E(),B("div",{class:ne(["line",[S.includes("row")?"row":"col",t.select.has(S)&&"visible"]]),key:S,style:q(g(t.select.get(S)))},null,6))),128))]))}});const io=ee(co,[["__scopeId","data-v-792c43f5"]]),uo=Z({__name:"index",setup(e){return(o,s)=>{const d=H("n-watermark");return E(),X(d,{id:"go-edit-watermark",content:r(wt),cross:"",selectable:"","font-size":16,"line-height":16,width:500,height:150,"x-offset":12,"y-offset":80,rotate:-15},null,8,["content"])}}});const po=ee(uo,[["__scopeId","data-v-15e7d440"]]),vt=e=>(De("data-v-fd03a995"),e=e(),Me(),e),fo=vt(()=>R("div",{class:"select-background"},null,-1)),go=vt(()=>R("div",{class:"select-border"},null,-1)),vo=[fo,go],ho=Z({__name:"index",setup(e){_e(v=>({"3a6a09b6":g.value}));const o=me(),s=te(),{isSelect:d,scale:t}=Ie(s.getEditCanvas),g=$(()=>o.getAppTheme),l=K();return le(()=>s.getMousePosition,v=>{if(!d.value)return;const{startX:u,startY:i,x:p,y:a}=v,n={zIndex:Ze,x:0,y:0,w:0,h:0,offsetX:0,offsetY:0};p>u&&a>i?(n.x=u,n.y=i,n.w=Math.round((p-u)/t.value),n.h=Math.round((a-i)/t.value)):p>u&&a<i?(n.x=u,n.w=Math.round((p-u)/t.value),n.h=Math.round((i-a)/t.value),n.y=i-n.h):p<u&&a>i?(n.y=i,n.w=Math.round((u-p)/t.value),n.h=Math.round((a-i)/t.value),n.x=u-n.w):(n.w=Math.round((u-p)/t.value),n.h=Math.round((i-a)/t.value),n.x=u-n.w,n.y=i-n.h),l.value=P(P({},ke(n,Ze)),ce(n))},{deep:!0}),(v,u)=>r(d)?(E(),B("div",{key:0,class:"go-edit-select",style:q(l.value)},vo,4)):Le("",!0)}});const _o=ee(ho,[["__scopeId","data-v-fd03a995"]]),mo=Z({__name:"index",setup(e){const o=te(),{getEditCanvasConfig:s,getEditCanvas:d}=Ie(o),t=$(()=>({w:s.value.width,h:s.value.height})),g=$(()=>{const v={transform:`scale(${d.value.scale})`};return P(P({},ce(t.value)),v)}),l=$(()=>{const v=d.value.isCreate&&{"z-index":99999};return P(P({},ce(t.value)),v)});return(v,u)=>(E(),B("div",{class:"go-edit-range go-transition",style:q(g.value),onMousedown:u[0]||(u[0]=i=>r(eo)(i,void 0))},[Ke(v.$slots,"default",{},void 0,!0),k(r(po)),k(r(io)),k(r(_o)),R("div",{class:"go-edit-range-model",style:q(l.value)},null,4)],36))}});const Co=ee(mo,[["__scopeId","data-v-d34efa6e"]]),yo={class:"go-sketch-rule"},Pe=20,So=Z({__name:"index",setup(e){_e(_=>({"72798b56":f.value,"1b47bf80":w.value,"26ca27c0":a.value}));const o=te(),s=ut(),d=me();let t=[0,0],g=[0,0];const l=K(),v=K(!0),u=K(),i=K(),p=K(!1),a=K("auto"),{width:n,height:C}=Ie(o.getEditCanvasConfig),c=K(0),h=K(0),S=Fe({h:[],v:[]}),x=$(()=>o.getEditCanvas.scale),w=$(()=>`${C.value*2}px`),T=$(()=>d.getDarkTheme?{bgColor:"#18181c",longfgColor:"#4d4d4d",shortfgColor:"#4d4d4d",fontColor:"#4d4d4d",shadowColor:"#18181c",borderColor:"#18181c",cornerActiveColor:"#18181c"}:{}),f=$(()=>d.getAppTheme),L=_=>{if(_.ctrlKey||_.metaKey){_.preventDefault();let M=x.value;if(_.wheelDelta>=0&&x.value<2){M=x.value+.05,o.setScale(M);return}_.wheelDelta<0&&x.value>.1&&(M=x.value-.05,o.setScale(M))}},I=()=>{if(!l.value)return;const _=l.value.getBoundingClientRect(),M=u.value.getBoundingClientRect();c.value=(_.left+Pe-M.left)/x.value,h.value=(_.top+Pe-M.top)/x.value},oe=_=>{var Ce,z;if(_.preventDefault(),_.stopPropagation(),_.which==2)p.value=!0;else if(!((Ce=window.$KeyboardActive)!=null&&Ce.space))return;(z=document.activeElement)==null||z.blur();const M=_.pageX,U=_.pageY,W=tt(window,"mousemove",j=>{const Q=j.pageX-M,Ve=j.pageY-U,[$e,ye]=t,[Re,Se]=g;t=[ye,Q],g=[Se,Ve],l.value.scrollLeft-=ye>$e?Math.abs(ye-$e):-Math.abs(ye-$e),l.value.scrollTop-=Se>Re?Math.abs(Se-Re):-Math.abs(Se-Re)}),A=tt(window,"mouseup",()=>{W(),A(),t=[0,0],g=[0,0],p.value=!1})},Y=()=>{const _=document.getElementById("go-chart-edit-layout");return _?{height:_.clientHeight-20,width:_.clientWidth-20}:{width:n.value,height:C.value}},m=()=>{v.value=!1,setTimeout(()=>{v.value=!0},10)},O=()=>{const{width:_,height:M}=i.value.getBoundingClientRect(),{width:U,height:W}=Y();l.value.scrollLeft=_/2-U/2,l.value.scrollTop=M/2-W/2};return le(()=>d.getDarkTheme,()=>{m()}),le(()=>x.value,(_,M)=>{M!==_&&s.getRePositionCanvas?(s.setItemUnHandle(dt.RE_POSITION_CANVAS,!1),I(),setTimeout(()=>{O(),m()},400)):ct(m,20)}),le(()=>p.value,_=>{a.value=_?"grab":"auto"}),Ge(()=>{l.value&&(l.value.addEventListener("wheel",L,{passive:!1}),O())}),nt(()=>{l.value&&l.value.removeEventListener("wheel",L)}),window.onKeySpacePressHold=_=>{p.value=_},(_,M)=>{const U=H("sketch-rule");return E(),B("div",yo,[v.value?(E(),X(U,{key:0,thick:Pe,scale:x.value,width:Y().width,height:Y().height,startX:c.value,startY:h.value,lines:S,palette:T.value},null,8,["scale","width","height","startX","startY","lines","palette"])):Le("",!0),R("div",{ref_key:"$app",ref:l,class:"edit-screens",onScroll:I},[R("div",{ref_key:"$container",ref:i,class:"edit-screen-container",style:q({width:r(n)*2+"px"})},[R("div",{ref_key:"refSketchRuleBox",ref:u,class:"canvas",onMousedown:oe,style:q({marginLeft:"-"+(Y().width/2-25)+"px"})},[R("div",{style:q({pointerEvents:p.value?"none":"auto"})},[Ke(_.$slots,"default",{},void 0,!0)],4)],36)],4)],544)])}}});const To=ee(So,[["__scopeId","data-v-db14b1b7"]]),Ye={[D.ADD]:"新增",[D.DELETE]:"删除",[D.UPDATE]:"更新",[D.MOVE]:"移动",[D.PASTE]:"粘贴",[D.COPY]:"复制",[D.CUT]:"剪切",[D.TOP]:"置顶",[D.BOTTOM]:"置底",[D.UP]:"上移",[D.DOWN]:"下移",[D.GROUP]:"成组",[D.UN_GROUP]:"解组",[D.LOCK]:"锁定",[D.UNLOCK]:"解锁",[D.HIDE]:"隐藏",[D.SHOW]:"显示",[xe.CANVAS]:"画布初始化"};var xo=Array.prototype,wo=xo.reverse;function Eo(e){return e==null?e:wo.call(e)}var bo=Eo;const ko=Et(bo),ht=e=>(De("data-v-5a6ac549"),e=e(),Me(),e),Lo={class:"go-flex-items-center"},Io=ht(()=>R("span",{class:"btn-text"},"历史记录",-1)),Do={class:"history-list-box"},Mo=["title"],$o=ht(()=>R("div",{class:"popover-modal"},null,-1)),Ro=Z({__name:"index",setup(e){const{DesktopOutlineIcon:o,PencilIcon:s,TrashIcon:d,CopyIcon:t,LayersIcon:g,DuplicateIcon:l,HelpOutlineIcon:v,LockClosedOutlineIcon:u,LockOpenOutlineIcon:i,EyeOffOutlineIcon:p,EyeOutlineIcon:a}=pe.ionicons5,{StackedMoveIcon:n,Carbon3DCursorIcon:C,Carbon3DSoftwareIcon:c}=pe.carbon,h=Xt(),S=T=>{if(T.targetType===xe.CANVAS)return o;switch(T.actionType){case D.UPDATE:return s;case D.DELETE:return d;case D.PASTE:return t;case D.TOP:return g;case D.BOTTOM:return g;case D.UP:return g;case D.DOWN:return g;case D.MOVE:return n;case D.ADD:return l;case D.GROUP:return C;case D.UN_GROUP:return c;case D.LOCK:return u;case D.UNLOCK:return i;case D.HIDE:return p;case D.SHOW:return a;default:return s}},x=T=>{if(T.targetType===xe.CANVAS)return Ye[xe.CANVAS];if(T.actionType===D.GROUP||T.actionType===D.UN_GROUP)return`${Ye[T.actionType]}`;if(T.historyData.length)return`${Ye[T.actionType]} - ${T.historyData[0].chartConfig.title}`},w=$(()=>{const f=h.getBackStack.map(L=>({label:x(L),icon:S(L)}));return ko(f.filter(L=>L.label))});return(T,f)=>{const L=H("n-button"),I=H("n-icon"),oe=H("n-text"),Y=H("n-scrollbar"),m=H("n-popover"),O=H("n-tooltip");return E(),B("div",Lo,[k(m,{class:"edit-history-popover","show-arrow":!1,size:"small",trigger:"click",placement:"top-start"},{trigger:b(()=>[k(L,{class:"mr-10",secondary:"",size:"small",disabled:w.value.length===0},{default:b(()=>[Io]),_:1},8,["disabled"])]),default:b(()=>[R("div",Do,[k(Y,{style:{"max-height":"500px"}},{default:b(()=>[(E(!0),B(ae,null,ie(w.value,(_,M)=>(E(),B("div",{class:"list-item go-flex-items-center go-ellipsis-1",key:M,title:_.label},[k(I,{class:"item-icon",size:"16",depth:2,component:_.icon},null,8,["component"]),k(oe,{depth:"2"},{default:b(()=>[he(se(_.label),1)]),_:2},1024)],8,Mo))),128))]),_:1}),$o])]),_:1}),k(O,{trigger:"hover"},{trigger:b(()=>[k(I,{size:"21",depth:3},{default:b(()=>[k(r(v))]),_:1})]),default:b(()=>[R("span",null,"最多只保留"+se(r(bt))+"条记录",1)]),_:1})])}}});const Oo=ee(Ro,[["__scopeId","data-v-5a6ac549"]]),We=e=>(De("data-v-f19379a6"),e=e(),Me(),e),Uo=We(()=>R("th",null,"功能",-1)),Ho=We(()=>R("th",null,"Win 快捷键",-1)),Ao=We(()=>R("span",null," Mac 快捷键 ",-1)),Po={key:0},Yo={key:1},Bo=Z({__name:"ShortcutKeyModal",props:{modelShow:Boolean},emits:["update:modelShow"],setup(e,{emit:o}){const{CloseIcon:s}=pe.ionicons5,d=K(!1),t=o,g=e;le(()=>g.modelShow,u=>{d.value=u});const l=[{label:"拖拽画布",win:`${N.SPACE.toUpperCase()} + 🖱️ `,mac:`${F.SPACE.toUpperCase()} + 🖱️ `,macSource:!0},{label:"向 上/右/下/左 移动",win:`${N.CTRL.toUpperCase()} + ↑ 或 → 或 ↓ 或 ←`,mac:`${F.CTRL.toUpperCase()} + ↑ `},{label:"锁定",win:`${N.CTRL.toUpperCase()} + L `,mac:`${F.CTRL.toUpperCase()} + L `},{label:"解锁",win:`${N.CTRL.toUpperCase()} + ${N.SHIFT.toUpperCase()}+ L `,mac:`${F.CTRL.toUpperCase()} + ${F.SHIFT.toUpperCase()} + L `},{label:"展示",win:`${N.CTRL.toUpperCase()} + H `,mac:`${F.CTRL.toUpperCase()} + H `},{label:"隐藏",win:`${N.CTRL.toUpperCase()} + ${N.SHIFT.toUpperCase()} + H `,mac:`${F.CTRL.toUpperCase()} + ${F.SHIFT.toUpperCase()} + H `},{label:"删除",win:"Delete".toUpperCase(),mac:`${F.CTRL.toUpperCase()} + Backspace `},{label:"复制",win:`${N.CTRL.toUpperCase()} + C `,mac:`${F.CTRL.toUpperCase()} + C `},{label:"剪切",win:`${N.CTRL.toUpperCase()} + X `,mac:`${F.CTRL.toUpperCase()} + X `},{label:"粘贴",win:`${N.CTRL.toUpperCase()} + V `,mac:`${F.CTRL.toUpperCase()} + V `},{label:"后退",win:`${N.CTRL.toUpperCase()} + Z `,mac:`${F.CTRL.toUpperCase()} + Z `},{label:"前进",win:`${N.CTRL.toUpperCase()} + ${N.SHIFT.toUpperCase()} + Z `,mac:`${F.CTRL.toUpperCase()} + ${F.SHIFT.toUpperCase()} + Z `},{label:"多选",win:`${N.CTRL.toUpperCase()} + 🖱️ `,mac:`${F.CTRL_SOURCE_KEY.toUpperCase()} + 🖱️ `},{label:"创建分组",win:`${N.CTRL.toUpperCase()} + G / 🖱️ `,mac:`${F.CTRL_SOURCE_KEY.toUpperCase()} + G / 🖱️`},{label:"解除分组",win:`${N.CTRL.toUpperCase()} + ${N.SHIFT.toUpperCase()} + G `,mac:`${F.CTRL_SOURCE_KEY.toUpperCase()} + ${N.SHIFT.toUpperCase()} + G `}],v=()=>{t("update:modelShow",!1)};return(u,i)=>{const p=H("n-icon"),a=H("n-space"),n=H("n-gradient-text"),C=H("n-table"),c=H("n-modal");return E(),X(c,{show:d.value,"onUpdate:show":i[0]||(i[0]=h=>d.value=h),"mask-closable":!0,onAfterLeave:v},{default:b(()=>[k(C,{class:"model-content",bordered:!1,"single-line":!1},{default:b(()=>[R("thead",null,[R("tr",null,[Uo,Ho,R("th",null,[k(a,{justify:"space-between"},{default:b(()=>[Ao,k(p,{size:"20",class:"go-cursor-pointer",onClick:v},{default:b(()=>[k(r(s))]),_:1})]),_:1})])])]),R("tbody",null,[(E(),B(ae,null,ie(l,(h,S)=>R("tr",{key:S},[R("td",null,se(h.label),1),R("td",null,se(h.win),1),h.macSource?(E(),B("td",Po,se(h.mac),1)):(E(),B("td",Yo,[k(n,{size:22},{default:b(()=>[he(se(h.mac.substr(0,1)),1)]),_:2},1024),he(" + "+se(h.mac.substr(3)),1)]))])),64))])]),_:1})]),_:1},8,["show"])}}});const Xo=ee(Bo,[["__scopeId","data-v-f19379a6"]]),No=e=>(De("data-v-f48c63d8"),e=e(),Me(),e),Go={class:"go-edit-shortcut"},Ko=No(()=>R("span",null,"快捷键",-1)),Fo=Z({__name:"index",setup(e){const{DicomOverlayIcon:o}=pe.carbon,s=K(!1);return(d,t)=>{const g=H("n-icon"),l=H("n-button"),v=H("n-tooltip");return E(),B("div",Go,[k(Xo,{modelShow:s.value,"onUpdate:modelShow":t[0]||(t[0]=u=>s.value=u)},null,8,["modelShow"]),k(v,{trigger:"hover"},{trigger:b(()=>[k(l,{class:"scale-btn",secondary:"",size:"small",onClick:t[1]||(t[1]=u=>s.value=!0)},{default:b(()=>[k(g,{size:"21",depth:3},{default:b(()=>[k(r(o))]),_:1})]),_:1})]),default:b(()=>[Ko]),_:1})])}}});const zo=ee(Fo,[["__scopeId","data-v-f48c63d8"]]),Wo={class:"go-edit-bottom"},Vo=Z({__name:"index",setup(e){_e(w=>({"016b4d1d":t.value}));const{LockClosedOutlineIcon:o,LockOpenOutlineIcon:s}=pe.ionicons5,d=me(),t=K(d.getAppTheme),g=ut(),l=te(),{lockScale:v,scale:u}=Ie(l.getEditCanvas),i=K(null);let p=[{label:"200%",value:200},{label:"150%",value:150},{label:"100%",value:100},{label:"50%",value:50},{label:"自适应",value:0}];const a=K(""),n=w=>{var T;if((T=i.value)==null||T.blur(),w===0){g.setItemUnHandle(dt.RE_POSITION_CANVAS,!0),l.computedScale();return}l.setScale(w/100)},C=()=>{l.setEditCanvas(V.LOCK_SCALE,!v.value)},c=K(100),h=w=>`${w}%`,S=w=>{l.setScale(w/100)},x=Fe({100:""});return kt(()=>{const w=(u.value*100).toFixed(0);a.value=`${w}%`,c.value=parseInt(w)}),(w,T)=>{const f=H("n-text"),L=H("n-space"),I=H("n-select"),oe=H("n-icon"),Y=H("n-button"),m=H("n-tooltip"),O=H("n-slider");return E(),B("div",Wo,[k(L,null,{default:b(()=>[k(r(Oo)),k(f,{id:"keyboard-dress-show",depth:"3"})]),_:1}),k(L,{class:"bottom-ri"},{default:b(()=>[k(zo),k(I,{ref_key:"selectInstRef",ref:i,class:"scale-btn",value:a.value,"onUpdate:value":[T[0]||(T[0]=_=>a.value=_),n],size:"mini",disabled:r(v),options:r(p)},null,8,["value","disabled","options"]),k(m,{trigger:"hover"},{trigger:b(()=>[k(Y,{onClick:C,text:""},{default:b(()=>[k(oe,{class:ne(["lock-icon",{color:r(v)}]),size:"18",depth:2},{default:b(()=>[r(v)?(E(),X(r(o),{key:0})):(E(),X(r(s),{key:1}))]),_:1},8,["class"])]),_:1})]),default:b(()=>[R("span",null,se(r(v)?"解锁":"锁定")+"当前比例",1)]),_:1}),k(O,{class:"scale-slider",value:c.value,"onUpdate:value":[T[1]||(T[1]=_=>c.value=_),S],"default-value":50,min:10,max:200,step:5,"format-tooltip":h,disabled:r(v),marks:x},null,8,["value","disabled","marks"])]),_:1})])}}});const qo=ee(Vo,[["__scopeId","data-v-880c3f29"]]),ve=te(),jo=()=>{ve.setTargetSelectChart(void 0),Lt(It(ve.getStorageInfo()||[]),void 0,"json");const e=document.querySelector(".go-edit-range"),o=document.getElementById("go-edit-watermark");if(!e||!o){window.$message.error("导出失败！");return}const s=ve.getEditCanvas.scale;ve.setScale(1,!0),o.style.display="block",setTimeout(()=>{Dt(e,()=>{o&&(o.style.display="none"),ve.setScale(s,!0)})},600)},Jo=()=>{const e=K(),{updateComponent:o}=pt();return{importUploadFileListRef:e,importBeforeUpload:({file:t})=>{e.value=[];const g=t.file.type;return g!==ot.JSON&&g!==ot.TXT?(window.$message.warning("仅支持上传 【JSON】 格式文件，请重新上传！"),!1):!0},importCustomRequest:t=>{const{file:g}=t;Mt(()=>{g.file?$t(g.file).then(l=>{Wt({message:"请选择导入方式:",positiveText:"新增（可撤回）",negativeText:"覆盖（不可撤回）",negativeButtonProps:{type:"info",ghost:!1},onPositiveCallback:()=>ue(void 0,null,function*(){try{l=we(l),yield o(l,!1,!0),window.$message.success("导入成功！")}catch(v){console.log(v),window.$message.error("组件导入失败，请检查文件完整性!")}}),onNegativeCallback:()=>ue(void 0,null,function*(){try{l=we(l),yield o(l,!0,!0),window.$message.success("导入成功！")}catch(v){console.log(v),window.$message.error("组件导入失败，请检查文件完整性!")}})})}):window.$message.error("导入失败，请检查数据或联系管理员！")})}}};var re=(e=>(e.BUTTON="button",e.IMPORTUPLOAD="importUpload",e))(re||{});const Zo={class:"btn-item"},Qo=Z({__name:"index",setup(e){const{DownloadIcon:o,ShareIcon:s,PawIcon:d,SettingsSharpIcon:t,CreateIcon:g}=pe.ionicons5,l=lt(),v=te();Rt(),jt();let u=null;const i=K(!1),p=K(!0),a=K(!0),{importUploadFileListRef:n,importCustomRequest:C,importBeforeUpload:c}=Jo(),h=$(()=>l.getChartToolsStatus===Qe.ASIDE),S=$(()=>l.getChartToolsStatusHide),x=$(()=>p.value&&S.value),w=$(()=>{if(!h.value)return oe;const Y=[];return oe.map(m=>{Y.unshift(m)}),Y}),T=()=>{u=setTimeout(()=>{p.value&&(p.value=!1,a.value=!0)},200),setTimeout(()=>{a.value=!1},400)},f=()=>{clearTimeout(u),p.value||(p.value=!0)},L=()=>{window.$message.warning("请通过顶部【同步内容】按钮同步最新数据！"),v.setEditCanvas(V.IS_CODE_EDIT,!0),setTimeout(()=>{const Y=Ut(Ht.CHART_EDIT_NAME,"href");if(!Y)return;const m=it();I(m),At(Y,[m],void 0,!0)},2e3)},I=Y=>{const m=v.getStorageInfo(),O=Pt(Te.GO_CHART_STORAGE_LIST)||[];if(O!=null&&O.length){const _=O.findIndex(M=>M.id===Y);_!==-1?(O.splice(_,1,fe(P({},m),{id:Y})),Ae(Te.GO_CHART_STORAGE_LIST,O)):(O.push(fe(P({},m),{id:Y})),Ae(Te.GO_CHART_STORAGE_LIST,O))}else Ae(Te.GO_CHART_STORAGE_LIST,[fe(P({},m),{id:Y})])},oe=[{key:"import",type:re.IMPORTUPLOAD,name:"导入",icon:s},{key:"export",type:re.BUTTON,name:"导出",icon:o,handle:jo},{key:"edit",type:re.BUTTON,name:"编辑",icon:g,handle:L},{key:"setting",type:re.BUTTON,name:"设置",icon:t,handle:()=>{i.value=!0}}];return(Y,m)=>{const O=H("n-icon"),_=H("n-text"),M=H("n-button"),U=H("n-upload"),W=H("n-tooltip");return E(),B(ae,null,[R("div",{class:ne(["go-chart-edit-tools",[r(l).getChartToolsStatus,x.value?"isMini":"unMini"]]),onClick:m[1]||(m[1]=A=>p.value&&(p.value=!1)),onMouseenter:T,onMouseleave:f},[Ue(k(O,{class:"asideLogo",size:"22"},{default:b(()=>[k(r(d))]),_:1},512),[[He,r(l).getChartToolsStatus===r(Qe).ASIDE&&x.value]]),(E(!0),B(ae,null,ie(w.value,(A,Ce)=>(E(),X(W,{key:A.key,disabled:!h.value||S.value&&a.value,trigger:"hover",placement:"left"},{trigger:b(()=>[R("div",Zo,[A.type===r(re).BUTTON?(E(),X(M,{key:0,circle:h.value,secondary:"",onClick:A.handle},{icon:b(()=>[h.value?(E(),X(O,{key:0,size:"22"},{default:b(()=>[(E(),X(de(A.icon)))]),_:2},1024)):(E(),X(de(A.icon),{key:1}))]),default:b(()=>[Ue(k(_,{depth:"3"},{default:b(()=>[he(se(A.name),1)]),_:2},1536),[[He,!h.value]])]),_:2},1032,["circle","onClick"])):A.type===r(re).IMPORTUPLOAD?(E(),X(U,{key:1,"file-list":r(n),"onUpdate:fileList":m[0]||(m[0]=z=>Ot(n)?n.value=z:null),"show-file-list":!1,customRequest:r(C),onBeforeUpload:r(c)},{default:b(()=>[k(M,{circle:h.value,secondary:""},{icon:b(()=>[h.value?(E(),X(O,{key:0,size:"22"},{default:b(()=>[(E(),X(de(A.icon)))]),_:2},1024)):(E(),X(de(A.icon),{key:1}))]),default:b(()=>[Ue(k(_,{depth:"3"},{default:b(()=>[he(se(A.name),1)]),_:2},1536),[[He,!h.value]])]),_:2},1032,["circle"])]),_:2},1032,["file-list","customRequest","onBeforeUpload"])):Le("",!0)])]),default:b(()=>[R("span",null,se(A.name),1)]),_:2},1032,["disabled"]))),128))],34),k(r(qt),{modelShow:i.value,"onUpdate:modelShow":m[2]||(m[2]=A=>i.value=A)},null,8,["modelShow"])],64)}}});const es=ee(Qo,[["__scopeId","data-v-62b1993f"]]),ts=Z({__name:"index",setup(e){const o=te(),{handleContextMenu:s}=ze(),{updateComponent:d}=pt();Yt(Gt,null),Jt(()=>ue(this,null,function*(){}));const{mouseenterHandle:t,mouseleaveHandle:g,mousedownHandle:l,mouseClickHandle:v}=gt(),u=(w,T,f)=>{if(o.getTargetChart.selectId.length>1)return T.filter(I=>[J.GROUP,J.DELETE].includes(I.key));const L=[];return f.status.lock?L.push(J.LOCK):L.push(J.UNLOCK),f.status.hide?L.push(J.HIDE):L.push(J.SHOW),w.filter(I=>!L.includes(I.key))},i=$(()=>o.getEditCanvasConfig.chartThemeSetting),p=$(()=>at(o.getEditCanvasConfig.chartCustomThemeColorInfo)[o.getEditCanvasConfig.chartThemeColor]);$(()=>o.getEditCanvasConfig.filterShow);const a=$(()=>{const w=o.getEditCanvasConfig.background,T=o.getEditCanvasConfig.backgroundImage,I=o.getEditCanvasConfig.selectColor?{background:w||void 0}:{background:`url(${T}) no-repeat center center / cover !important`};return fe(P({},I),{width:"inherit",height:"inherit"})}),n=it();console.log("projectId:"+n);var C=sessionStorage.getItem("GO_CHART_STORAGE_LIST");if(console.log("listData:"+C),C){var c=we(C+"");if(console.log("chartObjList:",c),c&&c.length>0)for(var h=0;h<c.length;h++){var S=c[h];if(S.id==n&&S.editCanvasConfig){console.log("to update",S);var x={editCanvasConfig:S.editCanvasConfig,componentList:S.componentList,requestGlobalConfig:S.requestGlobalConfig};d(x,!0);break}}}return Ge(()=>{Kt()}),(w,T)=>(E(),X(r(Vt),{id:"go-chart-edit-layout",flex:!0,showTop:!1,showBottom:!0,depth:1,xScroll:!0,disabledScroll:!0,onMousedown:r(ft),onDrop:r(Qt),onDragover:r(st),onDragenter:r(st)},{aside:b(()=>[k(r(es))]),bottom:b(()=>[k(r(qo))]),default:b(()=>[k(r(To),null,{default:b(()=>[R("div",{id:"go-chart-edit-content",onContextmenu:T[0]||(T[0]=(...f)=>r(s)&&r(s)(...f))},[k(r(Co),null,{default:b(()=>[R("div",{style:q(P(P({},r(Ee)(r(o).getEditCanvasConfig)),a.value))},[(E(!0),B(ae,null,ie(r(o).getComponentList,(f,L)=>(E(),B("div",{key:f.id},[f.isGroup?(E(),X(r(ro),{key:0,groupData:f,groupIndex:L},null,8,["groupData","groupIndex"])):(E(),X(r(Ne),{key:1,"data-id":f.id,index:L,style:q(P(P({},r(ke)(f.attr,L)),r(rt)(f.styles))),item:f,onClick:I=>r(v)(I,f),onMousedown:I=>r(l)(I,f),onMouseenter:I=>r(t)(I,f),onMouseleave:I=>r(g)(I,f),onContextmenu:I=>r(s)(I,f,u)},{default:b(()=>[(E(),X(de(f.chartConfig.chartKey),{class:ne(["edit-content-chart",r(Be)(f.styles.animations)]),chartConfig:f,themeSetting:i.value,themeColor:p.value,style:q(P(P(P({},r(ce)(f.attr)),r(Ee)(f.styles)),r(Xe)(f.styles)))},null,8,["class","chartConfig","themeSetting","themeColor","style"]))]),_:2},1032,["data-id","index","style","item","onClick","onMousedown","onMouseenter","onMouseleave","onContextmenu"]))]))),128))],4)]),_:1})],32)]),_:1})]),_:1},8,["onMousedown","onDrop","onDragover","onDragenter"]))}});const Es=ee(ts,[["__scopeId","data-v-df7d2df9"]]);export{Es as default};
