<!--
/**
 * 支付订单 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2023-07-04 15:51:26
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('支付订单')}">支付订单</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 商户编号 , merchantId ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('商户编号')}" class="search-label merchantId-label">商户编号</span><span class="search-colon">:</span></div>
                        <input id="merchantId" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>
                    <!-- 商品描述 , body ,typeName=text_area, isHideInSearch=true -->
                    <!-- 异步通知地址 , notifyUrl ,typeName=text_area, isHideInSearch=true -->
                    <!-- 支付金额 , amount ,typeName=text_input, isHideInSearch=true -->
                    <!-- 用户 , userIp ,typeName=text_input, isHideInSearch=true -->
                    <!-- 订单失效时间 , expireTime ,typeName=date_input, isHideInSearch=true -->
                    <!-- 订单支付成功时间 , successTime ,typeName=date_input, isHideInSearch=true -->
                    <!-- 订单支付通知时间 , notifyTime ,typeName=date_input, isHideInSearch=true -->
                    <!-- 支付成功的订单拓展单编号 , successExtensionId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 退款次数 , refundTimes ,typeName=number_input, isHideInSearch=true -->
                    <!-- 退款总金额 , refundAmount ,typeName=text_input, isHideInSearch=true -->
                    <!-- 渠道编码 , channelCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 渠道用户 , channelUserId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 渠道手续费 , channelFeeRate ,typeName=number_input, isHideInSearch=true -->
                    <!-- 渠道手续金额 , channelFeeAmount ,typeName=text_input, isHideInSearch=true -->
                    <!-- 应用编号 , appId ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('应用编号')}" class="search-label appId-label">应用编号</span><span class="search-colon">:</span></div>
                        <input id="appId" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>
                    <!-- 渠道编号 , channelId ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('渠道编号')}" class="search-label channelId-label">渠道编号</span><span class="search-colon">:</span></div>
                        <input id="channelId" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>
                    <!-- 渠道订单 , channelOrderNo ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('渠道订单')}" class="search-label channelOrderNo-label">渠道订单</span><span class="search-colon">:</span></div>
                        <input id="channelOrderNo" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 支付状态 , status ,typeName=radio_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('支付状态')}" class="search-label status-label">支付状态</span><span class="search-colon">:</span></div>


                        <div id="status" th:data="${enum.toArray('com.dt.platform.constants.enums.common.PayOrderStatusEnum')}" style="width:150px"></div>
                    </div>
                    <!-- 退款状态 , refundStatus ,typeName=radio_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('退款状态')}" class="search-label refundStatus-label">退款状态</span><span class="search-colon">:</span></div>


                        <div id="refundStatus" th:data="${enum.toArray('com.dt.platform.constants.enums.common.PayRefundStatusEnum')}" style="width:150px"></div>
                    </div>
                    <!-- 回调状态 , notifyStatus ,typeName=radio_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('回调状态')}" class="search-label notifyStatus-label">回调状态</span><span class="search-colon">:</span></div>


                        <div id="notifyStatus" th:data="${enum.toArray('com.dt.platform.constants.enums.common.PayTaskNotifyStatusEnum')}" style="width:150px"></div>
                    </div>
                    <!-- 交易单号 , tradeNo ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('交易单号')}" class="search-label tradeNo-label">交易单号</span><span class="search-colon">:</span></div>
                        <input id="tradeNo" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 创建时间 , createTime ,typeName=date_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('创建时间')}" class="search-label createTime-label">创建时间</span><span class="search-colon">:</span></div>
                            <input type="text" id="createTime-begin" style="width: 150px" lay-verify="date" th:placeholder="${lang.translate('开始日期')}" autocomplete="off" class="layui-input search-input search-date-input"  readonly >
                            <span class="search-dash">-</span>
                            <input type="text" id="createTime-end"  style="width: 150px"  lay-verify="date" th:placeholder="${lang.translate('结束日期')}" autocomplete="off" class="layui-input search-input search-date-input" readonly>
                    </div>
                    <!-- 商户订单 , merchantOrderId ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('商户订单')}" class="search-label merchantOrderId-label">商户订单</span><span class="search-colon">:</span></div>
                        <input id="merchantOrderId" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>
                    <!-- 商品标题 , subject ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('商品标题')}" class="search-label subject-label">商品标题</span><span class="search-colon">:</span></div>
                        <input id="subject" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索','','cmp:table.search')}">搜索</span></button>
                <button id="search-button-advance" class="layui-btn layui-btn-primary icon-btn search-button-advance"><i class="layui-icon">&#xe671;</i><span th:text="${lang.translate('更多','','cmp:table.search')}">更多</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 84px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${perm.checkAuth('sys_pay_order:create')}" id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建','','cmp:table.button')}">新建</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${perm.checkAuth('sys_pay_order:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view"  data-id="{{d.id}}"> <span th:text="${lang.translate('查看','','cmp:table.ops')}">查看</span></button>
    <button th:if="${perm.checkAnyAuth('sys_pay_order:update','sys_pay_order:save')}" class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit"data-id="{{d.id}}"><span th:text="${lang.translate('修改','','cmp:table.ops')}">修改</span></button>


    <button th:if="${perm.checkAuth('sys_pay_order:delete')}" class="layui-btn layui-btn-xs layui-btn-danger ops-delete-button " lay-event="del" data-id="{{d.id}}"><span th:text="${lang.translate('删除','','cmp:table.ops')}">删除</span></button>


</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var RADIO_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.PayOrderStatusEnum')}]];
    var RADIO_NOTIFYSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.PayTaskNotifyStatusEnum')}]];
    var RADIO_REFUNDSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.PayRefundStatusEnum')}]];
    var AUTH_PREFIX="sys_pay_order";


</script>

<script th:src="'/business/common/pay_order/pay_order_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/common/pay_order/pay_order_list.js?'+${cacheKey}"></script>

</body>
</html>