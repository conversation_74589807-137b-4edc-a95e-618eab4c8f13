<!--
/**
 * 调用统计日志 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2023-06-07 15:51:38
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('调用统计日志')}">调用统计日志</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- ID , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 目标名称 , subject ,typeName=text_input, isHideInSearch=true -->
                    <!-- 请求的URI , uri ,typeName=text_input, isHideInSearch=true -->
                    <!-- 类型 , type ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('类型')}" class="search-label type-label">类型</span><span class="search-colon">:</span></div>
                        <input id="type" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>
                    <!-- token值 , token ,typeName=text_area, isHideInSearch=true -->
                    <!-- 会话ID , sessionId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 用户ID , userId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 用户姓名 , userName ,typeName=text_input, isHideInSearch=true -->
                    <!-- 日志跟踪ID , tid ,typeName=text_input, isHideInSearch=true -->
                    <!-- 请求参数 , parameter ,typeName=text_area, isHideInSearch=true -->
                    <!-- 响应数据 , response ,typeName=text_area, isHideInSearch=true -->
                    <!-- 结束时间 , endTime ,typeName=date_input, isHideInSearch=true -->
                    <!-- 异常信息 , exception ,typeName=text_area, isHideInSearch=true -->
                    <!-- 应用名称 , application ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('应用名称')}" class="search-label application-label">应用名称</span><span class="search-colon">:</span></div>
                        <input id="application" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>
                    <!-- 主机名称 , hostName ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('主机名称')}" class="search-label hostName-label">主机名称</span><span class="search-colon">:</span></div>
                        <input id="hostName" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>
                    <!-- 客户端IP , clientIp ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('客户端IP')}" class="search-label clientIp-label">客户端IP</span><span class="search-colon">:</span></div>
                        <input id="clientIp" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- UserAgent , userAgent ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('UserAgent')}" class="search-label userAgent-label">UserAgent</span><span class="search-colon">:</span></div>
                        <input id="userAgent" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>
                    <!-- 请求类型 , httpMethod ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('请求类型')}" class="search-label httpMethod-label">请求类型</span><span class="search-colon">:</span></div>
                        <input id="httpMethod" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>
                    <!-- 开始时间 , startTime ,typeName=date_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('开始时间')}" class="search-label startTime-label">开始时间</span><span class="search-colon">:</span></div>
                            <input type="text" id="startTime-begin" style="width: 150px" lay-verify="date" th:placeholder="${lang.translate('开始日期')}" autocomplete="off" class="layui-input search-input search-date-input"  readonly >
                            <span class="search-dash">-</span>
                            <input type="text" id="startTime-end"  style="width: 150px"  lay-verify="date" th:placeholder="${lang.translate('结束日期')}" autocomplete="off" class="layui-input search-input search-date-input" readonly>
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索','','cmp:table.search')}">搜索</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 84px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${perm.checkAuth('sys_invoke_log:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view"  data-id="{{d.id}}"> <span th:text="${lang.translate('查看','','cmp:table.ops')}">查看</span></button>
    <button th:if="${perm.checkAnyAuth('sys_invoke_log:update','sys_invoke_log:save')}" class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit"data-id="{{d.id}}"><span th:text="${lang.translate('修改','','cmp:table.ops')}">修改</span></button>




</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var AUTH_PREFIX="sys_invoke_log";


</script>

<script th:src="'/business/common/invoke_log/invoke_log_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/common/invoke_log/invoke_log_list.js?'+${cacheKey}"></script>

</body>
</html>