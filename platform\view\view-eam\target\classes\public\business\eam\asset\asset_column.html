
<!DOCTYPE html>
<html style="background-color: #FFFFFF;">
<body>


<div th:fragment="id" class="layui-form-item" style="display: none">
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('主键')}">主键</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="id" id="id" name="id" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('主键') }" type="text" class="layui-input"   />
    </div>
</div>




<div  th:fragment="category_id" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <div id="categoryId" input-type="select" ></div>
    </div>
</div>

<div  th:fragment="catalog_code_value" class="layui-form-item">
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}">catalogCodeValue</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="catalogCodeValue" id="catalogCodeValue" name="catalogCodeValue" th:placeholder="${ lang.translate('请输入'+'类型编号') }" type="text" class="layui-input"  />
    </div>
</div>



<div  th:fragment="category_code" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>

    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="categoryCode" id="categoryCode" name="categoryCode" th:placeholder="${ lang.translate('请输入') +''+  lang.translate(item.attribute.label) }" type="text" class="layui-input"   />
    </div>
</div>



<div th:fragment="business_code"  class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>

    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="businessCode" id="businessCode" name="businessCode" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label) }" type="text" class="layui-input"   />
    </div>
</div>



<div th:fragment="proc_id" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>

    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="procId" id="procId" name="procId" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label) }" type="text" class="layui-input"   />
    </div>
</div>



<div  th:fragment="status" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>

    <div class="layui-input-block layui-input-block-c1">
        <div id="status" input-type="select" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}"></div>
    </div>
</div>


<div  th:fragment="safety_level_code"  class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div></div>
    <div class="layui-input-block layui-input-block-c1">
        <div id="safetyLevelCode" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_safety_level'}"></div>
    </div>
</div>


<div  th:fragment="batch_code" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="batchCode" id="batchCode" name="batchCode" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label)}" type="text" class="layui-input"   />
    </div>
</div>



<div  th:fragment="asset_code" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1" id="assetCodeLabel">
        <div th:text="${lang.translate(item.attribute.label)}"></div>
        <div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="asset_code" id="assetCode" name="assetCode" th:placeholder="${ lang.translate('系统自动生成') }" type="text" class="layui-input"   />
    </div>
</div>



<div  th:fragment="asset_status"  class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <div id="assetStatus" input-type="select" th:data="${'/service-eam/eam-asset-status/query-list'}" extraParam="{}"></div>
    </div>
</div>



<div th:fragment="display"  class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="display" id="display" name="display" th:placeholder="${lang.translate('请输入') +''+ lang.translate('是否显示') }" type="text" class="layui-input"   />
    </div>
</div>



<div  th:fragment="scrap" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="scrap" id="scrap" name="scrap" th:placeholder="${lang.translate('请输入') +''+ lang.translate('是否报废') }" type="text" class="layui-input"   />
    </div>
</div>



<div  th:fragment="goods_id" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <div id="goodsId" input-type="select" th:data="${'/service-eam/eam-goods-stock/query-paged-list?ownerCode=goods'}" extraParam="{}"></div>
    </div>
</div>



<div  th:fragment="name" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="name" id="name" name="name" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label) }" type="text" class="layui-input"    />
    </div>
</div>



<div  th:fragment="manufacturer_id" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <div id="manufacturerId" input-type="select" th:data="${'/service-eam/eam-manufacturer/query-paged-list'}"></div>
    </div>
</div>



<div  th:fragment="model" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="model" id="model" name="model" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label) }" type="text" class="layui-input"   />
    </div>
</div>



<div  th:fragment="picture_id" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-upload layui-input-block layui-input-block-c1">
        <input input-type="upload" id="pictureId"  name="pictureId" lay-filter="pictureId" style="display: none">
        <button type="button" class="layui-btn" id="pictureId-button" th:text="${lang.translate('请选择图片')}">请选择图片</button>
        <div class="layui-upload-list" id="pictureId-file-list"></div>
    </div>
</div>




<div  th:fragment="unit" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="unit" id="unit" name="unit" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label) }" type="text" class="layui-input"   />
    </div>
</div>



<div  th:fragment="service_life" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="serviceLife" id="serviceLife" name="serviceLife" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label) }" type="text" class="layui-input"
         autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0"/>
    </div>
</div>



<div  th:fragment="serial_number" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="serialNumber" id="serialNumber" name="serialNumber" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label) }" type="text" class="layui-input"   />
    </div>
</div>



<div th:fragment="own_company_id" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="ownCompanyId" id="ownCompanyId" name="ownCompanyId"  type="hidden" class="layui-input"   />
        <button id="ownCompanyId-button" type="button" action-type="org-dialog" class="layui-btn   " style="width: 100%"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择所属公司')}" th:default-label="${lang.translate('请选择所属公司')}">请选择所属公司</span></button>
    </div>
</div>



<div  th:fragment="manager_id" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="managerId" id="managerId" name="managerId"  type="hidden" class="layui-input"   />
        <button id="managerId-button" type="button" action-type="emp-dialog" class="layui-btn   " style="width: 100%"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择管理人员')}" th:default-label="${lang.translate('请选择管理人员')}">请选择管理人员</span></button>
    </div>
</div>




<div  th:fragment="use_organization_id" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="useOrganizationId" id="useOrganizationId" name="useOrganizationId"  type="hidden" class="layui-input"   />
        <button id="useOrganizationId-button" type="button" action-type="org-dialog" class="layui-btn   " style="width: 100%"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择使用公司/部门')}" th:default-label="${lang.translate('请选择使用公司/部门')}">请选择使用公司/部门</span></button>
    </div>
</div>



<div  th:fragment="use_user_id" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="useUserId" id="useUserId" name="useUserId"  type="hidden" class="layui-input"   />
        <button id="useUserId-button" type="button" action-type="emp-dialog" class="layui-btn   " style="width: 100%"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择使用人员')}" th:default-label="${lang.translate('请选择使用人员')}">请选择使用人员</span></button>
    </div>
</div>




<div  th:fragment="position_id" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <div id="positionId" input-type="select" th:data="${'/service-eam/eam-position/query-paged-list?sortField=hierarchyName&sortType=asc'}"></div>
    </div>
</div>



<div  th:fragment="position_detail" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="positionDetail" id="positionDetail" name="positionDetail" th:placeholder="${ lang.translate('请输入') +''+lang.translate(item.attribute.label) }" type="text" class="layui-input"   />
    </div>
</div>



<div  th:fragment="warehouse_id" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <div id="warehouseId" input-type="select" th:data="${'/service-eam/eam-warehouse/query-paged-list'}"></div>
    </div>
</div>



<div  th:fragment="source_id"  class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div></div>
    <div class="layui-input-block layui-input-block-c1">
        <div id="sourceId" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_source'}"></div>
    </div>
</div>


<div  th:fragment="asset_number" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="assetNumber" id="assetNumber" name="assetNumber" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label) }" type="text" class="layui-input"      autocomplete="off" input-type="number_input" integer="true" decimal="false" allow-negative="true" step="1.0"   scale="0"/>
    </div>
</div>



<div  th:fragment="remain_number" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="remainNumber" id="remainNumber" name="remainNumber" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label) }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="true" decimal="false" allow-negative="true" step="1.0"   scale="0"/>
    </div>
</div>



<div  th:fragment="purchase_date" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input input-type="date" lay-filter="purchaseDate" id="purchaseDate" name="purchaseDate"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择') +''+ lang.translate(item.attribute.label) }" type="text" class="layui-input"   />
    </div>
</div>

<div  th:fragment="production_date" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input input-type="date" lay-filter="productionDate" id="productionDate" name="productionDate"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择') +''+lang.translate(item.attribute.label) }" type="text" class="layui-input"   />
    </div>
</div>

<div  th:fragment="register_date" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input input-type="date" lay-filter="registerDate" id="registerDate" name="registerDate"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择') +''+lang.translate(item.attribute.label )}" type="text" class="layui-input"   />
    </div>
</div>


<div  th:fragment="rfid" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="rfid" id="rfid" name="rfid" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label) }" type="text" class="layui-input"   />
    </div>
</div>



<div  th:fragment="attach" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-upload layui-input-block layui-input-block-c1">
        <input input-type="upload" id="attach"  name="attach" lay-filter="attach" style="display: none">
        <button type="button" class="layui-btn" id="attach-button" th:text="${lang.translate('选择附件')}">选择附件</button>
        <div class="layui-upload-list" id="attach-file-list"></div>
    </div>
</div>



<div  th:fragment="asset_notes" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div></div>
    <div class="layui-input-block layui-input-block-c1">
        <textarea lay-filter="assetNotes" id="assetNotes" name="assetNotes" th:placeholder="${ lang.translate('请输入') +''+lang.translate( item.attribute.label )}" class="layui-textarea" style="height: 120px" ></textarea>

    </div>
</div>



<div  th:fragment="maintainer_id" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <div id="maintainerId" input-type="select" th:data="${'/service-eam/eam-maintainer/query-paged-list'}"></div>
    </div>
</div>


<div  th:fragment="maintenance_status"  class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div></div>
    <div class="layui-input-block layui-input-block-c1">
       <div id="maintenanceStatus" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_maintenance_status'}"></div>
    </div>
</div>

<div th:fragment="maintenance_method" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div></div>
    <div class="layui-input-block layui-input-block-c1">
        <div id="maintenanceMethod" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_maintenance_method'}" extraParam="{}"></div>
    </div>
</div>

<div th:fragment="suggest_maintenance_method"  class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div></div>
    <div class="layui-input-block layui-input-block-c1">
        <div id="suggestMaintenanceMethod" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_suggest_maintenance_method'}" extraParam="{}"></div>
    </div>
</div>


<div  th:fragment="maintainer_name" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="maintainerName" id="maintainerName" name="maintainerName" th:placeholder="${ lang.translate('请输入') +''+lang.translate(item.attribute.label) }" type="text" class="layui-input"   />
    </div>
</div>





<div  th:fragment="contacts" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"> </div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="contacts" id="contacts" name="contacts" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label )}" type="text" class="layui-input"   />
    </div>
</div>



<div  th:fragment="contact_information" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="contactInformation" id="contactInformation" name="contactInformation" th:placeholder="${ lang.translate('请输入') +''+lang.translate(item.attribute.label) }" type="text" class="layui-input"   />
    </div>
</div>



<div th:fragment="director"  class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="director" id="director" name="director" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label )}" type="text" class="layui-input"   />
    </div>
</div>



<div   th:fragment="maintenance_start_date" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input input-type="date" lay-filter="maintenanceStartDate" id="maintenanceStartDate" name="maintenanceStartDate"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择') +''+ lang.translate(item.attribute.label) }" type="text" class="layui-input"   />
    </div>
</div>


<div  th:fragment="maintenance_end_date" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input input-type="date" lay-filter="maintenanceEndDate" id="maintenanceEndDate" name="maintenanceEndDate"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择') +''+lang.translate( item.attribute.label )}" type="text" class="layui-input"   />
    </div>
</div>


<div  th:fragment="maintenance_notes" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <textarea lay-filter="maintenanceNotes" id="maintenanceNotes" name="maintenanceNotes" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label) }" class="layui-textarea" style="height: 120px" ></textarea>
    </div>
</div>



<div th:fragment="financial_category_id" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1">
        <div th:text="${lang.translate(item.attribute.label)}"></div>
        <div th:if="${item.required} == 1" class="layui-required">*</div>
    </div>
    <div class="layui-input-block layui-input-block-c1">
        <div id="financialCategoryId" input-type="select" th:data="${'/service-eam/eam-category-finance/query-paged-list'}"></div>
    </div>
</div>



<div  th:fragment="financial_code" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="financialCode" id="financialCode" name="financialCode" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label) }" type="text" class="layui-input"   />
    </div>
</div>



<div  th:fragment="supplier_id" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <div id="supplierId" input-type="select" th:data="${'/service-eam/eam-supplier/query-paged-list'}"></div>
    </div>
</div>



<div  th:fragment="tax_amount_rate" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="taxAmountRate" id="taxAmountRate" name="taxAmountRate" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label) }" type="text" class="layui-input"      autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="2"/>
    </div>
</div>



<div th:fragment="tax_amount_price"  class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="taxAmountPrice" id="taxAmountPrice" name="taxAmountPrice" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label) }" type="text" class="layui-input"       autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="2"/>
    </div>
</div>



<div  th:fragment="maintenance_price" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div>   <div th:if="${item.required} == 1" class="layui-required">*</div>  </div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="maintenancePrice" id="maintenancePrice" name="maintenancePrice" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('维保价格') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />
    </div>
</div>


<div th:fragment="total_amount_price"  class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="totalAmountPrice" id="totalAmountPrice" name="totalAmountPrice" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label)}" type="text" class="layui-input"      autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="2"/>
    </div>
</div>



<div  th:fragment="original_unit_price" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="originalUnitPrice" id="originalUnitPrice" name="originalUnitPrice" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label )}" type="text" class="layui-input"       autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="2"/>
    </div>
</div>



<div  th:fragment="accumulated_depreciation" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="accumulatedDepreciation" id="accumulatedDepreciation" name="accumulatedDepreciation" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label )}" type="text" class="layui-input"       autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="2"/>
    </div>
</div>



<div  th:fragment="residuals_rate" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="residualsRate" id="residualsRate" name="residualsRate" th:placeholder="${ lang.translate('请输入') +''+lang.translate( item.attribute.label)}" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="2"/>
    </div>
</div>



<div  th:fragment="nav_price" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="navPrice" id="navPrice" name="navPrice" th:placeholder="${ lang.translate('请输入') +''+lang.translate(item.attribute.label) }" type="text" class="layui-input"    autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="2"/>
    </div>
</div>



<div  th:fragment="purchase_unit_price" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="purchaseUnitPrice" id="purchaseUnitPrice" name="purchaseUnitPrice" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label) }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="2"/>
    </div>
</div>




<div  th:fragment="current_year_depreciation" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="currentYearDepreciation" id="currentYearDepreciation" name="currentYearDepreciation" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label )}" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="2"/>
    </div>
</div>



<div  th:fragment="depreciation_year" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="depreciationYear" id="depreciationYear" name="depreciationYear" th:placeholder="${ lang.translate('请输入') +''+lang.translate( item.attribute.label) }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="2"/>
    </div>
</div>


<div  th:fragment="monthDepreciationPrice" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="monthDepreciationPrice" id="monthDepreciationPrice" name="monthDepreciationPrice" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label )}" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="2"/>
    </div>
</div>


<div  th:fragment="residuals_price" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="residualsPrice" id="residualsPrice" name="residualsPrice" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label) }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="2"/>
    </div>
</div>



<div  th:fragment="entry_time" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input input-type="date" lay-filter="entryTime" id="entryTime" name="entryTime"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label) }" type="text" class="layui-input"    lay-verify=""   />
    </div>
</div>

<!-- date_input : 入账时间 ,  entryTime  -->
<!--<div class="layui-form-item" >-->
<!--    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('入账时间')}">入账时间</div></div>-->
<!--    <div class="layui-input-block layui-input-block-c1">-->
<!--        <input input-type="date" lay-filter="entryTime" id="entryTime" name="entryTime"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择') +''+ lang.translate('入账时间') }" type="text" class="layui-input"    lay-verify=""   />-->
<!--    </div>-->
<!--</div>-->


<div  th:fragment="financial_notes" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <textarea lay-filter="financialNotes" id="financialNotes" name="financialNotes" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label) }" class="layui-textarea" style="height: 120px" ></textarea>
    </div>
</div>





<!--设备字段-->

<div  th:fragment="equipment_code" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="equipmentCode" id="equipmentCode" name="equipmentCode" th:placeholder="${ lang.translate('请输入') +''+lang.translate( item.attribute.label) }" type="text" class="layui-input"  />
    </div>
</div>

<div  th:fragment="equipment_status"  class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input input-type="radio" type="radio" name="equipmentStatus" lay-filter="equipmentStatus" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.eam.AssetEquipmentStatusEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==0)}">
    </div>
</div>


<div  th:fragment="equipment_ip"  class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="equipmentIp" id="equipmentIp" name="equipmentIp" th:placeholder="${ lang.translate('请输入') +''+lang.translate( item.attribute.label )}" type="text" class="layui-input"  />
    </div>
</div>

<div  th:fragment="manage_ip" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="manageIp" id="manageIp" name="manageIp" th:placeholder="${ lang.translate('请输入') +''+lang.translate( item.attribute.label )}" type="text" class="layui-input"  />
    </div>
</div>

<div  th:fragment="equipment_cpu" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="equipmentCpu" id="equipmentCpu" name="equipmentCpu" th:placeholder="${ lang.translate('请输入') +''+lang.translate( item.attribute.label) }" type="text" class="layui-input"  />
    </div>
</div>

<div th:fragment="equipment_memory" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="equipmentMemory" id="equipmentMemory" name="equipmentMemory" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label )}" type="text" class="layui-input"  />
    </div>
</div>

<div  th:fragment="rack_id" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <div id="rackId" input-type="select" th:data="${'/service-eam/eam-asset-rack/query-paged-list?type=rack&sortField=hierarchyName&sortType=asc'}"></div>
    </div>
</div>


<div  th:fragment="equipment_label" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="equipmentLabel" id="equipmentLabel" name="equipmentLabel" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label) }" type="text" class="layui-input"  />
    </div>
</div>



<div th:fragment="rack_up_number" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="rackUpNumber" id="rackUpNumber" name="rackUpNumber" th:placeholder="${ lang.translate('请输入') +''+lang.translate( item.attribute.label )}" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="true" decimal="false" allow-negative="false" step="1.0"   scale="0" />
    </div>
</div>



<div  th:fragment="rack_down_number" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="rackDownNumber" id="rackDownNumber" name="rackDownNumber" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label )}" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="true" decimal="false" allow-negative="false" step="1.0"   scale="0" />
    </div>
</div>

<div  th:fragment="equipment_environment_code" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <div id="equipmentEnvironmentCode" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_equipment_environment'}"></div>
    </div>
</div>


<div  th:fragment="equipment_conf" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <textarea lay-filter="equipmentConf" id="equipmentConf" name="equipmentConf" th:placeholder="${ lang.translate('请输入') +''+lang.translate(item.attribute.label) }" class="layui-textarea" style="height: 120px" ></textarea>
     </div>
</div>


<div  th:fragment="purpose" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="contacts" id="purpose" name="purpose" th:placeholder="${ lang.translate('请输入') +''+lang.translate(item.attribute.label ) }" type="text" class="layui-input"   />
    </div>
</div>

<div  th:fragment="equipment_serial_number" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="equipmentSerialNumber" id="equipmentSerialNumber" name="equipmentSerialNumber" th:placeholder="${ lang.translate('请输入') +''+lang.translate( item.attribute.label )}" type="text" class="layui-input"   />
    </div>
</div>

<div  th:fragment="last_verification_date" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input input-type="date" lay-filter="lastVerificationDate" id="lastVerificationDate" name="lastVerificationDate"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择') +''+ lang.translate(item.attribute.label) }" type="text" class="layui-input"   />
    </div>
</div>



<div  th:fragment="label" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="label" id="label" name="label" th:placeholder="${ lang.translate('请输入') +''+lang.translate( item.attribute.label) }" type="text" class="layui-input"   />
    </div>
</div>

<div  th:fragment="label2" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <textarea lay-filter="label2" id="label2" name="label2" th:placeholder="${ lang.translate('请输入') +''+lang.translate(item.attribute.label) }" class="layui-textarea" style="height: 120px" ></textarea>
    </div>
</div>

<div th:fragment="label3" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"> </div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="label3" id="label3" name="label3" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label )}" type="text" class="layui-input"   />
    </div>
</div>

<div  th:fragment="label4" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"> </div></div>
    <div class="layui-input-block layui-input-block-c1">
        <textarea lay-filter="label4" id="label4" name="label4" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label )}" class="layui-textarea" style="height: 120px" ></textarea>
    </div>
</div>


<div  th:fragment="label5" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"> </div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="label5" id="label5" name="label5" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label )}" type="text" class="layui-input"   />
    </div>
</div>


<div th:fragment="goods_stock_id" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div></div>
    <div class="layui-input-block layui-input-block-c1">
        <div id="goodsStockId" input-type="select" th:data="${'/service-eam/eam-goods-stock/query-paged-list'}" extraParam="{}"></div>
    </div>
</div>


<!-- select_box : 财务选项 ,  financialOption  -->
<div th:fragment="financial_option" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <div id="financialOption" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_financial_options'}" extraParam="{}"></div>
    </div>
</div>

<!-- select_box : 费用项目 ,  expenseItem  -->
<div th:fragment="expense_item" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <div id="expenseItem" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_expense_items'}" extraParam="{}"></div>
    </div>
</div>

<!-- text_input : 客户信息 ,  customerInfo -->
<div th:fragment="customer_info" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="customerInfo" id="customerInfo" name="customerInfo" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label )}" type="text" class="layui-input"  />
    </div>
</div>


<!-- number_input : 已用期限 ,  assetUsedServiceLife  -->
<div th:fragment="asset_used_service_life" class="layui-form-item" >
    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate(item.attribute.label)}"></div><div th:if="${item.required} == 1" class="layui-required">*</div></div>
    <div class="layui-input-block layui-input-block-c1">
        <input lay-filter="assetUsedServiceLife" id="assetUsedServiceLife" name="assetUsedServiceLife" th:placeholder="${ lang.translate('请输入') +''+ lang.translate(item.attribute.label )}" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="true" decimal="false" allow-negative="true" step="1.0"   scale="0" />
    </div>
</div>





</body>
</html>