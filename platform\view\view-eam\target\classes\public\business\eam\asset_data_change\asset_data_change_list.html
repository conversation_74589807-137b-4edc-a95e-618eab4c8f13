<!--
/**
 * 数据变更 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2021-10-08 16:01:30
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <title th:text="${lang.translate('数据变更')}">数据变更</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" th:href="@{/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css}" rel="stylesheet"/>
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon"> <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
    <style>
    </style>
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 流程 , procId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 办理状态 , status ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit" id="status-search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('办理状态')}" class="search-label status-label">办理状态</span><span class="search-colon">:</span></div>
                        <div id="status" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}" style="width:150px"></div>
                    </div>
                    <!-- 变更类型 , changeType ,typeName=text_input, isHideInSearch=true -->
                    <!-- 制单人 , originatorId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 业务编号 , businessCode ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('业务编号')}" class="search-label businessCode-label">业务编号</span><span class="search-colon">:</span></div>
                        <input id="businessCode" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>
                    <!-- 备注 , notes ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('备注')}" class="search-label notes-label">备注</span><span class="search-colon">:</span></div>
                        <input id="notes" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>
                    <!-- 变更日期 , changeDate ,typeName=date_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('变更日期')}" class="search-label changeDate-label">变更日期</span><span class="search-colon">:</span></div>
                            <input type="text" id="changeDate-begin" style="width: 150px" lay-verify="date" th:placeholder="${lang.translate('开始日期')}" autocomplete="off" class="layui-input search-input search-date-input"  readonly >
                            <span class="search-dash">-</span>
                            <input type="text" id="changeDate-end"  style="width: 150px"  lay-verify="date" th:placeholder="${lang.translate('结束日期')}" autocomplete="off" class="layui-input search-input search-date-input" readonly>
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索')}">搜索</span></button>
            </div>
        </div>

        <div style="margin-top: 42px ">
            <table class="layui-table" th:id="(${tableId})"  lay-filter="data-table"></table>
        </div>

    </div>
</div>
<!--id="data-table"-->

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>

<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${dataChangeCreateBtn}"  id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button" lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建')}">新建</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${dataChangeViewBtn}"     class="layui-btn layui-btn-primary layui-btn-xs ops-view-button" lay-event="view" th:text="${lang.translate('查看')}" data-id="{{d.id}}">查看</button>
    <button th:if="${dataChangeModifyBtn}"   class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button" lay-event="edit" th:text="${lang.translate('修改')}" data-id="{{d.id}}">修改</button>
    <button th:if="${dataChangeDeleteBtn}"   class="layui-btn layui-btn-xs layui-btn-danger ops-delete-button" lay-event="del" th:text="${lang.translate('删除')}" data-id="{{d.id}}">删除</button>
    <button th:if="${forApprovalBtn}"        class="layui-btn layui-btn-xs for-approval-button" lay-event="for-approval" th:text="${lang.translate('送审')}" data-id="{{d.id}}">送审</button>
    <button th:if="${confirmDataBtn}"        class="layui-btn layui-btn-xs confirm-data-button" lay-event="confirm-data" th:text="${lang.translate('确认')}" data-id="{{d.id}}">确认</button>
    <button th:if="${revokeDataBtn}"         class="layui-btn layui-btn-xs revoke-data-button" lay-event="revoke-data" th:text="${lang.translate('撤销')}" data-id="{{d.id}}">撤销</button>

    <button th:if="${agreeBtn}" id="agree-button" class="layui-btn layui-btn-xs                   agree-button" lay-event="agree" data-id="{{d.id}}"><i class="layui-icon"></i><span th:text="${lang.translate('同意')}"同意</span></button>
    <button th:if="${denyBtn}"  id="deny-button" class="layui-btn layui-btn-xs  layui-btn-danger  deny-button" lay-event="deny" data-id="{{d.id}}"><i class="layui-icon"></i><span th:text="${lang.translate('拒绝')}">拒绝</span></button>


</script>


<script th:inline="javascript">

    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var AUTH_PREFIX="eam_asset_data_change";

    var TABLE_ID=[[${tableId}]];
    // 变更类型
    var PAGE_TYPE = [[${pageType}]] ;
    // 变更类型
    var CHANGE_TYPE = [[${changeType}]] ;
    // 是否需要审批
    var APPROVAL_REQUIRED = [[${approvalRequired}]] ;


    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}]];
    var SELECT_ASSETSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetStatusEnum')}]];
    var SELECT_EQUIPMENTSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetEquipmentStatusEnum')}]];

    var ASSET_CATEGORY_DATA =  [[${assetCategoryData}]];
    var ATTRIBUTE_LIST_DATA =  [[${attributeListData}]];

</script>

<script th:src="'/business/eam/asset/asset_column_list.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset_data_change/asset_data_change_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset_data_change/asset_data_change_list.js?'+${cacheKey}"></script>

</body>
</html>
