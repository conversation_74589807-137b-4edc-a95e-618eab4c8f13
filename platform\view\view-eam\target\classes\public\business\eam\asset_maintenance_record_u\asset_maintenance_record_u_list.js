/**
 * 维保更新记录 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-10-29 15:12:33
 */


function ListPage() {

	var settings,admin,form,table,layer,util,fox,upload,xmSelect;
	//模块基础路径
	const moduleURL="/service-eam/eam-asset-maintenance-record-u";
	var dataTable=null;
	var sort=null;
	/**
      * 入口函数，初始化
      */
	this.init=function(layui) {

     	admin = layui.admin,settings = layui.settings,form = layui.form,upload = layui.upload,laydate= layui.laydate;
		table = layui.table,layer = layui.layer,util = layui.util,fox = layui.foxnic,xmSelect = layui.xmSelect,dropdown=layui.dropdown;;

		if(window.pageExt.list.beforeInit) {
			window.pageExt.list.beforeInit();
		}
     	//渲染表格
     	renderTable();
		//初始化搜索输入框组件
		initSearchFields();
		//绑定搜索框事件
		bindSearchEvent();
		//绑定按钮事件
		bindButtonEvent();
		//绑定行操作按钮事件
    	bindRowOperationEvent();
     }


     /**
      * 渲染表格
      */
    function renderTable() {
		$(window).resize(function() {
			fox.adjustSearchElement();
		});
		fox.adjustSearchElement();
		//
		 var marginTop=$(".search-bar").height()+$(".search-bar").css("padding-top")+$(".search-bar").css("padding-bottom")
		 $("#table-area").css("margin-top",marginTop+"px");
		//
		function renderTableInternal() {

			var ps={searchField: "$composite"};
			var contitions={};

			if(window.pageExt.list.beforeQuery){
				window.pageExt.list.beforeQuery(contitions,ps,"tableInit");
			}
			ps.searchValue=JSON.stringify(contitions);

			var templet=window.pageExt.list.templet;
			if(templet==null) {
				templet=function(field,value,row) {
					if(value==null) return "";
					return value;
				}
			}
			var h=$(".search-bar").height();
			var tableConfig={
				elem: '#data-table',
				toolbar: '#toolbarTemplate',
				defaultToolbar: ['filter', 'print',{title: '刷新数据',layEvent: 'refresh-data',icon: 'layui-icon-refresh-3'}],
				url: moduleURL +'/query-paged-list',
				height: 'full-'+(h+28),
				limit: 50,
				where: ps,
				cols: [[
					{ fixed: 'left',type: 'numbers' },
					{ fixed: 'left',type:'checkbox'}
					,{ field: 'id', align:"left",fixed:false,  hide:true, sort: true  , title: fox.translate('主键') , templet: function (d) { return templet('id',d.id,d);}  }
					,{ field: 'maintainerId', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('维保商'), templet: function (d) { return templet('maintainerId' ,fox.joinLabel(d.maintnainer,"maintainerName",',','','maintainerId'),d);}}
					,{ field: 'sMaintainerId', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('状态'), templet:function (d){ return templet('sMaintainerId',fox.getEnumText(RADIO_SMAINTAINERID_DATA,d.sMaintainerId,'','sMaintainerId'),d);}}
					,{ field: 'uMaintainerId', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('更新方式'), templet:function (d){ return templet('uMaintainerId',fox.getEnumText(RADIO_UMAINTAINERID_DATA,d.uMaintainerId,'','uMaintainerId'),d);}}
					,{ field: 'uSMaintainerId', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('更新方式'), templet:function (d){ return templet('uSMaintainerId',fox.getEnumText(RADIO_USMAINTAINERID_DATA,d.uSMaintainerId,'','uSMaintainerId'),d);}}
					,{ field: 'maintainerName', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('维保厂商名称') , templet: function (d) { return templet('maintainerName',d.maintainerName,d);}  }
					,{ field: 'sMaintainerName', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('状态'), templet:function (d){ return templet('sMaintainerName',fox.getEnumText(RADIO_SMAINTAINERNAME_DATA,d.sMaintainerName,'','sMaintainerName'),d);}}
					,{ field: 'uMaintainerName', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('更新方式'), templet:function (d){ return templet('uMaintainerName',fox.getEnumText(RADIO_UMAINTAINERNAME_DATA,d.uMaintainerName,'','uMaintainerName'),d);}}
					,{ field: 'uSMaintainerName', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('更新方式'), templet:function (d){ return templet('uSMaintainerName',fox.getEnumText(RADIO_USMAINTAINERNAME_DATA,d.uSMaintainerName,'','uSMaintainerName'),d);}}
					,{ field: 'maintenanceStatus', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('维保状态'), templet: function (d) { return templet('maintenanceStatus' ,fox.joinLabel(d.assetMaintenanceStatus,"label",',','','maintenanceStatus'),d);}}
					,{ field: 'sMaintenanceStatus', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('状态'), templet:function (d){ return templet('sMaintenanceStatus',fox.getEnumText(RADIO_SMAINTENANCESTATUS_DATA,d.sMaintenanceStatus,'','sMaintenanceStatus'),d);}}
					,{ field: 'uMaintenanceStatus', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('更新方式'), templet:function (d){ return templet('uMaintenanceStatus',fox.getEnumText(RADIO_UMAINTENANCESTATUS_DATA,d.uMaintenanceStatus,'','uMaintenanceStatus'),d);}}
					,{ field: 'uSMaintenanceStatus', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('更新方式'), templet:function (d){ return templet('uSMaintenanceStatus',fox.getEnumText(RADIO_USMAINTENANCESTATUS_DATA,d.uSMaintenanceStatus,'','uSMaintenanceStatus'),d);}}
					,{ field: 'maintenanceMethod', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('维保方式'), templet: function (d) { return templet('maintenanceMethod' ,fox.joinLabel(d.maintenanceMethodData,"label",',','','maintenanceMethod'),d);}}
					,{ field: 'sMaintenanceMethod', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('状态'), templet:function (d){ return templet('sMaintenanceMethod',fox.getEnumText(RADIO_SMAINTENANCEMETHOD_DATA,d.sMaintenanceMethod,'','sMaintenanceMethod'),d);}}
					,{ field: 'uMaintenanceMethod', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('更新方式'), templet:function (d){ return templet('uMaintenanceMethod',fox.getEnumText(RADIO_UMAINTENANCEMETHOD_DATA,d.uMaintenanceMethod,'','uMaintenanceMethod'),d);}}
					,{ field: 'uSMaintenanceMethod', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('更新方式'), templet:function (d){ return templet('uSMaintenanceMethod',fox.getEnumText(RADIO_USMAINTENANCEMETHOD_DATA,d.uSMaintenanceMethod,'','uSMaintenanceMethod'),d);}}
					,{ field: 'contactInformation', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('联系方式') , templet: function (d) { return templet('contactInformation',d.contactInformation,d);}  }
					,{ field: 'sContactInformation', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('状态'), templet:function (d){ return templet('sContactInformation',fox.getEnumText(RADIO_SCONTACTINFORMATION_DATA,d.sContactInformation,'','sContactInformation'),d);}}
					,{ field: 'uContactInformation', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('更新方式'), templet:function (d){ return templet('uContactInformation',fox.getEnumText(RADIO_UCONTACTINFORMATION_DATA,d.uContactInformation,'','uContactInformation'),d);}}
					,{ field: 'uSContactInformation', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('更新方式'), templet:function (d){ return templet('uSContactInformation',fox.getEnumText(RADIO_USCONTACTINFORMATION_DATA,d.uSContactInformation,'','uSContactInformation'),d);}}
					,{ field: 'suggestMaintenanceMethod', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('建议维保方式'), templet: function (d) { return templet('suggestMaintenanceMethod' ,fox.joinLabel(d.suggestMaintenanceMethodData,"label",',','','suggestMaintenanceMethod'),d);}}
					,{ field: 'sSuggestMaintenanceMethod', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('状态'), templet:function (d){ return templet('sSuggestMaintenanceMethod',fox.getEnumText(RADIO_SSUGGESTMAINTENANCEMETHOD_DATA,d.sSuggestMaintenanceMethod,'','sSuggestMaintenanceMethod'),d);}}
					,{ field: 'uSuggestMaintenanceMethod', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('更新方式'), templet:function (d){ return templet('uSuggestMaintenanceMethod',fox.getEnumText(RADIO_USUGGESTMAINTENANCEMETHOD_DATA,d.uSuggestMaintenanceMethod,'','uSuggestMaintenanceMethod'),d);}}
					,{ field: 'uSSuggestMaintenanceMethod', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('更新方式'), templet:function (d){ return templet('uSSuggestMaintenanceMethod',fox.getEnumText(RADIO_USSUGGESTMAINTENANCEMETHOD_DATA,d.uSSuggestMaintenanceMethod,'','uSSuggestMaintenanceMethod'),d);}}
					,{ field: 'contacts', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('联系人') , templet: function (d) { return templet('contacts',d.contacts,d);}  }
					,{ field: 'sContacts', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('状态'), templet:function (d){ return templet('sContacts',fox.getEnumText(RADIO_SCONTACTS_DATA,d.sContacts,'','sContacts'),d);}}
					,{ field: 'uContacts', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('更新方式'), templet:function (d){ return templet('uContacts',fox.getEnumText(RADIO_UCONTACTS_DATA,d.uContacts,'','uContacts'),d);}}
					,{ field: 'uSContacts', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('更新方式'), templet:function (d){ return templet('uSContacts',fox.getEnumText(RADIO_USCONTACTS_DATA,d.uSContacts,'','uSContacts'),d);}}
					,{ field: 'director', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('负责人') , templet: function (d) { return templet('director',d.director,d);}  }
					,{ field: 'sDirector', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('状态'), templet:function (d){ return templet('sDirector',fox.getEnumText(RADIO_SDIRECTOR_DATA,d.sDirector,'','sDirector'),d);}}
					,{ field: 'uDirector', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('更新方式'), templet:function (d){ return templet('uDirector',fox.getEnumText(RADIO_UDIRECTOR_DATA,d.uDirector,'','uDirector'),d);}}
					,{ field: 'uSDirector', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('更新方式'), templet:function (d){ return templet('uSDirector',fox.getEnumText(RADIO_USDIRECTOR_DATA,d.uSDirector,'','uSDirector'),d);}}
					,{ field: 'maintenanceStartDate', align:"right", fixed:false, hide:false, sort: true   ,title: fox.translate('维保开始时间') ,templet: function (d) { return templet('maintenanceStartDate',fox.dateFormat(d.maintenanceStartDate,"yyyy-MM-dd"),d); }  }
					,{ field: 'sMaintenanceStartDate', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('状态'), templet:function (d){ return templet('sMaintenanceStartDate',fox.getEnumText(RADIO_SMAINTENANCESTARTDATE_DATA,d.sMaintenanceStartDate,'','sMaintenanceStartDate'),d);}}
					,{ field: 'uMaintenanceStartDate', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('更新方式'), templet:function (d){ return templet('uMaintenanceStartDate',fox.getEnumText(RADIO_UMAINTENANCESTARTDATE_DATA,d.uMaintenanceStartDate,'','uMaintenanceStartDate'),d);}}
					,{ field: 'uSMaintenanceStartDate', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('更新方式'), templet:function (d){ return templet('uSMaintenanceStartDate',fox.getEnumText(RADIO_USMAINTENANCESTARTDATE_DATA,d.uSMaintenanceStartDate,'','uSMaintenanceStartDate'),d);}}
					,{ field: 'maintenanceEndDate', align:"right", fixed:false, hide:false, sort: true   ,title: fox.translate('维保到期时间') ,templet: function (d) { return templet('maintenanceEndDate',fox.dateFormat(d.maintenanceEndDate,"yyyy-MM-dd"),d); }  }
					,{ field: 'sMaintenanceEndDate', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('状态'), templet:function (d){ return templet('sMaintenanceEndDate',fox.getEnumText(RADIO_SMAINTENANCEENDDATE_DATA,d.sMaintenanceEndDate,'','sMaintenanceEndDate'),d);}}
					,{ field: 'uMaintenanceEndDate', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('更新方式'), templet:function (d){ return templet('uMaintenanceEndDate',fox.getEnumText(RADIO_UMAINTENANCEENDDATE_DATA,d.uMaintenanceEndDate,'','uMaintenanceEndDate'),d);}}
					,{ field: 'uSMaintenanceEndDate', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('更新方式'), templet:function (d){ return templet('uSMaintenanceEndDate',fox.getEnumText(RADIO_USMAINTENANCEENDDATE_DATA,d.uSMaintenanceEndDate,'','uSMaintenanceEndDate'),d);}}
					,{ field: 'maintenanceNotes', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('备注') , templet: function (d) { return templet('maintenanceNotes',d.maintenanceNotes,d);}  }
					,{ field: 'sMaintenanceNotes', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('状态'), templet:function (d){ return templet('sMaintenanceNotes',fox.getEnumText(RADIO_SMAINTENANCENOTES_DATA,d.sMaintenanceNotes,'','sMaintenanceNotes'),d);}}
					,{ field: 'uMaintenanceNotes', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('更新方式'), templet:function (d){ return templet('uMaintenanceNotes',fox.getEnumText(RADIO_UMAINTENANCENOTES_DATA,d.uMaintenanceNotes,'','uMaintenanceNotes'),d);}}
					,{ field: 'uSMaintenanceNotes', align:"left",fixed:false,  hide:false, sort: true  , title: fox.translate('更新方式'), templet:function (d){ return templet('uSMaintenanceNotes',fox.getEnumText(RADIO_USMAINTENANCENOTES_DATA,d.uSMaintenanceNotes,'','uSMaintenanceNotes'),d);}}
					,{ field: 'createTime', align:"right", fixed:false, hide:false, sort: true   ,title: fox.translate('创建时间') ,templet: function (d) { return templet('createTime',fox.dateFormat(d.createTime,"yyyy-MM-dd HH:mm:ss"),d); }  }
					,{ field: fox.translate('空白列'), align:"center", hide:false, sort: false, title: "",minWidth:8,width:8,unresize:true}
					,{ field: 'row-ops', fixed: 'right', align: 'center', toolbar: '#tableOperationTemplate', title: fox.translate('操作'), width: 160 }
				]],
				done: function (data) { window.pageExt.list.afterQuery && window.pageExt.list.afterQuery(data); },
				footer : {
					exportExcel : false ,
					importExcel : false 
				}
			};
			window.pageExt.list.beforeTableRender && window.pageExt.list.beforeTableRender(tableConfig);
			dataTable=fox.renderTable(tableConfig);
			//绑定排序事件
			table.on('sort(data-table)', function(obj){
			  refreshTableData(obj.sortField,obj.type);
			});
			window.pageExt.list.afterTableRender && window.pageExt.list.afterTableRender();
		}
		setTimeout(renderTableInternal,1);
    };

	/**
	 * 刷新单号数据
	 * */
	function refreshRowData(data,remote) {
		var context=dataTable.getDataRowContext( { id : data.id } );
		if(context==null) return;
		if(remote) {
			admin.post(moduleURL+"/get-by-id", { id : data.id }, function (r) {
				if (r.success) {
					data = r.data;
					context.update(data);
					fox.renderFormInputs(form);
				} else {
					fox.showMessage(data);
				}
			});
		} else {
			context.update(data);
			fox.renderFormInputs(form);
		}
	}

	/**
      * 刷新表格数据
      */
	function refreshTableData(sortField,sortType,reset) {
		function getSelectedValue(id,prop) { var xm=xmSelect.get(id,true); return xm==null ? null : xm.getValue(prop);}
		var value = {};
		value.maintainerId={ inputType:"select_box", value: getSelectedValue("#maintainerId","value") ,fillBy:["maintnainer"]  , label:getSelectedValue("#maintainerId","nameStr") };
		value.maintenanceStatus={ inputType:"select_box", value: getSelectedValue("#maintenanceStatus","value") ,fillBy:["assetMaintenanceStatus"]  , label:getSelectedValue("#maintenanceStatus","nameStr") };
		value.maintenanceMethod={ inputType:"select_box", value: getSelectedValue("#maintenanceMethod","value") ,fillBy:["maintenanceMethodData"]  , label:getSelectedValue("#maintenanceMethod","nameStr") };
		value.suggestMaintenanceMethod={ inputType:"select_box", value: getSelectedValue("#suggestMaintenanceMethod","value") ,fillBy:["suggestMaintenanceMethodData"]  , label:getSelectedValue("#suggestMaintenanceMethod","nameStr") };
		var ps={searchField:"$composite"};
		if(window.pageExt.list.beforeQuery){
			if(!window.pageExt.list.beforeQuery(value,ps,"refresh")) return;
		}
		ps.searchValue=JSON.stringify(value);
		if(sortField) {
			ps.sortField=sortField;
			ps.sortType=sortType;
			sort={ field : sortField,type : sortType} ;
		} else {
			if(sort) {
				ps.sortField=sort.field;
				ps.sortType=sort.type;
			} 		}
		if(reset) {
			table.reload('data-table', { where : ps , page:{ curr:1 } });
		} else {
			table.reload('data-table', { where : ps });
		}
	}


	/**
	  * 获得已经选中行的数据,不传入 field 时，返回所有选中的记录，指定 field 时 返回指定的字段集合
	  */
	function getCheckedList(field) {
		var checkStatus = table.checkStatus('data-table');
		var data = checkStatus.data;
		if(!field) return data;
		for(var i=0;i<data.length;i++) data[i]=data[i][field];
		return data;
	}

	/**
	 * 重置搜索框
	 */
	function resetSearchFields() {
		$('#search-field').val("");
		$('#search-input').val("");
		layui.form.render();
	}

	function initSearchFields() {

		fox.switchSearchRow(1);

		//渲染 maintainerId 下拉字段
		fox.renderSelectBox({
			el: "maintainerId",
			radio: true,
			size: "small",
			filterable: true,
			on: function(data){
				setTimeout(function () {
					window.pageExt.list.onSelectBoxChanged && window.pageExt.list.onSelectBoxChanged("maintainerId",data.arr,data.change,data.isAdd);
				},1);
			},
			//转换数据
			searchField: "maintainerName", //请自行调整用于搜索的字段名称
			extraParam: {}, //额外的查询参数，Object 或是 返回 Object 的函数
			transform: function(data) {
				//要求格式 :[{name: '水果', value: 1},{name: '蔬菜', value: 2}]
				var opts=[];
				if(!data) return opts;
				for (var i = 0; i < data.length; i++) {
					if(!data[i]) continue;
					opts.push({data:data[i],name:data[i].maintainerName,value:data[i].id});
				}
				return opts;
			}
		});
		//渲染 maintenanceStatus 下拉字段
		fox.renderSelectBox({
			el: "maintenanceStatus",
			radio: true,
			size: "small",
			filterable: false,
			on: function(data){
				setTimeout(function () {
					window.pageExt.list.onSelectBoxChanged && window.pageExt.list.onSelectBoxChanged("maintenanceStatus",data.arr,data.change,data.isAdd);
				},1);
			},
			//转换数据
			transform: function(data) {
				//要求格式 :[{name: '水果', value: 1},{name: '蔬菜', value: 2}]
				var opts=[];
				if(!data) return opts;
				for (var i = 0; i < data.length; i++) {
					if(!data[i]) continue;
					opts.push({data:data[i],name:data[i].label,value:data[i].code});
				}
				return opts;
			}
		});
		//渲染 maintenanceMethod 下拉字段
		fox.renderSelectBox({
			el: "maintenanceMethod",
			radio: true,
			size: "small",
			filterable: false,
			on: function(data){
				setTimeout(function () {
					window.pageExt.list.onSelectBoxChanged && window.pageExt.list.onSelectBoxChanged("maintenanceMethod",data.arr,data.change,data.isAdd);
				},1);
			},
			//转换数据
			transform: function(data) {
				//要求格式 :[{name: '水果', value: 1},{name: '蔬菜', value: 2}]
				var opts=[];
				if(!data) return opts;
				for (var i = 0; i < data.length; i++) {
					if(!data[i]) continue;
					opts.push({data:data[i],name:data[i].label,value:data[i].code});
				}
				return opts;
			}
		});
		//渲染 suggestMaintenanceMethod 下拉字段
		fox.renderSelectBox({
			el: "suggestMaintenanceMethod",
			radio: true,
			size: "small",
			filterable: false,
			on: function(data){
				setTimeout(function () {
					window.pageExt.list.onSelectBoxChanged && window.pageExt.list.onSelectBoxChanged("suggestMaintenanceMethod",data.arr,data.change,data.isAdd);
				},1);
			},
			//转换数据
			transform: function(data) {
				//要求格式 :[{name: '水果', value: 1},{name: '蔬菜', value: 2}]
				var opts=[];
				if(!data) return opts;
				for (var i = 0; i < data.length; i++) {
					if(!data[i]) continue;
					opts.push({data:data[i],name:data[i].label,value:data[i].code});
				}
				return opts;
			}
		});
		fox.renderSearchInputs();
		window.pageExt.list.afterSearchInputReady && window.pageExt.list.afterSearchInputReady();
	}

	/**
	 * 绑定搜索框事件
	 */
	function bindSearchEvent() {
		//回车键查询
        $(".search-input").keydown(function(event) {
			if(event.keyCode !=13) return;
		  	refreshTableData(null,null,true);
        });

        // 搜索按钮点击事件
        $('#search-button').click(function () {
			refreshTableData(null,null,true);
        });

		// 搜索按钮点击事件
		$('#search-button-advance').click(function () {
			fox.switchSearchRow(1,function (ex){
				if(ex=="1") {
					$('#search-button-advance span').text("关闭");
				} else {
					$('#search-button-advance span').text("更多");
				}
			});
		});

	}

	/**
	 * 绑定按钮事件
	  */
	function bindButtonEvent() {

		//头工具栏事件
		table.on('toolbar(data-table)', function(obj){
			var checkStatus = table.checkStatus(obj.config.id);
			var selected=getCheckedList("id");
			if(window.pageExt.list.beforeToolBarButtonEvent) {
				var doNext=window.pageExt.list.beforeToolBarButtonEvent(selected,obj);
				if(!doNext) return;
			}
			switch(obj.event){
				case 'create':
					admin.putTempData('eam-asset-maintenance-record-u-form-data', {});
					openCreateFrom();
					break;
				case 'batch-del':
					batchDelete(selected);
					break;
				case 'refresh-data':
					refreshTableData();
					break;
				case 'other':
					break;
			};
		});


		//添加按钮点击事件
        function openCreateFrom() {
        	//设置新增是初始化数据
        	var data={};
			admin.putTempData('eam-asset-maintenance-record-u-form-data-form-action', "create",true);
            showEditForm(data);
        };

        //批量删除按钮点击事件
        function batchDelete(selected) {

        	if(window.pageExt.list.beforeBatchDelete) {
				var doNext=window.pageExt.list.beforeBatchDelete(selected);
				if(!doNext) return;
			}

			var ids=getCheckedList("id");
            if(ids.length==0) {
				top.layer.msg(fox.translate('请选择需要删除的')+fox.translate('维保更新记录')+"!");
            	return;
            }
            //调用批量删除接口
			top.layer.confirm(fox.translate('确定删除已选中的')+fox.translate('维保更新记录')+fox.translate('吗？'), function (i) {
                top.layer.close(i);
				admin.post(moduleURL+"/delete-by-ids", { ids: ids }, function (data) {
                    if (data.success) {
						if(window.pageExt.list.afterBatchDelete) {
							var doNext=window.pageExt.list.afterBatchDelete(data);
							if(!doNext) return;
						}
						fox.showMessage(data);
                        refreshTableData();
                    } else {
						fox.showMessage(data);
                    }
                },{delayLoading:200,elms:[$("#delete-button")]});
			});
        }
	}

    /**
     * 绑定行操作按钮事件
     */
    function bindRowOperationEvent() {
		// 工具条点击事件
		table.on('tool(data-table)', function (obj) {
			var data = obj.data;
			var layEvent = obj.event;

			if(window.pageExt.list.beforeRowOperationEvent) {
				var doNext=window.pageExt.list.beforeRowOperationEvent(data,obj);
				if(!doNext) return;
			}

			admin.putTempData('eam-asset-maintenance-record-u-form-data-form-action', "",true);
			if (layEvent === 'edit') { // 修改
				admin.post(moduleURL+"/get-by-id", { id : data.id }, function (data) {
					if(data.success) {
						admin.putTempData('eam-asset-maintenance-record-u-form-data-form-action', "edit",true);
						showEditForm(data.data);
					} else {
						 fox.showMessage(data);
					}
				});
			} else if (layEvent === 'view') { // 查看
				admin.post(moduleURL+"/get-by-id", { id : data.id }, function (data) {
					if(data.success) {
						admin.putTempData('eam-asset-maintenance-record-u-form-data-form-action', "view",true);
						showEditForm(data.data);
					} else {
						fox.showMessage(data);
					}
				});
			}
			else if (layEvent === 'del') { // 删除

				if(window.pageExt.list.beforeSingleDelete) {
					var doNext=window.pageExt.list.beforeSingleDelete(data);
					if(!doNext) return;
				}

				top.layer.confirm(fox.translate('确定删除此')+fox.translate('维保更新记录')+fox.translate('吗？'), function (i) {
					top.layer.close(i);
					admin.post(moduleURL+"/delete", { id : data.id }, function (data) {
						top.layer.closeAll('loading');
						if (data.success) {
							if(window.pageExt.list.afterSingleDelete) {
								var doNext=window.pageExt.list.afterSingleDelete(data);
								if(!doNext) return;
							}
							fox.showMessage(data);
							refreshTableData();
						} else {
							fox.showMessage(data);
						}
					},{delayLoading:100, elms:[$(".ops-delete-button[data-id='"+data.id+"']")]});
				});
			}
			
		});

    };

    /**
     * 打开编辑窗口
     */
	function showEditForm(data) {
		if(window.pageExt.list.beforeEdit) {
			var doNext=window.pageExt.list.beforeEdit(data);
			if(!doNext) return;
		}
		var action=admin.getTempData('eam-asset-maintenance-record-u-form-data-form-action');
		var queryString="";
		if(data && data.id) queryString='id=' + data.id;
		if(window.pageExt.list.makeFormQueryString) {
			queryString=window.pageExt.list.makeFormQueryString(data,queryString,action);
		}
		admin.putTempData('eam-asset-maintenance-record-u-form-data', data);
		var area=admin.getTempData('eam-asset-maintenance-record-u-form-area');
		var height= (area && area.height) ? area.height : ($(window).height()*0.6);
		var top= (area && area.top) ? area.top : (($(window).height()-height)/2);
		var title = fox.translate('维保更新记录');
		if(action=="create") title=fox.translate('添加')+title;
		else if(action=="edit") title=fox.translate('修改')+title;
		else if(action=="view") title=fox.translate('查看')+title;

		admin.popupCenter({
			title: title,
			resize: false,
			offset: [top,null],
			area: ["80%",height+"px"],
			type: 2,
			id:"eam-asset-maintenance-record-u-form-data-win",
			content: '/business/eam/asset_maintenance_record_u/asset_maintenance_record_u_form.html' + (queryString?("?"+queryString):""),
			finish: function () {
				if(action=="create") {
					refreshTableData();
				}
				if(action=="edit") {
					false?refreshTableData():refreshRowData(data,true);
				}
			}
		});
	};

	window.module={
		refreshTableData: refreshTableData,
		refreshRowData: refreshRowData,
		getCheckedList: getCheckedList,
		showEditForm: showEditForm
	};

	window.pageExt.list.ending && window.pageExt.list.ending();

};


layui.use(['form', 'table', 'util', 'settings', 'admin', 'upload','foxnic','xmSelect','laydate','dropdown'],function() {
	var task=setInterval(function (){
		if(!window["pageExt"]) return;
		clearInterval(task);
		(new ListPage()).init(layui);
	},1);
});