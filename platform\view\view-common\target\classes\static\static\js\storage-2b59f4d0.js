import{aF as g,S as c}from"./index-bb2cbf17.js";import{u as l}from"./chartEditStore-55fbe93c.js";const n=l(),u=()=>{const s=document.location.hash.split("/"),e=s&&s[s.length-1],t=g(c.GO_CHART_STORAGE_LIST);if(t){for(let o=0;o<t.length;o++)if(e.toString()===t[o].id){const{editCanvasConfig:a,requestGlobalConfig:i,componentList:r}=t[o];return n.editCanvasConfig=a,n.requestGlobalConfig=i,n.componentList=r,t[o]}}};export{u as g};
