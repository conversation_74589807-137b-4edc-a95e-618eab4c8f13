var s=Object.defineProperty,m=Object.defineProperties;var d=Object.getOwnPropertyDescriptors;var r=Object.getOwnPropertySymbols;var g=Object.prototype.hasOwnProperty,C=Object.prototype.propertyIsEnumerable;var i=(e,o,p)=>o in e?s(e,o,{enumerable:!0,configurable:!0,writable:!0,value:p}):e[o]=p,a=(e,o)=>{for(var p in o||(o={}))g.call(o,p)&&i(e,p,o[p]);if(r)for(var p of r(o))C.call(o,p)&&i(e,p,o[p]);return e},f=(e,o)=>m(e,d(o));var t=(e,o,p)=>(i(e,typeof o!="symbol"?o+"":o,p),p);import{aM as c,a8 as l}from"./index-bb2cbf17.js";import{d as h}from"./chartEditStore-55fbe93c.js";import{g as n}from"./index-0ec04aee.js";import"./plugin-3ef0fcec.js";import"./icon-f36697ff.js";import"./SettingItem-7fe1cfec.js";/* empty css                                                                      */import"./SettingItemBox-500aaf18.js";import"./CollapseItem.vue_vue_type_script_setup_true_lang-9b6d8958.js";import"./index.esm.min-f1367f57.js";import"./http-36f53bd1.js";import"./lodash-d17632fd.js";import"./fileTypeEnum-21359a08.js";const D={dataset:10*60,useEndDate:!1,endDate:new Date().getTime(),style:"时分秒",showDay:!1,flipperBgColor:"#16293E",flipperTextColor:"#4A9EF8FF",flipperWidth:30,flipperHeight:50,flipperRadius:5,flipperGap:10,flipperType:"down",flipperSpeed:450};class H extends h{constructor(){super(...arguments);t(this,"key",n.key);t(this,"attr",f(a({},c),{w:500,h:100,zIndex:-1}));t(this,"chartConfig",l(n));t(this,"option",l(D))}}export{H as default,D as option};
