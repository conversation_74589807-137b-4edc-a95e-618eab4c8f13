import{d as L,b8 as R,m as B,l as S,a0 as U,r as s,u as t,o as n,c as i,e as v,w as o,f as c,F as d,s as m,E as _,ao as z,t as V,a5 as A,j as $}from"./index-bb2cbf17.js";/* empty css                                                                      */import{_ as N}from"./CollapseItem.vue_vue_type_script_setup_true_lang-9b6d8958.js";import"./chartEditStore-55fbe93c.js";import{u as T}from"./useTargetData.hook-a16b3b4d.js";import"./plugin-3ef0fcec.js";import"./icon-f36697ff.js";const Y=[{label:"强调动画",children:[{label:"弹跳",value:"bounce"},{label:"闪烁",value:"flash"},{label:"放大缩小",value:"pulse"},{label:"放大缩小弹簧",value:"rubberBand"},{label:"左右晃动",value:"headShake"},{label:"左右扇形摇摆",value:"swing"},{label:"放大晃动缩小",value:"tada"},{label:"扇形摇摆",value:"wobble"},{label:"左右上下晃动",value:"jello"}]},{label:"移入动画",children:[{label:"渐显",value:"fadeIn"},{label:"向右进入",value:"fadeInLeft"},{label:"向左进入",value:"fadeInRight"},{label:"向上进入",value:"fadeInUp"},{label:"向下进入",value:"fadeInDown"},{label:"向右长距进入",value:"fadeInLeftBig"},{label:"向左长距进入",value:"fadeInRightBig"},{label:"向上长距进入",value:"fadeInUpBig"},{label:"向下长距进入",value:"fadeInDownBig"},{label:"旋转进入",value:"rotateIn"},{label:"左顺时针旋转",value:"rotateInDownLeft"},{label:"右逆时针旋转",value:"rotateInDownRight"},{label:"左逆时针旋转",value:"rotateInUpLeft"},{label:"右逆时针旋转",value:"rotateInUpRight"},{label:"弹入",value:"bounceIn"},{label:"向右弹入",value:"bounceInLeft"},{label:"向左弹入",value:"bounceInRight"},{label:"向上弹入",value:"bounceInUp"},{label:"向下弹入",value:"bounceInDown"},{label:"光速从右进入",value:"lightSpeedInRight"},{label:"光速从左进入",value:"lightSpeedInLeft"},{label:"光速从右退出",value:"lightSpeedOutRight"},{label:"光速从左退出",value:"lightSpeedOutLeft"},{label:"Y轴旋转",value:"flip"},{label:"中心X轴旋转",value:"flipInX"},{label:"中心Y轴旋转",value:"flipInY"},{label:"左长半径旋转",value:"rollIn"},{label:"由小变大进入",value:"zoomIn"},{label:"左变大进入",value:"zoomInLeft"},{label:"右变大进入",value:"zoomInRight"},{label:"向上变大进入",value:"zoomInUp"},{label:"向下变大进入",value:"zoomInDown"},{label:"向右滑动展开",value:"slideInLeft"},{label:"向左滑动展开",value:"slideInRight"},{label:"向上滑动展开",value:"slideInUp"},{label:"向下滑动展开",value:"slideInDown"}]}],j={key:0,class:"go-chart-configurations-animations"},E=L({__name:"index",setup(F){R(l=>({d4c88a06:f.value}));const p=B(),r=S(""),{targetData:a}=T(),f=U(()=>p.getAppTheme),g=l=>{const u=a.value.styles.animations;return u.length?u[0]===l:!1},I=()=>{a.value.styles.animations=[]},h=l=>{a.value.styles.animations=[l.value]};return(l,u)=>{const x=s("n-button"),w=s("n-grid-item"),y=s("n-grid");return t(a)?(n(),i("div",j,[v(x,{class:"clear-btn go-my-2",disabled:!t(a).styles.animations.length,onClick:I},{default:o(()=>[c(" 清除动画 ")]),_:1},8,["disabled"]),(n(!0),i(d,null,m(t(Y),(b,k)=>(n(),_(t(N),{key:k,name:b.label,expanded:!0},{default:o(()=>[v(y,{"x-gap":6,"y-gap":10,cols:3},{default:o(()=>[(n(!0),i(d,null,m(b.children,(e,D)=>(n(),_(w,{class:z(["animation-item go-transition-quick",[g(e.value)&&"active",r.value===e.value&&`animate__animated  animate__${e.value}`]]),key:D,onMouseover:C=>r.value=e.value,onClick:C=>h(e)},{default:o(()=>[c(V(e.label),1)]),_:2},1032,["class","onMouseover","onClick"]))),128))]),_:2},1024)]),_:2},1032,["name"]))),128))])):A("",!0)}}});const J=$(E,[["__scopeId","data-v-6ba9ba9c"]]);export{J as default};
