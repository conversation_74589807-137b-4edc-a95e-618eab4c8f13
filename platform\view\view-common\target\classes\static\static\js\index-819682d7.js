import{aH as a,cR as u,aI as r,d as S,aG as l,a0 as P,r as I,u as t,o as c,c as O,e as b,w as g,E as T,a5 as d,aa as _}from"./index-bb2cbf17.js";import{l as v}from"./index-56351f34.js";/* empty css                                                                      */import{S as D}from"./SettingItemBox-500aaf18.js";import{c as h}from"./chartEditStore-55fbe93c.js";import{u as q}from"./useTargetData.hook-a16b3b4d.js";var N=(e=>(e[e.NULL=0]="NULL",e[e.SUCCESS=1]="SUCCESS",e[e.FAILURE=2]="FAILURE",e))(N||{}),U=(e=>(e.FILTER="数据过滤",e.MAPPING="数据映射",e.CONTENT="数据内容",e))(U||{}),i=(e=>(e.STATIC="静态数据",e.AJAX="动态请求",e.<PERSON>="公共接口",e))(i||{});const j=[{label:a.GET,value:a.GET,style:{color:"greenyellow",fontWeight:"bold"}},{label:a.POST,value:a.POST,style:{color:"skyblue",fontWeight:"bold"}},{label:a.PUT,value:a.PUT,style:{color:"goldenrod",fontWeight:"bold"}},{label:a.PATCH,value:a.PATCH,style:{color:"violet",fontWeight:"bold"}},{label:a.DELETE,value:a.DELETE,disabled:!0,style:{fontWeight:"bold"}}],B=[{label:u[r.SECOND],value:r.SECOND},{label:u[r.MINUTE],value:r.MINUTE},{label:u[r.HOUR],value:r.HOUR},{label:u[r.DAY],value:r.DAY}],L={key:0,class:"go-chart-configurations-data"},k=S({__name:"index",setup(e){const m=v(()=>_(()=>import("./index-278e084f.js"),["static/js/index-278e084f.js","static/js/index-922b8da1.js","static/js/index-bb2cbf17.js","static/css/index-a8adda2b.css","static/js/chartEditStore-55fbe93c.js","static/js/plugin-3ef0fcec.js","static/js/icon-f36697ff.js","static/js/useTargetData.hook-a16b3b4d.js","static/js/EditorWorker-54a88558.js","static/js/editorWorker-43a98755.js","static/css/EditorWorker-97fea4eb.css","static/js/http-36f53bd1.js","static/js/fileTypeEnum-21359a08.js","static/css/index-652a2e6b.css","static/js/index-56351f34.js","static/css/index-83eadabc.css","static/js/SettingItemBox-500aaf18.js","static/css/StylesSetting-7ca7a4ce.css","static/css/index-4dd462f0.css"])),A=v(()=>_(()=>import("./index-924623e0.js"),["static/js/index-924623e0.js","static/js/index-bb2cbf17.js","static/css/index-a8adda2b.css","static/js/icon-f36697ff.js","static/js/SettingItem-7fe1cfec.js","static/css/StylesSetting-7ca7a4ce.css","static/js/SettingItemBox-500aaf18.js","static/js/chartEditStore-55fbe93c.js","static/js/plugin-3ef0fcec.js","static/js/pondIndex.vue_vue_type_style_index_0_scoped_de860d6d_lang-a2489be1.js","static/js/useTargetData.hook-a16b3b4d.js","static/js/EditorWorker-54a88558.js","static/js/editorWorker-43a98755.js","static/css/EditorWorker-97fea4eb.css","static/css/pondIndex-61161fee.css","static/js/http-36f53bd1.js","static/js/index-922b8da1.js","static/js/fileTypeEnum-21359a08.js","static/css/index-652a2e6b.css","static/js/index-56351f34.js","static/css/index-83eadabc.css","static/css/index-c8da2751.css"])),y=v(()=>_(()=>import("./index-164a9f55.js"),["static/js/index-164a9f55.js","static/js/index-bb2cbf17.js","static/css/index-a8adda2b.css","static/js/icon-f36697ff.js","static/js/http-36f53bd1.js","static/js/SettingItemBox-500aaf18.js","static/css/StylesSetting-7ca7a4ce.css","static/js/chartEditStore-55fbe93c.js","static/js/plugin-3ef0fcec.js","static/js/noData-9e194391.js","static/js/useTargetData.hook-a16b3b4d.js","static/js/pondIndex.vue_vue_type_style_index_0_scoped_de860d6d_lang-a2489be1.js","static/js/SettingItem-7fe1cfec.js","static/js/EditorWorker-54a88558.js","static/js/editorWorker-43a98755.js","static/css/EditorWorker-97fea4eb.css","static/css/pondIndex-61161fee.css","static/js/lodash-d17632fd.js","static/js/index-922b8da1.js","static/js/fileTypeEnum-21359a08.js","static/css/index-652a2e6b.css","static/js/index-56351f34.js","static/css/index-83eadabc.css","static/css/index-287b937d.css"])),{targetData:o}=q(),C=[{label:i.STATIC,value:l.STATIC},{label:i.AJAX,value:l.AJAX},{label:i.Pond,value:l.Pond}],f=P(()=>{var p,s,n;return((p=o.value.chartConfig)==null?void 0:p.chartFrame)===h.STATIC||typeof((n=(s=o.value)==null?void 0:s.option)==null?void 0:n.dataset)=="undefined"});return(p,s)=>{const n=I("n-select");return t(o)?(c(),O("div",L,[b(t(D),{name:"请求方式",alone:!0},{default:g(()=>[b(n,{value:t(o).request.requestDataType,"onUpdate:value":s[0]||(s[0]=E=>t(o).request.requestDataType=E),disabled:f.value,options:C},null,8,["value","disabled"])]),_:1}),t(o).request.requestDataType===t(l).STATIC?(c(),T(t(m),{key:0})):d("",!0),t(o).request.requestDataType===t(l).AJAX?(c(),T(t(A),{key:1})):d("",!0),t(o).request.requestDataType===t(l).Pond?(c(),T(t(y),{key:2})):d("",!0)])):d("",!0)}}}),G=Object.freeze(Object.defineProperty({__proto__:null,default:k},Symbol.toStringTag,{value:"Module"}));export{N as D,U as T,j as a,G as i,B as s};
