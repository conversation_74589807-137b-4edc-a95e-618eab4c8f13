<!--
/**
 * 库存调拨 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2024-04-23 11:16:32
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
	<title th:text="${lang.translate('库存调拨')}">库存调拨</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="opacity:0">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-2802-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- text_input : 业务名称 ,  name -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('业务名称')}">业务名称</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="name" id="name" name="name" th:placeholder="${ lang.translate('请输入'+'业务名称') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            
                <!-- date_input : 业务日期 ,  businessDate  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('业务日期')}">业务日期</div></div>
                    <div class="layui-input-block ">
                        <input input-type="date" lay-filter="businessDate" id="businessDate" name="businessDate"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择'+'业务日期') }" type="text" class="layui-input"    lay-verify=""   />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- select_box : 调入库位 ,  positionInId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('调入库位')}">调入库位</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="positionInId" input-type="select" th:data="${'/service-eam/eam-warehouse-position/query-paged-list'}" extraParam="{}"></div>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-8945-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >

                <!-- text_area : 转移说明 ,  content  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('转移说明')}">转移说明</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="content" id="content" name="content" th:placeholder="${ lang.translate('请输入'+'转移说明') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>
                            <!-- upload : 附件 ,  attachId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('附件')}">附件</div></div>
                    <div class="layui-upload layui-input-block ">
                        <input input-type="upload" id="attachId"  name="attachId" lay-filter="attachId" style="display: none">
                        <button type="button" class="layui-btn" id="attachId-button" th:text="${lang.translate('选择附件')}">选择附件</button>
                        <div class="layui-upload-list" id="attachId-file-list"></div>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->
        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-5416-fieldset">
        <legend>物品列表</legend>
        </fieldset>
        <div class="layui-row form-row" style="text-align: center;" id="random-5416-content">
            <div style="display: inline-block;padding-right: 8px;padding-left: 8px" class="layui-col-xs12">
            <iframe id="random-5416-iframe" js-fn="goodsSelectList" class="form-iframe" frameborder="0" style="width: 100%"></iframe>
            </div>
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 20px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_asset_stock_goods_tranfer:create','eam_asset_stock_goods_tranfer:update','eam_asset_stock_goods_tranfer:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}]];
    var VALIDATE_CONFIG={"businessDate":{"date":true,"labelInForm":"业务日期","inputType":"date_input"},"name":{"labelInForm":"业务名称","inputType":"text_input","required":true},"positionInId":{"labelInForm":"调入库位","inputType":"select_box","required":true}};
    var AUTH_PREFIX="eam_asset_stock_goods_tranfer";

    // OPER_TYPE
    var OPER_TYPE = [[${operType}]] ;
    // OWNER_TYPE
    var OWNER_TYPE = [[${ownerType}]] ;

</script>



<script th:src="'/business/eam/asset_stock_goods_tranfer/asset_stock_goods_tranfer_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset_stock_goods_tranfer/asset_stock_goods_tranfer_form.js?'+${cacheKey}"></script>

</body>
</html>