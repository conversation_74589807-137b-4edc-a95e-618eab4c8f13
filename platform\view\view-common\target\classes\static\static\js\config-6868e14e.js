var E=Object.defineProperty,N=Object.defineProperties;var f=Object.getOwnPropertyDescriptors;var o=Object.getOwnPropertySymbols;var d=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable;var n=(r,a,t)=>a in r?E(r,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[a]=t,e=(r,a)=>{for(var t in a||(a={}))d.call(a,t)&&n(r,t,a[t]);if(o)for(var t of o(a))c.call(a,t)&&n(r,t,a[t]);return r},i=(r,a)=>N(r,f(a));var m=(r,a,t)=>(n(r,typeof a!="symbol"?a+"":a,t),t);import{aM as y,a8 as p}from"./index-bb2cbf17.js";import{d as C}from"./chartEditStore-55fbe93c.js";import{T as s}from"./index-0ec04aee.js";import"./plugin-3ef0fcec.js";import"./icon-f36697ff.js";import"./SettingItem-7fe1cfec.js";/* empty css                                                                      */import"./SettingItemBox-500aaf18.js";import"./CollapseItem.vue_vue_type_script_setup_true_lang-9b6d8958.js";import"./index.esm.min-f1367f57.js";import"./http-36f53bd1.js";import"./lodash-d17632fd.js";import"./fileTypeEnum-21359a08.js";const g=[{startArray:{name:"杭州",N:30.246026,E:120.210792},endArray:[{name:"曼谷",N:22,E:100.49074172973633},{name:"澳大利亚",N:-23.68477416688374,E:133.857421875},{name:"新疆维吾尔自治区",N:41.748,E:84.9023},{name:"德黑兰",N:35,E:51},{name:"德黑兰",N:35,E:51},{name:"美国",N:34.125447565116126,E:241.7431640625},{name:"英国",N:51.508742458803326,E:359.82421875},{name:"巴西",N:-9.96885060854611,E:668.1445312499999}]},{startArray:{name:"北京",N:39.89491,E:116.322056},endArray:[{name:"西藏",N:29.660361,E:91.132212},{name:"广西",N:22.830824,E:108.30616},{name:"江西",N:28.676493,E:115.892151},{name:"贵阳",N:26.647661,E:106.630153}]}],h={dataset:g};class j extends C{constructor(){super(...arguments);m(this,"key",s.key);m(this,"attr",i(e({},y),{w:800,h:800,zIndex:-1}));m(this,"chartConfig",p(s));m(this,"option",p(h))}}export{j as default,h as option};
