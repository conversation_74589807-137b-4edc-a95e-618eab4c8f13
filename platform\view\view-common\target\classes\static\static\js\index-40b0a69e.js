import{e as y,B as u}from"./index.esm.min-f1367f57.js";import"./chartEditStore-55fbe93c.js";import{bw as _,d as $,bl as w,bp as v,bm as A,bq as C,bv as L,br as k,bs as H,bt as z,l as m,L as B,r as l,o as d,E as h,w as r,e as i,u as f,a5 as S}from"./index-bb2cbf17.js";import"./plugin-3ef0fcec.js";import"./icon-f36697ff.js";const O=["legend","xAxis","yAxis","grid"],P=e=>{const s=(a,n)=>parseInt(`${a*Math.random()}`,10)*2,t=[260,251,200,334,366,256,253];return e.map((a,n)=>({name:`data${n+1}`,type:"bar",data:t.map((o,c)=>s(o))}))},q=e=>y({tooltip:{trigger:"axis",showContent:!1,axisPointer:{type:"shadow"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:e.map((s,t)=>`data${t+1}`),axisTick:{alignWithLabel:!0}},yAxis:{show:!0,type:"value"},series:P(e||[])},O),G=["legend","xAxis","yAxis","grid"],I=e=>{const s=(a,n)=>parseInt(`${a*Math.random()}`,10)*2,t=[130,251,200,334,366,456,223];return e.map((a,n)=>({name:`data${n+1}`,type:"line",smooth:!0,lineStyle:{width:1,type:"solid"},emphasis:{focus:"series"},areaStyle:{opacity:.8,color:new _(0,0,0,1,[{offset:1,color:a},{offset:0,color:a}])},showSymbol:!1,data:t.reverse().map((o,c)=>s(o))}))},M=e=>y({tooltip:{trigger:"axis",showContent:!1,axisPointer:{type:"shadow"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:e.map((s,t)=>`data${t+1}`),axisTick:{alignWithLabel:!0}},yAxis:{show:!0,type:"value"},series:I(e||[])},G),E=$({__name:"index",props:{color:{type:Array,required:!0}},setup(e){const s=e;w([v,A,C,L,k,H,z]);const t=m(),a=m(),n={width:"528px",height:"200px"};return B(()=>s.color,o=>{t.value=q(o),a.value=M(o)},{immediate:!0,deep:!0}),(o,c)=>{const p=l("n-tab-pane"),b=l("n-tabs"),x=l("n-card"),g=l("n-space");return d(),h(g,null,{default:r(()=>[t.value?(d(),h(x,{key:0,class:"go-mt-3",bordered:!1,role:"dialog",size:"small","aria-modal":"true"},{default:r(()=>[i(b,{type:"segment",size:"small",animated:""},{default:r(()=>[i(p,{name:"柱状图",tab:"柱状图"},{default:r(()=>[i(f(u),{ref:"vChartRefBar",theme:{color:e.color},option:t.value,"manual-update":!0,autoresize:"",style:n},null,8,["theme","option"])]),_:1}),i(p,{name:"折线图",tab:"折线图"},{default:r(()=>[i(f(u),{ref:"vChartRefLine",theme:{color:e.color},option:a.value,"manual-update":!0,autoresize:"",style:n},null,8,["theme","option"])]),_:1})]),_:1})]),_:1})):S("",!0)]),_:1})}}});export{E as default};
