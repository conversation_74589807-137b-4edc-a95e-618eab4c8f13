import{d as c,r as _,c as n,e as r,w as d,z as p,P as i,A as l,B as m,q as t,o as u,f,j as v}from"./index-bb2cbf17.js";const x="/static/svg/500-0e2ea61d.svg",s=e=>(l("data-v-bc79baef"),e=e(),m(),e),b={class:"go-error"},g=s(()=>t("div",{class:"text-center"},[t("img",{src:x,alt:""})],-1)),h=s(()=>t("div",{class:"text-center"},[t("h1",{class:"text-base text-gray-500"},"抱歉，服务器出错了")],-1)),y=c({__name:"500",setup(e){function o(){p(i.BASE_HOME_NAME)}return(B,E)=>{const a=_("n-button");return u(),n("div",b,[g,h,r(a,{type:"primary",secondary:"",onClick:o},{default:d(()=>[f("回到首页")]),_:1})])}}});const C=v(y,[["__scopeId","data-v-bc79baef"]]);export{C as default};
