var f=Object.defineProperty,l=Object.defineProperties;var C=Object.getOwnPropertyDescriptors;var e=Object.getOwnPropertySymbols;var c=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable;var p=(t,o,r)=>o in t?f(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,m=(t,o)=>{for(var r in o||(o={}))c.call(o,r)&&p(t,r,o[r]);if(e)for(var r of e(o))d.call(o,r)&&p(t,r,o[r]);return t},a=(t,o)=>l(t,C(o));var i=(t,o,r)=>(p(t,typeof o!="symbol"?o+"":o,r),r);import{aM as g,a8 as n}from"./index-bb2cbf17.js";import{d as b}from"./chartEditStore-55fbe93c.js";import{h as s}from"./index-0ec04aee.js";import"./plugin-3ef0fcec.js";import"./icon-f36697ff.js";import"./SettingItem-7fe1cfec.js";/* empty css                                                                      */import"./SettingItemBox-500aaf18.js";import"./CollapseItem.vue_vue_type_script_setup_true_lang-9b6d8958.js";import"./index.esm.min-f1367f57.js";import"./http-36f53bd1.js";import"./lodash-d17632fd.js";import"./fileTypeEnum-21359a08.js";const h={border:6,bgColor:"#84a5e9",borderColor:"#84a5e9"};class v extends b{constructor(){super(...arguments);i(this,"key",s.key);i(this,"attr",a(m({},g),{w:150,h:150,zIndex:-1}));i(this,"chartConfig",n(s));i(this,"option",n(h))}}export{v as default,h as option};
