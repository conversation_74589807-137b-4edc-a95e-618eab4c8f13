var ne=Object.defineProperty,re=Object.defineProperties;var se=Object.getOwnPropertyDescriptors;var R=Object.getOwnPropertySymbols;var ae=Object.prototype.hasOwnProperty,ie=Object.prototype.propertyIsEnumerable;var A=(e,t,o)=>t in e?ne(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,a=(e,t)=>{for(var o in t||(t={}))ae.call(t,o)&&A(e,o,t[o]);if(R)for(var o of R(t))ie.call(t,o)&&A(e,o,t[o]);return e},K=(e,t)=>re(e,se(t));var C=(e,t,o)=>new Promise((n,i)=>{var u=s=>{try{d(o.next(s))}catch(l){i(l)}},y=s=>{try{d(o.throw(s))}catch(l){i(l)}},d=s=>s.done?n(s.value):Promise.resolve(s.value).then(u,y);d((o=o.apply(e,t)).next())});import{g as X}from"./storage-2b59f4d0.js";import"./querySelectorAll-a1c431d2.js";import{ap as ce,d as g,o as c,c as p,F as Y,s as F,ao as S,u as r,aq as M,an as f,ar as H,E as m,as as V,at as j,au as x,av as z,G as J,j as T,a0 as b,n as le,aw as ue,l as Q,ax as de,ay as pe,q as ye,e as L,a5 as B,az as O,w as ve,aA as fe,aB as P,aC as me,S as ge}from"./index-bb2cbf17.js";import{u as he,f as we,a as _e}from"./index-0ec04aee.js";import{u as U}from"./useLifeHandler.hook-faa8e356.js";import{u as k,C as Ce}from"./chartEditStore-55fbe93c.js";import{l as q}from"./listen-92ff7612.js";import"./SettingItem-7fe1cfec.js";/* empty css                                                                      */import"./SettingItemBox-500aaf18.js";import"./CollapseItem.vue_vue_type_script_setup_true_lang-9b6d8958.js";import"./icon-f36697ff.js";import"./index.esm.min-f1367f57.js";import"./http-36f53bd1.js";import"./lodash-d17632fd.js";import"./plugin-3ef0fcec.js";import"./fileTypeEnum-21359a08.js";const W=(e,t)=>({zIndex:t+1,left:`${e.x}px`,top:`${e.y}px`}),$=(e,t)=>({width:`${t?t*e.w:e.w}px`,height:`${t?t*e.h:e.h}px`}),Z=e=>({display:e.hide?"none":"block"}),ee=e=>{const t={};return e&&e.overFlowHidden&&(t.overflow="hidden"),t},Se=e=>{const t=e.selectColor?{background:e.background}:{background:`url(${e.backgroundImage}) center center / cover no-repeat !important`};return a({position:"relative",width:e.width?`${e.width||100}px`:"100%",height:e.height?`${e.height}px`:"100%"},t)},xe=()=>{window.$KeyboardActive={ctrl:!1,space:!1},document.onkeydown=e=>{const{keyCode:t}=e;if(t==32&&e.target==document.body&&e.preventDefault(),[17,32].includes(t)&&window.$KeyboardActive)switch(t){case 17:window.$KeyboardActive.ctrl=!0;break;case 32:window.$KeyboardActive.space=!0;const o=document.querySelector(".go-preview");o&&o.style.position==="absolute"&&(o.style.cursor="move");break}},document.onkeyup=e=>{const{keyCode:t}=e;if(t==32&&e.target==document.body&&e.preventDefault(),[17,32].includes(t)&&window.$KeyboardActive)switch(t){case 17:window.$KeyboardActive.ctrl=!1;break;case 32:window.$KeyboardActive.space=!1;break}const o=document.querySelector(".go-preview");o&&(o.style.cursor="default")}};let D=[0,0],I=[0,0];const G=e=>{var y,d;const t=document.querySelector(".go-preview");if(!t||t.style.position!=="absolute"||!((y=window.$KeyboardActive)!=null&&y.space))return;e.preventDefault(),e.stopPropagation(),(d=document.activeElement)==null||d.blur();const o=e.screenX,n=e.screenY,i=q(window,"mousemove",ce(s=>{const l=s.screenX-o,h=s.screenY-n,[v,w]=D,[E,_]=I;if(D=[w,l],I=[_,h],t){const te=t.style.left?Number(t.style.left.split("px")[0]):0,oe=t.style.top?Number(t.style.top.split("px")[0]):0;t.style.left=te+(w>v?Math.abs(w-v):-Math.abs(w-v))+"px",t.style.top=oe+(_>E?Math.abs(_-E):-Math.abs(_-E))+"px"}},20)),u=q(window,"mouseup",()=>{D=[0,0],I=[0,0],i(),u()})},be=g({__name:"index",props:{groupData:{type:Object,required:!0},themeSetting:{type:Object,required:!0},themeColor:{type:Object,required:!0},groupIndex:{type:Number,required:!0}},setup(e){return(t,o)=>(c(),p("div",{class:S(r(M)(e.groupData.styles.animations)),style:f(a(a({},r($)(e.groupData.attr)),r(x)(e.groupData.styles)))},[(c(!0),p(Y,null,F(e.groupData.groupList,n=>(c(),p("div",{class:S(["chart-item",r(M)(n.styles.animations)]),key:n.id,style:f(a(a(a(a({},r(W)(n.attr,e.groupIndex)),r(Z)(n.status)),r(ee)(n.preview)),r(H)(n.styles)))},[(c(),m(J(n.chartConfig.chartKey),V({id:n.id,chartConfig:n,themeSetting:e.themeSetting,themeColor:e.themeColor,style:a(a(a({},r($)(n.attr)),r(x)(n.styles)),r(z)(n.styles))},j(r(U)(n))),null,16,["id","chartConfig","themeSetting","themeColor","style"]))],6))),128))],6))}});const ke=T(be,[["__scopeId","data-v-6e91b444"]]),$e=g({__name:"index",setup(e){const{initDataPond:t,clearMittDataPondMap:o}=he(),n=k(),i=b(()=>n.editCanvasConfig.chartThemeSetting),u=b(()=>ue(n.editCanvasConfig.chartCustomThemeColorInfo)[n.editCanvasConfig.chartThemeColor]);return o(),le(()=>{t(k)}),(y,d)=>(c(!0),p(Y,null,F(r(n).componentList,(s,l)=>(c(),p("div",{class:S(["chart-item",r(M)(s.styles.animations)]),key:s.id,style:f(a(a(a(a(a(a({},r(W)(s.attr,l)),r(z)(s.styles)),r(Z)(s.status)),r(ee)(s.preview)),r(H)(s.styles)),r($)(s.attr)))},[s.isGroup?(c(),m(r(ke),{key:0,groupData:s,groupIndex:l,themeSetting:i.value,themeColor:u.value},null,8,["groupData","groupIndex","themeSetting","themeColor"])):(c(),m(J(s.chartConfig.chartKey),V({key:1,id:s.id,chartConfig:s,themeSetting:i.value,themeColor:u.value,style:a(a({},r($)(s.attr)),r(x)(s.styles))},j(r(U)(s))),null,16,["id","chartConfig","themeSetting","themeColor","style"]))],6))),128))}});const N=T($e,[["__scopeId","data-v-f751a93f"]]),Ee=e=>{const t=Q(!1),o=setInterval(()=>{if(window.$vue.component){clearInterval(o);const n=i=>{window.$vue.component(i.chartConfig.chartKey)||window.$vue.component(i.chartConfig.chartKey,we(i.chartConfig))};e.componentList.forEach(i=>C(void 0,null,function*(){i.isGroup?i.groupList.forEach(u=>{n(u)}):n(i)})),t.value=!0}},200);return{show:t}},De=e=>{const t=k();t.requestGlobalConfig=e[Ce.REQUEST_GLOBAL_CONFIG]},Ie=g({__name:"suspenseIndex",setup(e){return C(this,null,function*(){let t,o;[t,o]=de(()=>X()),yield t,o();const n=k();pe(`预览-${n.editCanvasConfig.projectName}`);const i=b(()=>a(a({overflow:"hidden"},Se(n.editCanvasConfig)),x(n.editCanvasConfig))),u=b(()=>{const l=n.editCanvasConfig.previewScaleType;return l===O.SCROLL_Y||l===O.SCROLL_X});De(n);const{entityRef:y,previewRef:d}=_e(n),{show:s}=Ee(n);return xe(),(l,h)=>(c(),p("div",{class:S(`go-preview ${r(n).editCanvasConfig.previewScaleType}`),onMousedown:h[0]||(h[0]=(...v)=>r(G)&&r(G)(...v))},[u.value?(c(),p("div",{key:0,ref_key:"entityRef",ref:y,class:"go-preview-entity"},[ye("div",{ref_key:"previewRef",ref:d,class:"go-preview-scale"},[r(s)?(c(),p("div",{key:0,style:f(i.value)},[L(r(N))],4)):B("",!0)],512)],512)):(c(),p("div",{key:1,ref_key:"previewRef",ref:d,class:"go-preview-scale"},[r(s)?(c(),p("div",{key:0,style:f(i.value)},[L(r(N))],4)):B("",!0)],512))],34))})}});const Me=T(Ie,[["__scopeId","data-v-c8c8cb5b"]]),Le=g({__name:"index",setup(e){return(t,o)=>(c(),m(fe,null,{default:ve(()=>[L(Me)]),_:1}))}}),Qe=g({__name:"wrapper",setup(e){let t=Q(Date.now());return[P.JSON,P.CHART_TO_PREVIEW].forEach(o=>{!window.opener||!window.opener.addEventListener||window.opener.addEventListener(o,n=>C(this,null,function*(){const i=yield X();me(ge.GO_CHART_STORAGE_LIST,[K(a({},n.detail),{id:i.id})]),t.value=Date.now()}))}),(o,n)=>(c(),m(Le,{key:r(t)}))}});export{Qe as default};
