<!--
/**
 * 保养方案 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2024-03-25 08:44:45
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('保养方案')}">保养方案</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 计划状态 , status ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('计划状态')}" class="search-label status-label">计划状态</span><span class="search-colon">:</span></div>
                        <div id="status" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.EamPlanStatusEnum')}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 保养设备 , assetId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 保养周期 , actionCycleId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 开始时间 , startTime ,typeName=date_input, isHideInSearch=true -->
                    <!-- 结束时间 , endTime ,typeName=date_input, isHideInSearch=true -->
                    <!-- 预计工时(时) , totalCost ,typeName=number_input, isHideInSearch=true -->
                    <!-- 超时工时(分) , timeout ,typeName=number_input, isHideInSearch=true -->
                    <!-- 方案说明 , info ,typeName=text_area, isHideInSearch=true -->
                    <!-- 备注 , notes ,typeName=text_area, isHideInSearch=true -->
                    <!-- 制单人 , originatorId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 上次执行 , lastTime ,typeName=date_input, isHideInSearch=true -->
                    <!-- 下次执行 , nextTime ,typeName=date_input, isHideInSearch=true -->
                    <!-- 修改人ID , updateBy ,typeName=text_input, isHideInSearch=true -->
                    <!-- 保养项目数 , itemCount ,typeName=text_input, isHideInSearch=true -->
                    <!-- 保养类型 , maintainType ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('保养类型')}" class="search-label maintainType-label">保养类型</span><span class="search-colon">:</span></div>
                        <div id="maintainType" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_maintain_type'}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 计划单据 , code ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('计划单据')}" class="search-label code-label">计划单据</span><span class="search-colon">:</span></div>
                        <input id="code" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 计划名称 , name ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('计划名称')}" class="search-label name-label">计划名称</span><span class="search-colon">:</span></div>
                        <input id="name" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 循环方式 , cycleMethod ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('循环方式')}" class="search-label cycleMethod-label">循环方式</span><span class="search-colon">:</span></div>
                        <div id="cycleMethod" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.MaintainCycleMethodEnum')}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 保养班组 , groupId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('保养班组')}" class="search-label groupId-label">保养班组</span><span class="search-colon">:</span></div>
                        <div id="groupId" th:data="${'/service-eam/eam-maintain-group/query-list'}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 保养设备 , assetCode ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('保养设备')}" class="search-label assetCode-label">保养设备</span><span class="search-colon">:</span></div>
                        <input id="assetCode" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 设备名称 , assetName ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('设备名称')}" class="search-label assetName-label">设备名称</span><span class="search-colon">:</span></div>
                        <input id="assetName" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 设备型号 , assetModel ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('设备型号')}" class="search-label assetModel-label">设备型号</span><span class="search-colon">:</span></div>
                        <input id="assetModel" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 设备序列 , assetSn ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('设备序列')}" class="search-label assetSn-label">设备序列</span><span class="search-colon">:</span></div>
                        <input id="assetSn" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索','','cmp:table.search')}">搜索</span></button>
                <button id="search-button-advance" class="layui-btn layui-btn-primary icon-btn search-button-advance"><i class="layui-icon">&#xe671;</i><span th:text="${lang.translate('更多','','cmp:table.search')}">更多</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 42px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${perm.checkAuth('eam_maintain_plan:create')}" id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建','','cmp:table.button')}">新建</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${perm.checkAuth('eam_maintain_plan:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view"  data-id="{{d.id}}"> <span th:text="${lang.translate('查看','','cmp:table.ops')}">查看</span></button>
    <button th:if="${perm.checkAnyAuth('eam_maintain_plan:update','eam_maintain_plan:save')}" class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit"data-id="{{d.id}}"><span th:text="${lang.translate('修改','','cmp:table.ops')}">修改</span></button>


    <button th:if="${perm.checkAuth('eam_maintain_plan:delete')}" class="layui-btn layui-btn-xs layui-btn-danger ops-delete-button " lay-event="del" data-id="{{d.id}}"><span th:text="${lang.translate('删除','','cmp:table.ops')}">删除</span></button>

    <button th:if="${perm.checkAuth('eam_maintain_plan:start')}"class="layui-btn layui-btn-xs  start-button " lay-event="start" data-id="{{d.id}}"><span th:text="${lang.translate('启动','','cmp:table.ops')}">启动</span></button>
    <button th:if="${perm.checkAuth('eam_maintain_plan:stop')}"class="layui-btn layui-btn-xs  stop-button " lay-event="stop" data-id="{{d.id}}"><span th:text="${lang.translate('停用','','cmp:table.ops')}">停用</span></button>
    <button th:if="${perm.checkAuth('eam_maintain_plan:execute')}"class="layui-btn layui-btn-xs  execute-button " lay-event="execute" data-id="{{d.id}}"><span th:text="${lang.translate('创建任务','','cmp:table.ops')}">创建任务</span></button>

</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.EamPlanStatusEnum')}]];
    var SELECT_CYCLEMETHOD_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.MaintainCycleMethodEnum')}]];
    var AUTH_PREFIX="eam_maintain_plan";


</script>

<script th:src="'/business/eam/maintain_plan/maintain_plan_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/maintain_plan/maintain_plan_list.js?'+${cacheKey}"></script>

</body>
</html>