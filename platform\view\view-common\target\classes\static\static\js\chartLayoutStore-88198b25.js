var h=Object.defineProperty;var a=Object.getOwnPropertySymbols;var l=Object.prototype.hasOwnProperty,g=Object.prototype.propertyIsEnumerable;var r=(e,t,s)=>t in e?h(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,i=(e,t)=>{for(var s in t||(t={}))l.call(t,s)&&r(e,s,t[s]);if(a)for(var s of a(t))g.call(t,s)&&r(e,s,t[s]);return e};import{b1 as p,aP as u,x as y,S}from"./index-bb2cbf17.js";import{u as A}from"./chartEditStore-55fbe93c.js";var n=(e=>(e.SINGLE="single",e.DOUBLE="double",e))(n||{}),o=(e=>(e.THUMBNAIL="thumbnail",e.TEXT="text",e))(o||{}),P=(e=>(e.LAYERS="layers",e.CHARTS="charts",e.DETAILS="details",e.Chart_TYPE="chartType",e.LAYER_TYPE="layerType",e.PERCENTAGE="percentage",e.RE_POSITION_CANVAS="rePositionCanvas",e))(P||{});const C=A(),{GO_CHART_LAYOUT_STORE:c}=S,L=p(c),v=u({id:"useChartLayoutStore",state:()=>i({layers:!0,charts:!0,details:!1,chartType:n.SINGLE,layerType:o.THUMBNAIL,percentage:0,rePositionCanvas:!1},L),getters:{getLayers(){return this.layers},getCharts(){return this.charts},getDetails(){return this.details},getChartType(){return this.chartType},getLayerType(){return this.layerType},getPercentage(){return this.percentage},getRePositionCanvas(){return this.rePositionCanvas}},actions:{setItem(e,t,s=!0){this.$patch(T=>{T[e]=t}),y(c,this.$state),this.rePositionCanvas=!0,s&&setTimeout(()=>{C.computedScale()},500)},setItemUnHandle(e,t){this.$patch(s=>{s[e]=t})}}});export{P as C,o as L,n as a,v as u};
