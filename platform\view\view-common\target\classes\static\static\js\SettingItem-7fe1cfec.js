import{d as s,r as n,o,c as r,b as i,e as d,w as c,f as m,t as l,an as p,j as _}from"./index-bb2cbf17.js";/* empty css                                                                      */const f=s({__name:"SettingItem",props:{name:{type:String,required:!1},width:{type:Number,required:!1}},setup(e){return(t,u)=>{const a=n("n-text");return o(),r("div",{class:"go-setting-item",style:p({width:e.width+"px"})},[i(t.$slots,"default",{},void 0,!0),d(a,{class:"name",depth:"3"},{default:c(()=>[m(l(e.name),1)]),_:1})],4)}}}),g=_(f,[["__scopeId","data-v-30fb98bd"]]);export{g as S};
