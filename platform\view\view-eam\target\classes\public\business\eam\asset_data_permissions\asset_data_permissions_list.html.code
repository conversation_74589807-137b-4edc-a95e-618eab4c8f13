<!--
/**
 * 资产数据权限 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2023-12-23 09:56:02
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('资产数据权限')}">资产数据权限</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 名称 , name ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('名称')}" class="search-label name-label">名称</span><span class="search-colon">:</span></div>
                        <input id="name" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 权限状态 , status ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 归属 , ownerCode ,typeName=select_box, isHideInSearch=true -->
                    <!-- 所属权限状态 , ownOrgAuthorityEnable ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 所在所属状态 , ownOrgLocalEnable ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 所属联动状态 , ownOrgCascadeEnable ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 备注 , ownOrgNotes ,typeName=text_input, isHideInSearch=true -->
                    <!-- 组织权限状态 , orgAuthorityEnable ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 所在组织状态 , orgLocalEnable ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 组织联动状态 , orgCascadeEnable ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 备注 , orgNotes ,typeName=text_input, isHideInSearch=true -->
                    <!-- 分类权限状态 , catalogAuthorityEnable ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 分类级联状态 , catalogCascadeEnable ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 备注 , catalogNotes ,typeName=text_input, isHideInSearch=true -->
                    <!-- 位置权限状态 , positionAuthorityEnable ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 备注 , positionNotes ,typeName=text_input, isHideInSearch=true -->
                    <!-- 仓库 , warehouseAuthorityEnable ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 备注 , warehouseNotes ,typeName=text_input, isHideInSearch=true -->
                    <!-- 优先级 , priority ,typeName=number_input, isHideInSearch=true -->
                    <!-- 备注 , notes ,typeName=text_area, isHideInSearch=true -->
                    <!-- 修改人ID , updateBy ,typeName=text_input, isHideInSearch=true -->
                    <!-- 存放位置 , positionIds ,typeName=select_box, isHideInSearch=true -->
                    <!-- 仓库位置 , warehouseIds ,typeName=select_box, isHideInSearch=true -->
                    <!-- 资产分类 , categoryIds ,typeName=select_box, isHideInSearch=true -->
                    <!-- 使用组织 , organizationIds ,typeName=button, isHideInSearch=true -->
                    <!-- 所属组织 , ownOrganizationIds ,typeName=button, isHideInSearch=true -->
                    <!-- 权限编码 , code ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('权限编码')}" class="search-label code-label">权限编码</span><span class="search-colon">:</span></div>
                        <input id="code" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 业务角色 , roleCode ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('业务角色')}" class="search-label roleCode-label">业务角色</span><span class="search-colon">:</span></div>
                        <div id="roleCode" th:data="${'/service-system/sys-busi-role/query-paged-list'}" style="width:180px" extraParam="{}"></div>
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索','','cmp:table.search')}">搜索</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 42px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${perm.checkAuth('eam_asset_data_permissions:create')}" id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建','','cmp:table.button')}">新建</span></button>
        <button id="in-multiple-roles"  class="layui-btn icon-btn layui-btn-sm  in-mul-roles " lay-event="tool-in-multiple-roles"><span th:text="${lang.translate('多角色','','cmp:table.button')}">多角色</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${perm.checkAuth('eam_asset_data_permissions:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view"  data-id="{{d.id}}"> <span th:text="${lang.translate('查看','','cmp:table.ops')}">查看</span></button>
    <button th:if="${perm.checkAnyAuth('eam_asset_data_permissions:update','eam_asset_data_permissions:save')}" class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit"data-id="{{d.id}}"><span th:text="${lang.translate('修改','','cmp:table.ops')}">修改</span></button>


    <button th:if="${perm.checkAuth('eam_asset_data_permissions:delete')}" class="layui-btn layui-btn-xs layui-btn-danger ops-delete-button " lay-event="del" data-id="{{d.id}}"><span th:text="${lang.translate('删除','','cmp:table.ops')}">删除</span></button>

    <button class="layui-btn layui-btn-xs " lay-event="person-detail" data-id="{{d.id}}"><span th:text="${lang.translate('人员明细','','cmp:table.ops')}">人员明细</span></button>

</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var RADIO_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var SELECT_OWNERCODE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetOwnerCodeEnum')}]];
    var RADIO_OWNORGAUTHORITYENABLE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_OWNORGLOCALENABLE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_OWNORGCASCADEENABLE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_ORGAUTHORITYENABLE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_ORGLOCALENABLE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_ORGCASCADEENABLE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_CATALOGAUTHORITYENABLE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_CATALOGCASCADEENABLE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_POSITIONAUTHORITYENABLE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_WAREHOUSEAUTHORITYENABLE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var AUTH_PREFIX="eam_asset_data_permissions";

    // 资产分类数据
    var CATEGORY_CODE = [[${categoryCode}]] ;

</script>

<script th:src="'/business/eam/asset_data_permissions/asset_data_permissions_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset_data_permissions/asset_data_permissions_list.js?'+${cacheKey}"></script>

</body>
</html>