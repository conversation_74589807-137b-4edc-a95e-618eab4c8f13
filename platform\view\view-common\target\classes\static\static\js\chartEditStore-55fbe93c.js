var F=Object.defineProperty,Y=Object.defineProperties;var W=Object.getOwnPropertyDescriptors;var G=Object.getOwnPropertySymbols;var K=Object.prototype.hasOwnProperty,V=Object.prototype.propertyIsEnumerable;var U=(t,e,s)=>e in t?F(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,P=(t,e)=>{for(var s in e||(e={}))K.call(e,s)&&U(t,s,e[s]);if(G)for(var s of G(e))V.call(e,s)&&U(t,s,e[s]);return t},H=(t,e)=>Y(t,W(e));var d=(t,e,s)=>(U(t,typeof e!="symbol"?e+"":e,s),s);import{aG as X,aH as z,aI as j,aJ as $,aK as Q,aL as J,M as E,aM as Z,a8 as R,aN as w,aO as y,aP as M,aQ as tt,$ as et,aR as st,aS as rt,aT as at,aU as ot,aV as it,aW as x,aX as _,a6 as b,a7 as nt,aY as ht}from"./index-bb2cbf17.js";import{a as C,b as i,l as p}from"./plugin-3ef0fcec.js";var B=(t=>(t.ECHARTS="echarts",t.NAIVE_UI="naiveUI",t.COMMON="common",t.STATIC="static",t))(B||{});const It=[{label:"正常",value:"normal"},{label:"正片叠底",value:"multiply"},{label:"叠加",value:"overlay"},{label:"滤色",value:"screen"},{label:"变暗",value:"darken"},{label:"变亮",value:"lighten"},{label:"颜色减淡",value:"color-dodge"},{label:"颜色加深",value:"color-burn;"},{label:"强光",value:"hard-light"},{label:"柔光",value:"soft-light"},{label:"差值",value:"difference"},{label:"排除",value:"exclusion"},{label:"色相",value:"hue"},{label:"饱和度",value:"saturation"},{label:"颜色",value:"color"},{label:"亮度",value:"luminosity"}];var ct=(t=>(t.CHARTS="Charts",t.TABLES="Tables",t.INFORMATIONS="Informations",t.PHOTOS="Photos",t.ICONS="Icons",t.DECORATES="Decorates",t))(ct||{}),lt=(t=>(t.CHARTS="图表",t.TABLES="列表",t.INFORMATIONS="信息",t.PHOTOS="图片",t.ICONS="图标",t.DECORATES="小组件",t))(lt||{}),ut=(t=>(t[t.VIEW=0]="VIEW",t[t.CONFIG=1]="CONFIG",t))(ut||{});const dt={requestDataType:X.STATIC,requestHttpType:z.GET,requestUrl:"",requestInterval:void 0,requestIntervalUnit:j.SECOND,requestContentType:$.DEFAULT,requestParamsBodyType:Q.NONE,requestSQLContent:{sql:"select * from  where"},requestParams:{Body:{"form-data":{},"x-www-form-urlencoded":{},json:"",xml:""},Header:{},Params:{}}};class ft{constructor(){d(this,"id",E());d(this,"isGroup",!1);d(this,"attr",H(P({},Z),{zIndex:-1}));d(this,"styles",{filterShow:!1,hueRotate:0,saturate:1,contrast:1,brightness:1,opacity:1,rotateZ:0,rotateX:0,rotateY:0,skewX:0,skewY:0,blendMode:"normal",animations:[]});d(this,"preview",{overFlowHidden:!1});d(this,"status",{lock:!1,hide:!1});d(this,"request",R(dt));d(this,"filter");d(this,"events",{baseEvent:{[w.ON_CLICK]:void 0,[w.ON_DBL_CLICK]:void 0,[w.ON_MOUSE_ENTER]:void 0,[w.ON_MOUSE_LEAVE]:void 0},advancedEvents:{[y.VNODE_MOUNTED]:void 0,[y.VNODE_BEFORE_MOUNT]:void 0},interactEvents:[]})}}class gt extends ft{constructor(){super(...arguments);d(this,"isGroup",!0);d(this,"chartConfig",{key:"group",chartKey:"group",conKey:"group",category:"group",categoryName:"group",package:"group",chartFrame:B.COMMON,title:J,image:""});d(this,"groupList",[]);d(this,"key","group");d(this,"option",{});d(this,"id",E());d(this,"attr",{w:0,h:0,x:0,y:0,offsetX:0,offsetY:0,zIndex:-1})}}var o=(t=>(t.ADD="add",t.DELETE="delete",t.UPDATE="update",t.MOVE="move",t.COPY="copy",t.CUT="cut",t.PASTE="paste",t.TOP="top",t.BOTTOM="bottom",t.UP="up",t.DOWN="down",t.GROUP="group",t.UN_GROUP="unGroup",t.LOCK="lock",t.UNLOCK="unLock",t.HIDE="hide",t.SHOW="show",t))(o||{}),f=(t=>(t.CANVAS="canvas",t.CHART="chart",t))(f||{}),Ct=(t=>(t.BACK_STACK="backStack",t.FORWARD_STACK="forwardStack",t))(Ct||{}),D=(t=>(t.ID="id",t.TARGET_TYPE="targetType",t.ACTION_TYPE="actionType",t.HISTORY_DATA="historyData",t))(D||{});const pt=M({id:"useChartHistoryStore",state:()=>({backStack:[],forwardStack:[]}),getters:{getBackStack(){return this.backStack},getForwardStack(){return this.forwardStack}},actions:{createStackItem(t,e,s=f.CHART){this.pushBackStackItem(Object.freeze({[D.ID]:new Date().getTime().toString(),[D.HISTORY_DATA]:t,[D.ACTION_TYPE]:e,[D.TARGET_TYPE]:s}))},canvasInit(t){this.createStackItem([t],o.ADD,f.CANVAS)},pushBackStackItem(t,e=!1){t instanceof Array?this.backStack=[...this.backStack,...t]:this.backStack.push(t),this.backStack.splice(0,this.backStack.length-tt),!e&&this.clearForwardStack()},pushForwardStack(t){t instanceof Array?this.forwardStack=[...this.forwardStack,...t]:this.forwardStack.push(t)},popBackStackItem(){if(this.backStack.length>0)return this.backStack.pop()},popForwardStack(){if(this.forwardStack.length>0)return this.forwardStack.pop()},clearForwardStack(){this.forwardStack=[]},clearBackStack(){const t=this.getBackStack[0];this.backStack=[t]},backAction(){try{if(C(),this.getBackStack.length>1){const t=this.popBackStackItem();if(!t){i();return}return this.pushForwardStack(t),i(),t}i()}catch(t){p()}},forwardAction(){try{if(C(),this.getForwardStack.length){const t=this.popForwardStack();if(!t){i();return}return this.pushBackStackItem(t,!0),i(),t}i()}catch(t){p()}},createAddHistory(t){this.createStackItem(t,o.ADD,f.CHART)},createUpdateHistory(t){this.createStackItem(t,o.UPDATE,f.CHART)},createDeleteHistory(t){this.createStackItem(t,o.DELETE,f.CHART)},createMoveHistory(t){this.createStackItem(t,o.MOVE,f.CHART)},createLayerHistory(t,e){this.createStackItem(t,e,f.CHART)},createPasteHistory(t){this.createStackItem(t,o.CUT,f.CHART)},createGroupHistory(t){this.createStackItem(t,o.GROUP,f.CHART)},createUnGroupHistory(t){this.createStackItem(t,o.UN_GROUP,f.CHART)},createLockHistory(t){this.createStackItem(t,o.LOCK,f.CHART)},createUnLockHistory(t){this.createStackItem(t,o.UNLOCK,f.CHART)},createHideHistory(t){this.createStackItem(t,o.HIDE,f.CHART)},createShowHistory(t){this.createStackItem(t,o.SHOW,f.CHART)}}});var N=(t=>(t.EDIT_LAYOUT_DOM="editLayoutDom",t.EDIT_CONTENT_DOM="editContentDom",t.OFFSET="offset",t.SCALE="scale",t.USER_SCALE="userScale",t.LOCK_SCALE="lockScale",t.IS_CREATE="isCreate",t.IS_DRAG="isDrag",t.IS_SELECT="isSelect",t.IS_CODE_EDIT="isCodeEdit",t))(N||{}),St=(t=>(t.PROJECT_NAME="projectName",t.WIDTH="width",t.HEIGHT="height",t.CHART_THEME_COLOR="chartThemeColor",t.CHART_CUSTOM_THEME_COLOR_INFO="chartCustomThemeColorInfo",t.CHART_THEME_SETTING="chartThemeSetting",t.BACKGROUND="background",t.BACKGROUND_IMAGE="backgroundImage",t.SELECT_COLOR="selectColor",t.PREVIEW_SCALE_TYPE="previewScaleType",t))(St||{}),N=(t=>(t.START_X="startX",t.START_Y="startY",t.X="x",t.Y="y",t))(N||{}),m=(t=>(t.EDIT_RANGE="editRange",t.EDIT_CANVAS="editCanvas",t.RIGHT_MENU_SHOW="rightMenuShow",t.MOUSE_POSITION="mousePosition",t.TARGET_CHART="targetChart",t.RECORD_CHART="recordChart",t.EDIT_CANVAS_CONFIG="editCanvasConfig",t.REQUEST_GLOBAL_CONFIG="requestGlobalConfig",t.COMPONENT_LIST="componentList",t))(m||{});const g=pt(),Tt=et(),kt=M({id:"useChartEditStore",state:()=>({editCanvas:{editLayoutDom:null,editContentDom:null,offset:20,scale:1,userScale:1,lockScale:!1,isCreate:!1,isDrag:!1,isSelect:!1,isCodeEdit:!1},rightMenuShow:!1,mousePosition:{startX:0,startY:0,x:0,y:0},targetChart:{hoverId:void 0,selectId:[]},recordChart:void 0,editCanvasConfig:{projectName:void 0,width:1920,height:1080,filterShow:!1,hueRotate:0,saturate:1,contrast:1,brightness:1,opacity:1,rotateZ:0,rotateX:0,rotateY:0,skewX:0,skewY:0,blendMode:"normal",background:void 0,backgroundImage:void 0,selectColor:!0,chartThemeColor:st,chartCustomThemeColorInfo:void 0,chartThemeSetting:rt,previewScaleType:at},requestGlobalConfig:{requestDataPond:[],requestOriginUrl:"",requestInterval:ot,requestIntervalUnit:it,requestParams:{Body:{"form-data":{},"x-www-form-urlencoded":{},json:"",xml:""},Header:{},Params:{}}},componentList:[]}),getters:{getMousePosition(){return this.mousePosition},getRightMenuShow(){return this.rightMenuShow},getEditCanvas(){return this.editCanvas},getEditCanvasConfig(){return this.editCanvasConfig},getTargetChart(){return this.targetChart},getRecordChart(){return this.recordChart},getRequestGlobalConfig(){return this.requestGlobalConfig},getComponentList(){return this.componentList}},actions:{getStorageInfo(){return{[m.EDIT_CANVAS_CONFIG]:this.getEditCanvasConfig,[m.COMPONENT_LIST]:this.getComponentList,[m.REQUEST_GLOBAL_CONFIG]:this.getRequestGlobalConfig}},setEditCanvas(t,e){this.editCanvas[t]=e},setEditCanvasConfig(t,e){this.editCanvasConfig[t]=e},setRightMenuShow(t){this.rightMenuShow=t},setTargetHoverChart(t){this.targetChart.hoverId=t},setTargetSelectChart(t,e=!1){if(!this.targetChart.selectId.find(s=>s===t)){if(!t){this.targetChart.selectId=[];return}if(e){if(x(t)){this.targetChart.selectId.push(t);return}if(_(t)){this.targetChart.selectId.push(...t);return}}else{if(x(t)){this.targetChart.selectId=[t];return}if(_(t)){this.targetChart.selectId=t;return}}}},setRecordChart(t){this.recordChart=R(t)},setMousePosition(t,e,s,r){t&&(this.mousePosition.x=t),e&&(this.mousePosition.y=e),s&&(this.mousePosition.startX=s),r&&(this.mousePosition.startY=r)},fetchTargetIndex(t){const e=t||this.getTargetChart.selectId.length&&this.getTargetChart.selectId[0]||void 0;if(!e)return i(),-1;const s=this.componentList.findIndex(r=>r.id===e);if(s!==-1)return s;{const r=this.getComponentList.length;for(let a=0;a<r;a++)if(this.getComponentList[a].isGroup){for(const h of this.getComponentList[a].groupList)if(h.id===e)return a}}return-1},idPreFormat(t){const e=[];return t?(x(t)&&e.push(t),_(t)&&e.push(...t),e):(e.push(...this.getTargetChart.selectId),e)},addComponentList(t,e=!1,s=!1){if(t instanceof Array){t.forEach(r=>{this.addComponentList(r,e,s)});return}if(s&&g.createAddHistory([t]),e){this.componentList.unshift(t);return}this.componentList.push(t)},removeComponentList(t,e=!0){try{const s=this.idPreFormat(t),r=[];if(!s.length)return;C(),s.forEach(a=>{const h=this.fetchTargetIndex(a);h!==-1&&(r.push(this.getComponentList[h]),this.componentList.splice(h,1))}),e&&g.createDeleteHistory(r),i();return}catch(s){p()}},resetComponentPosition(t,e){const s=this.fetchTargetIndex(t.id);if(s>-1){const r=this.getComponentList[s];e?r.attr=Object.assign(r.attr,{x:t.attr.x+t.attr.offsetX,y:t.attr.y+t.attr.offsetY}):r.attr=Object.assign(r.attr,{x:t.attr.x,y:t.attr.y})}},moveComponentList(t){g.createMoveHistory(t)},updateComponentList(t,e){t<1&&t>this.getComponentList.length||(this.componentList[t]=e)},setPageStyle(t,e){const s=this.getEditCanvas.editContentDom;s&&(s.style[t]=e)},setBothEnds(t=!1,e=!0){try{if(this.getTargetChart.selectId.length>1)return;C();const s=this.getComponentList.length;if(s<2){i();return}const r=this.fetchTargetIndex(),a=this.getComponentList[r];if(r!==-1){if(t&&r===0||!t&&r===s-1){i();return}const h=(l,u)=>{const n=R(l);return n.attr.zIndex=u,n};e&&g.createLayerHistory([h(a,r)],t?o.BOTTOM:o.TOP),this.addComponentList(a,t),this.getComponentList.splice(t?r+1:r,1),i();return}}catch(s){p()}},setTop(t=!0){this.setBothEnds(!1,t)},setBottom(t=!0){this.setBothEnds(!0,t)},wrap(t=!1,e=!0){try{if(this.getTargetChart.selectId.length>1)return;C();const s=this.getComponentList.length;if(s<2){i();return}const r=this.fetchTargetIndex();if(r!==-1){if(t&&r===0||!t&&r===s-1){i();return}const a=t?r-1:r+1,h=this.getComponentList[r],l=this.getComponentList[a];e&&g.createLayerHistory([h],t?o.DOWN:o.UP),this.updateComponentList(r,l),this.updateComponentList(a,h),i();return}}catch(s){p()}},setUp(t=!0){this.wrap(!1,t)},setDown(t=!0){this.wrap(!0,t)},setCopy(t=!1){try{if(this.getTargetChart.selectId.length>1||document.getElementsByClassName("n-modal-body-wrapper").length)return;C();const e=this.fetchTargetIndex();if(e!==-1){const s={charts:this.getComponentList[e],type:t?o.CUT:o.COPY};this.setRecordChart(s),window.$message.success(t?"剪切图表成功":"复制图表成功！"),i()}}catch(e){p()}},setCut(){this.setCopy(!0)},setParse(){try{C();const t=this.getRecordChart;if(t===void 0){i();return}const e=a=>(a=R(a),a.attr.x=this.getMousePosition.startX,a.attr.y=this.getMousePosition.startY,a.id=E(),a.isGroup&&a.groupList.forEach(h=>{h.id=E()}),a),s=t.type===o.CUT;(Array.isArray(t.charts)?t.charts:[t.charts]).forEach(a=>{this.addComponentList(e(a),void 0,!0),s&&(this.setTargetSelectChart(a.id),this.removeComponentList(void 0,!0))}),s&&this.setRecordChart(void 0),i()}catch(t){p()}},setBackAndSetForwardHandle(t,e=!1){if(t.targetType===f.CANVAS){this.editCanvas=t.historyData[0];return}this.setTargetSelectChart();let s=t.historyData;_(s)&&s.forEach(c=>{this.setTargetSelectChart(c.id,!0)});const r=t.actionType===o.ADD,a=t.actionType===o.DELETE;if(r||a){if(r&&e||a&&!e){s.forEach(c=>{this.addComponentList(c)});return}s.forEach(c=>{this.removeComponentList(c.id,!1)});return}if(t.actionType===o.MOVE){s.forEach(c=>{this.resetComponentPosition(c,e)});return}const l=t.actionType===o.TOP,u=t.actionType===o.BOTTOM;if(l||u){if(!e){l&&this.getComponentList.pop(),u&&this.getComponentList.shift(),this.getComponentList.splice(s[0].attr.zIndex,0,s[0]);return}l&&this.setTop(!1),u&&this.setBottom(!1)}const n=t.actionType===o.UP,S=t.actionType===o.DOWN;if(n||S){if(n&&e||S&&!e){this.setUp(!1);return}this.setDown(!1);return}const L=t.actionType===o.GROUP,T=t.actionType===o.UN_GROUP;if(L||T){if(L&&e||T&&!e){const c=[];s.length>1?s.forEach(A=>{c.push(A.id)}):s[0].groupList.forEach(q=>{c.unshift(q.id)}),this.setGroup(c,!1);return}s.length>1?this.setUnGroup([s[0].id],void 0,!1):this.setUnGroup([s[0].groupList[0].id],void 0,!1);return}const v=t.actionType===o.LOCK,O=t.actionType===o.UNLOCK;if(v||O){if(v&&e||O&&!e){s.forEach(c=>{this.setLock(!c.status.lock,!1)});return}s.forEach(c=>{this.setUnLock(!1)});return}const I=t.actionType===o.HIDE,k=t.actionType===o.SHOW;if(I||k){if(I&&e||k&&!e){s.forEach(c=>{this.setHide(!c.status.hide,!1)});return}s.forEach(c=>{this.setShow(!1)});return}},setBack(){try{C();const t=g.backAction();if(!t){i();return}this.setBackAndSetForwardHandle(t),i()}catch(t){p()}},setForward(){try{C();const t=g.forwardAction();if(!t){i();return}this.setBackAndSetForwardHandle(t,!0),i()}catch(t){p()}},setMove(t){const e=this.fetchTargetIndex();if(e===-1)return;const s=this.getComponentList[e].attr,r=Tt.getChartMoveDistance;switch(t){case b.ARROW_UP:s.y-=r;break;case b.ARROW_RIGHT:s.x+=r;break;case b.ARROW_DOWN:s.y+=r;break;case b.ARROW_LEFT:s.x-=r;break}},setGroup(t,e=!0){try{const s=this.idPreFormat(t)||this.getTargetChart.selectId;if(s.length<2)return;C();const r=new gt,a={l:this.getEditCanvasConfig.width,t:this.getEditCanvasConfig.height,r:0,b:0},h=[],l=[],u=[];s.forEach(n=>{const S=this.fetchTargetIndex(n);S!==-1&&this.getComponentList[S].isGroup?this.setUnGroup([n],L=>{L.forEach(T=>{this.addComponentList(T),u.push(T.id)})},!1):S!==-1&&u.push(n)}),u.forEach(n=>{const S=this.componentList.splice(this.fetchTargetIndex(n),1)[0],{x:L,y:T,w:v,h:O}=S.attr,{l:I,t:k,r:c,b:A}=a;a.l=I>L?L:I,a.t=k>T?T:k,a.r=c<L+v?L+v:c,a.b=A<T+O?T+O:A,h.unshift(S),l.push(nt(S))}),e&&g.createGroupHistory(l),h.forEach(n=>{n.attr.x=n.attr.x-a.l,n.attr.y=n.attr.y-a.t,r.groupList.push(n)}),r.attr.x=a.l,r.attr.y=a.t,r.attr.w=a.r-a.l,r.attr.h=a.b-a.t,this.addComponentList(r),this.setTargetSelectChart(r.id),i()}catch(s){console.log(s),window.$message.error("创建分组失败，请联系管理员！"),i()}},setUnGroup(t,e,s=!0){try{const r=t||this.getTargetChart.selectId;if(r.length!==1)return;C();const a=l=>{const u=this.getComponentList[l];u.isGroup&&(s&&g.createUnGroupHistory(R([u])),u.groupList.reverse().forEach(n=>{n.attr.x=n.attr.x+u.attr.x,n.attr.y=n.attr.y+u.attr.y,e||this.addComponentList(n)}),this.setTargetSelectChart(u.id),this.removeComponentList(u.id,!1),e&&e(u.groupList))},h=this.fetchTargetIndex(r[0]);h!==-1&&a(h),i()}catch(r){console.log(r),window.$message.error("解除分组失败，请联系管理员！"),i()}},setLock(t=!0,e=!0){try{if(this.getTargetChart.selectId.length>1)return;C();const s=this.fetchTargetIndex();if(s!==-1){const r=this.getComponentList[s];r.status.lock=t,e&&(t?g.createLockHistory([r]):g.createUnLockHistory([r])),this.updateComponentList(s,r),t&&this.setTargetSelectChart(void 0),i();return}}catch(s){p()}},setUnLock(t=!0){this.setLock(!1,t)},setHide(t=!0,e=!0){try{if(this.getTargetChart.selectId.length>1)return;C();const s=this.fetchTargetIndex();if(s!==-1){const r=this.getComponentList[s];r.status.hide=t,e&&(t?g.createHideHistory([r]):g.createShowHistory([r])),this.updateComponentList(s,r),i(),t&&this.setTargetSelectChart(void 0)}}catch(s){p()}},setShow(t=!0){this.setHide(!1,t)},setPageSize(t){this.setPageStyle("height",`${this.editCanvasConfig.height*t}px`),this.setPageStyle("width",`${this.editCanvasConfig.width*t}px`)},computedScale(){if(this.getEditCanvas.editLayoutDom){const t=this.getEditCanvas.editLayoutDom.clientWidth-this.getEditCanvas.offset*2-5,e=this.getEditCanvas.editLayoutDom.clientHeight-this.getEditCanvas.offset*4,s=this.editCanvasConfig.width,r=this.editCanvasConfig.height,a=parseFloat((s/r).toFixed(5));if(parseFloat((t/e).toFixed(5))>a){const l=parseFloat((e*a/s).toFixed(5));this.setScale(l>1?1:l)}else{const l=parseFloat((t/a/r).toFixed(5));this.setScale(l>1?1:l)}}else window.$message.warning("请先创建画布，再进行缩放")},listenerScale(){const t=ht(this.computedScale,200);return t(),window.addEventListener("resize",t),()=>{window.removeEventListener("resize",t)}},setScale(t,e=!1){(!this.getEditCanvas.lockScale||e)&&(this.setPageSize(t),this.getEditCanvas.userScale=t,this.getEditCanvas.scale=t)}}});export{It as B,m as C,St as E,ut as F,Ct as H,ct as P,pt as a,lt as b,B as c,ft as d,gt as e,N as f,o as g,f as h,dt as r,kt as u};
