const l=!!(typeof window!="undefined"&&window.document&&window.document.createElement);var o=!1,c=!1;try{var a={get passive(){return o=!0},get once(){return c=o=!0}};l&&(window.addEventListener("test",a,a),window.removeEventListener("test",a,!0))}catch(r){}function E(r,e,t,n){if(n&&typeof n!="boolean"&&!c){var d=n.once,u=n.capture,i=t;!c&&d&&(i=t.__once||function m(w){this.removeEventListener(e,m,u),t.call(this,w)},t.__once=i),r.addEventListener(e,i,o?n:u)}r.addEventListener(e,t,n)}var s=new Date().getTime();function p(r){var e=new Date().getTime(),t=Math.max(0,16-(e-s)),n=setTimeout(r,t);return s=e,n}var y=["","webkit","moz","o","ms"],f=p,v=function(e,t){return e+(e?t[0].toUpperCase()+t.substr(1):t)+"AnimationFrame"};l&&y.some(function(r){var e=v(r,"request");return e in window&&(v(r,"cancel"),f=function(n){return window[e](n)}),!!f});Function.prototype.bind.call(Function.prototype.call,[].slice);Function.prototype.bind.call(Function.prototype.call,[].slice);export{E as a};
