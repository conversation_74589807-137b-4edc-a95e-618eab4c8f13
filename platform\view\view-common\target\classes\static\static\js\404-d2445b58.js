import{d as _,r as c,c as n,e as r,w as p,z as d,P as i,A as f,B as l,q as t,o as m,f as u,j as v}from"./index-bb2cbf17.js";const x="/static/svg/404-2ee16551.svg",s=e=>(f("data-v-007fa46f"),e=e(),l(),e),g={class:"go-error"},h=s(()=>t("div",{class:"text-center"},[t("img",{src:x,alt:""})],-1)),y=s(()=>t("div",{class:"text-center"},[t("h1",{class:"text-base text-gray-500"},"抱歉，你访问的页面不存在")],-1)),B=_({__name:"404",setup(e){function o(){d(i.BASE_HOME_NAME)}return(E,N)=>{const a=c("n-button");return m(),n("div",g,[h,y,r(a,{type:"primary",onClick:o},{default:p(()=>[u("回到首页")]),_:1})])}}});const I=v(B,[["__scopeId","data-v-007fa46f"]]);export{I as default};
