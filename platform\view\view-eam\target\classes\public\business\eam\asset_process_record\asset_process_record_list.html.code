<!--
/**
 * 资产处理记录 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2024-05-01 09:45:41
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('资产处理记录')}">资产处理记录</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 资产名称 , assetId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 业务编号 , businessCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 操作类型 , processType ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('操作类型')}" class="search-label processType-label">操作类型</span><span class="search-colon">:</span></div>
                        <div id="processType" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetOperateEnum')}" style="width:140px" extraParam="{}"></div>
                    </div>
                    <!-- 使用人 , useUserId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 操作人员 , processUserId ,typeName=button, isHideInSearch=true -->
                    <!-- 操作时间 , processdTime ,typeName=date_input, isHideInSearch=true -->
                    <!-- 修改人ID , updateBy ,typeName=text_input, isHideInSearch=true -->
                    <!-- 操作内容 , content ,typeName=text_area, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('操作内容')}" class="search-label content-label">操作内容</span><span class="search-colon">:</span></div>
                        <input id="content" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>
                    <!-- 备注 , notes ,typeName=text_area, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('备注')}" class="search-label notes-label">备注</span><span class="search-colon">:</span></div>
                        <input id="notes" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索','','cmp:table.search')}">搜索</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 42px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${perm.checkAuth('eam_asset_process_record:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view"  data-id="{{d.id}}"> <span th:text="${lang.translate('查看','','cmp:table.ops')}">查看</span></button>




</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var SELECT_PROCESSTYPE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetOperateEnum')}]];
    var AUTH_PREFIX="eam_asset_process_record";

    // source
    var SOURCE = [[${source}]] ;
    // 资产ID
    var ASSET_ID = [[${assetId}]] ;

</script>

<script th:src="'/business/eam/asset_process_record/asset_process_record_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset_process_record/asset_process_record_list.js?'+${cacheKey}"></script>

</body>
</html>