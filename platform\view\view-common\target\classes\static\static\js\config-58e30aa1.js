var a=Object.defineProperty;var s=(t,o,i)=>o in t?a(t,o,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[o]=i;var r=(t,o,i)=>(s(t,typeof o!="symbol"?o+"":o,i),i);import{a8 as m}from"./index-bb2cbf17.js";import{d as e}from"./chartEditStore-55fbe93c.js";import{G as p}from"./index-0ec04aee.js";import{l as n}from"./logo-aa8b8747.js";import"./plugin-3ef0fcec.js";import"./icon-f36697ff.js";import"./SettingItem-7fe1cfec.js";/* empty css                                                                      */import"./SettingItemBox-500aaf18.js";import"./CollapseItem.vue_vue_type_script_setup_true_lang-9b6d8958.js";import"./index.esm.min-f1367f57.js";import"./http-36f53bd1.js";import"./lodash-d17632fd.js";import"./fileTypeEnum-21359a08.js";const f={dataset:n,fit:"contain",borderRadius:10};class R extends e{constructor(){super(...arguments);r(this,"key",p.key);r(this,"chartConfig",m(p));r(this,"option",m(f))}}export{R as default,f as option};
