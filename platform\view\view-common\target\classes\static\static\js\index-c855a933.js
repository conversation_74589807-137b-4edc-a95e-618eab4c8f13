import{d as G,Z as M,p as y,X as a,a0 as g,r,o as s,E as c,w as t,Q,e as n,u as X,R as Y,c as C,F as f,s as S,G as h,q as v,t as b,a$ as Z,j as J}from"./index-bb2cbf17.js";import{g as U}from"./plugin-3ef0fcec.js";import{i as ee}from"./icon-f36697ff.js";import{u as te}from"./useKeyboard.hook-d93304b3.js";import{u as oe,a as se,H as l}from"./chartEditStore-55fbe93c.js";import{u as A,C as i}from"./chartLayoutStore-88198b25.js";const ne=G({__name:"index",setup(ae){const{LayersIcon:H,BarChartIcon:w,PrismIcon:B,HomeIcon:I,ArrowBackIcon:L,ArrowForwardIcon:D}=ee.ionicons5,{setItem:E}=A(),{getLayers:R,getCharts:x,getDetails:F}=M(A()),d=oe(),u=se(),K=y([{key:i.CHARTS,select:x,title:"图表组件",icon:a(w)},{key:i.LAYERS,select:R,title:"图层控制",icon:a(H)},{key:i.DETAILS,select:F,title:"详情设置",icon:a(B)}]),T=g(()=>u.getBackStack.length>1),z=g(()=>u.getForwardStack.length>0),$=y([{key:l.BACK_STACK,select:T,title:"后退",icon:a(L)},{key:l.FORWARD_STACK,select:z,title:"前进",icon:a(D)}]),q=o=>o.key===i.DETAILS?o.select?"":"primary":o.select?"primary":"",N=o=>{E(o.key,!o.select)},O=o=>{switch(o.key){case l.BACK_STACK:d.setBack();break;case l.FORWARD_STACK:d.setForward();break}},P=()=>{U({message:"返回将不会保存任何操作",isMaskClosable:!0,onPositiveCallback:()=>{Z(),te()}})};return(o,p)=>{const V=r("n-icon"),_=r("n-button"),k=r("n-tooltip"),W=r("n-divider"),m=r("n-space");return s(),c(m,{class:"header-left-btn",size:25},{default:t(()=>[Q(n(_,{size:"small",quaternary:"",onClick:p[0]||(p[0]=e=>P())},{icon:t(()=>[n(V,{depth:3},{default:t(()=>[n(X(I))]),_:1})]),_:1},512),[[Y,1>2]]),n(m,null,{default:t(()=>[(s(!0),C(f,null,S(K,e=>(s(),c(k,{key:e.key,placement:"bottom",trigger:"hover"},{trigger:t(()=>[n(_,{size:"small",ghost:"",type:q(e),focusable:!1,onClick:j=>N(e)},{default:t(()=>[(s(),c(h(e.icon)))]),_:2},1032,["type","onClick"])]),default:t(()=>[v("span",null,b(e.title),1)]),_:2},1024))),128)),n(W,{vertical:""}),(s(!0),C(f,null,S($,e=>(s(),c(k,{key:e.key,placement:"bottom",trigger:"hover"},{trigger:t(()=>[n(_,{size:"small",ghost:"",type:"primary",disabled:!e.select,onClick:j=>O(e)},{default:t(()=>[(s(),c(h(e.icon)))]),_:2},1032,["disabled","onClick"])]),default:t(()=>[v("span",null,b(e.title),1)]),_:2},1024))),128))]),_:1})]),_:1})}}});const ue=J(ne,[["__scopeId","data-v-b450cd81"]]);export{ue as default};
