import{u}from"./chartLayoutStore-88198b25.js";import{d as i,m as _,l as s,a0 as d,L as f,r as a,o as h,E as g,w as v,q as r,e as x}from"./index-bb2cbf17.js";import"./chartEditStore-55fbe93c.js";import"./plugin-3ef0fcec.js";import"./icon-f36697ff.js";const C=r("span",null," 拼命加载中... ",-1),T=i({__name:"index",setup(w){const n=u(),c=_(),o=s(!1),t=s(0),l=d(()=>c.getAppTheme);return f(()=>n.getPercentage,e=>{if(e===0){setTimeout(()=>{t.value=e,o.value=!1},500);return}t.value=e,o.value=e>0}),(e,y)=>{const p=a("n-progress"),m=a("n-modal");return h(),g(m,{show:o.value,"close-on-esc":!1,"transform-origin":"center"},{default:v(()=>[r("div",null,[C,x(p,{type:"line",color:l.value,percentage:t.value,style:{width:"300px"}},null,8,["color","percentage"])])]),_:1},8,["show"])}}});export{T as default};
