var f=Object.defineProperty,c=Object.defineProperties;var d=Object.getOwnPropertyDescriptors;var p=Object.getOwnPropertySymbols;var g=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable;var m=(i,o,t)=>o in i?f(i,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[o]=t,a=(i,o)=>{for(var t in o||(o={}))g.call(o,t)&&m(i,t,o[t]);if(p)for(var t of p(o))l.call(o,t)&&m(i,t,o[t]);return i},n=(i,o)=>c(i,d(o));var r=(i,o,t)=>(m(i,typeof o!="symbol"?o+"":o,t),t);import{aM as C,a8 as s}from"./index-bb2cbf17.js";import{d as u}from"./chartEditStore-55fbe93c.js";import{E as e}from"./index-0ec04aee.js";import"./plugin-3ef0fcec.js";import"./icon-f36697ff.js";import"./SettingItem-7fe1cfec.js";/* empty css                                                                      */import"./SettingItemBox-500aaf18.js";import"./CollapseItem.vue_vue_type_script_setup_true_lang-9b6d8958.js";import"./index.esm.min-f1367f57.js";import"./http-36f53bd1.js";import"./lodash-d17632fd.js";import"./fileTypeEnum-21359a08.js";const h={dataset:"https://www.mtruning.club/",borderRadius:10};class v extends u{constructor(){super(...arguments);r(this,"key",e.key);r(this,"attr",n(a({},C),{w:1200,h:800,zIndex:-1}));r(this,"chartConfig",s(e));r(this,"option",s(h))}}export{v as default,h as option};
