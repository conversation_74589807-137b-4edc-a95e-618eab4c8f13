var f=Object.defineProperty;var o=Object.getOwnPropertySymbols;var p=Object.prototype.hasOwnProperty,u=Object.prototype.propertyIsEnumerable;var s=(t,e,a)=>e in t?f(t,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[e]=a,n=(t,e)=>{for(var a in e||(e={}))p.call(e,a)&&s(t,a,e[a]);if(o)for(var a of o(e))u.call(e,a)&&s(t,a,e[a]);return t};import{d as _,r,o as g,c as S,e as l,w as i,f as x,t as y,b as c,q as h,an as v,j as B}from"./index-bb2cbf17.js";/* empty css                                                                      */const q={class:"go-config-item-box"},C=_({__name:"SettingItemBox",props:{name:{type:String,required:!1},alone:{type:Boolean,default:!1,required:!1},itemRightStyle:{type:Object,default:()=>{},required:!1}},setup(t){return(e,a)=>{const d=r("n-space"),m=r("n-text");return g(),S("div",q,[l(m,{class:"item-left",depth:"2"},{default:i(()=>[x(y(t.name)+" ",1),l(d,{size:5},{default:i(()=>[c(e.$slots,"name",{},void 0,!0)]),_:3})]),_:3}),h("div",{class:"item-right",style:v(n({gridTemplateColumns:t.alone?"1fr":"1fr 1fr"},t.itemRightStyle))},[c(e.$slots,"default",{},void 0,!0)],4)])}}}),V=B(C,[["__scopeId","data-v-9746c50f"]]);export{V as S};
