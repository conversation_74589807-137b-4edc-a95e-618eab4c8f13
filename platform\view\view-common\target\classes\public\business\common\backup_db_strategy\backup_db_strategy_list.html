<!--
/**
 * 备份策略 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2023-01-26 10:45:13
 */
 -->
<!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <title th:text="${lang.translate('备份策略')}">备份策略</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="display:none">

        <input name="id" id="id"  type="hidden"/>

        <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-4467-content">

            <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column" >

                <!-- radio_box : 启用状态 ,  status  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"  style="width:70px" ><div th:text="${lang.translate('启用状态')}">启用状态</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input input-type="radio" type="radio" name="status" lay-filter="status" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==-1)}">
                    </div>
                </div>

                <!-- text_input : 备份路径 ,  dataPath -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1" style="width:70px" ><div th:text="${lang.translate('备份路径')}">备份路径</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="dataPath" id="dataPath" name="dataPath" th:placeholder="${ lang.translate('请输入'+'备份路径') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>

                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"  style="width:70px"><div th:text="${lang.translate('备份保留')}">备份保留</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="dataKeep" id="dataKeep" name="dataKeep" th:placeholder="${ lang.translate('请输入'+'备份保留') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="true" decimal="false" allow-negative="true" step="1.0"   scale="0" />
                    </div>
                </div>

                <!-- text_input : 备注 ,  notes -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"  style="width:70px"><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入'+'备注') }" type="text" class="layui-input"  />
                    </div>
                </div>
                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 250px"></div>


    </form>

</div>
<div class="model-form-footer">
     <button th:if="${perm.checkAnyAuth('sys_backup_db_strategy:create','sys_backup_db_strategy:update','sys_backup_db_strategy:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var RADIO_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var VALIDATE_CONFIG={"status":{"labelInForm":"启用状态","inputType":"radio_box","required":true},"dataPath":{"labelInForm":"备份路径","inputType":"text_input","required":true}};
    var AUTH_PREFIX="sys_backup_db_strategy";


</script>



<script th:src="'/business/common/backup_db_strategy/backup_db_strategy_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/common/backup_db_strategy/backup_db_strategy_list.js?'+${cacheKey}"></script>

</body>
</html>
