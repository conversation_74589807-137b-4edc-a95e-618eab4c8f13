import{d as m,r as n,o as i,c as p,e as t,w as o,q as u,b as l,F as f}from"./index-bb2cbf17.js";const g=m({__name:"CollapseItem",props:{name:{type:String,required:!0},expanded:{type:Boolean,required:!1,default:!1}},setup(a){const s=e=>{e.preventDefault(),e.stopPropagation()};return(e,_)=>{const r=n("n-divider"),d=n("n-collapse-item"),c=n("n-collapse");return i(),p(f,null,[t(r,{style:{margin:"10px 0"}}),t(c,{"arrow-placement":"right","default-expanded-names":a.expanded?a.name:null,accordion:""},{"header-extra":o(()=>[u("div",{onClick:s},[l(e.$slots,"header")])]),default:o(()=>[t(d,{title:a.name,name:a.name},{default:o(()=>[l(e.$slots,"default")]),_:3},8,["title","name"])]),_:3},8,["default-expanded-names"])],64)}}});export{g as _};
