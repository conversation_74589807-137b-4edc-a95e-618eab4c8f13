<!--
/**
 * 标签布局 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-12-02 19:38:49
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
	<title th:text="${lang.translate('标签布局')}">标签布局</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="display:none">


         <!--开始：group 循环-->


        <div class="layui-row form-row" id="default-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column" >

                <!-- text_input : 主键 ,  id -->
                <div class="layui-form-item" style="display: none">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('主键')}">主键</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="id" id="id" name="id" th:placeholder="${ lang.translate('请输入'+'主键') }" type="text" class="layui-input"  />
                    </div>
                </div>

                <!-- text_input : 模版 ,  tplId -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('模版')}">模版</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="tplId" id="tplId" name="tplId" th:placeholder="${ lang.translate('请输入'+'模版') }" type="text" class="layui-input"  />
                    </div>
                </div>

                <!-- text_input : 字段ID ,  colId -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('字段ID')}">字段ID</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="colId" id="colId" name="colId" th:placeholder="${ lang.translate('请输入'+'字段ID') }" type="text" class="layui-input"  />
                    </div>
                </div>

                <!-- text_input : 字段编码 ,  colCode -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('字段编码')}">字段编码</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="colCode" id="colCode" name="colCode" th:placeholder="${ lang.translate('请输入'+'字段编码') }" type="text" class="layui-input"  />
                    </div>
                </div>

                <!-- select_box : 类型 ,  type  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('类型')}">类型</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <div id="type" input-type="select" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AsseLabelTableCellTypeEnum')}" extraParam="{}"></div>
                    </div>
                </div>

                <!-- number_input : 行数 ,  rowNumber  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('行数')}">行数</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="rowNumber" id="rowNumber" name="rowNumber" th:placeholder="${ lang.translate('请输入'+'行数') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="true" decimal="false" allow-negative="true" step="1.0"   scale="0" />
                    </div>
                </div>

                <!-- number_input : 行高度 ,  rowHeight  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('行高度')}">行高度</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="rowHeight" id="rowHeight" name="rowHeight" th:placeholder="${ lang.translate('请输入'+'行高度') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />
                    </div>
                </div>

                <!-- number_input : row ,  rowSpan  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('row')}">row</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="rowSpan" id="rowSpan" name="rowSpan" th:placeholder="${ lang.translate('请输入'+'row') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="true" decimal="false" allow-negative="true" step="1.0"   scale="0" />
                    </div>
                </div>

                <!-- number_input : col ,  colSpan  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('col')}">col</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="colSpan" id="colSpan" name="colSpan" th:placeholder="${ lang.translate('请输入'+'col') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="true" decimal="false" allow-negative="true" step="1.0"   scale="0" />
                    </div>
                </div>

                <!-- number_input : 顺序 ,  sort  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('顺序')}">顺序</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="sort" id="sort" name="sort" th:placeholder="${ lang.translate('请输入'+'顺序') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="true" decimal="false" allow-negative="true" step="1.0"   scale="0" />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_asset_label_layout:create','eam_asset_label_layout:update','eam_asset_label_layout:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var SELECT_TYPE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AsseLabelTableCellTypeEnum')}]];
    var VALIDATE_CONFIG={"type":{"labelInForm":"类型","inputType":"select_box","required":true}};
    var AUTH_PREFIX="eam_asset_label_layout";


</script>



<script th:src="'/business/eam/asset_label_layout/asset_label_layout_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset_label_layout/asset_label_layout_form.js?'+${cacheKey}"></script>

</body>
</html>
