<!--
/**
 * 资产处理记录 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2024-05-01 09:45:42
 */
 -->
<!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('资产处理记录')}">资产处理记录</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="opacity:0">

        <input name="id" id="id"  type="hidden"/>

        <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-8323-content">

            <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- text_input : 业务编号 ,  businessCode -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('业务编号')}">业务编号</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="businessCode" id="businessCode" name="businessCode" th:placeholder="${ lang.translate('请输入'+'业务编号') }" type="text" class="layui-input"  />
                    </div>
                </div>

                <!-- button : 操作人员 ,  processUserId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('操作人员')}">操作人员</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="processUserId" id="processUserId" name="processUserId"  type="hidden" class="layui-input"   />
                        <button id="processUserId-button" type="button" action-type="emp-dialog" class="layui-btn   " style="width: 100%" default-width="100%" auto-width="false"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择人员')}" th:default-label="${lang.translate('请选择人员')}">按钮文本</span></button>
                    </div>
                </div>
                <!--结束：栏次内字段循环-->
            </div>
            <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- select_box : 操作类型 ,  processType  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('操作类型')}">操作类型</div></div>
                    <div class="layui-input-block ">
                        <div id="processType" input-type="select" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetOperateEnum')}" extraParam="{}"></div>
                    </div>
                </div>

                <!-- date_input : 操作时间 ,  processdTime  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('操作时间')}">操作时间</div></div>
                    <div class="layui-input-block ">
                        <input input-type="date" lay-filter="processdTime" id="processdTime" name="processdTime"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择'+'操作时间') }" type="text" class="layui-input"    lay-verify=""   />
                    </div>
                </div>
                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-2378-content">

            <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >

                <!-- text_area : 操作内容 ,  content  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('操作内容')}">操作内容</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="content" id="content" name="content" th:placeholder="${ lang.translate('请输入'+'操作内容') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>

                <!-- text_area : 备注 ,  notes  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入'+'备注') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>
                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 80px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}"  style="margin-right: 15px" >取消</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var SELECT_PROCESSTYPE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetOperateEnum')}]];
    var VALIDATE_CONFIG={"processdTime":{"date":true,"labelInForm":"操作时间","inputType":"date_input"}};
    var AUTH_PREFIX="eam_asset_process_record";


</script>



<script th:src="'/business/eam/asset_process_record/asset_process_record_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset_process_record/asset_process_record_form.js?'+${cacheKey}"></script>

</body>
</html>