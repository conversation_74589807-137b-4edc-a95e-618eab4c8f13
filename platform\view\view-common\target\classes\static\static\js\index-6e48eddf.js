var ke=Object.defineProperty,xe=Object.defineProperties;var ye=Object.getOwnPropertyDescriptors;var ie=Object.getOwnPropertySymbols;var we=Object.prototype.hasOwnProperty,Ie=Object.prototype.propertyIsEnumerable;var de=(r,_,v)=>_ in r?ke(r,_,{enumerable:!0,configurable:!0,writable:!0,value:v}):r[_]=v,ee=(r,_)=>{for(var v in _||(_={}))we.call(_,v)&&de(r,v,_[v]);if(ie)for(var v of ie(_))Ie.call(_,v)&&de(r,v,_[v]);return r},oe=(r,_)=>xe(r,ye(_));import{d as _e,b8 as Ee,l as te,p as le,L as pe,a8 as D,a0 as ne,cW as $e,cX as He,cY as Te,r as n,o as p,c as b,e,w as o,f as y,q as i,F as B,s as M,ao as se,Q as Se,u as w,R as Oe,an as W,a5 as ae,aa as Re,a9 as z,cZ as De,c_ as ze,A as ve,B as me,j as fe,M as ue,E as q,t as Ae}from"./index-bb2cbf17.js";import{n as Le}from"./noData-9e194391.js";import{g as U}from"./plugin-3ef0fcec.js";import{i as Ce}from"./icon-f36697ff.js";import{u as Ue,E as Q}from"./chartEditStore-55fbe93c.js";import{l as Be}from"./index-56351f34.js";const ce=r=>(ve("data-v-04bb59e0"),r=r(),me(),r),Me={key:0,class:"create-color-setting"},Pe={class:"color-list-box go-mt-3","x-gap":12,"y-gap":12,cols:4},je={class:"go-flex-items-center"},Ne=ce(()=>i("span",{class:"go-mr-4"},"添加",-1)),Fe={class:"expend-color-box"},Ve=["onClick"],qe=ce(()=>i("div",{class:"n-color-picker-checkboard"},null,-1)),Qe=["onClick"],We=ce(()=>i("div",{class:"n-color-picker-checkboard"},null,-1)),Xe=_e({__name:"index",props:{selectColor:Object},emits:["updateColor"],setup(r,{emit:_}){Ee(l=>({"3d26029e":d.color}));const v=r,T=_,{AddIcon:X,TrashIcon:Y}=Ce.ionicons5,Z=Be(()=>Re(()=>import("./index-40b0a69e.js"),["static/js/index-40b0a69e.js","static/js/index.esm.min-f1367f57.js","static/js/index-bb2cbf17.js","static/css/index-a8adda2b.css","static/js/chartEditStore-55fbe93c.js","static/js/plugin-3ef0fcec.js","static/js/icon-f36697ff.js"])),s=te(),d=le({index:-1,color:""});pe(()=>{var l;return(l=v.selectColor)==null?void 0:l.id},()=>{var l;s.value=D(v.selectColor),d.index=0,d.color=(l=s.value)==null?void 0:l.color[0]},{immediate:!0,deep:!1});const S=ne(()=>C(d.color)),C=l=>{if(!l)return{default:[],fade:[]};const a=36,m=[],I=[],t=[];for(let c=0;c<a;c++)I.unshift($e(l,1/100*(c+1))),m.push(He(l,3.5/100*(c+1)));return m.forEach((c,g)=>{t.unshift(Te(c,1/100*(g+1)))}),{default:[...I.reverse().splice(0,parseInt(`${a/2}`)-9),...m.splice(0,parseInt(`${a/2}`))],fade:t.reverse().splice(0,27)}},u=l=>({backgroundColor:l}),h=(l,a)=>{d.color=l,d.index=a},k=(l,a)=>{l&&(d.color=l),a&&(d.index=a),z(()=>{T("updateColor",s.value)})},P=(l,a)=>{const m=a?De(l):ze(l);s.value&&(s.value.color[d.index]=m),z(()=>{T("updateColor",s.value)})},A=()=>{var a,m,I;const l=((m=s.value)==null?void 0:m.color[((a=s.value)==null?void 0:a.color.length)-1])||"#2c2c31";(I=s.value)==null||I.color.push(l),z(()=>{T("updateColor",s.value)})},j=l=>{var a,m;(a=s.value)==null||a.color.splice(l,1),l===d.index&&k((m=s.value)==null?void 0:m.color[l-1],l-1)},G=()=>{z(()=>{T("updateColor",s.value)})};return(l,a)=>{const m=n("n-input-group-label"),I=n("n-input"),t=n("n-input-group"),c=n("n-tag"),g=n("n-space"),E=n("n-color-picker"),N=n("n-icon"),L=n("n-tooltip"),O=n("n-button"),J=n("n-scrollbar"),R=n("n-card"),F=n("n-text"),V=n("n-divider");return s.value?(p(),b("div",Me,[e(R,{bordered:!1,role:"dialog",size:"small","aria-modal":"true"},{default:o(()=>[e(g,{justify:"space-between"},{default:o(()=>[e(t,null,{default:o(()=>[e(m,null,{default:o(()=>[y("名称:")]),_:1}),e(I,{class:"create-color-name",value:s.value.name,"onUpdate:value":a[0]||(a[0]=x=>s.value.name=x),valueModifiers:{trim:!0},maxlength:"8","show-count":"",clearable:"",onChange:G},null,8,["value"])]),_:1}),e(c,{type:"warning"},{default:o(()=>[y("底部图表仅展示 7 条数据")]),_:1})]),_:1}),e(J,{style:{"max-height":"132px"}},{default:o(()=>[i("div",Pe,[(p(!0),b(B,null,M(s.value.color,(x,f)=>(p(),b("div",{class:"color-list-item",key:f},[i("div",{class:se(["go-flex-items-center",{select:f===d.index}])},[e(E,{style:{width:"95px"},value:s.value.color[f],"onUpdate:value":$=>s.value.color[f]=$,"show-preview":!0,modes:["hex"],onComplete:$=>k($,f),"onUpdate:show":$=>h(x,f)},null,8,["value","onUpdate:value","onComplete","onUpdate:show"]),Se(i("div",null,[e(L,{trigger:"hover"},{trigger:o(()=>[e(N,{class:"go-ml-1 go-cursor-pointer",size:"16",depth:3,onClick:$=>j(f)},{default:o(()=>[e(w(Y))]),_:2},1032,["onClick"])]),default:o(()=>[y(" 删除颜色 ")]),_:2},1024)],512),[[Oe,f>5]])],2)]))),128)),i("div",null,[e(O,{type:"primary",secondary:"",onClick:A},{default:o(()=>[i("div",je,[Ne,e(N,{size:"16"},{default:o(()=>[e(w(X))]),_:1})])]),_:1})])])]),_:1})]),_:1}),i("div",Fe,[e(R,{class:"go-mt-3 expend-color",bordered:!1,role:"dialog",size:"small","aria-modal":"true"},{default:o(()=>[e(F,null,{default:o(()=>[y("默认扩展色：")]),_:1}),e(V,{style:{margin:"10px 0"}}),e(g,{size:[4,0],justify:"center"},{default:o(()=>[(p(!0),b(B,null,M(S.value.default,(x,f)=>(p(),b("div",{class:"color-computed-item",key:f,onClick:$=>P(x,!1)},[qe,i("div",{style:W(u(x))},null,4)],8,Ve))),128))]),_:1})]),_:1}),e(R,{class:"go-mt-3 expend-color",bordered:!1,role:"dialog",size:"small","aria-modal":"true"},{default:o(()=>[e(F,null,{default:o(()=>[y("透明扩展色：")]),_:1}),e(V,{style:{margin:"10px 0"}}),e(g,{size:[4,0],justify:"center"},{default:o(()=>[(p(!0),b(B,null,M(S.value.fade,(x,f)=>(p(),b("div",{class:"color-computed-item",key:f,onClick:$=>P(x,!0)},[We,i("div",{style:W(u(x))},null,4)],8,Qe))),128))]),_:1})]),_:1})]),e(w(Z),{color:w(D)(s.value.color).splice(0,7)},null,8,["color"])])):ae("",!0)}}});const Ye=fe(Xe,[["__scopeId","data-v-04bb59e0"]]),he=r=>(ve("data-v-1aebdbd9"),r=r(),me(),r),Ze={class:"create-content"},Ge={class:"create-color-setting-box"},Je={key:1,class:"no-data go-flex-center"},Ke=["src"],eo={class:"color-list-box"},oo={class:"color-list"},to=he(()=>i("span",null," 创建 ",-1)),lo=he(()=>i("span",null," 应用数据 ",-1)),no={class:"go-flex-items-center"},so=_e({__name:"index",props:{modelShow:Boolean},emits:["update:modelShow","editSaveHandle"],setup(r,{emit:_}){const v=r,T=_,{DuplicateOutlineIcon:X,TrashIcon:Y,ArrowDownIcon:Z}=Ce.ionicons5,s={id:ue(),name:"未命名",color:["#6ae5bb","#69e3de","#5ac5ee","#5ac4ee","#4498ec","#3c7ddf"]},d=Ue(),S=te(!1);let C=le(d.getEditCanvasConfig.chartCustomThemeColorInfo||[]);const u=te(void 0),h=le({selectInfo:C[0]});pe(()=>v.modelShow,t=>{S.value=t,t&&C.length&&(h.selectInfo=C[0])});const k=ne(()=>{var t;return(t=h==null?void 0:h.selectInfo)==null?void 0:t.id}),P=ne(()=>d.getEditCanvasConfig.chartThemeColor),A=t=>{t.id!==k.value&&(u.value!==void 0?U({message:"当前有变动未保存，是否直接放弃修改？",onPositiveCallback:()=>{u.value=void 0,h.selectInfo=t}}):h.selectInfo=t)},j=()=>{const t=()=>{const c=oe(ee({},D(s)),{id:ue()});h.selectInfo=c,C.push(c),A(c),u.value=c,a(!1)};u.value!==void 0?U({message:"当前有变动未保存，是否直接放弃修改？",onPositiveCallback:()=>{u.value=void 0,t()}}):t()},G=t=>{const c=()=>{C.splice(t,1),d.setEditCanvasConfig(Q.CHART_CUSTOM_THEME_COLOR_INFO,D(C)),z(()=>{C.length?A(C[t-1>-1?t-1:t]):h.selectInfo=void 0})};u.value!==void 0?U({message:"当前有变动未保存，是否直接放弃修改？",onPositiveCallback:()=>{u.value=void 0,c()}}):U({message:"是否删除此颜色？",onPositiveCallback:()=>{c()}})},l=t=>{u.value=t},a=(t=!0)=>{if(!u.value)return;const c=C.findIndex(g=>{var E;return g.id===((E=u.value)==null?void 0:E.id)});if(c!==-1){t&&window.$message.success("数据应用成功！");const g=D(oe(ee({},u.value),{name:u.value.name||"未定义"}));C.splice(c,1,g),u.value=void 0;const E=d.getEditCanvasConfig.chartThemeColor;d.setEditCanvasConfig(Q.CHART_THEME_COLOR,"dark"),z(()=>{d.setEditCanvasConfig(Q.CHART_CUSTOM_THEME_COLOR_INFO,D(C)),d.setEditCanvasConfig(Q.CHART_THEME_COLOR,E)})}else window.$message.error("数据应用失败！")},m=()=>{const t=()=>{u.value=void 0,h.selectInfo=void 0,T("update:modelShow",!1)};u.value!==void 0?U({message:"当前有变动未保存，是否直接放弃修改？",onPositiveCallback:()=>{t()}}):t()},I=t=>`linear-gradient(to right, ${t.color[0]} 0%, ${t.color[5]} 100%)`;return(t,c)=>{const g=n("n-text"),E=n("n-timeline-item"),N=n("n-timeline"),L=n("n-icon"),O=n("n-button"),J=n("n-badge"),R=n("n-space"),F=n("n-divider"),V=n("n-a"),x=n("n-ellipsis"),f=n("n-card"),$=n("n-tooltip"),ge=n("n-modal");return p(),q(ge,{class:"go-chart-create-color",show:S.value,"onUpdate:show":c[0]||(c[0]=H=>S.value=H),"mask-closable":!1,closeOnEsc:!1},{default:o(()=>[e(f,{bordered:!1,role:"dialog",size:"small","aria-modal":"true",style:{width:"900px",height:"720px"}},{header:o(()=>[]),"header-extra":o(()=>[]),action:o(()=>[e(R,{justify:"end"},{default:o(()=>[e(O,{onClick:m},{default:o(()=>[y("操作完成")]),_:1})]),_:1})]),default:o(()=>[i("div",Ze,[i("div",Ge,[k.value?(p(),q(w(Ye),{key:0,selectColor:h.selectInfo,onUpdateColor:l},null,8,["selectColor"])):(p(),b("div",Je,[i("img",{src:w(Le),alt:"暂无数据"},null,8,Ke),e(g,{depth:3},{default:o(()=>[y("暂未选择自定义颜色")]),_:1})]))]),i("div",eo,[e(N,{class:"pond-item-timeline",style:{width:"20px"}},{default:o(()=>[e(E,{type:"info"}),e(E,{type:"success"})]),_:1}),i("div",oo,[e(R,null,{default:o(()=>[e(O,{class:se(["create-btn",{"is-full":!k.value}]),type:"primary",ghost:!k.value,secondary:!!k.value,onClick:j},{icon:o(()=>[e(L,null,{default:o(()=>[e(w(X))]),_:1})]),default:o(()=>[to]),_:1},8,["class","ghost","secondary"]),k.value?(p(),q(J,{key:0,show:u.value!==void 0,dot:""},{default:o(()=>[e(O,{class:"create-btn",type:"info",secondary:"",onClick:a},{icon:o(()=>[e(L,null,{default:o(()=>[e(w(Z))]),_:1})]),default:o(()=>[lo]),_:1})]),_:1},8,["show"])):ae("",!0)]),_:1}),e(F,{style:{margin:"10px 0"}}),k.value?ae("",!0):(p(),q(g,{key:0,class:"not-data-text",depth:3},{default:o(()=>[y(" 暂无自定义颜色， "),e(V,{onClick:j},{default:o(()=>[y("立即创建")]),_:1})]),_:1})),(p(!0),b(B,null,M(w(C),(H,re)=>(p(),b("div",{class:"color-card-box",key:re},[e(f,{class:se(["color-card",{selected:H.id===k.value}]),size:"small",hoverable:"",embedded:"",onClick:K=>A(H)},{default:o(()=>[i("div",no,[e(x,{style:{"text-align":"left",width:"70px"}},{default:o(()=>[y(Ae(H.name),1)]),_:2},1024),(p(!0),b(B,null,M(H.color,(K,be)=>(p(),b("span",{class:"theme-color-item",key:be,style:W({backgroundColor:K})},null,4))),128))]),i("div",{class:"theme-bottom",style:W({backgroundImage:I(H)})},null,4)]),_:2},1032,["class","onClick"]),e($,{trigger:"hover"},{trigger:o(()=>[e(O,{text:"",disabled:H.id===P.value,onClick:K=>G(re)},{default:o(()=>[e(L,{class:"go-ml-1 go-cursor-pointer",size:"16",depth:3},{default:o(()=>[e(w(Y))]),_:1})]),_:2},1032,["disabled","onClick"])]),default:o(()=>[y(" 删除自定义颜色 ")]),_:2},1024)]))),128))])])])]),_:1})]),_:1},8,["show"])}}});const vo=fe(so,[["__scopeId","data-v-1aebdbd9"]]);export{vo as default};
