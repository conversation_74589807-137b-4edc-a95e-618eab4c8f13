import{d as g,l as i,a0 as I,ay as w,r as n,o as B,E as T,w as s,e as o,u as k,f as R,Q as r,q as b,t as N,R as _,b3 as P,b4 as S,a9 as z,j as K}from"./index-bb2cbf17.js";import{i as V}from"./icon-f36697ff.js";import{u as D,E as F}from"./chartEditStore-55fbe93c.js";import"./plugin-3ef0fcec.js";const M={class:"title"},j=g({__name:"index",setup(q){const{FishIcon:p}=V.ionicons5,d=D(),a={value:!1},c=i(null),e=i((()=>{const t=S();return t.length?t[0]:""})()||""),f=I(()=>{e.value=e.value.replace(/\s/g,"");const t=e.value.length?e.value:"新项目";return w("工作空间"),d.setEditCanvasConfig(F.PROJECT_NAME,t),t}),v=()=>{a.value=!0,z(()=>{c.value&&c.value.focus()})},l=()=>{a.value=!1};return(t,u)=>{const m=n("n-icon"),h=n("n-button"),x=n("n-text"),y=n("n-input"),C=n("n-space");return B(),T(C,null,{default:s(()=>[o(m,{size:"20",depth:3},{default:s(()=>[o(k(p))]),_:1}),o(x,{onClick:v},{default:s(()=>[R(" 工作空间 "),r(o(h,{secondary:"",size:"tiny"},{default:s(()=>[b("span",M,N(f.value),1)]),_:1},512),[[_,!1]])]),_:1}),r(o(y,{ref_key:"inputInstRef",ref:c,size:"small",type:"text",maxlength:"16","show-count":"",placeholder:"请输入项目名称",value:e.value,"onUpdate:value":u[0]||(u[0]=E=>e.value=E),valueModifiers:{trim:!0},onKeyup:P(l,["enter"]),onBlur:l},null,8,["value","onKeyup"]),[[_,a.value]])]),_:1})}}});const U=K(j,[["__scopeId","data-v-122820cc"]]);export{U as default};
