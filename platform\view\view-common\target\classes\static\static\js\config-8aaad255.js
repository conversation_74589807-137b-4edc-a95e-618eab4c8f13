var c=Object.defineProperty,f=Object.defineProperties;var C=Object.getOwnPropertyDescriptors;var r=Object.getOwnPropertySymbols;var g=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable;var n=(i,t,o)=>t in i?c(i,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):i[t]=o,e=(i,t)=>{for(var o in t||(t={}))g.call(t,o)&&n(i,o,t[o]);if(r)for(var o of r(t))l.call(t,o)&&n(i,o,t[o]);return i},p=(i,t)=>f(i,C(t));var a=(i,t,o)=>(n(i,typeof t!="symbol"?t+"":t,o),o);import{aM as E,a8 as s,cy as u}from"./index-bb2cbf17.js";import{d as T}from"./chartEditStore-55fbe93c.js";import{s as m,t as d,v as A}from"./index-0ec04aee.js";import"./plugin-3ef0fcec.js";import"./icon-f36697ff.js";import"./SettingItem-7fe1cfec.js";/* empty css                                                                      */import"./SettingItemBox-500aaf18.js";import"./CollapseItem.vue_vue_type_script_setup_true_lang-9b6d8958.js";import"./index.esm.min-f1367f57.js";import"./http-36f53bd1.js";import"./lodash-d17632fd.js";import"./fileTypeEnum-21359a08.js";const I={[u]:A.DATA,pageValue:1,sizeValue:[2,4,8,10,20],pageSize:4,dataset:10};class w extends T{constructor(){super(...arguments);a(this,"key",m.key);a(this,"attr",p(e({},E),{w:395,h:32,zIndex:-1}));a(this,"chartConfig",s(m));a(this,"interactActions",d);a(this,"option",s(I))}}export{w as default,I as option};
