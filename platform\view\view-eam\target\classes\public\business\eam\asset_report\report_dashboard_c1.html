<!--
/**
 * 存放位置 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2021-10-12 12:47:14
 */
 -->
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <title th:text="${lang.translate('资产统计面板')}">资产统计面板</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" th:href="@{/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css}" rel="stylesheet"/>
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">


    <style>
        .layui-card {border:1px solid #f2f2f2;border-radius:5px;}
        .icon {margin-right:10px;color:#1aa094;}
        .icon-cray {color:#ffb800!important;}
        .icon-blue {color:#1e9fff!important;}
        .icon-tip {color:#ff5722!important;}
        .layuimini-qiuck-module {text-align:center;margin-top: 10px}
        .layuimini-qiuck-module a i {display:inline-block;width:100%;height:60px;line-height:60px;text-align:center;border-radius:2px;font-size:30px;background-color:#F8F8F8;color:#333;transition:all .3s;-webkit-transition:all .3s;}
        .layuimini-qiuck-module a cite {position:relative;top:2px;display:block;color:#666;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;font-size:14px;}
        .welcome-module {width:100%;height:210px;}
        .panel {background-color:white!important;border:1px solid transparent;border-radius:3px;-webkit-box-shadow:0 1px 1px rgba(0,0,0,.05);box-shadow:0 1px 1px rgba(0,0,0,.05)}
        .panel-body {padding:10px}
        .panel-title {margin-top:0;margin-bottom:0;font-size:12px;color:inherit}
        .label {display:inline;padding:.2em .6em .3em;font-size:75%;font-weight:700;line-height:1;color:#fff;text-align:center;white-space:nowrap;vertical-align:baseline;border-radius:.25em;margin-top: .3em;}
        .layui-red {color:red}
        .main_btn > p {height:40px;}
        .layui-bg-number {background-color:#F8F8F8;}
        .layuimini-notice:hover {background:#f6f6f6;}
        .layuimini-notice {padding:7px 16px;clear:both;font-size:12px !important;cursor:pointer;position:relative;transition:background 0.2s ease-in-out;}
        .layuimini-notice-title,.layuimini-notice-label {
            padding-right: 70px !important;text-overflow:ellipsis!important;overflow:hidden!important;white-space:nowrap!important;}
        .layuimini-notice-title {line-height:28px;font-size:14px;}
        .layuimini-notice-extra {position:absolute;top:50%;margin-top:-8px;right:16px;display:inline-block;height:16px;color:#999;}



        table {
            border-collapse: collapse;
            border-spacing: 0;
        }

        td,th {
            padding: 0;
        }

        .pure-table {
            border-collapse: collapse;
            border-spacing: 0;
            empty-cells: show;
            border: 1px solid #cbcbcb;
        }

        .pure-table caption {
            color: #000;
            font: italic 85%/1 arial,sans-serif;
            padding: 1em 0;

            text-align: center;
        }

        .pure-table td,.pure-table th {
            border-left: 1px solid #cbcbcb;
            border-width: 0 0 0 1px;
            font-size: inherit;
            margin: 0;
            overflow: visible;
            padding: .5em 1em;
        }

        .pure-table thead {
            background-color: #e0e0e0;
            color: #000;
            text-align: left;
            vertical-align: bottom;
        }

        .pure-table td {
            background-color: transparent;
        }

        .pure-table-odd td {
            background-color: #f2f2f2;
        }



        .hh{
            height: 120px;
        }
        .ht{
            margin-top:10px;
        }
        .hm{
            margin-top:7px;
            margin-bottom:5px;
        }

    </style>
</head>

<body>




<div class="layui-fluid">
    <div class="layui-row layui-col-space10">

        <div class="layui-row layui-col-space10">
            <div class="layui-col-md2">

                <div class="panel layui-bg-number banner hh" >
                    <div class="panel-body">
                        <div class="panel-title ht">
                            <span class="label pull-right layui-bg-blue">实时</span>
                            <h5>资产总数</h5>
                        </div>
                        <div class="panel-content">
                            <h1 id="assetCnt" class="no-margins hm">0</h1>
                            <small>当前资产总记录数</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-col-md2">
                <div class="panel layui-bg-number hh">
                    <div class="panel-body">
                        <div class="panel-title ht">
                            <span class="label pull-right layui-bg-blue">实时</span>
                            <h5>近30天维保到期</h5>
                        </div>
                        <div class="panel-content">
                            <h1 id="maintenanceEndCnt" class="no-margins hm">0</h1>
                            <small>近30天维保到期</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-col-md2">
                <div class="panel layui-bg-number hh">
                    <div class="panel-body">
                        <div class="panel-title ht">
                            <span class="label pull-right layui-bg-blue">实时</span>
                            <h5>待确认转移单</h5>
                        </div>
                        <div class="panel-content">
                            <h1 id="waitAssetTranferCnt" class="no-margins hm">0</h1>
                            <small>待确认转移单</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-col-md2">
                <div class="panel layui-bg-number hh">
                    <div class="panel-body">
                        <div class="panel-title ht">
                            <span class="label pull-right layui-bg-blue">实时</span>
                            <h5>资产总价值</h5>
                        </div>
                        <div class="panel-content">
                            <h1 id="assetNavPrice" class="no-margins hm">0</h1>
                            <small>当前资产总价值</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-col-md2">
                <div class="panel layui-bg-number hh">
                    <div class="panel-body">
                        <div class="panel-title ht">
                            <span class="label pull-right layui-bg-blue">实时</span>
                            <h5>资产处理总数</h5>
                        </div>
                        <div class="panel-content">
                            <h1 id="assetCleanCnt" class="no-margins hm">0</h1>
                            <small>当前资产处理总记录数</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-col-md2">
                <div class="panel layui-bg-number hh">
                    <div class="panel-body">
                        <div class="panel-title ht">
                            <span class="label pull-right layui-bg-blue">实时</span>
                            <h5>资产报修总数</h5>
                        </div>
                        <div class="panel-content">
                            <h1 id="assetRepairCnt"   class="no-margins hm">0</h1>
                            <small>当前资产报修总记录数</small>
                        </div>
                    </div>
                </div>


            </div>
        </div>

    </div>
    <!--            </div>-->
    <!--        </div>-->
    <!--    </div>-->
</div>

<div class="layui-fluid">
    <div id="main_content">
        <iframe id="mainFrame" name="mainFrame" scrolling="no"  th:src="'/business/datacenter/rack/rack_dev_show_list.html?nodeId=none'"
                frameborder="0" style="padding: 0px; width: 100%; height: 1000px;"></iframe>
        <!--        <iframe  frameborder=0 width="100%"  height="300" id="mapData" th:src="'/business/datacenter/rack/rack_dev_show_list.html?nodeId=none'"  ></iframe>-->
        <!--   -->
    </div>
</div>




<style>
    table{
    }
    table, td, th
    {
        overflow-y: scroll;
        border:1px solid #7CD8A5;
    }
    th
    {
        background-color:#7E96FE;
        color:white;
    }
    .custTable{
        overflow:scroll;
        width:100%;
        height:300px;
        overflow-y: scroll;
        text-align: center;

    }
    .custTable tbody{
        overflow-y: scroll;
    }
    .custTable,tr,td{
        height: 35px!important;
    }

</style>

<div class="layui-fluid">
    <div class="layui-row layui-col-space10">
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">
                    资产状态表
                </div>
                <div class="layui-card-body">
                    <div class="layui-row layui-col-space10">
                        <div style="height:300px!important;overflow: scroll">
                            <table  class="custTable" id="assetStatus" style="height:300px!important;width:100%;text-align:left;">
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">
                    资产状态图
                </div>
                <div class="layui-card-body">
                    <div class="layui-row layui-col-space10">

                        <!--                        <div class="layui-col-md4">-->
                        <div id="assetStatusPie"  style="height:300px"></div>
                        <!--                        </div>-->

                    </div>

                </div>
            </div>
        </div>
    </div>
</div>



<div class="layui-fluid">
    <div class="layui-row layui-col-space10">
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">
                    资产所属分布
                </div>
                <div class="layui-card-body">
                    <div class="layui-row layui-col-space10">
                        <div id="ownerAssetPie"  style="height:300px"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">
                    存放位置分布
                </div>
                <div class="layui-card-body">
                    <div class="layui-row layui-col-space10">
                        <div id="posAssetPie"  style="height:300px"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="layui-fluid">
    <div class="layui-row layui-col-space10">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">
                    资产分类情况
                </div>
                <div class="layui-card-body">
                    <div id="assetCata" style="width:100%;height:300px"></div>
                </div>
            </div>
        </div>
    </div>
</div>


</div>

<script>

</script>





<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>

<script type="text/javascript" src="/extmodule/echarts/echarts.js" th:src="'/extmodule/echarts/echarts.js?'+${cacheKey}" ></script>



<script th:inline="javascript">
    var TENANT_ID = [[${tenantId}]] ;
</script>

<script th:src="'/business/eam/asset_report/report_dashboard.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset_report/report_dashboard_ext.js?'+${cacheKey}"></script>
</body>
</html>



<script type="text/javascript">

    document.getElementById("mainFrame").style.height = "0px";//最好设置为minHeight
    //$("#mainFrame").height(0);
  //  var url="/business/datacenter/rack/rack_dev_show_list.html?nodeId=none";
    var url="http://*************:99/mapX.html"
    $("#mainFrame").attr("src", url);

    startInit('mainFrame', 560);
    var browserVersion = window.navigator.userAgent.toUpperCase();
    var isOpera = browserVersion.indexOf("OPERA") > -1 ? true : false;
    var isFireFox = browserVersion.indexOf("FIREFOX") > -1 ? true : false;
    var isChrome = browserVersion.indexOf("CHROME") > -1 ? true : false;
    var isSafari = browserVersion.indexOf("SAFARI") > -1 ? true : false;
    var isIE = (!!window.ActiveXObject || "ActiveXObject" in window);
    var isIE9More = (! -[1,] == false);


    function reinitIframe(iframeId, minHeight) {
        try {
            var iframe = document.getElementById(iframeId);
            var bHeight = 0;
            if (isChrome == false && isSafari == false) {
                try {
                    bHeight = iframe.contentWindow.document.body.scrollHeight;
                } catch (ex) {
                }
            }
            var dHeight = 0;
            if (isFireFox == true)
                dHeight = iframe.contentWindow.document.documentElement.offsetHeight + 2;//如果火狐浏览器高度不断增加删除+2
            else if (isIE == false && isOpera == false && iframe.contentWindow) {
                try {
                    dHeight = iframe.contentWindow.document.documentElement.scrollHeight;
                } catch (ex) {
                }
            }
            else if (isIE == true && isIE9More) {//ie9+
                var heightDeviation = bHeight - eval("window.IE9MoreRealHeight" + iframeId);
                if (heightDeviation == 0) {
                    bHeight += 3;
                } else if (heightDeviation != 3) {
                    eval("window.IE9MoreRealHeight" + iframeId + "=" + bHeight);
                    bHeight += 3;
                }
            }
            else//ie[6-8]、OPERA
                bHeight += 3;

            var height = Math.max(bHeight, dHeight);
            if (height < minHeight) height = minHeight;
            //alert(iframe.contentWindow.document.body.scrollHeight + "~" + iframe.contentWindow.document.documentElement.scrollHeight);
            iframe.style.height = height + "px";
        } catch (ex) { }
    }
    function startInit(iframeId, minHeight) {
        eval("window.IE9MoreRealHeight" + iframeId + "=0");
        window.setInterval("reinitIframe('" + iframeId + "'," + minHeight + ")", 100);
    }

</script>