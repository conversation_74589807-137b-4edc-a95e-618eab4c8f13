<!--
/**
 * eam_asset_depreciation_detail 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-11-26 21:09:29
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <title th:text="${lang.translate('eam_asset_depreciation_detail',null,'eam_asset_dep_detail')}">eam_asset_depreciation_detail</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">
        <div class="search-bar" style="height: 40px">
<!--            <div class="search-input-rows" style="opacity: 0">-->
<!--                &lt;!&ndash; 搜索输入区域 &ndash;&gt;-->
<!--                <div class="layui-form toolbar search-inputs">-->
<!--                </div>-->
<!--            </div>-->
            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索','','cmp:table.search')}">搜索</span></button>
            </div>
        </div>
        <div id="table-area" style="margin-top: 42px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
<!--    <div class="layui-btn-container">-->
<!--        <button id="depreciation-start"  th:if="${perm.checkAuth('eam_asset_depreciation_oper:start')}"class="layui-btn icon-btn layui-btn-sm  depreciationStart-btn " lay-event="tool-depreciation-start"><span th:text="${lang.translate('导入资产','','cmp:table.button')}">导入资产</span></button>-->
<!--        <button id="depreciation-execute"  th:if="${perm.checkAuth('eam_asset_depreciation_oper:execute')}"class="layui-btn icon-btn layui-btn-sm  depreciationExecute-btn " lay-event="tool-depreciation-execute"><span th:text="${lang.translate('执行折旧','','cmp:table.button')}">执行折旧</span></button>-->
<!--        <button id="depreciation-sync"  th:if="${perm.checkAuth('eam_asset_depreciation_oper:sync')}"class="layui-btn icon-btn layui-btn-sm  depreciationSync-btn " lay-event="tool-depreciation-sync"><span th:text="${lang.translate('同步数据','','cmp:table.button')}">同步数据</span></button>-->
<!--        <button id="depreciation-export"  th:if="${perm.checkAuth('eam_asset_depreciation_oper:export')}"class="layui-btn icon-btn layui-btn-sm  depreciationExport-btn " lay-event="tool-depreciation-export"><span th:text="${lang.translate('数据导出','','cmp:table.button')}">数据导出</span></button>-->
<!--    </div>-->
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">



<!--    <button th:if="${perm.checkAuth('eam_asset_depreciation_detail:delete')}" class="layui-btn layui-btn-xs layui-btn-danger ops-delete-button " lay-event="del" data-id="{{d.id}}"><span th:text="${lang.translate('删除','','cmp:table.ops')}">删除</span></button>-->


</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var SELECT_FIRSTDEPRECIATIONMETHOD_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetFirstDepreciationMethodTypeEnum')}]];
    var SELECT_DEPRECIATIONMETHOD_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetDepreciationMethodEnum')}]];
    var SELECT_RESULT_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetDetailDepreciationResultEnum')}]];
    var AUTH_PREFIX="eam_asset_depreciation_detail_history";

    // OPER_ID
    var ASSET_ID = [[${assetId}]] ;

</script>

<script th:src="'/business/eam/asset_depreciation_detail/asset_depreciation_detail_history_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset_depreciation_detail/asset_depreciation_detail_history_list.js?'+${cacheKey}"></script>

</body>
</html>
