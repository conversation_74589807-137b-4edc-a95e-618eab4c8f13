<!--
/**
 * 资产字段配置 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2023-10-17 16:21:49
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
	<title th:text="${lang.translate('资产字段配置')}">资产字段配置</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="opacity:0">


         <!--开始：group 循环-->


        <div class="layui-row form-row" id="default-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column" >

                <!-- text_input : 主键 ,  id -->
                <div class="layui-form-item" style="display: none"inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('主键')}">主键</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="id" id="id" name="id" th:placeholder="${ lang.translate('请输入'+'主键') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 字段编码 ,  code -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('字段编码')}">字段编码</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="code" id="code" name="code" th:placeholder="${ lang.translate('请输入'+'字段编码') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            
                <!-- text_input : 字段名称 ,  label -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('字段名称')}">字段名称</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="label" id="label" name="label" th:placeholder="${ lang.translate('请输入'+'字段名称') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            
                <!-- radio_box : 字段值类型 ,  valueType  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('字段值类型')}">字段值类型</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input input-type="radio" type="radio" name="valueType" lay-filter="valueType" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.eam.AssetAttributeValueTypeEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==-1)}">
                    </div>
                </div>
            
                <!-- text_input : 取值路径 ,  valuePath -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('取值路径')}">取值路径</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="valuePath" id="valuePath" name="valuePath" th:placeholder="${ lang.translate('请输入'+'取值路径') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 标签备注 ,  labelNotes -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('标签备注')}">标签备注</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="labelNotes" id="labelNotes" name="labelNotes" th:placeholder="${ lang.translate('请输入'+'标签备注') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- radio_box : 所属维度 ,  dimension  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('所属维度')}">所属维度</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input input-type="radio" type="radio" name="dimension" lay-filter="dimension" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.eam.AssetAttributeDimensionEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==-1)}">
                    </div>
                </div>
            
                <!-- radio_box : 状态 ,  status  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('状态')}">状态</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input input-type="radio" type="radio" name="status" lay-filter="status" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.common.StatusValidEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==-1)}">
                    </div>
                </div>
            
                <!-- radio_box : 是否必选 ,  required  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('是否必选')}">是否必选</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input input-type="radio" type="radio" name="required" lay-filter="required" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.common.StatusYNEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==-1)}">
                    </div>
                </div>
            
                <!-- radio_box : 是否修改 ,  requiredModify  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('是否修改')}">是否修改</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input input-type="radio" type="radio" name="requiredModify" lay-filter="requiredModify" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.common.StatusYNEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==-1)}">
                    </div>
                </div>
            
                <!-- radio_box : 组件类型 ,  componentType  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('组件类型')}">组件类型</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input input-type="radio" type="radio" name="componentType" lay-filter="componentType" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.eam.AssetAttributeComponentTypeEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==-1)}">
                    </div>
                </div>
            
                <!-- text_area : 组件内容 ,  componentContent  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('组件内容')}">组件内容</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <textarea lay-filter="componentContent" id="componentContent" name="componentContent" th:placeholder="${ lang.translate('请输入'+'组件内容') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>
            
                <!-- text_area : 备注 ,  notes  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <textarea lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入'+'备注') }" class="layui-textarea" style="height: 30px" ></textarea>
                    </div>
                </div>
            
                <!-- text_input : 修改人ID ,  updateBy -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('修改人ID')}">修改人ID</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="updateBy" id="updateBy" name="updateBy" th:placeholder="${ lang.translate('请输入'+'修改人ID') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- select_box : 归属 ,  owner  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('归属')}">归属</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <div id="owner" input-type="select" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetAttributeOwnerEnum')}" extraParam="{}"></div>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 80px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_asset_attribute:create','eam_asset_attribute:update','eam_asset_attribute:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var RADIO_VALUETYPE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetAttributeValueTypeEnum')}]];
    var RADIO_DIMENSION_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetAttributeDimensionEnum')}]];
    var RADIO_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusValidEnum')}]];
    var RADIO_REQUIRED_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusYNEnum')}]];
    var RADIO_REQUIREDMODIFY_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusYNEnum')}]];
    var RADIO_COMPONENTTYPE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetAttributeComponentTypeEnum')}]];
    var SELECT_OWNER_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetAttributeOwnerEnum')}]];
    var VALIDATE_CONFIG={"owner":{"labelInForm":"归属","inputType":"select_box","required":true},"componentType":{"labelInForm":"组件类型","inputType":"radio_box","required":true},"code":{"labelInForm":"字段编码","inputType":"text_input","required":true},"valueType":{"labelInForm":"字段值类型","inputType":"radio_box","required":true},"label":{"labelInForm":"字段名称","inputType":"text_input","required":true},"dimension":{"labelInForm":"所属维度","inputType":"radio_box","required":true},"requiredModify":{"labelInForm":"是否修改","inputType":"radio_box","required":true},"required":{"labelInForm":"是否必选","inputType":"radio_box","required":true},"status":{"labelInForm":"状态","inputType":"radio_box","required":true}};
    var AUTH_PREFIX="eam_asset_attribute";


</script>



<script th:src="'/business/eam/asset_attribute/asset_attribute_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset_attribute/asset_attribute_form.js?'+${cacheKey}"></script>

</body>
</html>