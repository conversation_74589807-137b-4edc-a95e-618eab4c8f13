<!--
/**
 * 标签模版 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-12-18 12:30:47
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
	<title th:text="${lang.translate('标签模版')}">标签模版</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="display:none">


         <!--开始：group 循环-->


        <div class="layui-row form-row" id="default-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column" >

                <!-- text_input : 主键 ,  id -->
                <div class="layui-form-item" style="display: none">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('主键')}">主键</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="id" id="id" name="id" th:placeholder="${ lang.translate('请输入'+'主键') }" type="text" class="layui-input"  />
                    </div>
                </div>

                <!-- text_input : 类型 ,  type -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('类型')}">类型</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="type" id="type" name="type" th:placeholder="${ lang.translate('请输入'+'类型') }" type="text" class="layui-input"  />
                    </div>
                </div>

                <!-- text_input : 是否自定义 ,  isCustom -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('是否自定义')}">是否自定义</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="isCustom" id="isCustom" name="isCustom" th:placeholder="${ lang.translate('请输入'+'是否自定义') }" type="text" class="layui-input"  />
                    </div>
                </div>

                <!-- text_area : 字段 ,  colIds  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('字段')}">字段</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <textarea lay-filter="colIds" id="colIds" name="colIds" th:placeholder="${ lang.translate('请输入'+'字段') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>

                <!-- text_input : 位置:u ,  imagePosition -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('位置:u')}">位置:u</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="imagePosition" id="imagePosition" name="imagePosition" th:placeholder="${ lang.translate('请输入'+'位置:u') }" type="text" class="layui-input"  />
                    </div>
                </div>

                <!-- text_input : 图像 ,  imageColId -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('图像')}">图像</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="imageColId" id="imageColId" name="imageColId" th:placeholder="${ lang.translate('请输入'+'图像') }" type="text" class="layui-input"  />
                    </div>
                </div>

                <!-- text_input : 图像显示 ,  imageShow -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('图像显示')}">图像显示</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="imageShow" id="imageShow" name="imageShow" th:placeholder="${ lang.translate('请输入'+'图像显示') }" type="text" class="layui-input"  />
                    </div>
                </div>

                <!-- text_input : 图像label显示 ,  imageLabelShow -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('图像label显示')}">图像label显示</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="imageLabelShow" id="imageLabelShow" name="imageLabelShow" th:placeholder="${ lang.translate('请输入'+'图像label显示') }" type="text" class="layui-input"  />
                    </div>
                </div>

                <!-- text_input : 图像类型txm ,  imageType -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('图像类型txm')}">图像类型txm</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="imageType" id="imageType" name="imageType" th:placeholder="${ lang.translate('请输入'+'图像类型txm') }" type="text" class="layui-input"  />
                    </div>
                </div>

                <!-- text_area : 类型 ,  labelFormatContent  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('类型')}">类型</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <textarea lay-filter="labelFormatContent" id="labelFormatContent" name="labelFormatContent" th:placeholder="${ lang.translate('请输入'+'类型') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>

                <!-- text_input : 备注 ,  notes -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入'+'备注') }" type="text" class="layui-input"  />
                    </div>
                </div>

                <!-- number_input : 表格marginTop(mm) ,  labelTableMarginTop  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('表格marginTop(mm)')}">表格marginTop(mm)</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="labelTableMarginTop" id="labelTableMarginTop" name="labelTableMarginTop" th:placeholder="${ lang.translate('请输入'+'表格marginTop(mm)') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />
                    </div>
                </div>

                <!-- number_input : 表格marginBottom(mm) ,  labelTableMarginBottom  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('表格marginBottom(mm)')}">表格marginBottom(mm)</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="labelTableMarginBottom" id="labelTableMarginBottom" name="labelTableMarginBottom" th:placeholder="${ lang.translate('请输入'+'表格marginBottom(mm)') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />
                    </div>
                </div>

                <!-- number_input : 表格marginLeft(mm) ,  labelTableMarginLeft  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('表格marginLeft(mm)')}">表格marginLeft(mm)</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="labelTableMarginLeft" id="labelTableMarginLeft" name="labelTableMarginLeft" th:placeholder="${ lang.translate('请输入'+'表格marginLeft(mm)') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />
                    </div>
                </div>

                <!-- number_input : 表格marginRight(mm) ,  labelTableMarginRight  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('表格marginRight(mm)')}">表格marginRight(mm)</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="labelTableMarginRight" id="labelTableMarginRight" name="labelTableMarginRight" th:placeholder="${ lang.translate('请输入'+'表格marginRight(mm)') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />
                    </div>
                </div>

                <!-- text_input : 表格内Key是否加粗 ,  keyBold -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('表格内Key是否加粗')}">表格内Key是否加粗</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="keyBold" id="keyBold" name="keyBold" th:placeholder="${ lang.translate('请输入'+'表格内Key是否加粗') }" type="text" class="layui-input"  />
                    </div>
                </div>

                <!-- text_input : 表格内Value是否加粗 ,  valueBlod -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('表格内Value是否加粗')}">表格内Value是否加粗</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="valueBlod" id="valueBlod" name="valueBlod" th:placeholder="${ lang.translate('请输入'+'表格内Value是否加粗') }" type="text" class="layui-input"  />
                    </div>
                </div>

                <!-- number_input : 图片marginTop(mm) ,  imageMarginTop  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('图片marginTop(mm)')}">图片marginTop(mm)</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="imageMarginTop" id="imageMarginTop" name="imageMarginTop" th:placeholder="${ lang.translate('请输入'+'图片marginTop(mm)') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />
                    </div>
                </div>

                <!-- number_input : 图片marginBottom(mm) ,  imageMarginBottom  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('图片marginBottom(mm)')}">图片marginBottom(mm)</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="imageMarginBottom" id="imageMarginBottom" name="imageMarginBottom" th:placeholder="${ lang.translate('请输入'+'图片marginBottom(mm)') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />
                    </div>
                </div>

                <!-- number_input : 图片marginLeft(mm) ,  imageMarginLeft  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('图片marginLeft(mm)')}">图片marginLeft(mm)</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="imageMarginLeft" id="imageMarginLeft" name="imageMarginLeft" th:placeholder="${ lang.translate('请输入'+'图片marginLeft(mm)') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />
                    </div>
                </div>

                <!-- number_input : 图片marginRight(mm) ,  imageMarginRight  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('图片marginRight(mm)')}">图片marginRight(mm)</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="imageMarginRight" id="imageMarginRight" name="imageMarginRight" th:placeholder="${ lang.translate('请输入'+'图片marginRight(mm)') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />
                    </div>
                </div>

                <!-- number_input : 图片宽度 ,  imageWidth  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('图片宽度')}">图片宽度</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="imageWidth" id="imageWidth" name="imageWidth" th:placeholder="${ lang.translate('请输入'+'图片宽度') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />
                    </div>
                </div>

                <!-- number_input : 图片高度 ,  imageHeight  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('图片高度')}">图片高度</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="imageHeight" id="imageHeight" name="imageHeight" th:placeholder="${ lang.translate('请输入'+'图片高度') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_asset_label_tpl:create','eam_asset_label_tpl:update','eam_asset_label_tpl:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var VALIDATE_CONFIG={};
    var AUTH_PREFIX="eam_asset_label_tpl";


</script>



<script th:src="'/business/eam/asset_label_tpl/asset_label_tpl_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset_label_tpl/asset_label_tpl_form.js?'+${cacheKey}"></script>

</body>
</html>
