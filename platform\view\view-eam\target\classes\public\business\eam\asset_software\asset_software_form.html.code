<!--
/**
 * 软件资产 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-07-15 06:50:48
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
	<title th:text="${lang.translate('软件资产')}">软件资产</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="theme.ico" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="display:none">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->


        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-8183-fieldset">
            <legend>基本信息</legend>
        </fieldset>

        <div class="layui-row form-row" id="random-8183-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('软件名称')}">软件名称</div><div class="layui-required">*</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="name" id="name" name="name" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('软件名称') }" type="text" class="layui-input"    lay-verify="|required"  />
                        </div>
                    </div>

                                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('所属公司')}">所属公司</div><div class="layui-required">*</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="ownCompanyId" id="ownCompanyId" name="ownCompanyId"  type="hidden" class="layui-input"    lay-verify="|required"   />
                            <button id="ownCompanyId-button" type="button" action-type="org-dialog" class="layui-btn   " style="width: 100%"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择公司')}" th:default-label="${lang.translate('请选择公司')}">按钮文本</span></button>
                        </div>
                    </div>

                                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('使用公司')}">使用公司</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="useOrganizationId" id="useOrganizationId" name="useOrganizationId"  type="hidden" class="layui-input"   />
                            <button id="useOrganizationId-button" type="button" action-type="org-dialog" class="layui-btn   " style="width: 100%"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择组织节点')}" th:default-label="${lang.translate('请选择组织节点')}">按钮文本</span></button>
                        </div>
                    </div>

                <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >


                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('软件分类')}">软件分类</div><div class="layui-required">*</div></div>
                        <div class="layui-input-block ">
                            <div id="categoryId" input-type="select" th:data="${'/service-pcm/pcm-catalog/query-nodes'}" extraParam="{}"></div>
                        </div>
                    </div>


                                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('管理人')}">管理人</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="managerId" id="managerId" name="managerId"  type="hidden" class="layui-input"   />
                            <button id="managerId-button" type="button" action-type="emp-dialog" class="layui-btn   " style="width: 100%"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择人员')}" th:default-label="${lang.translate('请选择人员')}">按钮文本</span></button>
                        </div>
                    </div>

                
                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('登记时间')}">登记时间</div></div>
                        <div class="layui-input-block ">
                            <input input-type="date" lay-filter="registerDate" id="registerDate" name="registerDate"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择') +''+ lang.translate('登记时间') }" type="text" class="layui-input"    lay-verify=""   />
                        </div>
                    </div>

                <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('详细位置')}">详细位置</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="positionDetail" id="positionDetail" name="positionDetail" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('详细位置') }" type="text" class="layui-input"  />
                        </div>
                    </div>

                
                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('成本')}">成本</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="costPrice" id="costPrice" name="costPrice" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('成本') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />
                        </div>
                    </div>


                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->


        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-4545-fieldset">
            <legend>软件来源</legend>
        </fieldset>

        <div class="layui-row form-row" id="random-4545-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >


                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('来源')}">来源</div><div class="layui-required">*</div></div>
                        <div class="layui-input-block ">
                            <div id="sourceId" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_source'}" extraParam="{}"></div>
                        </div>
                    </div>


                                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('来源明细')}">来源明细</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="sourceDetail" id="sourceDetail" name="sourceDetail" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('来源明细') }" type="text" class="layui-input"  />
                        </div>
                    </div>

                <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >


                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('供应商')}">供应商</div></div>
                        <div class="layui-input-block ">
                            <div id="supplierId" input-type="select" th:data="${'/service-eam/eam-supplier/query-paged-list'}" extraParam="{}"></div>
                        </div>
                    </div>


                <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >


                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('购置日期')}">购置日期</div></div>
                        <div class="layui-input-block ">
                            <input input-type="date" lay-filter="purchaseDate" id="purchaseDate" name="purchaseDate"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择') +''+ lang.translate('购置日期') }" type="text" class="layui-input"    lay-verify=""   />
                        </div>
                    </div>

                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->


        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-4971-fieldset">
            <legend>授权信息</legend>
        </fieldset>

        <div class="layui-row form-row" id="random-4971-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >


                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('许可模式')}">许可模式</div></div>
                        <div class="layui-input-block ">
                            <div id="licenseMode" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=asset_software_license_mode'}" extraParam="{}"></div>
                        </div>
                    </div>


                
                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('版权类型')}">版权类型</div></div>
                        <div class="layui-input-block ">
                            <div id="copyrightType" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=asset_software_copyright_type'}" extraParam="{}"></div>
                        </div>
                    </div>


                
                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('无限授权')}">无限授权</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input input-type="radio" type="radio" name="authorizedNumberUnlimit" lay-filter="authorizedNumberUnlimit" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.common.StatusYNEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==1)}">
                    </div>
                </div>


                <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >


                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('可用数量')}">可用数量</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="authorizedAvailableNumber" id="authorizedAvailableNumber" name="authorizedAvailableNumber" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('可用数量') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="true" decimal="false" allow-negative="true" step="1.0"   scale="0" />
                        </div>
                    </div>


                
                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('授权数量')}">授权数量</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="authorizedNumber" id="authorizedNumber" name="authorizedNumber" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('授权数量') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="true" decimal="false" allow-negative="true" step="1.0"   scale="0" />
                        </div>
                    </div>


                <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >


                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('永久授权')}">永久授权</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input input-type="radio" type="radio" name="authorizationExpirationUnlimit" lay-filter="authorizationExpirationUnlimit" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.common.StatusYNEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==1)}">
                    </div>
                </div>


                
                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('授权到期时间')}">授权到期时间</div></div>
                        <div class="layui-input-block ">
                            <input input-type="date" lay-filter="authorizationExpirationDate" id="authorizationExpirationDate" name="authorizationExpirationDate"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择') +''+ lang.translate('授权到期时间') }" type="text" class="layui-input"    lay-verify=""   />
                        </div>
                    </div>

                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->



        <div class="layui-row form-row" id="random-0754-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >


                    <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('授权码')}">授权码</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="authorizationCode" id="authorizationCode" name="authorizationCode" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('授权码') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>


                
                    <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('授权信息')}">授权信息</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="authorizationInfo" id="authorizationInfo" name="authorizationInfo" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('授权信息') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>


                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->


        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-4255-fieldset">
            <legend>维保信息</legend>
        </fieldset>

        <div class="layui-row form-row" id="random-4255-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >


                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('维保商')}">维保商</div><div class="layui-required">*</div></div>
                        <div class="layui-input-block ">
                            <div id="maintainerId" input-type="select" th:data="${'/service-eam/eam-maintainer/query-paged-list'}" extraParam="{}"></div>
                        </div>
                    </div>


                
                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('是否维保')}">是否维保</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input input-type="radio" type="radio" name="needMaintenance" lay-filter="needMaintenance" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.common.StatusYNEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==1)}">
                    </div>
                </div>


                <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >


                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('维保到期时间')}">维保到期时间</div></div>
                        <div class="layui-input-block ">
                            <input input-type="date" lay-filter="maintenanceEndDate" id="maintenanceEndDate" name="maintenanceEndDate"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择') +''+ lang.translate('维保到期时间') }" type="text" class="layui-input"    lay-verify=""   />
                        </div>
                    </div>

                
                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('维保开始时间')}">维保开始时间</div></div>
                        <div class="layui-input-block ">
                            <input input-type="date" lay-filter="maintenanceStartDate" id="maintenanceStartDate" name="maintenanceStartDate"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择') +''+ lang.translate('维保开始时间') }" type="text" class="layui-input"    lay-verify=""   />
                        </div>
                    </div>

                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->


        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-4170-fieldset">
            <legend>其他信息</legend>
        </fieldset>

        <div class="layui-row form-row" id="random-4170-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column" >


                    <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('软件说明')}">软件说明</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="content" id="content" name="content" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('软件说明') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>


                
                    <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('备注') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>


                
                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('附件')}">附件</div></div>
                    <div class="layui-upload layui-input-block ">
                        <input input-type="upload" id="attachId"  name="attachId" lay-filter="attachId" style="display: none">
                        <button type="button" class="layui-btn" id="attachId-button" th:text="${lang.translate('选择附件')}">选择附件</button>
                        <div class="layui-upload-list" id="attachId-file-list"></div>
                    </div>
                </div>



                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 20px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_asset_software:create','eam_asset_software:update','eam_asset_software:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}]];
    var RADIO_AUTHORIZEDNUMBERUNLIMIT_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusYNEnum')}]];
    var RADIO_AUTHORIZATIONEXPIRATIONUNLIMIT_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusYNEnum')}]];
    var RADIO_NEEDMAINTENANCE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusYNEnum')}]];
    var VALIDATE_CONFIG={"sourceId":{"labelInForm":"来源","inputType":"select_box","required":true},"authorizedNumberUnlimit":{"labelInForm":"无限授权","inputType":"radio_box","required":true},"purchaseDate":{"date":true,"labelInForm":"购置日期","inputType":"date_input"},"needMaintenance":{"labelInForm":"是否维保","inputType":"radio_box","required":true},"ownCompanyId":{"labelInForm":"所属公司","inputType":"button","required":true},"maintainerId":{"labelInForm":"维保商","inputType":"select_box","required":true},"maintenanceEndDate":{"date":true,"labelInForm":"维保到期时间","inputType":"date_input"},"authorizationExpirationDate":{"date":true,"labelInForm":"授权到期时间","inputType":"date_input"},"name":{"labelInForm":"软件名称","inputType":"text_input","required":true},"maintenanceStartDate":{"date":true,"labelInForm":"维保开始时间","inputType":"date_input"},"categoryId":{"labelInForm":"软件分类","inputType":"select_box","required":true},"authorizationExpirationUnlimit":{"labelInForm":"永久授权","inputType":"radio_box","required":true},"registerDate":{"date":true,"labelInForm":"登记时间","inputType":"date_input"}};
    var AUTH_PREFIX="eam_asset_software";

    // ASSET_CATEGORY_DATA
    var ASSET_CATEGORY_DATA = [[${assetCategoryData}]] ;

</script>



<script th:src="'/business/eam/asset_software/asset_software_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset_software/asset_software_form.js?'+${cacheKey}"></script>

</body>
</html>