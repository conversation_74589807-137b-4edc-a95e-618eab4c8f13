<!--
/**
 * 支付订单 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2023-07-04 15:51:27
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
	<title th:text="${lang.translate('支付订单')}">支付订单</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="opacity:0">


         <!--开始：group 循环-->


        <div class="layui-row form-row" id="default-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column" >

                <!-- text_input : 主键 ,  id -->
                <div class="layui-form-item" style="display: none"inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('主键')}">主键</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="id" id="id" name="id" th:placeholder="${ lang.translate('请输入'+'主键') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- radio_box : 支付状态 ,  status  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('支付状态')}">支付状态</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input input-type="radio" type="radio" name="status" lay-filter="status" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.common.PayOrderStatusEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==0)}">
                    </div>
                </div>
            
                <!-- text_input : 交易单号 ,  tradeNo -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('交易单号')}">交易单号</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="tradeNo" id="tradeNo" name="tradeNo" th:placeholder="${ lang.translate('请输入'+'交易单号') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 商户编号 ,  merchantId -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('商户编号')}">商户编号</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="merchantId" id="merchantId" name="merchantId" th:placeholder="${ lang.translate('请输入'+'商户编号') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            
                <!-- text_input : 应用编号 ,  appId -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('应用编号')}">应用编号</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="appId" id="appId" name="appId" th:placeholder="${ lang.translate('请输入'+'应用编号') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            
                <!-- text_input : 商户订单 ,  merchantOrderId -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('商户订单')}">商户订单</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="merchantOrderId" id="merchantOrderId" name="merchantOrderId" th:placeholder="${ lang.translate('请输入'+'商户订单') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            
                <!-- text_input : 商品标题 ,  subject -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('商品标题')}">商品标题</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="subject" id="subject" name="subject" th:placeholder="${ lang.translate('请输入'+'商品标题') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            
                <!-- text_area : 商品描述 ,  body  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('商品描述')}">商品描述</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <textarea lay-filter="body" id="body" name="body" th:placeholder="${ lang.translate('请输入'+'商品描述') }" class="layui-textarea" style="height: 150px" ></textarea>
                    </div>
                </div>
            
                <!-- text_area : 异步通知地址 ,  notifyUrl  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('异步通知地址')}">异步通知地址</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <textarea lay-filter="notifyUrl" id="notifyUrl" name="notifyUrl" th:placeholder="${ lang.translate('请输入'+'异步通知地址') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>
            
                <!-- radio_box : 回调状态 ,  notifyStatus  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('回调状态')}">回调状态</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input input-type="radio" type="radio" name="notifyStatus" lay-filter="notifyStatus" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.common.PayTaskNotifyStatusEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==0)}">
                    </div>
                </div>
            
                <!-- text_input : 支付金额 ,  amount -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('支付金额')}">支付金额</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="amount" id="amount" name="amount" th:placeholder="${ lang.translate('请输入'+'支付金额') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            
                <!-- text_input : 用户 ,  userIp -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('用户')}">用户</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="userIp" id="userIp" name="userIp" th:placeholder="${ lang.translate('请输入'+'用户') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            
                <!-- date_input : 订单失效时间 ,  expireTime  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('订单失效时间')}">订单失效时间</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input input-type="date" lay-filter="expireTime" id="expireTime" name="expireTime"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择'+'订单失效时间') }" type="text" class="layui-input"    lay-verify="|required"   />
                    </div>
                </div>
            
                <!-- date_input : 订单支付成功时间 ,  successTime  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('订单支付成功时间')}">订单支付成功时间</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input input-type="date" lay-filter="successTime" id="successTime" name="successTime"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择'+'订单支付成功时间') }" type="text" class="layui-input"    lay-verify=""   />
                    </div>
                </div>
            
                <!-- date_input : 订单支付通知时间 ,  notifyTime  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('订单支付通知时间')}">订单支付通知时间</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input input-type="date" lay-filter="notifyTime" id="notifyTime" name="notifyTime"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择'+'订单支付通知时间') }" type="text" class="layui-input"    lay-verify=""   />
                    </div>
                </div>
            
                <!-- text_input : 支付成功的订单拓展单编号 ,  successExtensionId -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('支付成功的订单拓展单编号')}">支付成功的订单拓展单编号</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="successExtensionId" id="successExtensionId" name="successExtensionId" th:placeholder="${ lang.translate('请输入'+'支付成功的订单拓展单编号') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- radio_box : 退款状态 ,  refundStatus  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('退款状态')}">退款状态</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input input-type="radio" type="radio" name="refundStatus" lay-filter="refundStatus" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.common.PayRefundStatusEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==0)}">
                    </div>
                </div>
            
                <!-- number_input : 退款次数 ,  refundTimes  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('退款次数')}">退款次数</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="refundTimes" id="refundTimes" name="refundTimes" th:placeholder="${ lang.translate('请输入'+'退款次数') }" type="text" class="layui-input"    lay-verify="|required"   autocomplete="off" input-type="number_input" integer="true" decimal="false" allow-negative="true" step="1.0"   scale="0" />
                    </div>
                </div>
            
                <!-- text_input : 退款总金额 ,  refundAmount -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('退款总金额')}">退款总金额</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="refundAmount" id="refundAmount" name="refundAmount" th:placeholder="${ lang.translate('请输入'+'退款总金额') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            
                <!-- text_input : 渠道编号 ,  channelId -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('渠道编号')}">渠道编号</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="channelId" id="channelId" name="channelId" th:placeholder="${ lang.translate('请输入'+'渠道编号') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 渠道编码 ,  channelCode -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('渠道编码')}">渠道编码</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="channelCode" id="channelCode" name="channelCode" th:placeholder="${ lang.translate('请输入'+'渠道编码') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 渠道用户 ,  channelUserId -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('渠道用户')}">渠道用户</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="channelUserId" id="channelUserId" name="channelUserId" th:placeholder="${ lang.translate('请输入'+'渠道用户') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- number_input : 渠道手续费 ,  channelFeeRate  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('渠道手续费')}">渠道手续费</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="channelFeeRate" id="channelFeeRate" name="channelFeeRate" th:placeholder="${ lang.translate('请输入'+'渠道手续费') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />
                    </div>
                </div>
            
                <!-- text_input : 渠道手续金额 ,  channelFeeAmount -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('渠道手续金额')}">渠道手续金额</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="channelFeeAmount" id="channelFeeAmount" name="channelFeeAmount" th:placeholder="${ lang.translate('请输入'+'渠道手续金额') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 渠道订单 ,  channelOrderNo -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('渠道订单')}">渠道订单</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="channelOrderNo" id="channelOrderNo" name="channelOrderNo" th:placeholder="${ lang.translate('请输入'+'渠道订单') }" type="text" class="layui-input"  />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 100px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('sys_pay_order:create','sys_pay_order:update','sys_pay_order:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var RADIO_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.PayOrderStatusEnum')}]];
    var RADIO_NOTIFYSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.PayTaskNotifyStatusEnum')}]];
    var RADIO_REFUNDSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.PayRefundStatusEnum')}]];
    var VALIDATE_CONFIG={"amount":{"labelInForm":"支付金额","inputType":"text_input","required":true},"subject":{"labelInForm":"商品标题","inputType":"text_input","required":true},"notifyTime":{"date":true,"labelInForm":"订单支付通知时间","inputType":"date_input"},"notifyStatus":{"labelInForm":"回调状态","inputType":"radio_box","required":true},"refundStatus":{"labelInForm":"退款状态","inputType":"radio_box","required":true},"merchantOrderId":{"labelInForm":"商户订单","inputType":"text_input","required":true},"body":{"labelInForm":"商品描述","inputType":"text_area","required":true},"refundTimes":{"labelInForm":"退款次数","inputType":"number_input","required":true},"expireTime":{"date":true,"labelInForm":"订单失效时间","inputType":"date_input","required":true},"merchantId":{"labelInForm":"商户编号","inputType":"text_input","required":true},"appId":{"labelInForm":"应用编号","inputType":"text_input","required":true},"successTime":{"date":true,"labelInForm":"订单支付成功时间","inputType":"date_input"},"notifyUrl":{"labelInForm":"异步通知地址","inputType":"text_area","required":true},"userIp":{"labelInForm":"用户","inputType":"text_input","required":true},"status":{"labelInForm":"支付状态","inputType":"radio_box","required":true},"refundAmount":{"labelInForm":"退款总金额","inputType":"text_input","required":true}};
    var AUTH_PREFIX="sys_pay_order";


</script>



<script th:src="'/business/common/pay_order/pay_order_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/common/pay_order/pay_order_form.js?'+${cacheKey}"></script>

</body>
</html>