var N=(f,E,g)=>new Promise((S,y)=>{var I=r=>{try{o(g.next(r))}catch(p){y(p)}},c=r=>{try{o(g.throw(r))}catch(p){y(p)}},o=r=>r.done?S(r.value):Promise.resolve(r.value).then(I,c);o((g=g.apply(f,E)).next())});import{d as ue,l as O,az as h,L as _e,r as s,o as u,c as k,e,w as t,u as a,q as b,a5 as V,Q as pe,f as _,t as R,cl as U,R as me,E as w,cP as fe,F as D,s as H,G as F,a9 as ge,cQ as ve,A as Ce,B as be,aa as ye,j as he}from"./index-bb2cbf17.js";import{F as P}from"./fileTypeEnum-21359a08.js";import{u as ke,E as m}from"./chartEditStore-55fbe93c.js";/* empty css                                                                      */import{l as we}from"./index-56351f34.js";import{i as Y}from"./icon-f36697ff.js";import{S as Ee}from"./StylesSetting-d5a665a6.js";import"./plugin-3ef0fcec.js";import"./SettingItem-7fe1cfec.js";import"./SettingItemBox-500aaf18.js";import"./CollapseItem.vue_vue_type_script_setup_true_lang-9b6d8958.js";import"./logo-aa8b8747.js";const Se="/static/png/noImage.png",Ie=f=>(Ce("data-v-bcb3ba8f"),f=f(),be(),f),Le={class:"go-canvas-setting"},ze={class:"upload-box"},Te=["src"],xe={class:"upload-img"},Oe=Ie(()=>b("img",{src:Se},null,-1)),Re={class:"picker-height"},Ue=ue({__name:"index",setup(f){const{ColorPaletteIcon:E}=Y.ionicons5,{ScaleIcon:g,FitToScreenIcon:S,FitToHeightIcon:y,FitToWidthIcon:I}=Y.carbon,c=ke(),o=c.getEditCanvasConfig,r=c.getEditCanvas,p=O(),L=O(!1),z=O(0),$=we(()=>ye(()=>import("./index-02f5acf3.js"),["static/js/index-02f5acf3.js","static/js/index-bb2cbf17.js","static/css/index-a8adda2b.css","static/js/chartEditStore-55fbe93c.js","static/js/plugin-3ef0fcec.js","static/js/icon-f36697ff.js","static/js/index-56351f34.js","static/css/index-83eadabc.css","static/css/index-39f06c17.css"])),M=[{label:"应用颜色",value:0},{label:"应用背景",value:1}],X=[{key:"ChartTheme",title:"主题颜色",icon:E,render:$}],K=[{key:h.FIT,title:"自适应",icon:g,desc:"自适应比例展示，页面会有留白"},{key:h.SCROLL_Y,title:"Y轴滚动",icon:I,desc:"X轴铺满，Y轴自适应滚动"},{key:h.SCROLL_X,title:"X轴滚动",icon:y,desc:"Y轴铺满，X轴自适应滚动"},{key:h.FULL,title:"铺满",icon:S,desc:"强行拉伸画面，填充所有视图"}];_e(()=>o.selectColor,l=>{z.value=l?0:1},{immediate:!0});const A=l=>l>50,B=()=>{c.computedScale()},j=i=>N(this,[i],function*({file:l}){p.value=[];const d=l.file.type;return l.file.size>1024*1024*U?(window.$message.warning(`图片超出 ${U}M 限制，请重新上传！`),!1):d!==P.PNG&&d!==P.JPEG&&d!==P.GIF?(window.$message.warning("文件格式不符合，请重新上传！"),!1):!0}),q=l=>{o.selectColor=l==0},Q=()=>{c.setEditCanvasConfig(m.BACKGROUND_IMAGE,void 0),c.setEditCanvasConfig(m.SELECT_COLOR,!0)},W=()=>{L.value=!0,setTimeout(()=>{L.value=!1})},J=()=>{c.setEditCanvasConfig(m.BACKGROUND,void 0),o.backgroundImage&&c.setEditCanvasConfig(m.SELECT_COLOR,!1),W()},Z=l=>{const{file:i}=l;ge(()=>{if(i.file){const d=ve(i.file);c.setEditCanvasConfig(m.BACKGROUND_IMAGE,d),c.setEditCanvasConfig(m.SELECT_COLOR,!1)}else window.$message.error("添加图片失败，请稍后重试！")})},ee=l=>{c.setEditCanvasConfig(m.PREVIEW_SCALE_TYPE,l)};return(l,i)=>{const d=s("n-input-number"),T=s("n-form-item"),te=s("n-form"),C=s("n-text"),oe=s("n-upload-dragger"),ne=s("n-upload"),ae=s("n-color-picker"),v=s("n-space"),se=s("n-select"),x=s("n-button"),G=s("n-icon"),le=s("n-tooltip"),ie=s("n-button-group"),ce=s("n-divider"),re=s("n-tab-pane"),de=s("n-tabs");return u(),k("div",Le,[e(te,{inline:"","label-width":45,size:"small","label-placement":"left"},{default:t(()=>[e(T,{label:"宽度"},{default:t(()=>[e(d,{size:"small",value:a(o).width,"onUpdate:value":[i[0]||(i[0]=n=>a(o).width=n),B],disabled:a(r).lockScale,validator:A},null,8,["value","disabled"])]),_:1}),e(T,{label:"高度"},{default:t(()=>[e(d,{size:"small",value:a(o).height,"onUpdate:value":[i[1]||(i[1]=n=>a(o).height=n),B],disabled:a(r).lockScale,validator:A},null,8,["value","disabled"])]),_:1})]),_:1}),b("div",ze,[e(ne,{"file-list":p.value,"onUpdate:fileList":i[2]||(i[2]=n=>p.value=n),"show-file-list":!1,customRequest:Z,onBeforeUpload:j},{default:t(()=>[e(oe,null,{default:t(()=>[a(o).backgroundImage?(u(),k("img",{key:0,class:"upload-show",src:a(o).backgroundImage,alt:"背景"},null,8,Te)):V("",!0),pe(b("div",xe,[Oe,e(C,{class:"upload-desc",depth:"3"},{default:t(()=>[_(" 背景图需小于 "+R(a(U))+"M ，格式为 png/jpg/gif 的文件 ",1)]),_:1})],512),[[me,!a(o).backgroundImage]])]),_:1})]),_:1},8,["file-list"])]),e(v,{vertical:"",size:12},{default:t(()=>[e(v,null,{default:t(()=>[e(C,null,{default:t(()=>[_("背景颜色")]),_:1}),b("div",Re,[L.value?V("",!0):(u(),w(ae,{key:0,size:"small",style:{width:"250px"},value:a(o).background,"onUpdate:value":i[3]||(i[3]=n=>a(o).background=n),showPreview:!0,swatches:a(fe)},null,8,["value","swatches"]))])]),_:1}),e(v,null,{default:t(()=>[e(C,null,{default:t(()=>[_("应用类型")]),_:1}),e(se,{size:"small",style:{width:"250px"},value:z.value,"onUpdate:value":[i[4]||(i[4]=n=>z.value=n),q],disabled:!a(o).backgroundImage,options:M},null,8,["value","disabled"])]),_:1}),e(v,null,{default:t(()=>[e(C,null,{default:t(()=>[_("背景控制")]),_:1}),e(x,{class:"clear-btn",size:"small",disabled:!a(o).backgroundImage,onClick:Q},{default:t(()=>[_(" 清除背景 ")]),_:1},8,["disabled"]),e(x,{class:"clear-btn",size:"small",disabled:!a(o).background,onClick:J},{default:t(()=>[_(" 清除颜色 ")]),_:1},8,["disabled"])]),_:1}),e(v,null,{default:t(()=>[e(C,null,{default:t(()=>[_("适配方式")]),_:1}),e(ie,null,{default:t(()=>[(u(),k(D,null,H(K,n=>e(x,{key:n.key,type:a(o).previewScaleType===n.key?"primary":"tertiary",ghost:"",size:"small",onClick:Fe=>ee(n.key)},{default:t(()=>[e(le,{"show-arrow":!1,trigger:"hover"},{trigger:t(()=>[e(G,{class:"select-preview-icon",size:"18"},{default:t(()=>[(u(),w(F(n.icon)))]),_:2},1024)]),default:t(()=>[_(" "+R(n.desc),1)]),_:2},1024)]),_:2},1032,["type","onClick"])),64))]),_:1})]),_:1})]),_:1}),e(a(Ee),{isCanvas:!0,chartStyles:a(o)},null,8,["chartStyles"]),e(ce,{style:{margin:"10px 0"}}),e(de,{class:"tabs-box",size:"small",type:"segment"},{default:t(()=>[(u(),k(D,null,H(X,n=>e(re,{key:n.key,name:n.key,size:"small","display-directive":"show:lazy"},{tab:t(()=>[e(v,null,{default:t(()=>[b("span",null,R(n.title),1),e(G,{size:"16",class:"icon-position"},{default:t(()=>[(u(),w(F(n.icon)))]),_:2},1024)]),_:2},1024)]),default:t(()=>[(u(),w(F(n.render)))]),_:2},1032,["name"])),64))]),_:1})])}}});const je=he(Ue,[["__scopeId","data-v-bcb3ba8f"]]);export{je as default};
