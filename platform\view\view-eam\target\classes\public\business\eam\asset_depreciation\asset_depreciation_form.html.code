<!--
/**
 * 折旧方案 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2023-10-18 06:46:27
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
	<title th:text="${lang.translate('折旧方案')}">折旧方案</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="opacity:0">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-3614-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <!-- select_box : 编码 ,  code  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('编码')}">编码</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="code" input-type="select" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetDepreciationCodeEnum')}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- text_input : 折旧方案 ,  name -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('折旧方案')}">折旧方案</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="name" id="name" name="name" th:placeholder="${ lang.translate('请输入'+'折旧方案') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            
                <!-- select_box : 状态 ,  status  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('状态')}">状态</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="status" input-type="select" th:data="${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- number_input : 预计残值率 ,  preResidualRate  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('预计残值率')}">预计残值率</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="preResidualRate" id="preResidualRate" name="preResidualRate" th:placeholder="${ lang.translate('请输入'+'预计残值率') }" type="text" class="layui-input"    lay-verify="|required"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0"  value="0.0" />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <!-- select_box : 残值选择 ,  residualValueSelect  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('残值选择')}">残值选择</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="residualValueSelect" input-type="select" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetDepreciationResidualValueSelectEnum')}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- select_box : 折旧方式 ,  method  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('折旧方式')}">折旧方式</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="method" input-type="select" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetDepreciationMethodEnum')}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- select_box : 首次折旧方式 ,  firstDepreciationMethod  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('首次折旧方式')}">首次折旧方式</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="firstDepreciationMethod" input-type="select" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetFirstDepreciationMethodTypeEnum')}" extraParam="{}"></div>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <!-- select_box : 资产分类 ,  categoryIds  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('资产分类')}">资产分类</div></div>
                    <div class="layui-input-block ">
                        <div id="categoryIds" input-type="select" th:data="${'/service-pcm/pcm-catalog/query-nodes'}" extraParam="{}"></div>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-3430-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >

                <!-- text_area : 备注 ,  notes  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入'+'备注') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 20px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_asset_depreciation:create','eam_asset_depreciation:update','eam_asset_depreciation:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var SELECT_CODE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetDepreciationCodeEnum')}]];
    var SELECT_METHOD_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetDepreciationMethodEnum')}]];
    var SELECT_RESIDUALVALUESELECT_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetDepreciationResidualValueSelectEnum')}]];
    var SELECT_FIRSTDEPRECIATIONMETHOD_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetFirstDepreciationMethodTypeEnum')}]];
    var VALIDATE_CONFIG={"code":{"labelInForm":"编码","inputType":"select_box","required":true},"method":{"labelInForm":"折旧方式","inputType":"select_box","required":true},"name":{"labelInForm":"折旧方案","inputType":"text_input","required":true},"preResidualRate":{"labelInForm":"预计残值率","inputType":"number_input","required":true},"residualValueSelect":{"labelInForm":"残值选择","inputType":"select_box","required":true},"firstDepreciationMethod":{"labelInForm":"首次折旧方式","inputType":"select_box","required":true},"status":{"labelInForm":"状态","inputType":"select_box","required":true}};
    var AUTH_PREFIX="eam_asset_depreciation";

    // ASSET_CATEGORY_DATA
    var ASSET_CATEGORY_DATA = [[${assetCategoryData}]] ;

</script>



<script th:src="'/business/eam/asset_depreciation/asset_depreciation_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset_depreciation/asset_depreciation_form.js?'+${cacheKey}"></script>

</body>
</html>