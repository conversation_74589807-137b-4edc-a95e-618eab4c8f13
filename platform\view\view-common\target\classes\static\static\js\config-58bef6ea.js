var l=Object.defineProperty,f=Object.defineProperties;var c=Object.getOwnPropertyDescriptors;var a=Object.getOwnPropertySymbols;var g=Object.prototype.hasOwnProperty,C=Object.prototype.propertyIsEnumerable;var p=(t,o,i)=>o in t?l(t,o,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[o]=i,m=(t,o)=>{for(var i in o||(o={}))g.call(o,i)&&p(t,i,o[i]);if(a)for(var i of a(o))C.call(o,i)&&p(t,i,o[i]);return t},e=(t,o)=>f(t,c(o));var r=(t,o,i)=>(p(t,typeof o!="symbol"?o+"":o,i),i);import{aM as _,a8 as n}from"./index-bb2cbf17.js";import{d}from"./chartEditStore-55fbe93c.js";import{i as s}from"./index-0ec04aee.js";import"./plugin-3ef0fcec.js";import"./icon-f36697ff.js";import"./SettingItem-7fe1cfec.js";/* empty css                                                                      */import"./SettingItemBox-500aaf18.js";import"./CollapseItem.vue_vue_type_script_setup_true_lang-9b6d8958.js";import"./index.esm.min-f1367f57.js";import"./http-36f53bd1.js";import"./lodash-d17632fd.js";import"./fileTypeEnum-21359a08.js";const h={color_type:1,o_color:"#0a7ae2",i_color:"#119bfa",line_class:"svg_ani_flow"};class q extends d{constructor(){super(...arguments);r(this,"key",s.key);r(this,"attr",e(m({},_),{w:15,h:500,zIndex:-1}));r(this,"chartConfig",n(s));r(this,"option",n(h))}}export{q as default,h as option};
