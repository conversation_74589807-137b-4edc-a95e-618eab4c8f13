var c=Object.defineProperty,l=Object.defineProperties;var u=Object.getOwnPropertyDescriptors;var e=Object.getOwnPropertySymbols;var C=Object.prototype.hasOwnProperty,E=Object.prototype.propertyIsEnumerable;var n=(i,t,o)=>t in i?c(i,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):i[t]=o,r=(i,t)=>{for(var o in t||(t={}))C.call(t,o)&&n(i,o,t[o]);if(e)for(var o of e(t))E.call(t,o)&&n(i,o,t[o]);return i},s=(i,t)=>l(i,u(t));var a=(i,t,o)=>(n(i,typeof t!="symbol"?t+"":t,o),o);import{k as m,l as T,m as d,n as D,D as A,o as p}from"./index-0ec04aee.js";import{aM as y,a8 as f,cy as I}from"./index-bb2cbf17.js";import{d as g}from"./chartEditStore-55fbe93c.js";import"./SettingItem-7fe1cfec.js";/* empty css                                                                      */import"./SettingItemBox-500aaf18.js";import"./CollapseItem.vue_vue_type_script_setup_true_lang-9b6d8958.js";import"./icon-f36697ff.js";import"./index.esm.min-f1367f57.js";import"./http-36f53bd1.js";import"./lodash-d17632fd.js";import"./plugin-3ef0fcec.js";import"./fileTypeEnum-21359a08.js";const N={[I]:d.DATE,isPanel:0,dataset:D().valueOf(),defaultType:A.STATIC,differUnit:[p.DAY,p.DAY],differValue:[0,0]};class z extends g{constructor(){super(...arguments);a(this,"key",m.key);a(this,"attr",s(r({},y),{w:260,h:32,zIndex:-1}));a(this,"chartConfig",f(m));a(this,"interactActions",T);a(this,"option",f(N))}}export{z as default,N as option};
