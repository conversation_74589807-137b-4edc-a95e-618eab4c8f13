var m=Object.defineProperty;var f=(r,o,i)=>o in r?m(r,o,{enumerable:!0,configurable:!0,writable:!0,value:i}):r[o]=i;var t=(r,o,i)=>(f(r,typeof o!="symbol"?o+"":o,i),i);import{a8 as e}from"./index-bb2cbf17.js";import{d as a}from"./chartEditStore-55fbe93c.js";import{N as p}from"./index-0ec04aee.js";import"./plugin-3ef0fcec.js";import"./icon-f36697ff.js";import"./SettingItem-7fe1cfec.js";/* empty css                                                                      */import"./SettingItemBox-500aaf18.js";import"./CollapseItem.vue_vue_type_script_setup_true_lang-9b6d8958.js";import"./index.esm.min-f1367f57.js";import"./http-36f53bd1.js";import"./lodash-d17632fd.js";import"./fileTypeEnum-21359a08.js";const s={dataset:1e5,from:0,dur:3,precision:0,showSeparator:!0,numberSize:34,numberColor:"#4a9ef8",prefixText:"￥",prefixColor:"#4a9ef8",suffixText:"元",suffixColor:"#4a9ef8"};class T extends a{constructor(){super(...arguments);t(this,"key",p.key);t(this,"chartConfig",e(p));t(this,"option",e(s))}}export{T as default,s as option};
