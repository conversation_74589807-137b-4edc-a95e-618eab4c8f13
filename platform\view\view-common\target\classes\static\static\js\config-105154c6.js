var i=Object.defineProperty;var n=(e,a,o)=>a in e?i(e,a,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[a]=o;var t=(e,a,o)=>(n(e,typeof a!="symbol"?a+"":a,o),o);import{a8 as r}from"./index-bb2cbf17.js";import{d as l}from"./chartEditStore-55fbe93c.js";import{H as m}from"./index-0ec04aee.js";import"./plugin-3ef0fcec.js";import"./icon-f36697ff.js";import"./SettingItem-7fe1cfec.js";/* empty css                                                                      */import"./SettingItemBox-500aaf18.js";import"./CollapseItem.vue_vue_type_script_setup_true_lang-9b6d8958.js";import"./index.esm.min-f1367f57.js";import"./http-36f53bd1.js";import"./lodash-d17632fd.js";import"./fileTypeEnum-21359a08.js";const u=[{name:"荣成",value:26700},{name:"河南",value:20700},{name:"河北",value:18700},{name:"徐州",value:17800},{name:"漯河",value:16756},{name:"三门峡",value:12343},{name:"郑州",value:9822},{name:"周口",value:8912},{name:"濮阳",value:6834},{name:"信阳",value:5875},{name:"新乡",value:3832},{name:"大同",value:1811}],p={dataset:u,rowNum:5,waitTime:2,unit:"",sort:!0,color:"#1370fb",textColor:"#CDD2F8FF",borderColor:"#1370fb80",carousel:"single",indexFontSize:12,leftFontSize:12,rightFontSize:12,valueFormatter(e){return e.value}};class k extends l{constructor(){super(...arguments);t(this,"key",m.key);t(this,"chartConfig",r(m));t(this,"option",r(p))}}export{k as default,p as option};
