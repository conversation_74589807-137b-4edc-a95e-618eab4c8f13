var je;const ke="en";let Be=!1,Ie=!1,Ee=!1,B1=!1,mt=!1,gt=!1,xe,De=ke,hn,J;const V=typeof self=="object"?self:typeof global=="object"?global:{};let I;typeof V.vscode!="undefined"&&typeof V.vscode.process!="undefined"?I=V.vscode.process:typeof process!="undefined"&&(I=process);const dn=typeof((je=I==null?void 0:I.versions)===null||je===void 0?void 0:je.electron)=="string",mn=dn&&(I==null?void 0:I.type)==="renderer";if(typeof navigator=="object"&&!mn)J=navigator.userAgent,Be=J.indexOf("Windows")>=0,Ie=J.indexOf("Macintosh")>=0,gt=(J.indexOf("Macintosh")>=0||J.indexOf("iPad")>=0||J.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0,Ee=J.indexOf("Linux")>=0,mt=!0,xe=navigator.language,De=xe;else if(typeof I=="object"){Be=I.platform==="win32",Ie=I.platform==="darwin",Ee=I.platform==="linux",Ee&&I.env.SNAP&&I.env.SNAP_REVISION,I.env.CI||I.env.BUILD_ARTIFACTSTAGINGDIRECTORY,xe=ke,De=ke;const e=I.env.VSCODE_NLS_CONFIG;if(e)try{const t=JSON.parse(e),n=t.availableLanguages["*"];xe=t.locale,De=n||ke,hn=t._translationsConfigFile}catch(t){}B1=!0}else console.error("Unable to resolve platform.");const _e=Be,gn=Ie,gs=Ee,bs=B1,bn=mt,ws=mt&&typeof V.importScripts=="function",Cs=gt,j=J,_s=De,ps=(()=>{if(typeof V.postMessage=="function"&&!V.importScripts){let e=[];V.addEventListener("message",n=>{if(n.data&&n.data.vscodeScheduleAsyncWork)for(let s=0,i=e.length;s<i;s++){const a=e[s];if(a.id===n.data.vscodeScheduleAsyncWork){e.splice(s,1),a.callback();return}}});let t=0;return n=>{const s=++t;e.push({id:s,callback:n}),V.postMessage({vscodeScheduleAsyncWork:s},"*")}}return e=>setTimeout(e)})(),Ls=Ie||gt?2:Be?1:3;let At=!0,yt=!1;function Ns(){if(!yt){yt=!0;const e=new Uint8Array(2);e[0]=1,e[1]=2,At=new Uint16Array(e.buffer)[0]===512+1}return At}const wn=!!(j&&j.indexOf("Chrome")>=0),Ss=!!(j&&j.indexOf("Firefox")>=0),vs=!!(!wn&&j&&j.indexOf("Safari")>=0),As=!!(j&&j.indexOf("Edg/")>=0);j&&j.indexOf("Android")>=0;const Cn="`~!@#$%^&*()-=+[{]}\\|;:'\",.<>/?";function _n(e=""){let t="(-?\\d*\\.\\d\\w*)|([^";for(const n of Cn)e.indexOf(n)>=0||(t+="\\"+n);return t+="\\s]+)",new RegExp(t,"g")}const I1=_n();function pn(e){let t=I1;if(e&&e instanceof RegExp)if(e.global)t=e;else{let n="g";e.ignoreCase&&(n+="i"),e.multiline&&(n+="m"),e.unicode&&(n+="u"),t=new RegExp(e.source,n)}return t.lastIndex=0,t}const Ln={maxLen:1e3,windowSize:15,timeBudget:150};function bt(e,t,n,s,i=Ln){if(n.length>i.maxLen){let u=e-i.maxLen/2;return u<0?u=0:s+=u,n=n.substring(u,e+i.maxLen/2),bt(e,t,n,s,i)}const a=Date.now(),o=e-1-s;let l=-1,c=null;for(let u=1;!(Date.now()-a>=i.timeBudget);u++){const h=o-i.windowSize*u;t.lastIndex=Math.max(0,h);const f=Nn(t,n,o,l);if(!f&&c||(c=f,h<=0))break;l=h}if(c){const u={word:c[0],startColumn:s+1+c.index,endColumn:s+1+c.index+c[0].length};return t.lastIndex=0,u}return null}function Nn(e,t,n,s){let i;for(;i=e.exec(t);){const a=i.index||0;if(a<=n&&e.lastIndex>=n)return i;if(s>0&&a>s)return null}return null}function ys(e,t=0){return e[e.length-(1+t)]}function Ms(e){if(e.length===0)throw new Error("Invalid tail call");return[e.slice(0,e.length-1),e[e.length-1]]}function xs(e,t,n=(s,i)=>s===i){if(e===t)return!0;if(!e||!t||e.length!==t.length)return!1;for(let s=0,i=e.length;s<i;s++)if(!n(e[s],t[s]))return!1;return!0}function Fs(e,t,n){let s=0,i=e.length-1;for(;s<=i;){const a=(s+i)/2|0,o=n(e[a],t);if(o<0)s=a+1;else if(o>0)i=a-1;else return a}return-(s+1)}function ks(e,t){let n=0,s=e.length;if(s===0)return 0;for(;n<s;){const i=Math.floor((n+s)/2);t(e[i])?s=i:n=i+1}return n}function Mt(e,t,n){if(e=e|0,e>=t.length)throw new TypeError("invalid index");let s=t[Math.floor(t.length*Math.random())],i=[],a=[],o=[];for(let l of t){const c=n(l,s);c<0?i.push(l):c>0?a.push(l):o.push(l)}return e<i.length?Mt(e,i,n):e<i.length+o.length?o[0]:Mt(e-(i.length+o.length),a,n)}function Es(e,t){const n=[];let s;for(const i of e.slice(0).sort(t))!s||t(s[0],i)!==0?(s=[i],n.push(s)):s.push(i);return n}function Ds(e){return e.filter(t=>!!t)}function Rs(e){return!Array.isArray(e)||e.length===0}function Ps(e){return Array.isArray(e)&&e.length>0}function Vs(e,t=n=>n){const n=new Set;return e.filter(s=>{const i=t(s);return n.has(i)?!1:(n.add(i),!0)})}function Bs(e,t){const n=Sn(e,t);if(n!==-1)return e[n]}function Sn(e,t){for(let n=e.length-1;n>=0;n--){const s=e[n];if(t(s))return n}return-1}function Is(e,t){return e.length>0?e[0]:t}function Us(e){return[].concat(...e)}function Ts(e,t){let n=typeof t=="number"?e:0;typeof t=="number"?n=e:(n=0,t=e);const s=[];if(n<=t)for(let i=n;i<t;i++)s.push(i);else for(let i=n;i>t;i--)s.push(i);return s}function vn(e,t,n){const s=e.slice(0,t),i=e.slice(t);return s.concat(n,i)}function Ws(e,t){const n=e.indexOf(t);n>-1&&(e.splice(n,1),e.unshift(t))}function qs(e,t){const n=e.indexOf(t);n>-1&&(e.splice(n,1),e.push(t))}function Hs(e){return Array.isArray(e)?e:[e]}function An(e,t,n){const s=U1(e,t),i=e.length,a=n.length;e.length=i+a;for(let o=i-1;o>=s;o--)e[o+a]=e[o];for(let o=0;o<a;o++)e[o+s]=n[o]}function zs(e,t,n,s){const i=U1(e,t),a=e.splice(i,n);return An(e,i,s),a}function U1(e,t){return t<0?Math.max(t+e.length,0):Math.min(t,e.length)}function Os(e,t){return(n,s)=>t(e(n),e(s))}const $s=(e,t)=>e-t;function yn(e,t){if(e.length===0)return;let n=e[0];for(let s=1;s<e.length;s++){const i=e[s];t(i,n)>0&&(n=i)}return n}function Gs(e,t){if(e.length===0)return;let n=e[0];for(let s=1;s<e.length;s++){const i=e[s];t(i,n)>=0&&(n=i)}return n}function js(e,t){return yn(e,(n,s)=>-t(n,s))}class Qs{constructor(t){this.items=t,this.firstIdx=0,this.lastIdx=this.items.length-1}takeWhile(t){let n=this.firstIdx;for(;n<this.items.length&&t(this.items[n]);)n++;const s=n===this.firstIdx?null:this.items.slice(this.firstIdx,n);return this.firstIdx=n,s}takeFromEndWhile(t){let n=this.lastIdx;for(;n>=0&&t(this.items[n]);)n--;const s=n===this.lastIdx?null:this.items.slice(n+1,this.lastIdx+1);return this.lastIdx=n,s}peek(){return this.items[this.firstIdx]}dequeue(){const t=this.items[this.firstIdx];return this.firstIdx++,t}takeCount(t){const n=this.items.slice(this.firstIdx,this.firstIdx+t);return this.firstIdx+=t,n}}function Mn(e){return Array.isArray(e)}function xn(e){return typeof e=="string"}function be(e){return typeof e=="object"&&e!==null&&!Array.isArray(e)&&!(e instanceof RegExp)&&!(e instanceof Date)}function Zs(e){return typeof e=="number"&&!isNaN(e)}function Ys(e){return!!e&&typeof e[Symbol.iterator]=="function"}function Js(e){return e===!0||e===!1}function Fn(e){return typeof e=="undefined"}function Xs(e){return!Oe(e)}function Oe(e){return Fn(e)||e===null}function Ks(e,t){if(!e)throw new Error(t?`Unexpected type, expected '${t}'`:"Unexpected type")}function ei(e){if(Oe(e))throw new Error("Assertion Failed: argument is undefined or null");return e}function kn(e){return typeof e=="function"}function ti(e,t){const n=Math.min(e.length,t.length);for(let s=0;s<n;s++)En(e[s],t[s])}function En(e,t){if(xn(t)){if(typeof e!==t)throw new Error(`argument does not match constraint: typeof ${t}`)}else if(kn(t)){try{if(e instanceof t)return}catch(n){}if(!Oe(e)&&e.constructor===t||t.length===1&&t.call(void 0,e)===!0)return;throw new Error("argument does not match one of these constraints: arg instanceof constraint, arg.constructor === constraint, nor constraint(arg) === true")}}function Dn(e){let t=[],n=Object.getPrototypeOf(e);for(;Object.prototype!==n;)t=t.concat(Object.getOwnPropertyNames(n)),n=Object.getPrototypeOf(n);return t}function T1(e){const t=[];for(const n of Dn(e))typeof e[n]=="function"&&t.push(n);return t}function Rn(e,t){const n=i=>function(){const a=Array.prototype.slice.call(arguments,0);return t(i,a)};let s={};for(const i of e)s[i]=n(i);return s}function ni(e){return e===null?void 0:e}function Pn(e,t="Unreachable"){throw new Error(t)}function Vn(e){if(!e||typeof e!="object"||e instanceof RegExp)return e;const t=Array.isArray(e)?[]:{};return Object.keys(e).forEach(n=>{e[n]&&typeof e[n]=="object"?t[n]=Vn(e[n]):t[n]=e[n]}),t}function ri(e){if(!e||typeof e!="object")return e;const t=[e];for(;t.length>0;){const n=t.shift();Object.freeze(n);for(const s in n)if(W1.call(n,s)){const i=n[s];typeof i=="object"&&!Object.isFrozen(i)&&t.push(i)}}return e}const W1=Object.prototype.hasOwnProperty;function si(e,t){return Ye(e,t,new Set)}function Ye(e,t,n){if(Oe(e))return e;const s=t(e);if(typeof s!="undefined")return s;if(Mn(e)){const i=[];for(const a of e)i.push(Ye(a,t,n));return i}if(be(e)){if(n.has(e))throw new Error("Cannot clone recursive data-structure");n.add(e);const i={};for(let a in e)W1.call(e,a)&&(i[a]=Ye(e[a],t,n));return n.delete(e),i}return e}function Bn(e,t,n=!0){return be(e)?(be(t)&&Object.keys(t).forEach(s=>{s in e?n&&(be(e[s])&&be(t[s])?Bn(e[s],t[s],n):e[s]=t[s]):e[s]=t[s]}),e):t}function Re(e,t){if(e===t)return!0;if(e==null||t===null||t===void 0||typeof e!=typeof t||typeof e!="object"||Array.isArray(e)!==Array.isArray(t))return!1;let n,s;if(Array.isArray(e)){if(e.length!==t.length)return!1;for(n=0;n<e.length;n++)if(!Re(e[n],t[n]))return!1}else{const i=[];for(s in e)i.push(s);i.sort();const a=[];for(s in t)a.push(s);if(a.sort(),!Re(i,a))return!1;for(n=0;n<i.length;n++)if(!Re(e[i[n]],t[i[n]]))return!1}return!0}function ii(e,t,n){const s=t(e);return typeof s=="undefined"?n:s}class In{constructor(){this.listeners=[],this.unexpectedErrorHandler=function(t){setTimeout(()=>{throw t.stack?new Error(t.message+`

`+t.stack):t},0)}}emit(t){this.listeners.forEach(n=>{n(t)})}onUnexpectedError(t){this.unexpectedErrorHandler(t),this.emit(t)}onUnexpectedExternalError(t){this.unexpectedErrorHandler(t)}}const q1=new In;function Un(e){H1(e)||q1.onUnexpectedError(e)}function ai(e){H1(e)||q1.onUnexpectedExternalError(e)}function xt(e){if(e instanceof Error){let{name:t,message:n}=e;const s=e.stacktrace||e.stack;return{$isError:!0,name:t,message:n,stack:s}}return e}const Ue="Canceled";function H1(e){return e instanceof Tn?!0:e instanceof Error&&e.name===Ue&&e.message===Ue}class Tn extends Error{constructor(){super(Ue),this.name=this.message}}function oi(){const e=new Error(Ue);return e.name=e.message,e}function li(e){return e?new Error(`Illegal argument: ${e}`):new Error("Illegal argument")}function ui(e){return e?new Error(`Illegal state: ${e}`):new Error("Illegal state")}class ci extends Error{constructor(t){super("NotSupported"),t&&(this.message=t)}}function Wn(e){const t=this;let n=!1,s;return function(){return n||(n=!0,s=e.apply(t,arguments)),s}}var Je;(function(e){function t(C){return C&&typeof C=="object"&&typeof C[Symbol.iterator]=="function"}e.is=t;const n=Object.freeze([]);function s(){return n}e.empty=s;function*i(C){yield C}e.single=i;function a(C){return C||n}e.from=a;function o(C){return!C||C[Symbol.iterator]().next().done===!0}e.isEmpty=o;function l(C){return C[Symbol.iterator]().next().value}e.first=l;function c(C,p){for(const g of C)if(p(g))return!0;return!1}e.some=c;function u(C,p){for(const g of C)if(p(g))return g}e.find=u;function*h(C,p){for(const g of C)p(g)&&(yield g)}e.filter=h;function*f(C,p){let g=0;for(const m of C)yield p(m,g++)}e.map=f;function*d(...C){for(const p of C)for(const g of p)yield g}e.concat=d;function*w(C){for(const p of C)for(const g of p)yield g}e.concatNested=w;function S(C,p,g){let m=g;for(const L of C)m=p(m,L);return m}e.reduce=S;function*v(C,p,g=C.length){for(p<0&&(p+=C.length),g<0?g+=C.length:g>C.length&&(g=C.length);p<g;p++)yield C[p]}e.slice=v;function M(C,p=Number.POSITIVE_INFINITY){const g=[];if(p===0)return[g,C];const m=C[Symbol.iterator]();for(let L=0;L<p;L++){const _=m.next();if(_.done)return[g,e.empty()];g.push(_.value)}return[g,{[Symbol.iterator](){return m}}]}e.consume=M;function E(C,p,g=(m,L)=>m===L){const m=C[Symbol.iterator](),L=p[Symbol.iterator]();for(;;){const _=m.next(),b=L.next();if(_.done!==b.done)return!1;if(_.done)return!0;if(!g(_.value,b.value))return!1}}e.equals=E})(Je||(Je={}));function fi(e){return e}class qn extends Error{constructor(t){super(`Encountered errors while disposing of store. Errors: [${t.join(", ")}]`),this.errors=t}}function hi(e){return typeof e.dispose=="function"&&e.dispose.length===0}function z1(e){if(Je.is(e)){let t=[];for(const n of e)if(n)try{n.dispose()}catch(s){t.push(s)}if(t.length===1)throw t[0];if(t.length>1)throw new qn(t);return Array.isArray(e)?[]:e}else if(e)return e.dispose(),e}function Hn(...e){return Te(()=>z1(e))}function Te(e){return{dispose:Wn(()=>{e()})}}class he{constructor(){this._toDispose=new Set,this._isDisposed=!1}dispose(){this._isDisposed||(this._isDisposed=!0,this.clear())}get isDisposed(){return this._isDisposed}clear(){try{z1(this._toDispose.values())}finally{this._toDispose.clear()}}add(t){if(!t)return t;if(t===this)throw new Error("Cannot register a disposable on itself!");return this._isDisposed?he.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this._toDispose.add(t),t}}he.DISABLE_DISPOSED_WARNING=!1;class ve{constructor(){this._store=new he,this._store}dispose(){this._store.dispose()}_register(t){if(t===this)throw new Error("Cannot register a disposable on itself!");return this._store.add(t)}}ve.None=Object.freeze({dispose(){}});class di{constructor(){this._isDisposed=!1}get value(){return this._isDisposed?void 0:this._value}set value(t){var n;this._isDisposed||t===this._value||((n=this._value)===null||n===void 0||n.dispose(),this._value=t)}clear(){this.value=void 0}dispose(){var t;this._isDisposed=!0,(t=this._value)===null||t===void 0||t.dispose(),this._value=void 0}clearAndLeak(){const t=this._value;return this._value=void 0,t}}class zn{constructor(){this.dispose=()=>{},this.unset=()=>{},this.isset=()=>!1}set(t){let n=t;return this.unset=()=>n=void 0,this.isset=()=>n!==void 0,this.dispose=()=>{n&&(n(),n=void 0)},this}}class mi{constructor(t){this.object=t}dispose(){}}class D{constructor(t){this.element=t,this.next=D.Undefined,this.prev=D.Undefined}}D.Undefined=new D(void 0);class Xe{constructor(){this._first=D.Undefined,this._last=D.Undefined,this._size=0}get size(){return this._size}isEmpty(){return this._first===D.Undefined}clear(){let t=this._first;for(;t!==D.Undefined;){const n=t.next;t.prev=D.Undefined,t.next=D.Undefined,t=n}this._first=D.Undefined,this._last=D.Undefined,this._size=0}unshift(t){return this._insert(t,!1)}push(t){return this._insert(t,!0)}_insert(t,n){const s=new D(t);if(this._first===D.Undefined)this._first=s,this._last=s;else if(n){const a=this._last;this._last=s,s.prev=a,a.next=s}else{const a=this._first;this._first=s,s.next=a,a.prev=s}this._size+=1;let i=!1;return()=>{i||(i=!0,this._remove(s))}}shift(){if(this._first!==D.Undefined){const t=this._first.element;return this._remove(this._first),t}}pop(){if(this._last!==D.Undefined){const t=this._last.element;return this._remove(this._last),t}}_remove(t){if(t.prev!==D.Undefined&&t.next!==D.Undefined){const n=t.prev;n.next=t.next,t.next.prev=n}else t.prev===D.Undefined&&t.next===D.Undefined?(this._first=D.Undefined,this._last=D.Undefined):t.next===D.Undefined?(this._last=this._last.prev,this._last.next=D.Undefined):t.prev===D.Undefined&&(this._first=this._first.next,this._first.prev=D.Undefined);this._size-=1}*[Symbol.iterator](){let t=this._first;for(;t!==D.Undefined;)yield t.element,t=t.next}}const On=V.performance&&typeof V.performance.now=="function";class $e{constructor(t){this._highResolution=On&&t,this._startTime=this._now(),this._stopTime=-1}static create(t=!0){return new $e(t)}stop(){this._stopTime=this._now()}elapsed(){return this._stopTime!==-1?this._stopTime-this._startTime:this._now()-this._startTime}_now(){return this._highResolution?V.performance.now():Date.now()}}var We;(function(e){e.None=()=>ve.None;function t(g){return(m,L=null,_)=>{let b=!1,N;return N=g(F=>{if(!b)return N?N.dispose():b=!0,m.call(L,F)},null,_),b&&N.dispose(),N}}e.once=t;function n(g,m,L){return c((_,b=null,N)=>g(F=>_.call(b,m(F)),null,N),L)}e.map=n;function s(g,m,L){return c((_,b=null,N)=>g(F=>{m(F),_.call(b,F)},null,N),L)}e.forEach=s;function i(g,m,L){return c((_,b=null,N)=>g(F=>m(F)&&_.call(b,F),null,N),L)}e.filter=i;function a(g){return g}e.signal=a;function o(...g){return(m,L=null,_)=>Hn(...g.map(b=>b(N=>m.call(L,N),null,_)))}e.any=o;function l(g,m,L,_){let b=L;return n(g,N=>(b=m(b,N),b),_)}e.reduce=l;function c(g,m){let L;const _={onFirstListenerAdd(){L=g(b.fire,b)},onLastListenerRemove(){L.dispose()}},b=new $(_);return m&&m.add(b),b.event}function u(g,m,L=100,_=!1,b,N){let F,y,A,G=0;const un={leakWarningThreshold:b,onFirstListenerAdd(){F=g(cn=>{G++,y=m(y,cn),_&&!A&&(Me.fire(y),y=void 0),clearTimeout(A),A=setTimeout(()=>{const fn=y;y=void 0,A=void 0,(!_||G>1)&&Me.fire(fn),G=0},L)})},onLastListenerRemove(){F.dispose()}},Me=new $(un);return N&&N.add(Me),Me.event}e.debounce=u;function h(g,m=(_,b)=>_===b,L){let _=!0,b;return i(g,N=>{const F=_||!m(N,b);return _=!1,b=N,F},L)}e.latch=h;function f(g,m,L){return[e.filter(g,m,L),e.filter(g,_=>!m(_),L)]}e.split=f;function d(g,m=!1,L=[]){let _=L.slice(),b=g(y=>{_?_.push(y):F.fire(y)});const N=()=>{_&&_.forEach(y=>F.fire(y)),_=null},F=new $({onFirstListenerAdd(){b||(b=g(y=>F.fire(y)))},onFirstListenerDidAdd(){_&&(m?setTimeout(N):N())},onLastListenerRemove(){b&&b.dispose(),b=null}});return F.event}e.buffer=d;class w{constructor(m){this.event=m}map(m){return new w(n(this.event,m))}forEach(m){return new w(s(this.event,m))}filter(m){return new w(i(this.event,m))}reduce(m,L){return new w(l(this.event,m,L))}latch(){return new w(h(this.event))}debounce(m,L=100,_=!1,b){return new w(u(this.event,m,L,_,b))}on(m,L,_){return this.event(m,L,_)}once(m,L,_){return t(this.event)(m,L,_)}}function S(g){return new w(g)}e.chain=S;function v(g,m,L=_=>_){const _=(...y)=>F.fire(L(...y)),b=()=>g.on(m,_),N=()=>g.removeListener(m,_),F=new $({onFirstListenerAdd:b,onLastListenerRemove:N});return F.event}e.fromNodeEventEmitter=v;function M(g,m,L=_=>_){const _=(...y)=>F.fire(L(...y)),b=()=>g.addEventListener(m,_),N=()=>g.removeEventListener(m,_),F=new $({onFirstListenerAdd:b,onLastListenerRemove:N});return F.event}e.fromDOMEventEmitter=M;function E(g){return new Promise(m=>t(g)(m))}e.toPromise=E;function C(g,m){return m(void 0),g(L=>m(L))}e.runAndSubscribe=C;function p(g,m){let L=null;function _(N){L==null||L.dispose(),L=new he,m(N,L)}_(void 0);const b=g(N=>_(N));return Te(()=>{b.dispose(),L==null||L.dispose()})}e.runAndSubscribeWithStore=p})(We||(We={}));class Ge{constructor(t){this._listenerCount=0,this._invocationCount=0,this._elapsedOverall=0,this._name=`${t}_${Ge._idPool++}`}start(t){this._stopWatch=new $e(!0),this._listenerCount=t}stop(){if(this._stopWatch){const t=this._stopWatch.elapsed();this._elapsedOverall+=t,this._invocationCount+=1,console.info(`did FIRE ${this._name}: elapsed_ms: ${t.toFixed(5)}, listener: ${this._listenerCount} (elapsed_overall: ${this._elapsedOverall.toFixed(2)}, invocations: ${this._invocationCount})`),this._stopWatch=void 0}}}Ge._idPool=0;class wt{constructor(t){this.value=t}static create(){var t;return new wt((t=new Error().stack)!==null&&t!==void 0?t:"")}print(){console.warn(this.value.split(`
`).slice(2).join(`
`))}}class $n{constructor(t,n,s){this.callback=t,this.callbackThis=n,this.stack=s,this.subscription=new zn}invoke(t){this.callback.call(this.callbackThis,t)}}class ${constructor(t){var n;this._disposed=!1,this._options=t,this._leakageMon=void 0,this._perfMon=!((n=this._options)===null||n===void 0)&&n._profName?new Ge(this._options._profName):void 0}dispose(){var t,n,s,i;this._disposed||(this._disposed=!0,this._listeners&&this._listeners.clear(),(t=this._deliveryQueue)===null||t===void 0||t.clear(),(s=(n=this._options)===null||n===void 0?void 0:n.onLastListenerRemove)===null||s===void 0||s.call(n),(i=this._leakageMon)===null||i===void 0||i.dispose())}get event(){return this._event||(this._event=(t,n,s)=>{var i,a,o;this._listeners||(this._listeners=new Xe);const l=this._listeners.isEmpty();l&&(!((i=this._options)===null||i===void 0)&&i.onFirstListenerAdd)&&this._options.onFirstListenerAdd(this);let c,u;this._leakageMon&&this._listeners.size>=30&&(u=wt.create(),c=this._leakageMon.check(u,this._listeners.size+1));const h=new $n(t,n,u),f=this._listeners.push(h);l&&(!((a=this._options)===null||a===void 0)&&a.onFirstListenerDidAdd)&&this._options.onFirstListenerDidAdd(this),!((o=this._options)===null||o===void 0)&&o.onListenerDidAdd&&this._options.onListenerDidAdd(this,t,n);const d=h.subscription.set(()=>{c&&c(),this._disposed||(f(),this._options&&this._options.onLastListenerRemove&&(this._listeners&&!this._listeners.isEmpty()||this._options.onLastListenerRemove(this)))});return s instanceof he?s.add(d):Array.isArray(s)&&s.push(d),d}),this._event}fire(t){var n,s;if(this._listeners){this._deliveryQueue||(this._deliveryQueue=new Xe);for(let i of this._listeners)this._deliveryQueue.push([i,t]);for((n=this._perfMon)===null||n===void 0||n.start(this._deliveryQueue.size);this._deliveryQueue.size>0;){const[i,a]=this._deliveryQueue.shift();try{i.invoke(a)}catch(o){Un(o)}}(s=this._perfMon)===null||s===void 0||s.stop()}}}class Gn extends ${constructor(t){super(t),this._isPaused=0,this._eventQueue=new Xe,this._mergeFn=t==null?void 0:t.merge}pause(){this._isPaused++}resume(){if(this._isPaused!==0&&--this._isPaused===0)if(this._mergeFn){const t=Array.from(this._eventQueue);this._eventQueue.clear(),super.fire(this._mergeFn(t))}else for(;!this._isPaused&&this._eventQueue.size!==0;)super.fire(this._eventQueue.shift())}fire(t){this._listeners&&(this._isPaused!==0?this._eventQueue.push(t):super.fire(t))}}class gi extends Gn{constructor(t){var n;super(t),this._delay=(n=t.delay)!==null&&n!==void 0?n:100}fire(t){this._handle||(this.pause(),this._handle=setTimeout(()=>{this._handle=void 0,this.resume()},this._delay)),super.fire(t)}}class bi{constructor(){this.buffers=[]}wrapEvent(t){return(n,s,i)=>t(a=>{const o=this.buffers[this.buffers.length-1];o?o.push(()=>n.call(s,a)):n.call(s,a)},void 0,i)}bufferEvents(t){const n=[];this.buffers.push(n);const s=t();return this.buffers.pop(),n.forEach(i=>i()),s}}class wi{constructor(){this.listening=!1,this.inputEvent=We.None,this.inputEventListener=ve.None,this.emitter=new $({onFirstListenerDidAdd:()=>{this.listening=!0,this.inputEventListener=this.inputEvent(this.emitter.fire,this.emitter)},onLastListenerRemove:()=>{this.listening=!1,this.inputEventListener.dispose()}}),this.event=this.emitter.event}set input(t){this.inputEvent=t,this.listening&&(this.inputEventListener.dispose(),this.inputEventListener=t(this.emitter.fire,this.emitter))}dispose(){this.inputEventListener.dispose(),this.emitter.dispose()}}const O1=Object.freeze(function(e,t){const n=setTimeout(e.bind(t),0);return{dispose(){clearTimeout(n)}}});var qe;(function(e){function t(n){return n===e.None||n===e.Cancelled||n instanceof Pe?!0:!n||typeof n!="object"?!1:typeof n.isCancellationRequested=="boolean"&&typeof n.onCancellationRequested=="function"}e.isCancellationToken=t,e.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:We.None}),e.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:O1})})(qe||(qe={}));class Pe{constructor(){this._isCancelled=!1,this._emitter=null}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?O1:(this._emitter||(this._emitter=new $),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=null)}}class jn{constructor(t){this._token=void 0,this._parentListener=void 0,this._parentListener=t&&t.onCancellationRequested(this.cancel,this)}get token(){return this._token||(this._token=new Pe),this._token}cancel(){this._token?this._token instanceof Pe&&this._token.cancel():this._token=qe.Cancelled}dispose(t=!1){t&&this.cancel(),this._parentListener&&this._parentListener.dispose(),this._token?this._token instanceof Pe&&this._token.dispose():this._token=qe.None}}class Ct{constructor(){this._keyCodeToStr=[],this._strToKeyCode=Object.create(null)}define(t,n){this._keyCodeToStr[t]=n,this._strToKeyCode[n.toLowerCase()]=t}keyCodeToStr(t){return this._keyCodeToStr[t]}strToKeyCode(t){return this._strToKeyCode[t.toLowerCase()]||0}}const Ve=new Ct,Ke=new Ct,et=new Ct,Qn=new Array(230),Zn=Object.create(null),Yn=Object.create(null),$1=[];for(let e=0;e<=193;e++)$1[e]=-1;(function(){const e="",t=[[0,1,0,"None",0,"unknown",0,"VK_UNKNOWN",e,e],[0,1,1,"Hyper",0,e,0,e,e,e],[0,1,2,"Super",0,e,0,e,e,e],[0,1,3,"Fn",0,e,0,e,e,e],[0,1,4,"FnLock",0,e,0,e,e,e],[0,1,5,"Suspend",0,e,0,e,e,e],[0,1,6,"Resume",0,e,0,e,e,e],[0,1,7,"Turbo",0,e,0,e,e,e],[0,1,8,"Sleep",0,e,0,"VK_SLEEP",e,e],[0,1,9,"WakeUp",0,e,0,e,e,e],[31,0,10,"KeyA",31,"A",65,"VK_A",e,e],[32,0,11,"KeyB",32,"B",66,"VK_B",e,e],[33,0,12,"KeyC",33,"C",67,"VK_C",e,e],[34,0,13,"KeyD",34,"D",68,"VK_D",e,e],[35,0,14,"KeyE",35,"E",69,"VK_E",e,e],[36,0,15,"KeyF",36,"F",70,"VK_F",e,e],[37,0,16,"KeyG",37,"G",71,"VK_G",e,e],[38,0,17,"KeyH",38,"H",72,"VK_H",e,e],[39,0,18,"KeyI",39,"I",73,"VK_I",e,e],[40,0,19,"KeyJ",40,"J",74,"VK_J",e,e],[41,0,20,"KeyK",41,"K",75,"VK_K",e,e],[42,0,21,"KeyL",42,"L",76,"VK_L",e,e],[43,0,22,"KeyM",43,"M",77,"VK_M",e,e],[44,0,23,"KeyN",44,"N",78,"VK_N",e,e],[45,0,24,"KeyO",45,"O",79,"VK_O",e,e],[46,0,25,"KeyP",46,"P",80,"VK_P",e,e],[47,0,26,"KeyQ",47,"Q",81,"VK_Q",e,e],[48,0,27,"KeyR",48,"R",82,"VK_R",e,e],[49,0,28,"KeyS",49,"S",83,"VK_S",e,e],[50,0,29,"KeyT",50,"T",84,"VK_T",e,e],[51,0,30,"KeyU",51,"U",85,"VK_U",e,e],[52,0,31,"KeyV",52,"V",86,"VK_V",e,e],[53,0,32,"KeyW",53,"W",87,"VK_W",e,e],[54,0,33,"KeyX",54,"X",88,"VK_X",e,e],[55,0,34,"KeyY",55,"Y",89,"VK_Y",e,e],[56,0,35,"KeyZ",56,"Z",90,"VK_Z",e,e],[22,0,36,"Digit1",22,"1",49,"VK_1",e,e],[23,0,37,"Digit2",23,"2",50,"VK_2",e,e],[24,0,38,"Digit3",24,"3",51,"VK_3",e,e],[25,0,39,"Digit4",25,"4",52,"VK_4",e,e],[26,0,40,"Digit5",26,"5",53,"VK_5",e,e],[27,0,41,"Digit6",27,"6",54,"VK_6",e,e],[28,0,42,"Digit7",28,"7",55,"VK_7",e,e],[29,0,43,"Digit8",29,"8",56,"VK_8",e,e],[30,0,44,"Digit9",30,"9",57,"VK_9",e,e],[21,0,45,"Digit0",21,"0",48,"VK_0",e,e],[3,1,46,"Enter",3,"Enter",13,"VK_RETURN",e,e],[9,1,47,"Escape",9,"Escape",27,"VK_ESCAPE",e,e],[1,1,48,"Backspace",1,"Backspace",8,"VK_BACK",e,e],[2,1,49,"Tab",2,"Tab",9,"VK_TAB",e,e],[10,1,50,"Space",10,"Space",32,"VK_SPACE",e,e],[83,0,51,"Minus",83,"-",189,"VK_OEM_MINUS","-","OEM_MINUS"],[81,0,52,"Equal",81,"=",187,"VK_OEM_PLUS","=","OEM_PLUS"],[87,0,53,"BracketLeft",87,"[",219,"VK_OEM_4","[","OEM_4"],[89,0,54,"BracketRight",89,"]",221,"VK_OEM_6","]","OEM_6"],[88,0,55,"Backslash",88,"\\",220,"VK_OEM_5","\\","OEM_5"],[0,0,56,"IntlHash",0,e,0,e,e,e],[80,0,57,"Semicolon",80,";",186,"VK_OEM_1",";","OEM_1"],[90,0,58,"Quote",90,"'",222,"VK_OEM_7","'","OEM_7"],[86,0,59,"Backquote",86,"`",192,"VK_OEM_3","`","OEM_3"],[82,0,60,"Comma",82,",",188,"VK_OEM_COMMA",",","OEM_COMMA"],[84,0,61,"Period",84,".",190,"VK_OEM_PERIOD",".","OEM_PERIOD"],[85,0,62,"Slash",85,"/",191,"VK_OEM_2","/","OEM_2"],[8,1,63,"CapsLock",8,"CapsLock",20,"VK_CAPITAL",e,e],[59,1,64,"F1",59,"F1",112,"VK_F1",e,e],[60,1,65,"F2",60,"F2",113,"VK_F2",e,e],[61,1,66,"F3",61,"F3",114,"VK_F3",e,e],[62,1,67,"F4",62,"F4",115,"VK_F4",e,e],[63,1,68,"F5",63,"F5",116,"VK_F5",e,e],[64,1,69,"F6",64,"F6",117,"VK_F6",e,e],[65,1,70,"F7",65,"F7",118,"VK_F7",e,e],[66,1,71,"F8",66,"F8",119,"VK_F8",e,e],[67,1,72,"F9",67,"F9",120,"VK_F9",e,e],[68,1,73,"F10",68,"F10",121,"VK_F10",e,e],[69,1,74,"F11",69,"F11",122,"VK_F11",e,e],[70,1,75,"F12",70,"F12",123,"VK_F12",e,e],[0,1,76,"PrintScreen",0,e,0,e,e,e],[79,1,77,"ScrollLock",79,"ScrollLock",145,"VK_SCROLL",e,e],[7,1,78,"Pause",7,"PauseBreak",19,"VK_PAUSE",e,e],[19,1,79,"Insert",19,"Insert",45,"VK_INSERT",e,e],[14,1,80,"Home",14,"Home",36,"VK_HOME",e,e],[11,1,81,"PageUp",11,"PageUp",33,"VK_PRIOR",e,e],[20,1,82,"Delete",20,"Delete",46,"VK_DELETE",e,e],[13,1,83,"End",13,"End",35,"VK_END",e,e],[12,1,84,"PageDown",12,"PageDown",34,"VK_NEXT",e,e],[17,1,85,"ArrowRight",17,"RightArrow",39,"VK_RIGHT","Right",e],[15,1,86,"ArrowLeft",15,"LeftArrow",37,"VK_LEFT","Left",e],[18,1,87,"ArrowDown",18,"DownArrow",40,"VK_DOWN","Down",e],[16,1,88,"ArrowUp",16,"UpArrow",38,"VK_UP","Up",e],[78,1,89,"NumLock",78,"NumLock",144,"VK_NUMLOCK",e,e],[108,1,90,"NumpadDivide",108,"NumPad_Divide",111,"VK_DIVIDE",e,e],[103,1,91,"NumpadMultiply",103,"NumPad_Multiply",106,"VK_MULTIPLY",e,e],[106,1,92,"NumpadSubtract",106,"NumPad_Subtract",109,"VK_SUBTRACT",e,e],[104,1,93,"NumpadAdd",104,"NumPad_Add",107,"VK_ADD",e,e],[3,1,94,"NumpadEnter",3,e,0,e,e,e],[94,1,95,"Numpad1",94,"NumPad1",97,"VK_NUMPAD1",e,e],[95,1,96,"Numpad2",95,"NumPad2",98,"VK_NUMPAD2",e,e],[96,1,97,"Numpad3",96,"NumPad3",99,"VK_NUMPAD3",e,e],[97,1,98,"Numpad4",97,"NumPad4",100,"VK_NUMPAD4",e,e],[98,1,99,"Numpad5",98,"NumPad5",101,"VK_NUMPAD5",e,e],[99,1,100,"Numpad6",99,"NumPad6",102,"VK_NUMPAD6",e,e],[100,1,101,"Numpad7",100,"NumPad7",103,"VK_NUMPAD7",e,e],[101,1,102,"Numpad8",101,"NumPad8",104,"VK_NUMPAD8",e,e],[102,1,103,"Numpad9",102,"NumPad9",105,"VK_NUMPAD9",e,e],[93,1,104,"Numpad0",93,"NumPad0",96,"VK_NUMPAD0",e,e],[107,1,105,"NumpadDecimal",107,"NumPad_Decimal",110,"VK_DECIMAL",e,e],[92,0,106,"IntlBackslash",92,"OEM_102",226,"VK_OEM_102",e,e],[58,1,107,"ContextMenu",58,"ContextMenu",93,e,e,e],[0,1,108,"Power",0,e,0,e,e,e],[0,1,109,"NumpadEqual",0,e,0,e,e,e],[71,1,110,"F13",71,"F13",124,"VK_F13",e,e],[72,1,111,"F14",72,"F14",125,"VK_F14",e,e],[73,1,112,"F15",73,"F15",126,"VK_F15",e,e],[74,1,113,"F16",74,"F16",127,"VK_F16",e,e],[75,1,114,"F17",75,"F17",128,"VK_F17",e,e],[76,1,115,"F18",76,"F18",129,"VK_F18",e,e],[77,1,116,"F19",77,"F19",130,"VK_F19",e,e],[0,1,117,"F20",0,e,0,"VK_F20",e,e],[0,1,118,"F21",0,e,0,"VK_F21",e,e],[0,1,119,"F22",0,e,0,"VK_F22",e,e],[0,1,120,"F23",0,e,0,"VK_F23",e,e],[0,1,121,"F24",0,e,0,"VK_F24",e,e],[0,1,122,"Open",0,e,0,e,e,e],[0,1,123,"Help",0,e,0,e,e,e],[0,1,124,"Select",0,e,0,e,e,e],[0,1,125,"Again",0,e,0,e,e,e],[0,1,126,"Undo",0,e,0,e,e,e],[0,1,127,"Cut",0,e,0,e,e,e],[0,1,128,"Copy",0,e,0,e,e,e],[0,1,129,"Paste",0,e,0,e,e,e],[0,1,130,"Find",0,e,0,e,e,e],[0,1,131,"AudioVolumeMute",112,"AudioVolumeMute",173,"VK_VOLUME_MUTE",e,e],[0,1,132,"AudioVolumeUp",113,"AudioVolumeUp",175,"VK_VOLUME_UP",e,e],[0,1,133,"AudioVolumeDown",114,"AudioVolumeDown",174,"VK_VOLUME_DOWN",e,e],[105,1,134,"NumpadComma",105,"NumPad_Separator",108,"VK_SEPARATOR",e,e],[110,0,135,"IntlRo",110,"ABNT_C1",193,"VK_ABNT_C1",e,e],[0,1,136,"KanaMode",0,e,0,e,e,e],[0,0,137,"IntlYen",0,e,0,e,e,e],[0,1,138,"Convert",0,e,0,e,e,e],[0,1,139,"NonConvert",0,e,0,e,e,e],[0,1,140,"Lang1",0,e,0,e,e,e],[0,1,141,"Lang2",0,e,0,e,e,e],[0,1,142,"Lang3",0,e,0,e,e,e],[0,1,143,"Lang4",0,e,0,e,e,e],[0,1,144,"Lang5",0,e,0,e,e,e],[0,1,145,"Abort",0,e,0,e,e,e],[0,1,146,"Props",0,e,0,e,e,e],[0,1,147,"NumpadParenLeft",0,e,0,e,e,e],[0,1,148,"NumpadParenRight",0,e,0,e,e,e],[0,1,149,"NumpadBackspace",0,e,0,e,e,e],[0,1,150,"NumpadMemoryStore",0,e,0,e,e,e],[0,1,151,"NumpadMemoryRecall",0,e,0,e,e,e],[0,1,152,"NumpadMemoryClear",0,e,0,e,e,e],[0,1,153,"NumpadMemoryAdd",0,e,0,e,e,e],[0,1,154,"NumpadMemorySubtract",0,e,0,e,e,e],[0,1,155,"NumpadClear",126,"Clear",12,"VK_CLEAR",e,e],[0,1,156,"NumpadClearEntry",0,e,0,e,e,e],[5,1,0,e,5,"Ctrl",17,"VK_CONTROL",e,e],[4,1,0,e,4,"Shift",16,"VK_SHIFT",e,e],[6,1,0,e,6,"Alt",18,"VK_MENU",e,e],[57,1,0,e,57,"Meta",0,"VK_COMMAND",e,e],[5,1,157,"ControlLeft",5,e,0,"VK_LCONTROL",e,e],[4,1,158,"ShiftLeft",4,e,0,"VK_LSHIFT",e,e],[6,1,159,"AltLeft",6,e,0,"VK_LMENU",e,e],[57,1,160,"MetaLeft",57,e,0,"VK_LWIN",e,e],[5,1,161,"ControlRight",5,e,0,"VK_RCONTROL",e,e],[4,1,162,"ShiftRight",4,e,0,"VK_RSHIFT",e,e],[6,1,163,"AltRight",6,e,0,"VK_RMENU",e,e],[57,1,164,"MetaRight",57,e,0,"VK_RWIN",e,e],[0,1,165,"BrightnessUp",0,e,0,e,e,e],[0,1,166,"BrightnessDown",0,e,0,e,e,e],[0,1,167,"MediaPlay",0,e,0,e,e,e],[0,1,168,"MediaRecord",0,e,0,e,e,e],[0,1,169,"MediaFastForward",0,e,0,e,e,e],[0,1,170,"MediaRewind",0,e,0,e,e,e],[114,1,171,"MediaTrackNext",119,"MediaTrackNext",176,"VK_MEDIA_NEXT_TRACK",e,e],[115,1,172,"MediaTrackPrevious",120,"MediaTrackPrevious",177,"VK_MEDIA_PREV_TRACK",e,e],[116,1,173,"MediaStop",121,"MediaStop",178,"VK_MEDIA_STOP",e,e],[0,1,174,"Eject",0,e,0,e,e,e],[117,1,175,"MediaPlayPause",122,"MediaPlayPause",179,"VK_MEDIA_PLAY_PAUSE",e,e],[0,1,176,"MediaSelect",123,"LaunchMediaPlayer",181,"VK_MEDIA_LAUNCH_MEDIA_SELECT",e,e],[0,1,177,"LaunchMail",124,"LaunchMail",180,"VK_MEDIA_LAUNCH_MAIL",e,e],[0,1,178,"LaunchApp2",125,"LaunchApp2",183,"VK_MEDIA_LAUNCH_APP2",e,e],[0,1,179,"LaunchApp1",0,e,0,"VK_MEDIA_LAUNCH_APP1",e,e],[0,1,180,"SelectTask",0,e,0,e,e,e],[0,1,181,"LaunchScreenSaver",0,e,0,e,e,e],[0,1,182,"BrowserSearch",115,"BrowserSearch",170,"VK_BROWSER_SEARCH",e,e],[0,1,183,"BrowserHome",116,"BrowserHome",172,"VK_BROWSER_HOME",e,e],[112,1,184,"BrowserBack",117,"BrowserBack",166,"VK_BROWSER_BACK",e,e],[113,1,185,"BrowserForward",118,"BrowserForward",167,"VK_BROWSER_FORWARD",e,e],[0,1,186,"BrowserStop",0,e,0,"VK_BROWSER_STOP",e,e],[0,1,187,"BrowserRefresh",0,e,0,"VK_BROWSER_REFRESH",e,e],[0,1,188,"BrowserFavorites",0,e,0,"VK_BROWSER_FAVORITES",e,e],[0,1,189,"ZoomToggle",0,e,0,e,e,e],[0,1,190,"MailReply",0,e,0,e,e,e],[0,1,191,"MailForward",0,e,0,e,e,e],[0,1,192,"MailSend",0,e,0,e,e,e],[109,1,0,e,109,"KeyInComposition",229,e,e,e],[111,1,0,e,111,"ABNT_C2",194,"VK_ABNT_C2",e,e],[91,1,0,e,91,"OEM_8",223,"VK_OEM_8",e,e],[0,1,0,e,0,e,0,"VK_KANA",e,e],[0,1,0,e,0,e,0,"VK_HANGUL",e,e],[0,1,0,e,0,e,0,"VK_JUNJA",e,e],[0,1,0,e,0,e,0,"VK_FINAL",e,e],[0,1,0,e,0,e,0,"VK_HANJA",e,e],[0,1,0,e,0,e,0,"VK_KANJI",e,e],[0,1,0,e,0,e,0,"VK_CONVERT",e,e],[0,1,0,e,0,e,0,"VK_NONCONVERT",e,e],[0,1,0,e,0,e,0,"VK_ACCEPT",e,e],[0,1,0,e,0,e,0,"VK_MODECHANGE",e,e],[0,1,0,e,0,e,0,"VK_SELECT",e,e],[0,1,0,e,0,e,0,"VK_PRINT",e,e],[0,1,0,e,0,e,0,"VK_EXECUTE",e,e],[0,1,0,e,0,e,0,"VK_SNAPSHOT",e,e],[0,1,0,e,0,e,0,"VK_HELP",e,e],[0,1,0,e,0,e,0,"VK_APPS",e,e],[0,1,0,e,0,e,0,"VK_PROCESSKEY",e,e],[0,1,0,e,0,e,0,"VK_PACKET",e,e],[0,1,0,e,0,e,0,"VK_DBE_SBCSCHAR",e,e],[0,1,0,e,0,e,0,"VK_DBE_DBCSCHAR",e,e],[0,1,0,e,0,e,0,"VK_ATTN",e,e],[0,1,0,e,0,e,0,"VK_CRSEL",e,e],[0,1,0,e,0,e,0,"VK_EXSEL",e,e],[0,1,0,e,0,e,0,"VK_EREOF",e,e],[0,1,0,e,0,e,0,"VK_PLAY",e,e],[0,1,0,e,0,e,0,"VK_ZOOM",e,e],[0,1,0,e,0,e,0,"VK_NONAME",e,e],[0,1,0,e,0,e,0,"VK_PA1",e,e],[0,1,0,e,0,e,0,"VK_OEM_CLEAR",e,e]];let n=[],s=[];for(const i of t){const[a,o,l,c,u,h,f,d,w,S]=i;if(s[l]||(s[l]=!0,Zn[c]=l,Yn[c.toLowerCase()]=l,o&&($1[l]=u)),!n[u]){if(n[u]=!0,!h)throw new Error(`String representation missing for key code ${u} around scan code ${c}`);Ve.define(u,h),Ke.define(u,w||h),et.define(u,S||w||h)}f&&(Qn[f]=u)}})();var Ft;(function(e){function t(l){return Ve.keyCodeToStr(l)}e.toString=t;function n(l){return Ve.strToKeyCode(l)}e.fromString=n;function s(l){return Ke.keyCodeToStr(l)}e.toUserSettingsUS=s;function i(l){return et.keyCodeToStr(l)}e.toUserSettingsGeneral=i;function a(l){return Ke.strToKeyCode(l)||et.strToKeyCode(l)}e.fromUserSettings=a;function o(l){if(l>=93&&l<=108)return null;switch(l){case 16:return"Up";case 18:return"Down";case 15:return"Left";case 17:return"Right"}return Ve.keyCodeToStr(l)}e.toElectronAccelerator=o})(Ft||(Ft={}));function Jn(e,t){const n=(t&65535)<<16>>>0;return(e|n)>>>0}let ce;if(typeof V.vscode!="undefined"&&typeof V.vscode.process!="undefined"){const e=V.vscode.process;ce={get platform(){return e.platform},get arch(){return e.arch},get env(){return e.env},cwd(){return e.cwd()}}}else typeof process!="undefined"?ce={get platform(){return process.platform},get arch(){return process.arch},get env(){return process.env},cwd(){return process.env.VSCODE_CWD||process.cwd()}}:ce={get platform(){return _e?"win32":gn?"darwin":"linux"},get arch(){},get env(){return{}},cwd(){return"/"}};const tt=ce.cwd,Xn=ce.env,ie=ce.platform,Kn=65,er=97,tr=90,nr=122,te=46,B=47,W=92,Z=58,rr=63;class G1 extends Error{constructor(t,n,s){let i;typeof n=="string"&&n.indexOf("not ")===0?(i="must not be",n=n.replace(/^not /,"")):i="must be";const a=t.indexOf(".")!==-1?"property":"argument";let o=`The "${t}" ${a} ${i} of type ${n}`;o+=`. Received type ${typeof s}`,super(o),this.code="ERR_INVALID_ARG_TYPE"}}function P(e,t){if(typeof e!="string")throw new G1(t,"string",e)}function x(e){return e===B||e===W}function nt(e){return e===B}function Y(e){return e>=Kn&&e<=tr||e>=er&&e<=nr}function He(e,t,n,s){let i="",a=0,o=-1,l=0,c=0;for(let u=0;u<=e.length;++u){if(u<e.length)c=e.charCodeAt(u);else{if(s(c))break;c=B}if(s(c)){if(!(o===u-1||l===1))if(l===2){if(i.length<2||a!==2||i.charCodeAt(i.length-1)!==te||i.charCodeAt(i.length-2)!==te){if(i.length>2){const h=i.lastIndexOf(n);h===-1?(i="",a=0):(i=i.slice(0,h),a=i.length-1-i.lastIndexOf(n)),o=u,l=0;continue}else if(i.length!==0){i="",a=0,o=u,l=0;continue}}t&&(i+=i.length>0?`${n}..`:"..",a=2)}else i.length>0?i+=`${n}${e.slice(o+1,u)}`:i=e.slice(o+1,u),a=u-o-1;o=u,l=0}else c===te&&l!==-1?++l:l=-1}return i}function j1(e,t){if(t===null||typeof t!="object")throw new G1("pathObject","Object",t);const n=t.dir||t.root,s=t.base||`${t.name||""}${t.ext||""}`;return n?n===t.root?`${n}${s}`:`${n}${e}${s}`:s}const T={resolve(...e){let t="",n="",s=!1;for(let i=e.length-1;i>=-1;i--){let a;if(i>=0){if(a=e[i],P(a,"path"),a.length===0)continue}else t.length===0?a=tt():(a=Xn[`=${t}`]||tt(),(a===void 0||a.slice(0,2).toLowerCase()!==t.toLowerCase()&&a.charCodeAt(2)===W)&&(a=`${t}\\`));const o=a.length;let l=0,c="",u=!1;const h=a.charCodeAt(0);if(o===1)x(h)&&(l=1,u=!0);else if(x(h))if(u=!0,x(a.charCodeAt(1))){let f=2,d=f;for(;f<o&&!x(a.charCodeAt(f));)f++;if(f<o&&f!==d){const w=a.slice(d,f);for(d=f;f<o&&x(a.charCodeAt(f));)f++;if(f<o&&f!==d){for(d=f;f<o&&!x(a.charCodeAt(f));)f++;(f===o||f!==d)&&(c=`\\\\${w}\\${a.slice(d,f)}`,l=f)}}}else l=1;else Y(h)&&a.charCodeAt(1)===Z&&(c=a.slice(0,2),l=2,o>2&&x(a.charCodeAt(2))&&(u=!0,l=3));if(c.length>0)if(t.length>0){if(c.toLowerCase()!==t.toLowerCase())continue}else t=c;if(s){if(t.length>0)break}else if(n=`${a.slice(l)}\\${n}`,s=u,u&&t.length>0)break}return n=He(n,!s,"\\",x),s?`${t}\\${n}`:`${t}${n}`||"."},normalize(e){P(e,"path");const t=e.length;if(t===0)return".";let n=0,s,i=!1;const a=e.charCodeAt(0);if(t===1)return nt(a)?"\\":e;if(x(a))if(i=!0,x(e.charCodeAt(1))){let l=2,c=l;for(;l<t&&!x(e.charCodeAt(l));)l++;if(l<t&&l!==c){const u=e.slice(c,l);for(c=l;l<t&&x(e.charCodeAt(l));)l++;if(l<t&&l!==c){for(c=l;l<t&&!x(e.charCodeAt(l));)l++;if(l===t)return`\\\\${u}\\${e.slice(c)}\\`;l!==c&&(s=`\\\\${u}\\${e.slice(c,l)}`,n=l)}}}else n=1;else Y(a)&&e.charCodeAt(1)===Z&&(s=e.slice(0,2),n=2,t>2&&x(e.charCodeAt(2))&&(i=!0,n=3));let o=n<t?He(e.slice(n),!i,"\\",x):"";return o.length===0&&!i&&(o="."),o.length>0&&x(e.charCodeAt(t-1))&&(o+="\\"),s===void 0?i?`\\${o}`:o:i?`${s}\\${o}`:`${s}${o}`},isAbsolute(e){P(e,"path");const t=e.length;if(t===0)return!1;const n=e.charCodeAt(0);return x(n)||t>2&&Y(n)&&e.charCodeAt(1)===Z&&x(e.charCodeAt(2))},join(...e){if(e.length===0)return".";let t,n;for(let a=0;a<e.length;++a){const o=e[a];P(o,"path"),o.length>0&&(t===void 0?t=n=o:t+=`\\${o}`)}if(t===void 0)return".";let s=!0,i=0;if(typeof n=="string"&&x(n.charCodeAt(0))){++i;const a=n.length;a>1&&x(n.charCodeAt(1))&&(++i,a>2&&(x(n.charCodeAt(2))?++i:s=!1))}if(s){for(;i<t.length&&x(t.charCodeAt(i));)i++;i>=2&&(t=`\\${t.slice(i)}`)}return T.normalize(t)},relative(e,t){if(P(e,"from"),P(t,"to"),e===t)return"";const n=T.resolve(e),s=T.resolve(t);if(n===s||(e=n.toLowerCase(),t=s.toLowerCase(),e===t))return"";let i=0;for(;i<e.length&&e.charCodeAt(i)===W;)i++;let a=e.length;for(;a-1>i&&e.charCodeAt(a-1)===W;)a--;const o=a-i;let l=0;for(;l<t.length&&t.charCodeAt(l)===W;)l++;let c=t.length;for(;c-1>l&&t.charCodeAt(c-1)===W;)c--;const u=c-l,h=o<u?o:u;let f=-1,d=0;for(;d<h;d++){const S=e.charCodeAt(i+d);if(S!==t.charCodeAt(l+d))break;S===W&&(f=d)}if(d!==h){if(f===-1)return s}else{if(u>h){if(t.charCodeAt(l+d)===W)return s.slice(l+d+1);if(d===2)return s.slice(l+d)}o>h&&(e.charCodeAt(i+d)===W?f=d:d===2&&(f=3)),f===-1&&(f=0)}let w="";for(d=i+f+1;d<=a;++d)(d===a||e.charCodeAt(d)===W)&&(w+=w.length===0?"..":"\\..");return l+=f,w.length>0?`${w}${s.slice(l,c)}`:(s.charCodeAt(l)===W&&++l,s.slice(l,c))},toNamespacedPath(e){if(typeof e!="string")return e;if(e.length===0)return"";const t=T.resolve(e);if(t.length<=2)return e;if(t.charCodeAt(0)===W){if(t.charCodeAt(1)===W){const n=t.charCodeAt(2);if(n!==rr&&n!==te)return`\\\\?\\UNC\\${t.slice(2)}`}}else if(Y(t.charCodeAt(0))&&t.charCodeAt(1)===Z&&t.charCodeAt(2)===W)return`\\\\?\\${t}`;return e},dirname(e){P(e,"path");const t=e.length;if(t===0)return".";let n=-1,s=0;const i=e.charCodeAt(0);if(t===1)return x(i)?e:".";if(x(i)){if(n=s=1,x(e.charCodeAt(1))){let l=2,c=l;for(;l<t&&!x(e.charCodeAt(l));)l++;if(l<t&&l!==c){for(c=l;l<t&&x(e.charCodeAt(l));)l++;if(l<t&&l!==c){for(c=l;l<t&&!x(e.charCodeAt(l));)l++;if(l===t)return e;l!==c&&(n=s=l+1)}}}}else Y(i)&&e.charCodeAt(1)===Z&&(n=t>2&&x(e.charCodeAt(2))?3:2,s=n);let a=-1,o=!0;for(let l=t-1;l>=s;--l)if(x(e.charCodeAt(l))){if(!o){a=l;break}}else o=!1;if(a===-1){if(n===-1)return".";a=n}return e.slice(0,a)},basename(e,t){t!==void 0&&P(t,"ext"),P(e,"path");let n=0,s=-1,i=!0,a;if(e.length>=2&&Y(e.charCodeAt(0))&&e.charCodeAt(1)===Z&&(n=2),t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let o=t.length-1,l=-1;for(a=e.length-1;a>=n;--a){const c=e.charCodeAt(a);if(x(c)){if(!i){n=a+1;break}}else l===-1&&(i=!1,l=a+1),o>=0&&(c===t.charCodeAt(o)?--o===-1&&(s=a):(o=-1,s=l))}return n===s?s=l:s===-1&&(s=e.length),e.slice(n,s)}for(a=e.length-1;a>=n;--a)if(x(e.charCodeAt(a))){if(!i){n=a+1;break}}else s===-1&&(i=!1,s=a+1);return s===-1?"":e.slice(n,s)},extname(e){P(e,"path");let t=0,n=-1,s=0,i=-1,a=!0,o=0;e.length>=2&&e.charCodeAt(1)===Z&&Y(e.charCodeAt(0))&&(t=s=2);for(let l=e.length-1;l>=t;--l){const c=e.charCodeAt(l);if(x(c)){if(!a){s=l+1;break}continue}i===-1&&(a=!1,i=l+1),c===te?n===-1?n=l:o!==1&&(o=1):n!==-1&&(o=-1)}return n===-1||i===-1||o===0||o===1&&n===i-1&&n===s+1?"":e.slice(n,i)},format:j1.bind(null,"\\"),parse(e){P(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const n=e.length;let s=0,i=e.charCodeAt(0);if(n===1)return x(i)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(x(i)){if(s=1,x(e.charCodeAt(1))){let f=2,d=f;for(;f<n&&!x(e.charCodeAt(f));)f++;if(f<n&&f!==d){for(d=f;f<n&&x(e.charCodeAt(f));)f++;if(f<n&&f!==d){for(d=f;f<n&&!x(e.charCodeAt(f));)f++;f===n?s=f:f!==d&&(s=f+1)}}}}else if(Y(i)&&e.charCodeAt(1)===Z){if(n<=2)return t.root=t.dir=e,t;if(s=2,x(e.charCodeAt(2))){if(n===3)return t.root=t.dir=e,t;s=3}}s>0&&(t.root=e.slice(0,s));let a=-1,o=s,l=-1,c=!0,u=e.length-1,h=0;for(;u>=s;--u){if(i=e.charCodeAt(u),x(i)){if(!c){o=u+1;break}continue}l===-1&&(c=!1,l=u+1),i===te?a===-1?a=u:h!==1&&(h=1):a!==-1&&(h=-1)}return l!==-1&&(a===-1||h===0||h===1&&a===l-1&&a===o+1?t.base=t.name=e.slice(o,l):(t.name=e.slice(o,a),t.base=e.slice(o,l),t.ext=e.slice(a,l))),o>0&&o!==s?t.dir=e.slice(0,o-1):t.dir=t.root,t},sep:"\\",delimiter:";",win32:null,posix:null},q={resolve(...e){let t="",n=!1;for(let s=e.length-1;s>=-1&&!n;s--){const i=s>=0?e[s]:tt();P(i,"path"),i.length!==0&&(t=`${i}/${t}`,n=i.charCodeAt(0)===B)}return t=He(t,!n,"/",nt),n?`/${t}`:t.length>0?t:"."},normalize(e){if(P(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===B,n=e.charCodeAt(e.length-1)===B;return e=He(e,!t,"/",nt),e.length===0?t?"/":n?"./":".":(n&&(e+="/"),t?`/${e}`:e)},isAbsolute(e){return P(e,"path"),e.length>0&&e.charCodeAt(0)===B},join(...e){if(e.length===0)return".";let t;for(let n=0;n<e.length;++n){const s=e[n];P(s,"path"),s.length>0&&(t===void 0?t=s:t+=`/${s}`)}return t===void 0?".":q.normalize(t)},relative(e,t){if(P(e,"from"),P(t,"to"),e===t||(e=q.resolve(e),t=q.resolve(t),e===t))return"";const n=1,s=e.length,i=s-n,a=1,o=t.length-a,l=i<o?i:o;let c=-1,u=0;for(;u<l;u++){const f=e.charCodeAt(n+u);if(f!==t.charCodeAt(a+u))break;f===B&&(c=u)}if(u===l)if(o>l){if(t.charCodeAt(a+u)===B)return t.slice(a+u+1);if(u===0)return t.slice(a+u)}else i>l&&(e.charCodeAt(n+u)===B?c=u:u===0&&(c=0));let h="";for(u=n+c+1;u<=s;++u)(u===s||e.charCodeAt(u)===B)&&(h+=h.length===0?"..":"/..");return`${h}${t.slice(a+c)}`},toNamespacedPath(e){return e},dirname(e){if(P(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===B;let n=-1,s=!0;for(let i=e.length-1;i>=1;--i)if(e.charCodeAt(i)===B){if(!s){n=i;break}}else s=!1;return n===-1?t?"/":".":t&&n===1?"//":e.slice(0,n)},basename(e,t){t!==void 0&&P(t,"ext"),P(e,"path");let n=0,s=-1,i=!0,a;if(t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let o=t.length-1,l=-1;for(a=e.length-1;a>=0;--a){const c=e.charCodeAt(a);if(c===B){if(!i){n=a+1;break}}else l===-1&&(i=!1,l=a+1),o>=0&&(c===t.charCodeAt(o)?--o===-1&&(s=a):(o=-1,s=l))}return n===s?s=l:s===-1&&(s=e.length),e.slice(n,s)}for(a=e.length-1;a>=0;--a)if(e.charCodeAt(a)===B){if(!i){n=a+1;break}}else s===-1&&(i=!1,s=a+1);return s===-1?"":e.slice(n,s)},extname(e){P(e,"path");let t=-1,n=0,s=-1,i=!0,a=0;for(let o=e.length-1;o>=0;--o){const l=e.charCodeAt(o);if(l===B){if(!i){n=o+1;break}continue}s===-1&&(i=!1,s=o+1),l===te?t===-1?t=o:a!==1&&(a=1):t!==-1&&(a=-1)}return t===-1||s===-1||a===0||a===1&&t===s-1&&t===n+1?"":e.slice(t,s)},format:j1.bind(null,"/"),parse(e){P(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const n=e.charCodeAt(0)===B;let s;n?(t.root="/",s=1):s=0;let i=-1,a=0,o=-1,l=!0,c=e.length-1,u=0;for(;c>=s;--c){const h=e.charCodeAt(c);if(h===B){if(!l){a=c+1;break}continue}o===-1&&(l=!1,o=c+1),h===te?i===-1?i=c:u!==1&&(u=1):i!==-1&&(u=-1)}if(o!==-1){const h=a===0&&n?1:a;i===-1||u===0||u===1&&i===o-1&&i===a+1?t.base=t.name=e.slice(h,o):(t.name=e.slice(h,i),t.base=e.slice(h,o),t.ext=e.slice(i,o))}return a>0?t.dir=e.slice(0,a-1):n&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};q.win32=T.win32=T;q.posix=T.posix=q;const Ci=ie==="win32"?T.normalize:q.normalize,_i=ie==="win32"?T.resolve:q.resolve,pi=ie==="win32"?T.relative:q.relative,Li=ie==="win32"?T.dirname:q.dirname,Ni=ie==="win32"?T.basename:q.basename,Si=ie==="win32"?T.extname:q.extname,vi=ie==="win32"?T.sep:q.sep,sr=/^\w[\w\d+.-]*$/,ir=/^\//,ar=/^\/\//;function kt(e,t){if(!e.scheme&&t)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!sr.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path){if(e.authority){if(!ir.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(ar.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}function or(e,t){return!e&&!t?"file":e}function lr(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==O&&(t=O+t):t=O;break}return t}const R="",O="/",ur=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class ne{constructor(t,n,s,i,a,o=!1){typeof t=="object"?(this.scheme=t.scheme||R,this.authority=t.authority||R,this.path=t.path||R,this.query=t.query||R,this.fragment=t.fragment||R):(this.scheme=or(t,o),this.authority=n||R,this.path=lr(this.scheme,s||R),this.query=i||R,this.fragment=a||R,kt(this,o))}static isUri(t){return t instanceof ne?!0:t?typeof t.authority=="string"&&typeof t.fragment=="string"&&typeof t.path=="string"&&typeof t.query=="string"&&typeof t.scheme=="string"&&typeof t.fsPath=="string"&&typeof t.with=="function"&&typeof t.toString=="function":!1}get fsPath(){return rt(this,!1)}with(t){if(!t)return this;let{scheme:n,authority:s,path:i,query:a,fragment:o}=t;return n===void 0?n=this.scheme:n===null&&(n=R),s===void 0?s=this.authority:s===null&&(s=R),i===void 0?i=this.path:i===null&&(i=R),a===void 0?a=this.query:a===null&&(a=R),o===void 0?o=this.fragment:o===null&&(o=R),n===this.scheme&&s===this.authority&&i===this.path&&a===this.query&&o===this.fragment?this:new ae(n,s,i,a,o)}static parse(t,n=!1){const s=ur.exec(t);return s?new ae(s[2]||R,Fe(s[4]||R),Fe(s[5]||R),Fe(s[7]||R),Fe(s[9]||R),n):new ae(R,R,R,R,R)}static file(t){let n=R;if(_e&&(t=t.replace(/\\/g,O)),t[0]===O&&t[1]===O){const s=t.indexOf(O,2);s===-1?(n=t.substring(2),t=O):(n=t.substring(2,s),t=t.substring(s)||O)}return new ae("file",n,t,R,R)}static from(t){const n=new ae(t.scheme,t.authority,t.path,t.query,t.fragment);return kt(n,!0),n}static joinPath(t,...n){if(!t.path)throw new Error("[UriError]: cannot call joinPath on URI without path");let s;return _e&&t.scheme==="file"?s=ne.file(T.join(rt(t,!0),...n)).path:s=q.join(t.path,...n),t.with({path:s})}toString(t=!1){return st(this,t)}toJSON(){return this}static revive(t){if(t){if(t instanceof ne)return t;{const n=new ae(t);return n._formatted=t.external,n._fsPath=t._sep===Q1?t.fsPath:null,n}}else return t}}const Q1=_e?1:void 0;class ae extends ne{constructor(){super(...arguments),this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||(this._fsPath=rt(this,!1)),this._fsPath}toString(t=!1){return t?st(this,!0):(this._formatted||(this._formatted=st(this,!1)),this._formatted)}toJSON(){const t={$mid:1};return this._fsPath&&(t.fsPath=this._fsPath,t._sep=Q1),this._formatted&&(t.external=this._formatted),this.path&&(t.path=this.path),this.scheme&&(t.scheme=this.scheme),this.authority&&(t.authority=this.authority),this.query&&(t.query=this.query),this.fragment&&(t.fragment=this.fragment),t}}const Z1={[58]:"%3A",[47]:"%2F",[63]:"%3F",[35]:"%23",[91]:"%5B",[93]:"%5D",[64]:"%40",[33]:"%21",[36]:"%24",[38]:"%26",[39]:"%27",[40]:"%28",[41]:"%29",[42]:"%2A",[43]:"%2B",[44]:"%2C",[59]:"%3B",[61]:"%3D",[32]:"%20"};function Et(e,t){let n,s=-1;for(let i=0;i<e.length;i++){const a=e.charCodeAt(i);if(a>=97&&a<=122||a>=65&&a<=90||a>=48&&a<=57||a===45||a===46||a===95||a===126||t&&a===47)s!==-1&&(n+=encodeURIComponent(e.substring(s,i)),s=-1),n!==void 0&&(n+=e.charAt(i));else{n===void 0&&(n=e.substr(0,i));const o=Z1[a];o!==void 0?(s!==-1&&(n+=encodeURIComponent(e.substring(s,i)),s=-1),n+=o):s===-1&&(s=i)}}return s!==-1&&(n+=encodeURIComponent(e.substring(s))),n!==void 0?n:e}function cr(e){let t;for(let n=0;n<e.length;n++){const s=e.charCodeAt(n);s===35||s===63?(t===void 0&&(t=e.substr(0,n)),t+=Z1[s]):t!==void 0&&(t+=e[n])}return t!==void 0?t:e}function rt(e,t){let n;return e.authority&&e.path.length>1&&e.scheme==="file"?n=`//${e.authority}${e.path}`:e.path.charCodeAt(0)===47&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&e.path.charCodeAt(2)===58?t?n=e.path.substr(1):n=e.path[1].toLowerCase()+e.path.substr(2):n=e.path,_e&&(n=n.replace(/\//g,"\\")),n}function st(e,t){const n=t?cr:Et;let s="",{scheme:i,authority:a,path:o,query:l,fragment:c}=e;if(i&&(s+=i,s+=":"),(a||i==="file")&&(s+=O,s+=O),a){let u=a.indexOf("@");if(u!==-1){const h=a.substr(0,u);a=a.substr(u+1),u=h.indexOf(":"),u===-1?s+=n(h,!1):(s+=n(h.substr(0,u),!1),s+=":",s+=n(h.substr(u+1),!1)),s+="@"}a=a.toLowerCase(),u=a.indexOf(":"),u===-1?s+=n(a,!1):(s+=n(a.substr(0,u),!1),s+=a.substr(u))}if(o){if(o.length>=3&&o.charCodeAt(0)===47&&o.charCodeAt(2)===58){const u=o.charCodeAt(1);u>=65&&u<=90&&(o=`/${String.fromCharCode(u+32)}:${o.substr(3)}`)}else if(o.length>=2&&o.charCodeAt(1)===58){const u=o.charCodeAt(0);u>=65&&u<=90&&(o=`${String.fromCharCode(u+32)}:${o.substr(2)}`)}s+=n(o,!0)}return l&&(s+="?",s+=n(l,!1)),c&&(s+="#",s+=t?c:Et(c,!1)),s}function Y1(e){try{return decodeURIComponent(e)}catch(t){return e.length>3?e.substr(0,3)+Y1(e.substr(3)):e}}const Dt=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function Fe(e){return e.match(Dt)?e.replace(Dt,t=>Y1(t)):e}class U{constructor(t,n){this.lineNumber=t,this.column=n}with(t=this.lineNumber,n=this.column){return t===this.lineNumber&&n===this.column?this:new U(t,n)}delta(t=0,n=0){return this.with(this.lineNumber+t,this.column+n)}equals(t){return U.equals(this,t)}static equals(t,n){return!t&&!n?!0:!!t&&!!n&&t.lineNumber===n.lineNumber&&t.column===n.column}isBefore(t){return U.isBefore(this,t)}static isBefore(t,n){return t.lineNumber<n.lineNumber?!0:n.lineNumber<t.lineNumber?!1:t.column<n.column}isBeforeOrEqual(t){return U.isBeforeOrEqual(this,t)}static isBeforeOrEqual(t,n){return t.lineNumber<n.lineNumber?!0:n.lineNumber<t.lineNumber?!1:t.column<=n.column}static compare(t,n){const s=t.lineNumber|0,i=n.lineNumber|0;if(s===i){const a=t.column|0,o=n.column|0;return a-o}return s-i}clone(){return new U(this.lineNumber,this.column)}toString(){return"("+this.lineNumber+","+this.column+")"}static lift(t){return new U(t.lineNumber,t.column)}static isIPosition(t){return t&&typeof t.lineNumber=="number"&&typeof t.column=="number"}}class k{constructor(t,n,s,i){t>s||t===s&&n>i?(this.startLineNumber=s,this.startColumn=i,this.endLineNumber=t,this.endColumn=n):(this.startLineNumber=t,this.startColumn=n,this.endLineNumber=s,this.endColumn=i)}isEmpty(){return k.isEmpty(this)}static isEmpty(t){return t.startLineNumber===t.endLineNumber&&t.startColumn===t.endColumn}containsPosition(t){return k.containsPosition(this,t)}static containsPosition(t,n){return!(n.lineNumber<t.startLineNumber||n.lineNumber>t.endLineNumber||n.lineNumber===t.startLineNumber&&n.column<t.startColumn||n.lineNumber===t.endLineNumber&&n.column>t.endColumn)}static strictContainsPosition(t,n){return!(n.lineNumber<t.startLineNumber||n.lineNumber>t.endLineNumber||n.lineNumber===t.startLineNumber&&n.column<=t.startColumn||n.lineNumber===t.endLineNumber&&n.column>=t.endColumn)}containsRange(t){return k.containsRange(this,t)}static containsRange(t,n){return!(n.startLineNumber<t.startLineNumber||n.endLineNumber<t.startLineNumber||n.startLineNumber>t.endLineNumber||n.endLineNumber>t.endLineNumber||n.startLineNumber===t.startLineNumber&&n.startColumn<t.startColumn||n.endLineNumber===t.endLineNumber&&n.endColumn>t.endColumn)}strictContainsRange(t){return k.strictContainsRange(this,t)}static strictContainsRange(t,n){return!(n.startLineNumber<t.startLineNumber||n.endLineNumber<t.startLineNumber||n.startLineNumber>t.endLineNumber||n.endLineNumber>t.endLineNumber||n.startLineNumber===t.startLineNumber&&n.startColumn<=t.startColumn||n.endLineNumber===t.endLineNumber&&n.endColumn>=t.endColumn)}plusRange(t){return k.plusRange(this,t)}static plusRange(t,n){let s,i,a,o;return n.startLineNumber<t.startLineNumber?(s=n.startLineNumber,i=n.startColumn):n.startLineNumber===t.startLineNumber?(s=n.startLineNumber,i=Math.min(n.startColumn,t.startColumn)):(s=t.startLineNumber,i=t.startColumn),n.endLineNumber>t.endLineNumber?(a=n.endLineNumber,o=n.endColumn):n.endLineNumber===t.endLineNumber?(a=n.endLineNumber,o=Math.max(n.endColumn,t.endColumn)):(a=t.endLineNumber,o=t.endColumn),new k(s,i,a,o)}intersectRanges(t){return k.intersectRanges(this,t)}static intersectRanges(t,n){let s=t.startLineNumber,i=t.startColumn,a=t.endLineNumber,o=t.endColumn,l=n.startLineNumber,c=n.startColumn,u=n.endLineNumber,h=n.endColumn;return s<l?(s=l,i=c):s===l&&(i=Math.max(i,c)),a>u?(a=u,o=h):a===u&&(o=Math.min(o,h)),s>a||s===a&&i>o?null:new k(s,i,a,o)}equalsRange(t){return k.equalsRange(this,t)}static equalsRange(t,n){return!!t&&!!n&&t.startLineNumber===n.startLineNumber&&t.startColumn===n.startColumn&&t.endLineNumber===n.endLineNumber&&t.endColumn===n.endColumn}getEndPosition(){return k.getEndPosition(this)}static getEndPosition(t){return new U(t.endLineNumber,t.endColumn)}getStartPosition(){return k.getStartPosition(this)}static getStartPosition(t){return new U(t.startLineNumber,t.startColumn)}toString(){return"["+this.startLineNumber+","+this.startColumn+" -> "+this.endLineNumber+","+this.endColumn+"]"}setEndPosition(t,n){return new k(this.startLineNumber,this.startColumn,t,n)}setStartPosition(t,n){return new k(t,n,this.endLineNumber,this.endColumn)}collapseToStart(){return k.collapseToStart(this)}static collapseToStart(t){return new k(t.startLineNumber,t.startColumn,t.startLineNumber,t.startColumn)}static fromPositions(t,n=t){return new k(t.lineNumber,t.column,n.lineNumber,n.column)}static lift(t){return t?new k(t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):null}static isIRange(t){return t&&typeof t.startLineNumber=="number"&&typeof t.startColumn=="number"&&typeof t.endLineNumber=="number"&&typeof t.endColumn=="number"}static areIntersectingOrTouching(t,n){return!(t.endLineNumber<n.startLineNumber||t.endLineNumber===n.startLineNumber&&t.endColumn<n.startColumn||n.endLineNumber<t.startLineNumber||n.endLineNumber===t.startLineNumber&&n.endColumn<t.startColumn)}static areIntersecting(t,n){return!(t.endLineNumber<n.startLineNumber||t.endLineNumber===n.startLineNumber&&t.endColumn<=n.startColumn||n.endLineNumber<t.startLineNumber||n.endLineNumber===t.startLineNumber&&n.endColumn<=t.startColumn)}static compareRangesUsingStarts(t,n){if(t&&n){const a=t.startLineNumber|0,o=n.startLineNumber|0;if(a===o){const l=t.startColumn|0,c=n.startColumn|0;if(l===c){const u=t.endLineNumber|0,h=n.endLineNumber|0;if(u===h){const f=t.endColumn|0,d=n.endColumn|0;return f-d}return u-h}return l-c}return a-o}return(t?1:0)-(n?1:0)}static compareRangesUsingEnds(t,n){return t.endLineNumber===n.endLineNumber?t.endColumn===n.endColumn?t.startLineNumber===n.startLineNumber?t.startColumn-n.startColumn:t.startLineNumber-n.startLineNumber:t.endColumn-n.endColumn:t.endLineNumber-n.endLineNumber}static spansMultipleLines(t){return t.endLineNumber>t.startLineNumber}toJSON(){return this}}class H extends k{constructor(t,n,s,i){super(t,n,s,i),this.selectionStartLineNumber=t,this.selectionStartColumn=n,this.positionLineNumber=s,this.positionColumn=i}toString(){return"["+this.selectionStartLineNumber+","+this.selectionStartColumn+" -> "+this.positionLineNumber+","+this.positionColumn+"]"}equalsSelection(t){return H.selectionsEqual(this,t)}static selectionsEqual(t,n){return t.selectionStartLineNumber===n.selectionStartLineNumber&&t.selectionStartColumn===n.selectionStartColumn&&t.positionLineNumber===n.positionLineNumber&&t.positionColumn===n.positionColumn}getDirection(){return this.selectionStartLineNumber===this.startLineNumber&&this.selectionStartColumn===this.startColumn?0:1}setEndPosition(t,n){return this.getDirection()===0?new H(this.startLineNumber,this.startColumn,t,n):new H(t,n,this.startLineNumber,this.startColumn)}getPosition(){return new U(this.positionLineNumber,this.positionColumn)}getSelectionStart(){return new U(this.selectionStartLineNumber,this.selectionStartColumn)}setStartPosition(t,n){return this.getDirection()===0?new H(t,n,this.endLineNumber,this.endColumn):new H(this.endLineNumber,this.endColumn,t,n)}static fromPositions(t,n=t){return new H(t.lineNumber,t.column,n.lineNumber,n.column)}static fromRange(t,n){return n===0?new H(t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):new H(t.endLineNumber,t.endColumn,t.startLineNumber,t.startColumn)}static liftSelection(t){return new H(t.selectionStartLineNumber,t.selectionStartColumn,t.positionLineNumber,t.positionColumn)}static selectionsArrEqual(t,n){if(t&&!n||!t&&n)return!1;if(!t&&!n)return!0;if(t.length!==n.length)return!1;for(let s=0,i=t.length;s<i;s++)if(!this.selectionsEqual(t[s],n[s]))return!1;return!0}static isISelection(t){return t&&typeof t.selectionStartLineNumber=="number"&&typeof t.selectionStartColumn=="number"&&typeof t.positionLineNumber=="number"&&typeof t.positionColumn=="number"}static createWithDirection(t,n,s,i,a){return a===0?new H(t,n,s,i):new H(s,i,t,n)}}var it=globalThis&&globalThis.__awaiter||function(e,t,n,s){function i(a){return a instanceof n?a:new n(function(o){o(a)})}return new(n||(n=Promise))(function(a,o){function l(h){try{u(s.next(h))}catch(f){o(f)}}function c(h){try{u(s.throw(h))}catch(f){o(f)}}function u(h){h.done?a(h.value):i(h.value).then(l,c)}u((s=s.apply(e,t||[])).next())})};let fr=class{constructor(){this._map=new Map,this._factories=new Map,this._onDidChange=new $,this.onDidChange=this._onDidChange.event,this._colorMap=null}fire(t){this._onDidChange.fire({changedLanguages:t,changedColorMap:!1})}register(t,n){return this._map.set(t,n),this.fire([t]),Te(()=>{this._map.get(t)===n&&(this._map.delete(t),this.fire([t]))})}registerFactory(t,n){var s;(s=this._factories.get(t))===null||s===void 0||s.dispose();const i=new hr(this,t,n);return this._factories.set(t,i),Te(()=>{const a=this._factories.get(t);!a||a!==i||(this._factories.delete(t),a.dispose())})}getOrCreate(t){return it(this,void 0,void 0,function*(){const n=this.get(t);if(n)return n;const s=this._factories.get(t);return!s||s.isResolved?null:(yield s.resolve(),this.get(t))})}get(t){return this._map.get(t)||null}isResolved(t){if(this.get(t))return!0;const s=this._factories.get(t);return!!(!s||s.isResolved)}setColorMap(t){this._colorMap=t,this._onDidChange.fire({changedLanguages:Array.from(this._map.keys()),changedColorMap:!0})}getColorMap(){return this._colorMap}getDefaultBackground(){return this._colorMap&&this._colorMap.length>2?this._colorMap[2]:null}};class hr extends ve{constructor(t,n,s){super(),this._registry=t,this._languageId=n,this._factory=s,this._isDisposed=!1,this._resolvePromise=null,this._isResolved=!1}get isResolved(){return this._isResolved}dispose(){this._isDisposed=!0,super.dispose()}resolve(){return it(this,void 0,void 0,function*(){return this._resolvePromise||(this._resolvePromise=this._create()),this._resolvePromise})}_create(){return it(this,void 0,void 0,function*(){const t=yield Promise.resolve(this._factory.createTokenizationSupport());this._isResolved=!0,t&&!this._isDisposed&&this._register(this._registry.register(this._languageId,t))})}}function yi(e){return e?e.replace(/\$\((.*?)\)/g,(t,n)=>` ${n} `).trim():""}class r{constructor(t,n,s){this.id=t,this.definition=n,this.description=s,r._allCodicons.push(this)}get classNames(){return"codicon codicon-"+this.id}get classNamesArray(){return["codicon","codicon-"+this.id]}get cssSelector(){return".codicon.codicon-"+this.id}static getAll(){return r._allCodicons}}r._allCodicons=[];r.add=new r("add",{fontCharacter:"\\ea60"});r.plus=new r("plus",r.add.definition);r.gistNew=new r("gist-new",r.add.definition);r.repoCreate=new r("repo-create",r.add.definition);r.lightbulb=new r("lightbulb",{fontCharacter:"\\ea61"});r.lightBulb=new r("light-bulb",{fontCharacter:"\\ea61"});r.repo=new r("repo",{fontCharacter:"\\ea62"});r.repoDelete=new r("repo-delete",{fontCharacter:"\\ea62"});r.gistFork=new r("gist-fork",{fontCharacter:"\\ea63"});r.repoForked=new r("repo-forked",{fontCharacter:"\\ea63"});r.gitPullRequest=new r("git-pull-request",{fontCharacter:"\\ea64"});r.gitPullRequestAbandoned=new r("git-pull-request-abandoned",{fontCharacter:"\\ea64"});r.recordKeys=new r("record-keys",{fontCharacter:"\\ea65"});r.keyboard=new r("keyboard",{fontCharacter:"\\ea65"});r.tag=new r("tag",{fontCharacter:"\\ea66"});r.tagAdd=new r("tag-add",{fontCharacter:"\\ea66"});r.tagRemove=new r("tag-remove",{fontCharacter:"\\ea66"});r.person=new r("person",{fontCharacter:"\\ea67"});r.personFollow=new r("person-follow",{fontCharacter:"\\ea67"});r.personOutline=new r("person-outline",{fontCharacter:"\\ea67"});r.personFilled=new r("person-filled",{fontCharacter:"\\ea67"});r.gitBranch=new r("git-branch",{fontCharacter:"\\ea68"});r.gitBranchCreate=new r("git-branch-create",{fontCharacter:"\\ea68"});r.gitBranchDelete=new r("git-branch-delete",{fontCharacter:"\\ea68"});r.sourceControl=new r("source-control",{fontCharacter:"\\ea68"});r.mirror=new r("mirror",{fontCharacter:"\\ea69"});r.mirrorPublic=new r("mirror-public",{fontCharacter:"\\ea69"});r.star=new r("star",{fontCharacter:"\\ea6a"});r.starAdd=new r("star-add",{fontCharacter:"\\ea6a"});r.starDelete=new r("star-delete",{fontCharacter:"\\ea6a"});r.starEmpty=new r("star-empty",{fontCharacter:"\\ea6a"});r.comment=new r("comment",{fontCharacter:"\\ea6b"});r.commentAdd=new r("comment-add",{fontCharacter:"\\ea6b"});r.alert=new r("alert",{fontCharacter:"\\ea6c"});r.warning=new r("warning",{fontCharacter:"\\ea6c"});r.search=new r("search",{fontCharacter:"\\ea6d"});r.searchSave=new r("search-save",{fontCharacter:"\\ea6d"});r.logOut=new r("log-out",{fontCharacter:"\\ea6e"});r.signOut=new r("sign-out",{fontCharacter:"\\ea6e"});r.logIn=new r("log-in",{fontCharacter:"\\ea6f"});r.signIn=new r("sign-in",{fontCharacter:"\\ea6f"});r.eye=new r("eye",{fontCharacter:"\\ea70"});r.eyeUnwatch=new r("eye-unwatch",{fontCharacter:"\\ea70"});r.eyeWatch=new r("eye-watch",{fontCharacter:"\\ea70"});r.circleFilled=new r("circle-filled",{fontCharacter:"\\ea71"});r.primitiveDot=new r("primitive-dot",{fontCharacter:"\\ea71"});r.closeDirty=new r("close-dirty",{fontCharacter:"\\ea71"});r.debugBreakpoint=new r("debug-breakpoint",{fontCharacter:"\\ea71"});r.debugBreakpointDisabled=new r("debug-breakpoint-disabled",{fontCharacter:"\\ea71"});r.debugHint=new r("debug-hint",{fontCharacter:"\\ea71"});r.primitiveSquare=new r("primitive-square",{fontCharacter:"\\ea72"});r.edit=new r("edit",{fontCharacter:"\\ea73"});r.pencil=new r("pencil",{fontCharacter:"\\ea73"});r.info=new r("info",{fontCharacter:"\\ea74"});r.issueOpened=new r("issue-opened",{fontCharacter:"\\ea74"});r.gistPrivate=new r("gist-private",{fontCharacter:"\\ea75"});r.gitForkPrivate=new r("git-fork-private",{fontCharacter:"\\ea75"});r.lock=new r("lock",{fontCharacter:"\\ea75"});r.mirrorPrivate=new r("mirror-private",{fontCharacter:"\\ea75"});r.close=new r("close",{fontCharacter:"\\ea76"});r.removeClose=new r("remove-close",{fontCharacter:"\\ea76"});r.x=new r("x",{fontCharacter:"\\ea76"});r.repoSync=new r("repo-sync",{fontCharacter:"\\ea77"});r.sync=new r("sync",{fontCharacter:"\\ea77"});r.clone=new r("clone",{fontCharacter:"\\ea78"});r.desktopDownload=new r("desktop-download",{fontCharacter:"\\ea78"});r.beaker=new r("beaker",{fontCharacter:"\\ea79"});r.microscope=new r("microscope",{fontCharacter:"\\ea79"});r.vm=new r("vm",{fontCharacter:"\\ea7a"});r.deviceDesktop=new r("device-desktop",{fontCharacter:"\\ea7a"});r.file=new r("file",{fontCharacter:"\\ea7b"});r.fileText=new r("file-text",{fontCharacter:"\\ea7b"});r.more=new r("more",{fontCharacter:"\\ea7c"});r.ellipsis=new r("ellipsis",{fontCharacter:"\\ea7c"});r.kebabHorizontal=new r("kebab-horizontal",{fontCharacter:"\\ea7c"});r.mailReply=new r("mail-reply",{fontCharacter:"\\ea7d"});r.reply=new r("reply",{fontCharacter:"\\ea7d"});r.organization=new r("organization",{fontCharacter:"\\ea7e"});r.organizationFilled=new r("organization-filled",{fontCharacter:"\\ea7e"});r.organizationOutline=new r("organization-outline",{fontCharacter:"\\ea7e"});r.newFile=new r("new-file",{fontCharacter:"\\ea7f"});r.fileAdd=new r("file-add",{fontCharacter:"\\ea7f"});r.newFolder=new r("new-folder",{fontCharacter:"\\ea80"});r.fileDirectoryCreate=new r("file-directory-create",{fontCharacter:"\\ea80"});r.trash=new r("trash",{fontCharacter:"\\ea81"});r.trashcan=new r("trashcan",{fontCharacter:"\\ea81"});r.history=new r("history",{fontCharacter:"\\ea82"});r.clock=new r("clock",{fontCharacter:"\\ea82"});r.folder=new r("folder",{fontCharacter:"\\ea83"});r.fileDirectory=new r("file-directory",{fontCharacter:"\\ea83"});r.symbolFolder=new r("symbol-folder",{fontCharacter:"\\ea83"});r.logoGithub=new r("logo-github",{fontCharacter:"\\ea84"});r.markGithub=new r("mark-github",{fontCharacter:"\\ea84"});r.github=new r("github",{fontCharacter:"\\ea84"});r.terminal=new r("terminal",{fontCharacter:"\\ea85"});r.console=new r("console",{fontCharacter:"\\ea85"});r.repl=new r("repl",{fontCharacter:"\\ea85"});r.zap=new r("zap",{fontCharacter:"\\ea86"});r.symbolEvent=new r("symbol-event",{fontCharacter:"\\ea86"});r.error=new r("error",{fontCharacter:"\\ea87"});r.stop=new r("stop",{fontCharacter:"\\ea87"});r.variable=new r("variable",{fontCharacter:"\\ea88"});r.symbolVariable=new r("symbol-variable",{fontCharacter:"\\ea88"});r.array=new r("array",{fontCharacter:"\\ea8a"});r.symbolArray=new r("symbol-array",{fontCharacter:"\\ea8a"});r.symbolModule=new r("symbol-module",{fontCharacter:"\\ea8b"});r.symbolPackage=new r("symbol-package",{fontCharacter:"\\ea8b"});r.symbolNamespace=new r("symbol-namespace",{fontCharacter:"\\ea8b"});r.symbolObject=new r("symbol-object",{fontCharacter:"\\ea8b"});r.symbolMethod=new r("symbol-method",{fontCharacter:"\\ea8c"});r.symbolFunction=new r("symbol-function",{fontCharacter:"\\ea8c"});r.symbolConstructor=new r("symbol-constructor",{fontCharacter:"\\ea8c"});r.symbolBoolean=new r("symbol-boolean",{fontCharacter:"\\ea8f"});r.symbolNull=new r("symbol-null",{fontCharacter:"\\ea8f"});r.symbolNumeric=new r("symbol-numeric",{fontCharacter:"\\ea90"});r.symbolNumber=new r("symbol-number",{fontCharacter:"\\ea90"});r.symbolStructure=new r("symbol-structure",{fontCharacter:"\\ea91"});r.symbolStruct=new r("symbol-struct",{fontCharacter:"\\ea91"});r.symbolParameter=new r("symbol-parameter",{fontCharacter:"\\ea92"});r.symbolTypeParameter=new r("symbol-type-parameter",{fontCharacter:"\\ea92"});r.symbolKey=new r("symbol-key",{fontCharacter:"\\ea93"});r.symbolText=new r("symbol-text",{fontCharacter:"\\ea93"});r.symbolReference=new r("symbol-reference",{fontCharacter:"\\ea94"});r.goToFile=new r("go-to-file",{fontCharacter:"\\ea94"});r.symbolEnum=new r("symbol-enum",{fontCharacter:"\\ea95"});r.symbolValue=new r("symbol-value",{fontCharacter:"\\ea95"});r.symbolRuler=new r("symbol-ruler",{fontCharacter:"\\ea96"});r.symbolUnit=new r("symbol-unit",{fontCharacter:"\\ea96"});r.activateBreakpoints=new r("activate-breakpoints",{fontCharacter:"\\ea97"});r.archive=new r("archive",{fontCharacter:"\\ea98"});r.arrowBoth=new r("arrow-both",{fontCharacter:"\\ea99"});r.arrowDown=new r("arrow-down",{fontCharacter:"\\ea9a"});r.arrowLeft=new r("arrow-left",{fontCharacter:"\\ea9b"});r.arrowRight=new r("arrow-right",{fontCharacter:"\\ea9c"});r.arrowSmallDown=new r("arrow-small-down",{fontCharacter:"\\ea9d"});r.arrowSmallLeft=new r("arrow-small-left",{fontCharacter:"\\ea9e"});r.arrowSmallRight=new r("arrow-small-right",{fontCharacter:"\\ea9f"});r.arrowSmallUp=new r("arrow-small-up",{fontCharacter:"\\eaa0"});r.arrowUp=new r("arrow-up",{fontCharacter:"\\eaa1"});r.bell=new r("bell",{fontCharacter:"\\eaa2"});r.bold=new r("bold",{fontCharacter:"\\eaa3"});r.book=new r("book",{fontCharacter:"\\eaa4"});r.bookmark=new r("bookmark",{fontCharacter:"\\eaa5"});r.debugBreakpointConditionalUnverified=new r("debug-breakpoint-conditional-unverified",{fontCharacter:"\\eaa6"});r.debugBreakpointConditional=new r("debug-breakpoint-conditional",{fontCharacter:"\\eaa7"});r.debugBreakpointConditionalDisabled=new r("debug-breakpoint-conditional-disabled",{fontCharacter:"\\eaa7"});r.debugBreakpointDataUnverified=new r("debug-breakpoint-data-unverified",{fontCharacter:"\\eaa8"});r.debugBreakpointData=new r("debug-breakpoint-data",{fontCharacter:"\\eaa9"});r.debugBreakpointDataDisabled=new r("debug-breakpoint-data-disabled",{fontCharacter:"\\eaa9"});r.debugBreakpointLogUnverified=new r("debug-breakpoint-log-unverified",{fontCharacter:"\\eaaa"});r.debugBreakpointLog=new r("debug-breakpoint-log",{fontCharacter:"\\eaab"});r.debugBreakpointLogDisabled=new r("debug-breakpoint-log-disabled",{fontCharacter:"\\eaab"});r.briefcase=new r("briefcase",{fontCharacter:"\\eaac"});r.broadcast=new r("broadcast",{fontCharacter:"\\eaad"});r.browser=new r("browser",{fontCharacter:"\\eaae"});r.bug=new r("bug",{fontCharacter:"\\eaaf"});r.calendar=new r("calendar",{fontCharacter:"\\eab0"});r.caseSensitive=new r("case-sensitive",{fontCharacter:"\\eab1"});r.check=new r("check",{fontCharacter:"\\eab2"});r.checklist=new r("checklist",{fontCharacter:"\\eab3"});r.chevronDown=new r("chevron-down",{fontCharacter:"\\eab4"});r.dropDownButton=new r("drop-down-button",r.chevronDown.definition);r.chevronLeft=new r("chevron-left",{fontCharacter:"\\eab5"});r.chevronRight=new r("chevron-right",{fontCharacter:"\\eab6"});r.chevronUp=new r("chevron-up",{fontCharacter:"\\eab7"});r.chromeClose=new r("chrome-close",{fontCharacter:"\\eab8"});r.chromeMaximize=new r("chrome-maximize",{fontCharacter:"\\eab9"});r.chromeMinimize=new r("chrome-minimize",{fontCharacter:"\\eaba"});r.chromeRestore=new r("chrome-restore",{fontCharacter:"\\eabb"});r.circleOutline=new r("circle-outline",{fontCharacter:"\\eabc"});r.debugBreakpointUnverified=new r("debug-breakpoint-unverified",{fontCharacter:"\\eabc"});r.circleSlash=new r("circle-slash",{fontCharacter:"\\eabd"});r.circuitBoard=new r("circuit-board",{fontCharacter:"\\eabe"});r.clearAll=new r("clear-all",{fontCharacter:"\\eabf"});r.clippy=new r("clippy",{fontCharacter:"\\eac0"});r.closeAll=new r("close-all",{fontCharacter:"\\eac1"});r.cloudDownload=new r("cloud-download",{fontCharacter:"\\eac2"});r.cloudUpload=new r("cloud-upload",{fontCharacter:"\\eac3"});r.code=new r("code",{fontCharacter:"\\eac4"});r.collapseAll=new r("collapse-all",{fontCharacter:"\\eac5"});r.colorMode=new r("color-mode",{fontCharacter:"\\eac6"});r.commentDiscussion=new r("comment-discussion",{fontCharacter:"\\eac7"});r.compareChanges=new r("compare-changes",{fontCharacter:"\\eafd"});r.creditCard=new r("credit-card",{fontCharacter:"\\eac9"});r.dash=new r("dash",{fontCharacter:"\\eacc"});r.dashboard=new r("dashboard",{fontCharacter:"\\eacd"});r.database=new r("database",{fontCharacter:"\\eace"});r.debugContinue=new r("debug-continue",{fontCharacter:"\\eacf"});r.debugDisconnect=new r("debug-disconnect",{fontCharacter:"\\ead0"});r.debugPause=new r("debug-pause",{fontCharacter:"\\ead1"});r.debugRestart=new r("debug-restart",{fontCharacter:"\\ead2"});r.debugStart=new r("debug-start",{fontCharacter:"\\ead3"});r.debugStepInto=new r("debug-step-into",{fontCharacter:"\\ead4"});r.debugStepOut=new r("debug-step-out",{fontCharacter:"\\ead5"});r.debugStepOver=new r("debug-step-over",{fontCharacter:"\\ead6"});r.debugStop=new r("debug-stop",{fontCharacter:"\\ead7"});r.debug=new r("debug",{fontCharacter:"\\ead8"});r.deviceCameraVideo=new r("device-camera-video",{fontCharacter:"\\ead9"});r.deviceCamera=new r("device-camera",{fontCharacter:"\\eada"});r.deviceMobile=new r("device-mobile",{fontCharacter:"\\eadb"});r.diffAdded=new r("diff-added",{fontCharacter:"\\eadc"});r.diffIgnored=new r("diff-ignored",{fontCharacter:"\\eadd"});r.diffModified=new r("diff-modified",{fontCharacter:"\\eade"});r.diffRemoved=new r("diff-removed",{fontCharacter:"\\eadf"});r.diffRenamed=new r("diff-renamed",{fontCharacter:"\\eae0"});r.diff=new r("diff",{fontCharacter:"\\eae1"});r.discard=new r("discard",{fontCharacter:"\\eae2"});r.editorLayout=new r("editor-layout",{fontCharacter:"\\eae3"});r.emptyWindow=new r("empty-window",{fontCharacter:"\\eae4"});r.exclude=new r("exclude",{fontCharacter:"\\eae5"});r.extensions=new r("extensions",{fontCharacter:"\\eae6"});r.eyeClosed=new r("eye-closed",{fontCharacter:"\\eae7"});r.fileBinary=new r("file-binary",{fontCharacter:"\\eae8"});r.fileCode=new r("file-code",{fontCharacter:"\\eae9"});r.fileMedia=new r("file-media",{fontCharacter:"\\eaea"});r.filePdf=new r("file-pdf",{fontCharacter:"\\eaeb"});r.fileSubmodule=new r("file-submodule",{fontCharacter:"\\eaec"});r.fileSymlinkDirectory=new r("file-symlink-directory",{fontCharacter:"\\eaed"});r.fileSymlinkFile=new r("file-symlink-file",{fontCharacter:"\\eaee"});r.fileZip=new r("file-zip",{fontCharacter:"\\eaef"});r.files=new r("files",{fontCharacter:"\\eaf0"});r.filter=new r("filter",{fontCharacter:"\\eaf1"});r.flame=new r("flame",{fontCharacter:"\\eaf2"});r.foldDown=new r("fold-down",{fontCharacter:"\\eaf3"});r.foldUp=new r("fold-up",{fontCharacter:"\\eaf4"});r.fold=new r("fold",{fontCharacter:"\\eaf5"});r.folderActive=new r("folder-active",{fontCharacter:"\\eaf6"});r.folderOpened=new r("folder-opened",{fontCharacter:"\\eaf7"});r.gear=new r("gear",{fontCharacter:"\\eaf8"});r.gift=new r("gift",{fontCharacter:"\\eaf9"});r.gistSecret=new r("gist-secret",{fontCharacter:"\\eafa"});r.gist=new r("gist",{fontCharacter:"\\eafb"});r.gitCommit=new r("git-commit",{fontCharacter:"\\eafc"});r.gitCompare=new r("git-compare",{fontCharacter:"\\eafd"});r.gitMerge=new r("git-merge",{fontCharacter:"\\eafe"});r.githubAction=new r("github-action",{fontCharacter:"\\eaff"});r.githubAlt=new r("github-alt",{fontCharacter:"\\eb00"});r.globe=new r("globe",{fontCharacter:"\\eb01"});r.grabber=new r("grabber",{fontCharacter:"\\eb02"});r.graph=new r("graph",{fontCharacter:"\\eb03"});r.gripper=new r("gripper",{fontCharacter:"\\eb04"});r.heart=new r("heart",{fontCharacter:"\\eb05"});r.home=new r("home",{fontCharacter:"\\eb06"});r.horizontalRule=new r("horizontal-rule",{fontCharacter:"\\eb07"});r.hubot=new r("hubot",{fontCharacter:"\\eb08"});r.inbox=new r("inbox",{fontCharacter:"\\eb09"});r.issueClosed=new r("issue-closed",{fontCharacter:"\\eba4"});r.issueReopened=new r("issue-reopened",{fontCharacter:"\\eb0b"});r.issues=new r("issues",{fontCharacter:"\\eb0c"});r.italic=new r("italic",{fontCharacter:"\\eb0d"});r.jersey=new r("jersey",{fontCharacter:"\\eb0e"});r.json=new r("json",{fontCharacter:"\\eb0f"});r.kebabVertical=new r("kebab-vertical",{fontCharacter:"\\eb10"});r.key=new r("key",{fontCharacter:"\\eb11"});r.law=new r("law",{fontCharacter:"\\eb12"});r.lightbulbAutofix=new r("lightbulb-autofix",{fontCharacter:"\\eb13"});r.linkExternal=new r("link-external",{fontCharacter:"\\eb14"});r.link=new r("link",{fontCharacter:"\\eb15"});r.listOrdered=new r("list-ordered",{fontCharacter:"\\eb16"});r.listUnordered=new r("list-unordered",{fontCharacter:"\\eb17"});r.liveShare=new r("live-share",{fontCharacter:"\\eb18"});r.loading=new r("loading",{fontCharacter:"\\eb19"});r.location=new r("location",{fontCharacter:"\\eb1a"});r.mailRead=new r("mail-read",{fontCharacter:"\\eb1b"});r.mail=new r("mail",{fontCharacter:"\\eb1c"});r.markdown=new r("markdown",{fontCharacter:"\\eb1d"});r.megaphone=new r("megaphone",{fontCharacter:"\\eb1e"});r.mention=new r("mention",{fontCharacter:"\\eb1f"});r.milestone=new r("milestone",{fontCharacter:"\\eb20"});r.mortarBoard=new r("mortar-board",{fontCharacter:"\\eb21"});r.move=new r("move",{fontCharacter:"\\eb22"});r.multipleWindows=new r("multiple-windows",{fontCharacter:"\\eb23"});r.mute=new r("mute",{fontCharacter:"\\eb24"});r.noNewline=new r("no-newline",{fontCharacter:"\\eb25"});r.note=new r("note",{fontCharacter:"\\eb26"});r.octoface=new r("octoface",{fontCharacter:"\\eb27"});r.openPreview=new r("open-preview",{fontCharacter:"\\eb28"});r.package_=new r("package",{fontCharacter:"\\eb29"});r.paintcan=new r("paintcan",{fontCharacter:"\\eb2a"});r.pin=new r("pin",{fontCharacter:"\\eb2b"});r.play=new r("play",{fontCharacter:"\\eb2c"});r.run=new r("run",{fontCharacter:"\\eb2c"});r.plug=new r("plug",{fontCharacter:"\\eb2d"});r.preserveCase=new r("preserve-case",{fontCharacter:"\\eb2e"});r.preview=new r("preview",{fontCharacter:"\\eb2f"});r.project=new r("project",{fontCharacter:"\\eb30"});r.pulse=new r("pulse",{fontCharacter:"\\eb31"});r.question=new r("question",{fontCharacter:"\\eb32"});r.quote=new r("quote",{fontCharacter:"\\eb33"});r.radioTower=new r("radio-tower",{fontCharacter:"\\eb34"});r.reactions=new r("reactions",{fontCharacter:"\\eb35"});r.references=new r("references",{fontCharacter:"\\eb36"});r.refresh=new r("refresh",{fontCharacter:"\\eb37"});r.regex=new r("regex",{fontCharacter:"\\eb38"});r.remoteExplorer=new r("remote-explorer",{fontCharacter:"\\eb39"});r.remote=new r("remote",{fontCharacter:"\\eb3a"});r.remove=new r("remove",{fontCharacter:"\\eb3b"});r.replaceAll=new r("replace-all",{fontCharacter:"\\eb3c"});r.replace=new r("replace",{fontCharacter:"\\eb3d"});r.repoClone=new r("repo-clone",{fontCharacter:"\\eb3e"});r.repoForcePush=new r("repo-force-push",{fontCharacter:"\\eb3f"});r.repoPull=new r("repo-pull",{fontCharacter:"\\eb40"});r.repoPush=new r("repo-push",{fontCharacter:"\\eb41"});r.report=new r("report",{fontCharacter:"\\eb42"});r.requestChanges=new r("request-changes",{fontCharacter:"\\eb43"});r.rocket=new r("rocket",{fontCharacter:"\\eb44"});r.rootFolderOpened=new r("root-folder-opened",{fontCharacter:"\\eb45"});r.rootFolder=new r("root-folder",{fontCharacter:"\\eb46"});r.rss=new r("rss",{fontCharacter:"\\eb47"});r.ruby=new r("ruby",{fontCharacter:"\\eb48"});r.saveAll=new r("save-all",{fontCharacter:"\\eb49"});r.saveAs=new r("save-as",{fontCharacter:"\\eb4a"});r.save=new r("save",{fontCharacter:"\\eb4b"});r.screenFull=new r("screen-full",{fontCharacter:"\\eb4c"});r.screenNormal=new r("screen-normal",{fontCharacter:"\\eb4d"});r.searchStop=new r("search-stop",{fontCharacter:"\\eb4e"});r.server=new r("server",{fontCharacter:"\\eb50"});r.settingsGear=new r("settings-gear",{fontCharacter:"\\eb51"});r.settings=new r("settings",{fontCharacter:"\\eb52"});r.shield=new r("shield",{fontCharacter:"\\eb53"});r.smiley=new r("smiley",{fontCharacter:"\\eb54"});r.sortPrecedence=new r("sort-precedence",{fontCharacter:"\\eb55"});r.splitHorizontal=new r("split-horizontal",{fontCharacter:"\\eb56"});r.splitVertical=new r("split-vertical",{fontCharacter:"\\eb57"});r.squirrel=new r("squirrel",{fontCharacter:"\\eb58"});r.starFull=new r("star-full",{fontCharacter:"\\eb59"});r.starHalf=new r("star-half",{fontCharacter:"\\eb5a"});r.symbolClass=new r("symbol-class",{fontCharacter:"\\eb5b"});r.symbolColor=new r("symbol-color",{fontCharacter:"\\eb5c"});r.symbolCustomColor=new r("symbol-customcolor",{fontCharacter:"\\eb5c"});r.symbolConstant=new r("symbol-constant",{fontCharacter:"\\eb5d"});r.symbolEnumMember=new r("symbol-enum-member",{fontCharacter:"\\eb5e"});r.symbolField=new r("symbol-field",{fontCharacter:"\\eb5f"});r.symbolFile=new r("symbol-file",{fontCharacter:"\\eb60"});r.symbolInterface=new r("symbol-interface",{fontCharacter:"\\eb61"});r.symbolKeyword=new r("symbol-keyword",{fontCharacter:"\\eb62"});r.symbolMisc=new r("symbol-misc",{fontCharacter:"\\eb63"});r.symbolOperator=new r("symbol-operator",{fontCharacter:"\\eb64"});r.symbolProperty=new r("symbol-property",{fontCharacter:"\\eb65"});r.wrench=new r("wrench",{fontCharacter:"\\eb65"});r.wrenchSubaction=new r("wrench-subaction",{fontCharacter:"\\eb65"});r.symbolSnippet=new r("symbol-snippet",{fontCharacter:"\\eb66"});r.tasklist=new r("tasklist",{fontCharacter:"\\eb67"});r.telescope=new r("telescope",{fontCharacter:"\\eb68"});r.textSize=new r("text-size",{fontCharacter:"\\eb69"});r.threeBars=new r("three-bars",{fontCharacter:"\\eb6a"});r.thumbsdown=new r("thumbsdown",{fontCharacter:"\\eb6b"});r.thumbsup=new r("thumbsup",{fontCharacter:"\\eb6c"});r.tools=new r("tools",{fontCharacter:"\\eb6d"});r.triangleDown=new r("triangle-down",{fontCharacter:"\\eb6e"});r.triangleLeft=new r("triangle-left",{fontCharacter:"\\eb6f"});r.triangleRight=new r("triangle-right",{fontCharacter:"\\eb70"});r.triangleUp=new r("triangle-up",{fontCharacter:"\\eb71"});r.twitter=new r("twitter",{fontCharacter:"\\eb72"});r.unfold=new r("unfold",{fontCharacter:"\\eb73"});r.unlock=new r("unlock",{fontCharacter:"\\eb74"});r.unmute=new r("unmute",{fontCharacter:"\\eb75"});r.unverified=new r("unverified",{fontCharacter:"\\eb76"});r.verified=new r("verified",{fontCharacter:"\\eb77"});r.versions=new r("versions",{fontCharacter:"\\eb78"});r.vmActive=new r("vm-active",{fontCharacter:"\\eb79"});r.vmOutline=new r("vm-outline",{fontCharacter:"\\eb7a"});r.vmRunning=new r("vm-running",{fontCharacter:"\\eb7b"});r.watch=new r("watch",{fontCharacter:"\\eb7c"});r.whitespace=new r("whitespace",{fontCharacter:"\\eb7d"});r.wholeWord=new r("whole-word",{fontCharacter:"\\eb7e"});r.window=new r("window",{fontCharacter:"\\eb7f"});r.wordWrap=new r("word-wrap",{fontCharacter:"\\eb80"});r.zoomIn=new r("zoom-in",{fontCharacter:"\\eb81"});r.zoomOut=new r("zoom-out",{fontCharacter:"\\eb82"});r.listFilter=new r("list-filter",{fontCharacter:"\\eb83"});r.listFlat=new r("list-flat",{fontCharacter:"\\eb84"});r.listSelection=new r("list-selection",{fontCharacter:"\\eb85"});r.selection=new r("selection",{fontCharacter:"\\eb85"});r.listTree=new r("list-tree",{fontCharacter:"\\eb86"});r.debugBreakpointFunctionUnverified=new r("debug-breakpoint-function-unverified",{fontCharacter:"\\eb87"});r.debugBreakpointFunction=new r("debug-breakpoint-function",{fontCharacter:"\\eb88"});r.debugBreakpointFunctionDisabled=new r("debug-breakpoint-function-disabled",{fontCharacter:"\\eb88"});r.debugStackframeActive=new r("debug-stackframe-active",{fontCharacter:"\\eb89"});r.debugStackframeDot=new r("debug-stackframe-dot",{fontCharacter:"\\eb8a"});r.debugStackframe=new r("debug-stackframe",{fontCharacter:"\\eb8b"});r.debugStackframeFocused=new r("debug-stackframe-focused",{fontCharacter:"\\eb8b"});r.debugBreakpointUnsupported=new r("debug-breakpoint-unsupported",{fontCharacter:"\\eb8c"});r.symbolString=new r("symbol-string",{fontCharacter:"\\eb8d"});r.debugReverseContinue=new r("debug-reverse-continue",{fontCharacter:"\\eb8e"});r.debugStepBack=new r("debug-step-back",{fontCharacter:"\\eb8f"});r.debugRestartFrame=new r("debug-restart-frame",{fontCharacter:"\\eb90"});r.callIncoming=new r("call-incoming",{fontCharacter:"\\eb92"});r.callOutgoing=new r("call-outgoing",{fontCharacter:"\\eb93"});r.menu=new r("menu",{fontCharacter:"\\eb94"});r.expandAll=new r("expand-all",{fontCharacter:"\\eb95"});r.feedback=new r("feedback",{fontCharacter:"\\eb96"});r.groupByRefType=new r("group-by-ref-type",{fontCharacter:"\\eb97"});r.ungroupByRefType=new r("ungroup-by-ref-type",{fontCharacter:"\\eb98"});r.account=new r("account",{fontCharacter:"\\eb99"});r.bellDot=new r("bell-dot",{fontCharacter:"\\eb9a"});r.debugConsole=new r("debug-console",{fontCharacter:"\\eb9b"});r.library=new r("library",{fontCharacter:"\\eb9c"});r.output=new r("output",{fontCharacter:"\\eb9d"});r.runAll=new r("run-all",{fontCharacter:"\\eb9e"});r.syncIgnored=new r("sync-ignored",{fontCharacter:"\\eb9f"});r.pinned=new r("pinned",{fontCharacter:"\\eba0"});r.githubInverted=new r("github-inverted",{fontCharacter:"\\eba1"});r.debugAlt=new r("debug-alt",{fontCharacter:"\\eb91"});r.serverProcess=new r("server-process",{fontCharacter:"\\eba2"});r.serverEnvironment=new r("server-environment",{fontCharacter:"\\eba3"});r.pass=new r("pass",{fontCharacter:"\\eba4"});r.stopCircle=new r("stop-circle",{fontCharacter:"\\eba5"});r.playCircle=new r("play-circle",{fontCharacter:"\\eba6"});r.record=new r("record",{fontCharacter:"\\eba7"});r.debugAltSmall=new r("debug-alt-small",{fontCharacter:"\\eba8"});r.vmConnect=new r("vm-connect",{fontCharacter:"\\eba9"});r.cloud=new r("cloud",{fontCharacter:"\\ebaa"});r.merge=new r("merge",{fontCharacter:"\\ebab"});r.exportIcon=new r("export",{fontCharacter:"\\ebac"});r.graphLeft=new r("graph-left",{fontCharacter:"\\ebad"});r.magnet=new r("magnet",{fontCharacter:"\\ebae"});r.notebook=new r("notebook",{fontCharacter:"\\ebaf"});r.redo=new r("redo",{fontCharacter:"\\ebb0"});r.checkAll=new r("check-all",{fontCharacter:"\\ebb1"});r.pinnedDirty=new r("pinned-dirty",{fontCharacter:"\\ebb2"});r.passFilled=new r("pass-filled",{fontCharacter:"\\ebb3"});r.circleLargeFilled=new r("circle-large-filled",{fontCharacter:"\\ebb4"});r.circleLargeOutline=new r("circle-large-outline",{fontCharacter:"\\ebb5"});r.combine=new r("combine",{fontCharacter:"\\ebb6"});r.gather=new r("gather",{fontCharacter:"\\ebb6"});r.table=new r("table",{fontCharacter:"\\ebb7"});r.variableGroup=new r("variable-group",{fontCharacter:"\\ebb8"});r.typeHierarchy=new r("type-hierarchy",{fontCharacter:"\\ebb9"});r.typeHierarchySub=new r("type-hierarchy-sub",{fontCharacter:"\\ebba"});r.typeHierarchySuper=new r("type-hierarchy-super",{fontCharacter:"\\ebbb"});r.gitPullRequestCreate=new r("git-pull-request-create",{fontCharacter:"\\ebbc"});r.runAbove=new r("run-above",{fontCharacter:"\\ebbd"});r.runBelow=new r("run-below",{fontCharacter:"\\ebbe"});r.notebookTemplate=new r("notebook-template",{fontCharacter:"\\ebbf"});r.debugRerun=new r("debug-rerun",{fontCharacter:"\\ebc0"});r.workspaceTrusted=new r("workspace-trusted",{fontCharacter:"\\ebc1"});r.workspaceUntrusted=new r("workspace-untrusted",{fontCharacter:"\\ebc2"});r.workspaceUnspecified=new r("workspace-unspecified",{fontCharacter:"\\ebc3"});r.terminalCmd=new r("terminal-cmd",{fontCharacter:"\\ebc4"});r.terminalDebian=new r("terminal-debian",{fontCharacter:"\\ebc5"});r.terminalLinux=new r("terminal-linux",{fontCharacter:"\\ebc6"});r.terminalPowershell=new r("terminal-powershell",{fontCharacter:"\\ebc7"});r.terminalTmux=new r("terminal-tmux",{fontCharacter:"\\ebc8"});r.terminalUbuntu=new r("terminal-ubuntu",{fontCharacter:"\\ebc9"});r.terminalBash=new r("terminal-bash",{fontCharacter:"\\ebca"});r.arrowSwap=new r("arrow-swap",{fontCharacter:"\\ebcb"});r.copy=new r("copy",{fontCharacter:"\\ebcc"});r.personAdd=new r("person-add",{fontCharacter:"\\ebcd"});r.filterFilled=new r("filter-filled",{fontCharacter:"\\ebce"});r.wand=new r("wand",{fontCharacter:"\\ebcf"});r.debugLineByLine=new r("debug-line-by-line",{fontCharacter:"\\ebd0"});r.inspect=new r("inspect",{fontCharacter:"\\ebd1"});r.layers=new r("layers",{fontCharacter:"\\ebd2"});r.layersDot=new r("layers-dot",{fontCharacter:"\\ebd3"});r.layersActive=new r("layers-active",{fontCharacter:"\\ebd4"});r.compass=new r("compass",{fontCharacter:"\\ebd5"});r.compassDot=new r("compass-dot",{fontCharacter:"\\ebd6"});r.compassActive=new r("compass-active",{fontCharacter:"\\ebd7"});r.azure=new r("azure",{fontCharacter:"\\ebd8"});r.issueDraft=new r("issue-draft",{fontCharacter:"\\ebd9"});r.gitPullRequestClosed=new r("git-pull-request-closed",{fontCharacter:"\\ebda"});r.gitPullRequestDraft=new r("git-pull-request-draft",{fontCharacter:"\\ebdb"});r.debugAll=new r("debug-all",{fontCharacter:"\\ebdc"});r.debugCoverage=new r("debug-coverage",{fontCharacter:"\\ebdd"});r.runErrors=new r("run-errors",{fontCharacter:"\\ebde"});r.folderLibrary=new r("folder-library",{fontCharacter:"\\ebdf"});r.debugContinueSmall=new r("debug-continue-small",{fontCharacter:"\\ebe0"});r.beakerStop=new r("beaker-stop",{fontCharacter:"\\ebe1"});r.graphLine=new r("graph-line",{fontCharacter:"\\ebe2"});r.graphScatter=new r("graph-scatter",{fontCharacter:"\\ebe3"});r.pieChart=new r("pie-chart",{fontCharacter:"\\ebe4"});r.bracket=new r("bracket",r.json.definition);r.bracketDot=new r("bracket-dot",{fontCharacter:"\\ebe5"});r.bracketError=new r("bracket-error",{fontCharacter:"\\ebe6"});r.lockSmall=new r("lock-small",{fontCharacter:"\\ebe7"});r.azureDevops=new r("azure-devops",{fontCharacter:"\\ebe8"});r.verifiedFilled=new r("verified-filled",{fontCharacter:"\\ebe9"});r.newLine=new r("newline",{fontCharacter:"\\ebea"});r.layout=new r("layout",{fontCharacter:"\\ebeb"});r.layoutActivitybarLeft=new r("layout-activitybar-left",{fontCharacter:"\\ebec"});r.layoutActivitybarRight=new r("layout-activitybar-right",{fontCharacter:"\\ebed"});r.layoutPanelLeft=new r("layout-panel-left",{fontCharacter:"\\ebee"});r.layoutPanelCenter=new r("layout-panel-center",{fontCharacter:"\\ebef"});r.layoutPanelJustify=new r("layout-panel-justify",{fontCharacter:"\\ebf0"});r.layoutPanelRight=new r("layout-panel-right",{fontCharacter:"\\ebf1"});r.layoutPanel=new r("layout-panel",{fontCharacter:"\\ebf2"});r.layoutSidebarLeft=new r("layout-sidebar-left",{fontCharacter:"\\ebf3"});r.layoutSidebarRight=new r("layout-sidebar-right",{fontCharacter:"\\ebf4"});r.layoutStatusbar=new r("layout-statusbar",{fontCharacter:"\\ebf5"});r.layoutMenubar=new r("layout-menubar",{fontCharacter:"\\ebf6"});r.layoutCentered=new r("layout-centered",{fontCharacter:"\\ebf7"});r.target=new r("target",{fontCharacter:"\\ebf8"});r.indent=new r("indent",{fontCharacter:"\\ebf9"});r.recordSmall=new r("record-small",{fontCharacter:"\\ebfa"});r.errorSmall=new r("error-small",{fontCharacter:"\\ebfb"});r.arrowCircleDown=new r("arrow-circle-down",{fontCharacter:"\\ebfc"});r.arrowCircleLeft=new r("arrow-circle-left",{fontCharacter:"\\ebfd"});r.arrowCircleRight=new r("arrow-circle-right",{fontCharacter:"\\ebfe"});r.arrowCircleUp=new r("arrow-circle-up",{fontCharacter:"\\ebff"});r.dialogError=new r("dialog-error",r.error.definition);r.dialogWarning=new r("dialog-warning",r.warning.definition);r.dialogInfo=new r("dialog-info",r.info.definition);r.dialogClose=new r("dialog-close",r.close.definition);r.treeItemExpanded=new r("tree-item-expanded",r.chevronDown.definition);r.treeFilterOnTypeOn=new r("tree-filter-on-type-on",r.listFilter.definition);r.treeFilterOnTypeOff=new r("tree-filter-on-type-off",r.listSelection.definition);r.treeFilterClear=new r("tree-filter-clear",r.close.definition);r.treeItemLoading=new r("tree-item-loading",r.loading.definition);r.menuSelection=new r("menu-selection",r.check.definition);r.menuSubmenu=new r("menu-submenu",r.chevronRight.definition);r.menuBarMore=new r("menubar-more",r.more.definition);r.scrollbarButtonLeft=new r("scrollbar-button-left",r.triangleLeft.definition);r.scrollbarButtonRight=new r("scrollbar-button-right",r.triangleRight.definition);r.scrollbarButtonUp=new r("scrollbar-button-up",r.triangleUp.definition);r.scrollbarButtonDown=new r("scrollbar-button-down",r.triangleDown.definition);r.toolBarMore=new r("toolbar-more",r.more.definition);r.quickInputBack=new r("quick-input-back",r.arrowLeft.definition);var Rt;(function(e){e.iconNameSegment="[A-Za-z0-9]+",e.iconNameExpression="[A-Za-z0-9-]+",e.iconModifierExpression="~[A-Za-z]+",e.iconNameCharacter="[A-Za-z0-9~-]";const t=new RegExp(`^(${e.iconNameExpression})(${e.iconModifierExpression})?$`);function n(a){if(a instanceof r)return["codicon","codicon-"+a.id];const o=t.exec(a.id);if(!o)return n(r.error);let[,l,c]=o;const u=["codicon","codicon-"+l];return c&&u.push("codicon-modifier-"+c.substr(1)),u}e.asClassNameArray=n;function s(a){return n(a).join(" ")}e.asClassName=s;function i(a){return"."+n(a).join(".")}e.asCSSSelector=i})(Rt||(Rt={}));class Mi{static getLanguageId(t){return(t&255)>>>0}static getTokenType(t){return(t&768)>>>8}static getFontStyle(t){return(t&15360)>>>10}static getForeground(t){return(t&8372224)>>>14}static getBackground(t){return(t&4286578688)>>>23}static getClassNameFromMetadata(t){let s="mtk"+this.getForeground(t);const i=this.getFontStyle(t);return i&1&&(s+=" mtki"),i&2&&(s+=" mtkb"),i&4&&(s+=" mtku"),i&8&&(s+=" mtks"),s}static getInlineStyleFromMetadata(t,n){const s=this.getForeground(t),i=this.getFontStyle(t);let a=`color: ${n[s]};`;i&1&&(a+="font-style: italic;"),i&2&&(a+="font-weight: bold;");let o="";return i&4&&(o+=" underline"),i&8&&(o+=" line-through"),o&&(a+=`text-decoration:${o};`),a}static getPresentationFromMetadata(t){const n=this.getForeground(t),s=this.getFontStyle(t);return{foreground:n,italic:!!(s&1),bold:!!(s&2),underline:!!(s&4),strikethrough:!!(s&8)}}}class dr{constructor(t,n,s){this._tokenBrand=void 0,this.offset=t,this.type=n,this.language=s}toString(){return"("+this.offset+", "+this.type+")"}}class xi{constructor(t,n){this._tokenizationResultBrand=void 0,this.tokens=t,this.endState=n}}class Fi{constructor(t,n){this._encodedTokenizationResultBrand=void 0,this.tokens=t,this.endState=n}}var Pt;(function(e){const t=new Map;t.set(0,r.symbolMethod),t.set(1,r.symbolFunction),t.set(2,r.symbolConstructor),t.set(3,r.symbolField),t.set(4,r.symbolVariable),t.set(5,r.symbolClass),t.set(6,r.symbolStruct),t.set(7,r.symbolInterface),t.set(8,r.symbolModule),t.set(9,r.symbolProperty),t.set(10,r.symbolEvent),t.set(11,r.symbolOperator),t.set(12,r.symbolUnit),t.set(13,r.symbolValue),t.set(15,r.symbolEnum),t.set(14,r.symbolConstant),t.set(15,r.symbolEnum),t.set(16,r.symbolEnumMember),t.set(17,r.symbolKeyword),t.set(27,r.symbolSnippet),t.set(18,r.symbolText),t.set(19,r.symbolColor),t.set(20,r.symbolFile),t.set(21,r.symbolReference),t.set(22,r.symbolCustomColor),t.set(23,r.symbolFolder),t.set(24,r.symbolTypeParameter),t.set(25,r.account),t.set(26,r.issues);function n(a){let o=t.get(a);return o||(console.info("No codicon found for CompletionItemKind "+a),o=r.symbolProperty),o}e.toIcon=n;const s=new Map;s.set("method",0),s.set("function",1),s.set("constructor",2),s.set("field",3),s.set("variable",4),s.set("class",5),s.set("struct",6),s.set("interface",7),s.set("module",8),s.set("property",9),s.set("event",10),s.set("operator",11),s.set("unit",12),s.set("value",13),s.set("constant",14),s.set("enum",15),s.set("enum-member",16),s.set("enumMember",16),s.set("keyword",17),s.set("snippet",27),s.set("text",18),s.set("color",19),s.set("file",20),s.set("reference",21),s.set("customcolor",22),s.set("folder",23),s.set("type-parameter",24),s.set("typeParameter",24),s.set("account",25),s.set("issue",26);function i(a,o){let l=s.get(a);return typeof l=="undefined"&&!o&&(l=9),l}e.fromString=i})(Pt||(Pt={}));var Vt;(function(e){e[e.Automatic=0]="Automatic",e[e.Explicit=1]="Explicit"})(Vt||(Vt={}));var Bt;(function(e){e[e.Invoke=1]="Invoke",e[e.TriggerCharacter=2]="TriggerCharacter",e[e.ContentChange=3]="ContentChange"})(Bt||(Bt={}));var It;(function(e){e[e.Text=0]="Text",e[e.Read=1]="Read",e[e.Write=2]="Write"})(It||(It={}));function ki(e){return e&&ne.isUri(e.uri)&&k.isIRange(e.range)&&(k.isIRange(e.originSelectionRange)||k.isIRange(e.targetSelectionRange))}var Ut;(function(e){const t=new Map;t.set(0,r.symbolFile),t.set(1,r.symbolModule),t.set(2,r.symbolNamespace),t.set(3,r.symbolPackage),t.set(4,r.symbolClass),t.set(5,r.symbolMethod),t.set(6,r.symbolProperty),t.set(7,r.symbolField),t.set(8,r.symbolConstructor),t.set(9,r.symbolEnum),t.set(10,r.symbolInterface),t.set(11,r.symbolFunction),t.set(12,r.symbolVariable),t.set(13,r.symbolConstant),t.set(14,r.symbolString),t.set(15,r.symbolNumber),t.set(16,r.symbolBoolean),t.set(17,r.symbolArray),t.set(18,r.symbolObject),t.set(19,r.symbolKey),t.set(20,r.symbolNull),t.set(21,r.symbolEnumMember),t.set(22,r.symbolStruct),t.set(23,r.symbolEvent),t.set(24,r.symbolOperator),t.set(25,r.symbolTypeParameter);function n(s){let i=t.get(s);return i||(console.info("No codicon found for SymbolKind "+s),i=r.symbolProperty),i}e.toIcon=n})(Ut||(Ut={}));class fe{constructor(t){this.value=t}}fe.Comment=new fe("comment");fe.Imports=new fe("imports");fe.Region=new fe("region");var Tt;(function(e){function t(n){return!n||typeof n!="object"?!1:typeof n.id=="string"&&typeof n.title=="string"}e.is=t})(Tt||(Tt={}));var Wt;(function(e){e[e.Type=1]="Type",e[e.Parameter=2]="Parameter"})(Wt||(Wt={}));const Ei=new fr;var qt;(function(e){e[e.Unknown=0]="Unknown",e[e.Disabled=1]="Disabled",e[e.Enabled=2]="Enabled"})(qt||(qt={}));var Ht;(function(e){e[e.KeepWhitespace=1]="KeepWhitespace",e[e.InsertAsSnippet=4]="InsertAsSnippet"})(Ht||(Ht={}));var zt;(function(e){e[e.Method=0]="Method",e[e.Function=1]="Function",e[e.Constructor=2]="Constructor",e[e.Field=3]="Field",e[e.Variable=4]="Variable",e[e.Class=5]="Class",e[e.Struct=6]="Struct",e[e.Interface=7]="Interface",e[e.Module=8]="Module",e[e.Property=9]="Property",e[e.Event=10]="Event",e[e.Operator=11]="Operator",e[e.Unit=12]="Unit",e[e.Value=13]="Value",e[e.Constant=14]="Constant",e[e.Enum=15]="Enum",e[e.EnumMember=16]="EnumMember",e[e.Keyword=17]="Keyword",e[e.Text=18]="Text",e[e.Color=19]="Color",e[e.File=20]="File",e[e.Reference=21]="Reference",e[e.Customcolor=22]="Customcolor",e[e.Folder=23]="Folder",e[e.TypeParameter=24]="TypeParameter",e[e.User=25]="User",e[e.Issue=26]="Issue",e[e.Snippet=27]="Snippet"})(zt||(zt={}));var Ot;(function(e){e[e.Deprecated=1]="Deprecated"})(Ot||(Ot={}));var $t;(function(e){e[e.Invoke=0]="Invoke",e[e.TriggerCharacter=1]="TriggerCharacter",e[e.TriggerForIncompleteCompletions=2]="TriggerForIncompleteCompletions"})($t||($t={}));var Gt;(function(e){e[e.EXACT=0]="EXACT",e[e.ABOVE=1]="ABOVE",e[e.BELOW=2]="BELOW"})(Gt||(Gt={}));var jt;(function(e){e[e.NotSet=0]="NotSet",e[e.ContentFlush=1]="ContentFlush",e[e.RecoverFromMarkers=2]="RecoverFromMarkers",e[e.Explicit=3]="Explicit",e[e.Paste=4]="Paste",e[e.Undo=5]="Undo",e[e.Redo=6]="Redo"})(jt||(jt={}));var Qt;(function(e){e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"})(Qt||(Qt={}));var Zt;(function(e){e[e.Text=0]="Text",e[e.Read=1]="Read",e[e.Write=2]="Write"})(Zt||(Zt={}));var Yt;(function(e){e[e.None=0]="None",e[e.Keep=1]="Keep",e[e.Brackets=2]="Brackets",e[e.Advanced=3]="Advanced",e[e.Full=4]="Full"})(Yt||(Yt={}));var Jt;(function(e){e[e.acceptSuggestionOnCommitCharacter=0]="acceptSuggestionOnCommitCharacter",e[e.acceptSuggestionOnEnter=1]="acceptSuggestionOnEnter",e[e.accessibilitySupport=2]="accessibilitySupport",e[e.accessibilityPageSize=3]="accessibilityPageSize",e[e.ariaLabel=4]="ariaLabel",e[e.autoClosingBrackets=5]="autoClosingBrackets",e[e.autoClosingDelete=6]="autoClosingDelete",e[e.autoClosingOvertype=7]="autoClosingOvertype",e[e.autoClosingQuotes=8]="autoClosingQuotes",e[e.autoIndent=9]="autoIndent",e[e.automaticLayout=10]="automaticLayout",e[e.autoSurround=11]="autoSurround",e[e.bracketPairColorization=12]="bracketPairColorization",e[e.guides=13]="guides",e[e.codeLens=14]="codeLens",e[e.codeLensFontFamily=15]="codeLensFontFamily",e[e.codeLensFontSize=16]="codeLensFontSize",e[e.colorDecorators=17]="colorDecorators",e[e.columnSelection=18]="columnSelection",e[e.comments=19]="comments",e[e.contextmenu=20]="contextmenu",e[e.copyWithSyntaxHighlighting=21]="copyWithSyntaxHighlighting",e[e.cursorBlinking=22]="cursorBlinking",e[e.cursorSmoothCaretAnimation=23]="cursorSmoothCaretAnimation",e[e.cursorStyle=24]="cursorStyle",e[e.cursorSurroundingLines=25]="cursorSurroundingLines",e[e.cursorSurroundingLinesStyle=26]="cursorSurroundingLinesStyle",e[e.cursorWidth=27]="cursorWidth",e[e.disableLayerHinting=28]="disableLayerHinting",e[e.disableMonospaceOptimizations=29]="disableMonospaceOptimizations",e[e.domReadOnly=30]="domReadOnly",e[e.dragAndDrop=31]="dragAndDrop",e[e.emptySelectionClipboard=32]="emptySelectionClipboard",e[e.extraEditorClassName=33]="extraEditorClassName",e[e.fastScrollSensitivity=34]="fastScrollSensitivity",e[e.find=35]="find",e[e.fixedOverflowWidgets=36]="fixedOverflowWidgets",e[e.folding=37]="folding",e[e.foldingStrategy=38]="foldingStrategy",e[e.foldingHighlight=39]="foldingHighlight",e[e.foldingImportsByDefault=40]="foldingImportsByDefault",e[e.foldingMaximumRegions=41]="foldingMaximumRegions",e[e.unfoldOnClickAfterEndOfLine=42]="unfoldOnClickAfterEndOfLine",e[e.fontFamily=43]="fontFamily",e[e.fontInfo=44]="fontInfo",e[e.fontLigatures=45]="fontLigatures",e[e.fontSize=46]="fontSize",e[e.fontWeight=47]="fontWeight",e[e.formatOnPaste=48]="formatOnPaste",e[e.formatOnType=49]="formatOnType",e[e.glyphMargin=50]="glyphMargin",e[e.gotoLocation=51]="gotoLocation",e[e.hideCursorInOverviewRuler=52]="hideCursorInOverviewRuler",e[e.hover=53]="hover",e[e.inDiffEditor=54]="inDiffEditor",e[e.inlineSuggest=55]="inlineSuggest",e[e.letterSpacing=56]="letterSpacing",e[e.lightbulb=57]="lightbulb",e[e.lineDecorationsWidth=58]="lineDecorationsWidth",e[e.lineHeight=59]="lineHeight",e[e.lineNumbers=60]="lineNumbers",e[e.lineNumbersMinChars=61]="lineNumbersMinChars",e[e.linkedEditing=62]="linkedEditing",e[e.links=63]="links",e[e.matchBrackets=64]="matchBrackets",e[e.minimap=65]="minimap",e[e.mouseStyle=66]="mouseStyle",e[e.mouseWheelScrollSensitivity=67]="mouseWheelScrollSensitivity",e[e.mouseWheelZoom=68]="mouseWheelZoom",e[e.multiCursorMergeOverlapping=69]="multiCursorMergeOverlapping",e[e.multiCursorModifier=70]="multiCursorModifier",e[e.multiCursorPaste=71]="multiCursorPaste",e[e.occurrencesHighlight=72]="occurrencesHighlight",e[e.overviewRulerBorder=73]="overviewRulerBorder",e[e.overviewRulerLanes=74]="overviewRulerLanes",e[e.padding=75]="padding",e[e.parameterHints=76]="parameterHints",e[e.peekWidgetDefaultFocus=77]="peekWidgetDefaultFocus",e[e.definitionLinkOpensInPeek=78]="definitionLinkOpensInPeek",e[e.quickSuggestions=79]="quickSuggestions",e[e.quickSuggestionsDelay=80]="quickSuggestionsDelay",e[e.readOnly=81]="readOnly",e[e.renameOnType=82]="renameOnType",e[e.renderControlCharacters=83]="renderControlCharacters",e[e.renderFinalNewline=84]="renderFinalNewline",e[e.renderLineHighlight=85]="renderLineHighlight",e[e.renderLineHighlightOnlyWhenFocus=86]="renderLineHighlightOnlyWhenFocus",e[e.renderValidationDecorations=87]="renderValidationDecorations",e[e.renderWhitespace=88]="renderWhitespace",e[e.revealHorizontalRightPadding=89]="revealHorizontalRightPadding",e[e.roundedSelection=90]="roundedSelection",e[e.rulers=91]="rulers",e[e.scrollbar=92]="scrollbar",e[e.scrollBeyondLastColumn=93]="scrollBeyondLastColumn",e[e.scrollBeyondLastLine=94]="scrollBeyondLastLine",e[e.scrollPredominantAxis=95]="scrollPredominantAxis",e[e.selectionClipboard=96]="selectionClipboard",e[e.selectionHighlight=97]="selectionHighlight",e[e.selectOnLineNumbers=98]="selectOnLineNumbers",e[e.showFoldingControls=99]="showFoldingControls",e[e.showUnused=100]="showUnused",e[e.snippetSuggestions=101]="snippetSuggestions",e[e.smartSelect=102]="smartSelect",e[e.smoothScrolling=103]="smoothScrolling",e[e.stickyTabStops=104]="stickyTabStops",e[e.stopRenderingLineAfter=105]="stopRenderingLineAfter",e[e.suggest=106]="suggest",e[e.suggestFontSize=107]="suggestFontSize",e[e.suggestLineHeight=108]="suggestLineHeight",e[e.suggestOnTriggerCharacters=109]="suggestOnTriggerCharacters",e[e.suggestSelection=110]="suggestSelection",e[e.tabCompletion=111]="tabCompletion",e[e.tabIndex=112]="tabIndex",e[e.unicodeHighlighting=113]="unicodeHighlighting",e[e.unusualLineTerminators=114]="unusualLineTerminators",e[e.useShadowDOM=115]="useShadowDOM",e[e.useTabStops=116]="useTabStops",e[e.wordSeparators=117]="wordSeparators",e[e.wordWrap=118]="wordWrap",e[e.wordWrapBreakAfterCharacters=119]="wordWrapBreakAfterCharacters",e[e.wordWrapBreakBeforeCharacters=120]="wordWrapBreakBeforeCharacters",e[e.wordWrapColumn=121]="wordWrapColumn",e[e.wordWrapOverride1=122]="wordWrapOverride1",e[e.wordWrapOverride2=123]="wordWrapOverride2",e[e.wrappingIndent=124]="wrappingIndent",e[e.wrappingStrategy=125]="wrappingStrategy",e[e.showDeprecated=126]="showDeprecated",e[e.inlayHints=127]="inlayHints",e[e.editorClassName=128]="editorClassName",e[e.pixelRatio=129]="pixelRatio",e[e.tabFocusMode=130]="tabFocusMode",e[e.layoutInfo=131]="layoutInfo",e[e.wrappingInfo=132]="wrappingInfo"})(Jt||(Jt={}));var Xt;(function(e){e[e.TextDefined=0]="TextDefined",e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"})(Xt||(Xt={}));var Kt;(function(e){e[e.LF=0]="LF",e[e.CRLF=1]="CRLF"})(Kt||(Kt={}));var e1;(function(e){e[e.None=0]="None",e[e.Indent=1]="Indent",e[e.IndentOutdent=2]="IndentOutdent",e[e.Outdent=3]="Outdent"})(e1||(e1={}));var t1;(function(e){e[e.Both=0]="Both",e[e.Right=1]="Right",e[e.Left=2]="Left",e[e.None=3]="None"})(t1||(t1={}));var n1;(function(e){e[e.Type=1]="Type",e[e.Parameter=2]="Parameter"})(n1||(n1={}));var r1;(function(e){e[e.Automatic=0]="Automatic",e[e.Explicit=1]="Explicit"})(r1||(r1={}));var at;(function(e){e[e.DependsOnKbLayout=-1]="DependsOnKbLayout",e[e.Unknown=0]="Unknown",e[e.Backspace=1]="Backspace",e[e.Tab=2]="Tab",e[e.Enter=3]="Enter",e[e.Shift=4]="Shift",e[e.Ctrl=5]="Ctrl",e[e.Alt=6]="Alt",e[e.PauseBreak=7]="PauseBreak",e[e.CapsLock=8]="CapsLock",e[e.Escape=9]="Escape",e[e.Space=10]="Space",e[e.PageUp=11]="PageUp",e[e.PageDown=12]="PageDown",e[e.End=13]="End",e[e.Home=14]="Home",e[e.LeftArrow=15]="LeftArrow",e[e.UpArrow=16]="UpArrow",e[e.RightArrow=17]="RightArrow",e[e.DownArrow=18]="DownArrow",e[e.Insert=19]="Insert",e[e.Delete=20]="Delete",e[e.Digit0=21]="Digit0",e[e.Digit1=22]="Digit1",e[e.Digit2=23]="Digit2",e[e.Digit3=24]="Digit3",e[e.Digit4=25]="Digit4",e[e.Digit5=26]="Digit5",e[e.Digit6=27]="Digit6",e[e.Digit7=28]="Digit7",e[e.Digit8=29]="Digit8",e[e.Digit9=30]="Digit9",e[e.KeyA=31]="KeyA",e[e.KeyB=32]="KeyB",e[e.KeyC=33]="KeyC",e[e.KeyD=34]="KeyD",e[e.KeyE=35]="KeyE",e[e.KeyF=36]="KeyF",e[e.KeyG=37]="KeyG",e[e.KeyH=38]="KeyH",e[e.KeyI=39]="KeyI",e[e.KeyJ=40]="KeyJ",e[e.KeyK=41]="KeyK",e[e.KeyL=42]="KeyL",e[e.KeyM=43]="KeyM",e[e.KeyN=44]="KeyN",e[e.KeyO=45]="KeyO",e[e.KeyP=46]="KeyP",e[e.KeyQ=47]="KeyQ",e[e.KeyR=48]="KeyR",e[e.KeyS=49]="KeyS",e[e.KeyT=50]="KeyT",e[e.KeyU=51]="KeyU",e[e.KeyV=52]="KeyV",e[e.KeyW=53]="KeyW",e[e.KeyX=54]="KeyX",e[e.KeyY=55]="KeyY",e[e.KeyZ=56]="KeyZ",e[e.Meta=57]="Meta",e[e.ContextMenu=58]="ContextMenu",e[e.F1=59]="F1",e[e.F2=60]="F2",e[e.F3=61]="F3",e[e.F4=62]="F4",e[e.F5=63]="F5",e[e.F6=64]="F6",e[e.F7=65]="F7",e[e.F8=66]="F8",e[e.F9=67]="F9",e[e.F10=68]="F10",e[e.F11=69]="F11",e[e.F12=70]="F12",e[e.F13=71]="F13",e[e.F14=72]="F14",e[e.F15=73]="F15",e[e.F16=74]="F16",e[e.F17=75]="F17",e[e.F18=76]="F18",e[e.F19=77]="F19",e[e.NumLock=78]="NumLock",e[e.ScrollLock=79]="ScrollLock",e[e.Semicolon=80]="Semicolon",e[e.Equal=81]="Equal",e[e.Comma=82]="Comma",e[e.Minus=83]="Minus",e[e.Period=84]="Period",e[e.Slash=85]="Slash",e[e.Backquote=86]="Backquote",e[e.BracketLeft=87]="BracketLeft",e[e.Backslash=88]="Backslash",e[e.BracketRight=89]="BracketRight",e[e.Quote=90]="Quote",e[e.OEM_8=91]="OEM_8",e[e.IntlBackslash=92]="IntlBackslash",e[e.Numpad0=93]="Numpad0",e[e.Numpad1=94]="Numpad1",e[e.Numpad2=95]="Numpad2",e[e.Numpad3=96]="Numpad3",e[e.Numpad4=97]="Numpad4",e[e.Numpad5=98]="Numpad5",e[e.Numpad6=99]="Numpad6",e[e.Numpad7=100]="Numpad7",e[e.Numpad8=101]="Numpad8",e[e.Numpad9=102]="Numpad9",e[e.NumpadMultiply=103]="NumpadMultiply",e[e.NumpadAdd=104]="NumpadAdd",e[e.NUMPAD_SEPARATOR=105]="NUMPAD_SEPARATOR",e[e.NumpadSubtract=106]="NumpadSubtract",e[e.NumpadDecimal=107]="NumpadDecimal",e[e.NumpadDivide=108]="NumpadDivide",e[e.KEY_IN_COMPOSITION=109]="KEY_IN_COMPOSITION",e[e.ABNT_C1=110]="ABNT_C1",e[e.ABNT_C2=111]="ABNT_C2",e[e.AudioVolumeMute=112]="AudioVolumeMute",e[e.AudioVolumeUp=113]="AudioVolumeUp",e[e.AudioVolumeDown=114]="AudioVolumeDown",e[e.BrowserSearch=115]="BrowserSearch",e[e.BrowserHome=116]="BrowserHome",e[e.BrowserBack=117]="BrowserBack",e[e.BrowserForward=118]="BrowserForward",e[e.MediaTrackNext=119]="MediaTrackNext",e[e.MediaTrackPrevious=120]="MediaTrackPrevious",e[e.MediaStop=121]="MediaStop",e[e.MediaPlayPause=122]="MediaPlayPause",e[e.LaunchMediaPlayer=123]="LaunchMediaPlayer",e[e.LaunchMail=124]="LaunchMail",e[e.LaunchApp2=125]="LaunchApp2",e[e.Clear=126]="Clear",e[e.MAX_VALUE=127]="MAX_VALUE"})(at||(at={}));var ot;(function(e){e[e.Hint=1]="Hint",e[e.Info=2]="Info",e[e.Warning=4]="Warning",e[e.Error=8]="Error"})(ot||(ot={}));var lt;(function(e){e[e.Unnecessary=1]="Unnecessary",e[e.Deprecated=2]="Deprecated"})(lt||(lt={}));var s1;(function(e){e[e.Inline=1]="Inline",e[e.Gutter=2]="Gutter"})(s1||(s1={}));var i1;(function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.TEXTAREA=1]="TEXTAREA",e[e.GUTTER_GLYPH_MARGIN=2]="GUTTER_GLYPH_MARGIN",e[e.GUTTER_LINE_NUMBERS=3]="GUTTER_LINE_NUMBERS",e[e.GUTTER_LINE_DECORATIONS=4]="GUTTER_LINE_DECORATIONS",e[e.GUTTER_VIEW_ZONE=5]="GUTTER_VIEW_ZONE",e[e.CONTENT_TEXT=6]="CONTENT_TEXT",e[e.CONTENT_EMPTY=7]="CONTENT_EMPTY",e[e.CONTENT_VIEW_ZONE=8]="CONTENT_VIEW_ZONE",e[e.CONTENT_WIDGET=9]="CONTENT_WIDGET",e[e.OVERVIEW_RULER=10]="OVERVIEW_RULER",e[e.SCROLLBAR=11]="SCROLLBAR",e[e.OVERLAY_WIDGET=12]="OVERLAY_WIDGET",e[e.OUTSIDE_EDITOR=13]="OUTSIDE_EDITOR"})(i1||(i1={}));var a1;(function(e){e[e.TOP_RIGHT_CORNER=0]="TOP_RIGHT_CORNER",e[e.BOTTOM_RIGHT_CORNER=1]="BOTTOM_RIGHT_CORNER",e[e.TOP_CENTER=2]="TOP_CENTER"})(a1||(a1={}));var o1;(function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=4]="Right",e[e.Full=7]="Full"})(o1||(o1={}));var l1;(function(e){e[e.Left=0]="Left",e[e.Right=1]="Right",e[e.None=2]="None"})(l1||(l1={}));var u1;(function(e){e[e.Off=0]="Off",e[e.On=1]="On",e[e.Relative=2]="Relative",e[e.Interval=3]="Interval",e[e.Custom=4]="Custom"})(u1||(u1={}));var c1;(function(e){e[e.None=0]="None",e[e.Text=1]="Text",e[e.Blocks=2]="Blocks"})(c1||(c1={}));var f1;(function(e){e[e.Smooth=0]="Smooth",e[e.Immediate=1]="Immediate"})(f1||(f1={}));var h1;(function(e){e[e.Auto=1]="Auto",e[e.Hidden=2]="Hidden",e[e.Visible=3]="Visible"})(h1||(h1={}));var ut;(function(e){e[e.LTR=0]="LTR",e[e.RTL=1]="RTL"})(ut||(ut={}));var d1;(function(e){e[e.Invoke=1]="Invoke",e[e.TriggerCharacter=2]="TriggerCharacter",e[e.ContentChange=3]="ContentChange"})(d1||(d1={}));var m1;(function(e){e[e.File=0]="File",e[e.Module=1]="Module",e[e.Namespace=2]="Namespace",e[e.Package=3]="Package",e[e.Class=4]="Class",e[e.Method=5]="Method",e[e.Property=6]="Property",e[e.Field=7]="Field",e[e.Constructor=8]="Constructor",e[e.Enum=9]="Enum",e[e.Interface=10]="Interface",e[e.Function=11]="Function",e[e.Variable=12]="Variable",e[e.Constant=13]="Constant",e[e.String=14]="String",e[e.Number=15]="Number",e[e.Boolean=16]="Boolean",e[e.Array=17]="Array",e[e.Object=18]="Object",e[e.Key=19]="Key",e[e.Null=20]="Null",e[e.EnumMember=21]="EnumMember",e[e.Struct=22]="Struct",e[e.Event=23]="Event",e[e.Operator=24]="Operator",e[e.TypeParameter=25]="TypeParameter"})(m1||(m1={}));var g1;(function(e){e[e.Deprecated=1]="Deprecated"})(g1||(g1={}));var b1;(function(e){e[e.Hidden=0]="Hidden",e[e.Blink=1]="Blink",e[e.Smooth=2]="Smooth",e[e.Phase=3]="Phase",e[e.Expand=4]="Expand",e[e.Solid=5]="Solid"})(b1||(b1={}));var w1;(function(e){e[e.Line=1]="Line",e[e.Block=2]="Block",e[e.Underline=3]="Underline",e[e.LineThin=4]="LineThin",e[e.BlockOutline=5]="BlockOutline",e[e.UnderlineThin=6]="UnderlineThin"})(w1||(w1={}));var C1;(function(e){e[e.AlwaysGrowsWhenTypingAtEdges=0]="AlwaysGrowsWhenTypingAtEdges",e[e.NeverGrowsWhenTypingAtEdges=1]="NeverGrowsWhenTypingAtEdges",e[e.GrowsOnlyWhenTypingBefore=2]="GrowsOnlyWhenTypingBefore",e[e.GrowsOnlyWhenTypingAfter=3]="GrowsOnlyWhenTypingAfter"})(C1||(C1={}));var _1;(function(e){e[e.None=0]="None",e[e.Same=1]="Same",e[e.Indent=2]="Indent",e[e.DeepIndent=3]="DeepIndent"})(_1||(_1={}));class Ae{static chord(t,n){return Jn(t,n)}}Ae.CtrlCmd=2048;Ae.Shift=1024;Ae.Alt=512;Ae.WinCtrl=256;function mr(){return{editor:void 0,languages:void 0,CancellationTokenSource:jn,Emitter:$,KeyCode:at,KeyMod:Ae,Position:U,Range:k,Selection:H,SelectionDirection:ut,MarkerSeverity:ot,MarkerTag:lt,Uri:ne,Token:dr}}class gr{constructor(t){this.computeFn=t,this.lastCache=void 0,this.lastArgKey=void 0}get(t){const n=JSON.stringify(t);return this.lastArgKey!==n&&(this.lastArgKey=n,this.lastCache=this.computeFn(t)),this.lastCache}}class J1{constructor(t){this.executor=t,this._didRun=!1}getValue(){if(!this._didRun)try{this._value=this.executor()}catch(t){this._error=t}finally{this._didRun=!0}if(this._error)throw this._error;return this._value}get rawValue(){return this._value}}var X1;function Di(e){return!e||typeof e!="string"?!0:e.trim().length===0}const br=/{(\d+)}/g;function Ri(e,...t){return t.length===0?e:e.replace(br,function(n,s){const i=parseInt(s,10);return isNaN(i)||i<0||i>=t.length?n:t[i]})}function Pi(e){return e.replace(/[<>&]/g,function(t){switch(t){case"<":return"&lt;";case">":return"&gt;";case"&":return"&amp;";default:return t}})}function K1(e){return e.replace(/[\\\{\}\*\+\?\|\^\$\.\[\]\(\)]/g,"\\$&")}function Vi(e,t=" "){const n=wr(e,t);return Cr(n,t)}function wr(e,t){if(!e||!t)return e;const n=t.length;if(n===0||e.length===0)return e;let s=0;for(;e.indexOf(t,s)===s;)s=s+n;return e.substring(s)}function Cr(e,t){if(!e||!t)return e;const n=t.length,s=e.length;if(n===0||s===0)return e;let i=s,a=-1;for(;a=e.lastIndexOf(t,i-1),!(a===-1||a+n!==i);){if(a===0)return"";i=a}return e.substring(0,i)}function Bi(e){return e.replace(/[\-\\\{\}\+\?\|\^\$\.\,\[\]\(\)\#\s]/g,"\\$&").replace(/[\*]/g,".*")}function Ii(e){return e.replace(/\*/g,"")}function _r(e,t,n={}){if(!e)throw new Error("Cannot create regex from empty string");t||(e=K1(e)),n.wholeWord&&(/\B/.test(e.charAt(0))||(e="\\b"+e),/\B/.test(e.charAt(e.length-1))||(e=e+"\\b"));let s="";return n.global&&(s+="g"),n.matchCase||(s+="i"),n.multiline&&(s+="m"),n.unicode&&(s+="u"),new RegExp(e,s)}function Ui(e){return e.source==="^"||e.source==="^$"||e.source==="$"||e.source==="^\\s*$"?!1:!!(e.exec("")&&e.lastIndex===0)}function Ti(e){return(e.global?"g":"")+(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")}function pr(e){return e.split(/\r\n|\r|\n/)}function Lr(e){for(let t=0,n=e.length;t<n;t++){const s=e.charCodeAt(t);if(s!==32&&s!==9)return t}return-1}function Wi(e,t=0,n=e.length){for(let s=t;s<n;s++){const i=e.charCodeAt(s);if(i!==32&&i!==9)return e.substring(t,s)}return e.substring(t,n)}function Nr(e,t=e.length-1){for(let n=t;n>=0;n--){const s=e.charCodeAt(n);if(s!==32&&s!==9)return n}return-1}function qi(e,t){return e<t?-1:e>t?1:0}function Sr(e,t,n=0,s=e.length,i=0,a=t.length){for(;n<s&&i<a;n++,i++){let c=e.charCodeAt(n),u=t.charCodeAt(i);if(c<u)return-1;if(c>u)return 1}const o=s-n,l=a-i;return o<l?-1:o>l?1:0}function Hi(e,t){return _t(e,t,0,e.length,0,t.length)}function _t(e,t,n=0,s=e.length,i=0,a=t.length){for(;n<s&&i<a;n++,i++){let c=e.charCodeAt(n),u=t.charCodeAt(i);if(c===u)continue;if(c>=128||u>=128)return Sr(e.toLowerCase(),t.toLowerCase(),n,s,i,a);p1(c)&&(c-=32),p1(u)&&(u-=32);const h=c-u;if(h!==0)return h}const o=s-n,l=a-i;return o<l?-1:o>l?1:0}function p1(e){return e>=97&&e<=122}function en(e){return e>=65&&e<=90}function zi(e,t){return e.length===t.length&&_t(e,t)===0}function Oi(e,t){const n=t.length;return t.length>e.length?!1:_t(e,t,0,n)===0}function $i(e,t){let n,s=Math.min(e.length,t.length);for(n=0;n<s;n++)if(e.charCodeAt(n)!==t.charCodeAt(n))return n;return s}function Gi(e,t){let n,s=Math.min(e.length,t.length);const i=e.length-1,a=t.length-1;for(n=0;n<s;n++)if(e.charCodeAt(i-n)!==t.charCodeAt(a-n))return n;return s}function pe(e){return 55296<=e&&e<=56319}function Le(e){return 56320<=e&&e<=57343}function pt(e,t){return(e-55296<<10)+(t-56320)+65536}function tn(e,t,n){const s=e.charCodeAt(n);if(pe(s)&&n+1<t){const i=e.charCodeAt(n+1);if(Le(i))return pt(s,i)}return s}function vr(e,t){const n=e.charCodeAt(t-1);if(Le(n)&&t>1){const s=e.charCodeAt(t-2);if(pe(s))return pt(s,n)}return n}class Lt{constructor(t,n=0){this._str=t,this._len=t.length,this._offset=n}get offset(){return this._offset}setOffset(t){this._offset=t}prevCodePoint(){const t=vr(this._str,this._offset);return this._offset-=t>=65536?2:1,t}nextCodePoint(){const t=tn(this._str,this._len,this._offset);return this._offset+=t>=65536?2:1,t}eol(){return this._offset>=this._len}}class nn{constructor(t,n=0){this._iterator=new Lt(t,n)}get offset(){return this._iterator.offset}nextGraphemeLength(){const t=ee.getInstance(),n=this._iterator,s=n.offset;let i=t.getGraphemeBreakType(n.nextCodePoint());for(;!n.eol();){const a=n.offset,o=t.getGraphemeBreakType(n.nextCodePoint());if(L1(i,o)){n.setOffset(a);break}i=o}return n.offset-s}prevGraphemeLength(){const t=ee.getInstance(),n=this._iterator,s=n.offset;let i=t.getGraphemeBreakType(n.prevCodePoint());for(;n.offset>0;){const a=n.offset,o=t.getGraphemeBreakType(n.prevCodePoint());if(L1(o,i)){n.setOffset(a);break}i=o}return s-n.offset}eol(){return this._iterator.eol()}}function Ar(e,t){return new nn(e,t).nextGraphemeLength()}function yr(e,t){return new nn(e,t).prevGraphemeLength()}function ji(e,t){t>0&&Le(e.charCodeAt(t))&&t--;const n=t+Ar(e,t);return[n-yr(e,n),n]}const Mr=/(?:[\u05BE\u05C0\u05C3\u05C6\u05D0-\u05F4\u0608\u060B\u060D\u061B-\u064A\u066D-\u066F\u0671-\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u0710\u0712-\u072F\u074D-\u07A5\u07B1-\u07EA\u07F4\u07F5\u07FA\u07FE-\u0815\u081A\u0824\u0828\u0830-\u0858\u085E-\u088E\u08A0-\u08C9\u200F\uFB1D\uFB1F-\uFB28\uFB2A-\uFD3D\uFD50-\uFDC7\uFDF0-\uFDFC\uFE70-\uFEFC]|\uD802[\uDC00-\uDD1B\uDD20-\uDE00\uDE10-\uDE35\uDE40-\uDEE4\uDEEB-\uDF35\uDF40-\uDFFF]|\uD803[\uDC00-\uDD23\uDE80-\uDEA9\uDEAD-\uDF45\uDF51-\uDF81\uDF86-\uDFF6]|\uD83A[\uDC00-\uDCCF\uDD00-\uDD43\uDD4B-\uDFFF]|\uD83B[\uDC00-\uDEBB])/;function Qi(e){return Mr.test(e)}const xr=/^[\t\n\r\x20-\x7E]*$/;function Fr(e){return xr.test(e)}const kr=/[\u2028\u2029]/;function Zi(e){return kr.test(e)}function Yi(e){return e>=11904&&e<=55215||e>=63744&&e<=64255||e>=65281&&e<=65374}function Er(e){return e>=127462&&e<=127487||e===8986||e===8987||e===9200||e===9203||e>=9728&&e<=10175||e===11088||e===11093||e>=127744&&e<=128591||e>=128640&&e<=128764||e>=128992&&e<=129008||e>=129280&&e<=129535||e>=129648&&e<=129782}const Ji=String.fromCharCode(65279);function Xi(e){return!!(e&&e.length>0&&e.charCodeAt(0)===65279)}function Ki(e,t=!1){return e?(t&&(e=e.replace(/\\./g,"")),e.toLowerCase()!==e):!1}function ea(e){return e=e%(2*26),e<26?String.fromCharCode(97+e):String.fromCharCode(65+e-26)}function L1(e,t){return e===0?t!==5&&t!==7:e===2&&t===3?!1:e===4||e===2||e===3||t===4||t===2||t===3?!0:!(e===8&&(t===8||t===9||t===11||t===12)||(e===11||e===9)&&(t===9||t===10)||(e===12||e===10)&&t===10||t===5||t===13||t===7||e===1||e===13&&t===14||e===6&&t===6)}class ee{constructor(){this._data=Dr()}static getInstance(){return ee._INSTANCE||(ee._INSTANCE=new ee),ee._INSTANCE}getGraphemeBreakType(t){if(t<32)return t===10?3:t===13?2:4;if(t<127)return 0;const n=this._data,s=n.length/3;let i=1;for(;i<=s;)if(t<n[3*i])i=2*i;else if(t>n[3*i+1])i=2*i+1;else return n[3*i+2];return 0}}ee._INSTANCE=null;function Dr(){return JSON.parse("[0,0,0,51229,51255,12,44061,44087,12,127462,127487,6,7083,7085,5,47645,47671,12,54813,54839,12,128678,128678,14,3270,3270,5,9919,9923,14,45853,45879,12,49437,49463,12,53021,53047,12,71216,71218,7,128398,128399,14,129360,129374,14,2519,2519,5,4448,4519,9,9742,9742,14,12336,12336,14,44957,44983,12,46749,46775,12,48541,48567,12,50333,50359,12,52125,52151,12,53917,53943,12,69888,69890,5,73018,73018,5,127990,127990,14,128558,128559,14,128759,128760,14,129653,129655,14,2027,2035,5,2891,2892,7,3761,3761,5,6683,6683,5,8293,8293,4,9825,9826,14,9999,9999,14,43452,43453,5,44509,44535,12,45405,45431,12,46301,46327,12,47197,47223,12,48093,48119,12,48989,49015,12,49885,49911,12,50781,50807,12,51677,51703,12,52573,52599,12,53469,53495,12,54365,54391,12,65279,65279,4,70471,70472,7,72145,72147,7,119173,119179,5,127799,127818,14,128240,128244,14,128512,128512,14,128652,128652,14,128721,128722,14,129292,129292,14,129445,129450,14,129734,129743,14,1476,1477,5,2366,2368,7,2750,2752,7,3076,3076,5,3415,3415,5,4141,4144,5,6109,6109,5,6964,6964,5,7394,7400,5,9197,9198,14,9770,9770,14,9877,9877,14,9968,9969,14,10084,10084,14,43052,43052,5,43713,43713,5,44285,44311,12,44733,44759,12,45181,45207,12,45629,45655,12,46077,46103,12,46525,46551,12,46973,46999,12,47421,47447,12,47869,47895,12,48317,48343,12,48765,48791,12,49213,49239,12,49661,49687,12,50109,50135,12,50557,50583,12,51005,51031,12,51453,51479,12,51901,51927,12,52349,52375,12,52797,52823,12,53245,53271,12,53693,53719,12,54141,54167,12,54589,54615,12,55037,55063,12,69506,69509,5,70191,70193,5,70841,70841,7,71463,71467,5,72330,72342,5,94031,94031,5,123628,123631,5,127763,127765,14,127941,127941,14,128043,128062,14,128302,128317,14,128465,128467,14,128539,128539,14,128640,128640,14,128662,128662,14,128703,128703,14,128745,128745,14,129004,129007,14,129329,129330,14,129402,129402,14,129483,129483,14,129686,129704,14,130048,131069,14,173,173,4,1757,1757,1,2200,2207,5,2434,2435,7,2631,2632,5,2817,2817,5,3008,3008,5,3201,3201,5,3387,3388,5,3542,3542,5,3902,3903,7,4190,4192,5,6002,6003,5,6439,6440,5,6765,6770,7,7019,7027,5,7154,7155,7,8205,8205,13,8505,8505,14,9654,9654,14,9757,9757,14,9792,9792,14,9852,9853,14,9890,9894,14,9937,9937,14,9981,9981,14,10035,10036,14,11035,11036,14,42654,42655,5,43346,43347,7,43587,43587,5,44006,44007,7,44173,44199,12,44397,44423,12,44621,44647,12,44845,44871,12,45069,45095,12,45293,45319,12,45517,45543,12,45741,45767,12,45965,45991,12,46189,46215,12,46413,46439,12,46637,46663,12,46861,46887,12,47085,47111,12,47309,47335,12,47533,47559,12,47757,47783,12,47981,48007,12,48205,48231,12,48429,48455,12,48653,48679,12,48877,48903,12,49101,49127,12,49325,49351,12,49549,49575,12,49773,49799,12,49997,50023,12,50221,50247,12,50445,50471,12,50669,50695,12,50893,50919,12,51117,51143,12,51341,51367,12,51565,51591,12,51789,51815,12,52013,52039,12,52237,52263,12,52461,52487,12,52685,52711,12,52909,52935,12,53133,53159,12,53357,53383,12,53581,53607,12,53805,53831,12,54029,54055,12,54253,54279,12,54477,54503,12,54701,54727,12,54925,54951,12,55149,55175,12,68101,68102,5,69762,69762,7,70067,70069,7,70371,70378,5,70720,70721,7,71087,71087,5,71341,71341,5,71995,71996,5,72249,72249,7,72850,72871,5,73109,73109,5,118576,118598,5,121505,121519,5,127245,127247,14,127568,127569,14,127777,127777,14,127872,127891,14,127956,127967,14,128015,128016,14,128110,128172,14,128259,128259,14,128367,128368,14,128424,128424,14,128488,128488,14,128530,128532,14,128550,128551,14,128566,128566,14,128647,128647,14,128656,128656,14,128667,128673,14,128691,128693,14,128715,128715,14,128728,128732,14,128752,128752,14,128765,128767,14,129096,129103,14,129311,129311,14,129344,129349,14,129394,129394,14,129413,129425,14,129466,129471,14,129511,129535,14,129664,129666,14,129719,129722,14,129760,129767,14,917536,917631,5,13,13,2,1160,1161,5,1564,1564,4,1807,1807,1,2085,2087,5,2307,2307,7,2382,2383,7,2497,2500,5,2563,2563,7,2677,2677,5,2763,2764,7,2879,2879,5,2914,2915,5,3021,3021,5,3142,3144,5,3263,3263,5,3285,3286,5,3398,3400,7,3530,3530,5,3633,3633,5,3864,3865,5,3974,3975,5,4155,4156,7,4229,4230,5,5909,5909,7,6078,6085,7,6277,6278,5,6451,6456,7,6744,6750,5,6846,6846,5,6972,6972,5,7074,7077,5,7146,7148,7,7222,7223,5,7416,7417,5,8234,8238,4,8417,8417,5,9000,9000,14,9203,9203,14,9730,9731,14,9748,9749,14,9762,9763,14,9776,9783,14,9800,9811,14,9831,9831,14,9872,9873,14,9882,9882,14,9900,9903,14,9929,9933,14,9941,9960,14,9974,9974,14,9989,9989,14,10006,10006,14,10062,10062,14,10160,10160,14,11647,11647,5,12953,12953,14,43019,43019,5,43232,43249,5,43443,43443,5,43567,43568,7,43696,43696,5,43765,43765,7,44013,44013,5,44117,44143,12,44229,44255,12,44341,44367,12,44453,44479,12,44565,44591,12,44677,44703,12,44789,44815,12,44901,44927,12,45013,45039,12,45125,45151,12,45237,45263,12,45349,45375,12,45461,45487,12,45573,45599,12,45685,45711,12,45797,45823,12,45909,45935,12,46021,46047,12,46133,46159,12,46245,46271,12,46357,46383,12,46469,46495,12,46581,46607,12,46693,46719,12,46805,46831,12,46917,46943,12,47029,47055,12,47141,47167,12,47253,47279,12,47365,47391,12,47477,47503,12,47589,47615,12,47701,47727,12,47813,47839,12,47925,47951,12,48037,48063,12,48149,48175,12,48261,48287,12,48373,48399,12,48485,48511,12,48597,48623,12,48709,48735,12,48821,48847,12,48933,48959,12,49045,49071,12,49157,49183,12,49269,49295,12,49381,49407,12,49493,49519,12,49605,49631,12,49717,49743,12,49829,49855,12,49941,49967,12,50053,50079,12,50165,50191,12,50277,50303,12,50389,50415,12,50501,50527,12,50613,50639,12,50725,50751,12,50837,50863,12,50949,50975,12,51061,51087,12,51173,51199,12,51285,51311,12,51397,51423,12,51509,51535,12,51621,51647,12,51733,51759,12,51845,51871,12,51957,51983,12,52069,52095,12,52181,52207,12,52293,52319,12,52405,52431,12,52517,52543,12,52629,52655,12,52741,52767,12,52853,52879,12,52965,52991,12,53077,53103,12,53189,53215,12,53301,53327,12,53413,53439,12,53525,53551,12,53637,53663,12,53749,53775,12,53861,53887,12,53973,53999,12,54085,54111,12,54197,54223,12,54309,54335,12,54421,54447,12,54533,54559,12,54645,54671,12,54757,54783,12,54869,54895,12,54981,55007,12,55093,55119,12,55243,55291,10,66045,66045,5,68325,68326,5,69688,69702,5,69817,69818,5,69957,69958,7,70089,70092,5,70198,70199,5,70462,70462,5,70502,70508,5,70750,70750,5,70846,70846,7,71100,71101,5,71230,71230,7,71351,71351,5,71737,71738,5,72000,72000,7,72160,72160,5,72273,72278,5,72752,72758,5,72882,72883,5,73031,73031,5,73461,73462,7,94192,94193,7,119149,119149,7,121403,121452,5,122915,122916,5,126980,126980,14,127358,127359,14,127535,127535,14,127759,127759,14,127771,127771,14,127792,127793,14,127825,127867,14,127897,127899,14,127945,127945,14,127985,127986,14,128000,128007,14,128021,128021,14,128066,128100,14,128184,128235,14,128249,128252,14,128266,128276,14,128335,128335,14,128379,128390,14,128407,128419,14,128444,128444,14,128481,128481,14,128499,128499,14,128526,128526,14,128536,128536,14,128543,128543,14,128556,128556,14,128564,128564,14,128577,128580,14,128643,128645,14,128649,128649,14,128654,128654,14,128660,128660,14,128664,128664,14,128675,128675,14,128686,128689,14,128695,128696,14,128705,128709,14,128717,128719,14,128725,128725,14,128736,128741,14,128747,128748,14,128755,128755,14,128762,128762,14,128981,128991,14,129009,129023,14,129160,129167,14,129296,129304,14,129320,129327,14,129340,129342,14,129356,129356,14,129388,129392,14,129399,129400,14,129404,129407,14,129432,129442,14,129454,129455,14,129473,129474,14,129485,129487,14,129648,129651,14,129659,129660,14,129671,129679,14,129709,129711,14,129728,129730,14,129751,129753,14,129776,129782,14,917505,917505,4,917760,917999,5,10,10,3,127,159,4,768,879,5,1471,1471,5,1536,1541,1,1648,1648,5,1767,1768,5,1840,1866,5,2070,2073,5,2137,2139,5,2274,2274,1,2363,2363,7,2377,2380,7,2402,2403,5,2494,2494,5,2507,2508,7,2558,2558,5,2622,2624,7,2641,2641,5,2691,2691,7,2759,2760,5,2786,2787,5,2876,2876,5,2881,2884,5,2901,2902,5,3006,3006,5,3014,3016,7,3072,3072,5,3134,3136,5,3157,3158,5,3260,3260,5,3266,3266,5,3274,3275,7,3328,3329,5,3391,3392,7,3405,3405,5,3457,3457,5,3536,3537,7,3551,3551,5,3636,3642,5,3764,3772,5,3895,3895,5,3967,3967,7,3993,4028,5,4146,4151,5,4182,4183,7,4226,4226,5,4253,4253,5,4957,4959,5,5940,5940,7,6070,6070,7,6087,6088,7,6158,6158,4,6432,6434,5,6448,6449,7,6679,6680,5,6742,6742,5,6754,6754,5,6783,6783,5,6912,6915,5,6966,6970,5,6978,6978,5,7042,7042,7,7080,7081,5,7143,7143,7,7150,7150,7,7212,7219,5,7380,7392,5,7412,7412,5,8203,8203,4,8232,8232,4,8265,8265,14,8400,8412,5,8421,8432,5,8617,8618,14,9167,9167,14,9200,9200,14,9410,9410,14,9723,9726,14,9733,9733,14,9745,9745,14,9752,9752,14,9760,9760,14,9766,9766,14,9774,9774,14,9786,9786,14,9794,9794,14,9823,9823,14,9828,9828,14,9833,9850,14,9855,9855,14,9875,9875,14,9880,9880,14,9885,9887,14,9896,9897,14,9906,9916,14,9926,9927,14,9935,9935,14,9939,9939,14,9962,9962,14,9972,9972,14,9978,9978,14,9986,9986,14,9997,9997,14,10002,10002,14,10017,10017,14,10055,10055,14,10071,10071,14,10133,10135,14,10548,10549,14,11093,11093,14,12330,12333,5,12441,12442,5,42608,42610,5,43010,43010,5,43045,43046,5,43188,43203,7,43302,43309,5,43392,43394,5,43446,43449,5,43493,43493,5,43571,43572,7,43597,43597,7,43703,43704,5,43756,43757,5,44003,44004,7,44009,44010,7,44033,44059,12,44089,44115,12,44145,44171,12,44201,44227,12,44257,44283,12,44313,44339,12,44369,44395,12,44425,44451,12,44481,44507,12,44537,44563,12,44593,44619,12,44649,44675,12,44705,44731,12,44761,44787,12,44817,44843,12,44873,44899,12,44929,44955,12,44985,45011,12,45041,45067,12,45097,45123,12,45153,45179,12,45209,45235,12,45265,45291,12,45321,45347,12,45377,45403,12,45433,45459,12,45489,45515,12,45545,45571,12,45601,45627,12,45657,45683,12,45713,45739,12,45769,45795,12,45825,45851,12,45881,45907,12,45937,45963,12,45993,46019,12,46049,46075,12,46105,46131,12,46161,46187,12,46217,46243,12,46273,46299,12,46329,46355,12,46385,46411,12,46441,46467,12,46497,46523,12,46553,46579,12,46609,46635,12,46665,46691,12,46721,46747,12,46777,46803,12,46833,46859,12,46889,46915,12,46945,46971,12,47001,47027,12,47057,47083,12,47113,47139,12,47169,47195,12,47225,47251,12,47281,47307,12,47337,47363,12,47393,47419,12,47449,47475,12,47505,47531,12,47561,47587,12,47617,47643,12,47673,47699,12,47729,47755,12,47785,47811,12,47841,47867,12,47897,47923,12,47953,47979,12,48009,48035,12,48065,48091,12,48121,48147,12,48177,48203,12,48233,48259,12,48289,48315,12,48345,48371,12,48401,48427,12,48457,48483,12,48513,48539,12,48569,48595,12,48625,48651,12,48681,48707,12,48737,48763,12,48793,48819,12,48849,48875,12,48905,48931,12,48961,48987,12,49017,49043,12,49073,49099,12,49129,49155,12,49185,49211,12,49241,49267,12,49297,49323,12,49353,49379,12,49409,49435,12,49465,49491,12,49521,49547,12,49577,49603,12,49633,49659,12,49689,49715,12,49745,49771,12,49801,49827,12,49857,49883,12,49913,49939,12,49969,49995,12,50025,50051,12,50081,50107,12,50137,50163,12,50193,50219,12,50249,50275,12,50305,50331,12,50361,50387,12,50417,50443,12,50473,50499,12,50529,50555,12,50585,50611,12,50641,50667,12,50697,50723,12,50753,50779,12,50809,50835,12,50865,50891,12,50921,50947,12,50977,51003,12,51033,51059,12,51089,51115,12,51145,51171,12,51201,51227,12,51257,51283,12,51313,51339,12,51369,51395,12,51425,51451,12,51481,51507,12,51537,51563,12,51593,51619,12,51649,51675,12,51705,51731,12,51761,51787,12,51817,51843,12,51873,51899,12,51929,51955,12,51985,52011,12,52041,52067,12,52097,52123,12,52153,52179,12,52209,52235,12,52265,52291,12,52321,52347,12,52377,52403,12,52433,52459,12,52489,52515,12,52545,52571,12,52601,52627,12,52657,52683,12,52713,52739,12,52769,52795,12,52825,52851,12,52881,52907,12,52937,52963,12,52993,53019,12,53049,53075,12,53105,53131,12,53161,53187,12,53217,53243,12,53273,53299,12,53329,53355,12,53385,53411,12,53441,53467,12,53497,53523,12,53553,53579,12,53609,53635,12,53665,53691,12,53721,53747,12,53777,53803,12,53833,53859,12,53889,53915,12,53945,53971,12,54001,54027,12,54057,54083,12,54113,54139,12,54169,54195,12,54225,54251,12,54281,54307,12,54337,54363,12,54393,54419,12,54449,54475,12,54505,54531,12,54561,54587,12,54617,54643,12,54673,54699,12,54729,54755,12,54785,54811,12,54841,54867,12,54897,54923,12,54953,54979,12,55009,55035,12,55065,55091,12,55121,55147,12,55177,55203,12,65024,65039,5,65520,65528,4,66422,66426,5,68152,68154,5,69291,69292,5,69633,69633,5,69747,69748,5,69811,69814,5,69826,69826,5,69932,69932,7,70016,70017,5,70079,70080,7,70095,70095,5,70196,70196,5,70367,70367,5,70402,70403,7,70464,70464,5,70487,70487,5,70709,70711,7,70725,70725,7,70833,70834,7,70843,70844,7,70849,70849,7,71090,71093,5,71103,71104,5,71227,71228,7,71339,71339,5,71344,71349,5,71458,71461,5,71727,71735,5,71985,71989,7,71998,71998,5,72002,72002,7,72154,72155,5,72193,72202,5,72251,72254,5,72281,72283,5,72344,72345,5,72766,72766,7,72874,72880,5,72885,72886,5,73023,73029,5,73104,73105,5,73111,73111,5,92912,92916,5,94095,94098,5,113824,113827,4,119142,119142,7,119155,119162,4,119362,119364,5,121476,121476,5,122888,122904,5,123184,123190,5,125252,125258,5,127183,127183,14,127340,127343,14,127377,127386,14,127491,127503,14,127548,127551,14,127744,127756,14,127761,127761,14,127769,127769,14,127773,127774,14,127780,127788,14,127796,127797,14,127820,127823,14,127869,127869,14,127894,127895,14,127902,127903,14,127943,127943,14,127947,127950,14,127972,127972,14,127988,127988,14,127992,127994,14,128009,128011,14,128019,128019,14,128023,128041,14,128064,128064,14,128102,128107,14,128174,128181,14,128238,128238,14,128246,128247,14,128254,128254,14,128264,128264,14,128278,128299,14,128329,128330,14,128348,128359,14,128371,128377,14,128392,128393,14,128401,128404,14,128421,128421,14,128433,128434,14,128450,128452,14,128476,128478,14,128483,128483,14,128495,128495,14,128506,128506,14,128519,128520,14,128528,128528,14,128534,128534,14,128538,128538,14,128540,128542,14,128544,128549,14,128552,128555,14,128557,128557,14,128560,128563,14,128565,128565,14,128567,128576,14,128581,128591,14,128641,128642,14,128646,128646,14,128648,128648,14,128650,128651,14,128653,128653,14,128655,128655,14,128657,128659,14,128661,128661,14,128663,128663,14,128665,128666,14,128674,128674,14,128676,128677,14,128679,128685,14,128690,128690,14,128694,128694,14,128697,128702,14,128704,128704,14,128710,128714,14,128716,128716,14,128720,128720,14,128723,128724,14,128726,128727,14,128733,128735,14,128742,128744,14,128746,128746,14,128749,128751,14,128753,128754,14,128756,128758,14,128761,128761,14,128763,128764,14,128884,128895,14,128992,129003,14,129008,129008,14,129036,129039,14,129114,129119,14,129198,129279,14,129293,129295,14,129305,129310,14,129312,129319,14,129328,129328,14,129331,129338,14,129343,129343,14,129351,129355,14,129357,129359,14,129375,129387,14,129393,129393,14,129395,129398,14,129401,129401,14,129403,129403,14,129408,129412,14,129426,129431,14,129443,129444,14,129451,129453,14,129456,129465,14,129472,129472,14,129475,129482,14,129484,129484,14,129488,129510,14,129536,129647,14,129652,129652,14,129656,129658,14,129661,129663,14,129667,129670,14,129680,129685,14,129705,129708,14,129712,129718,14,129723,129727,14,129731,129733,14,129744,129750,14,129754,129759,14,129768,129775,14,129783,129791,14,917504,917504,4,917506,917535,4,917632,917759,4,918000,921599,4,0,9,4,11,12,4,14,31,4,169,169,14,174,174,14,1155,1159,5,1425,1469,5,1473,1474,5,1479,1479,5,1552,1562,5,1611,1631,5,1750,1756,5,1759,1764,5,1770,1773,5,1809,1809,5,1958,1968,5,2045,2045,5,2075,2083,5,2089,2093,5,2192,2193,1,2250,2273,5,2275,2306,5,2362,2362,5,2364,2364,5,2369,2376,5,2381,2381,5,2385,2391,5,2433,2433,5,2492,2492,5,2495,2496,7,2503,2504,7,2509,2509,5,2530,2531,5,2561,2562,5,2620,2620,5,2625,2626,5,2635,2637,5,2672,2673,5,2689,2690,5,2748,2748,5,2753,2757,5,2761,2761,7,2765,2765,5,2810,2815,5,2818,2819,7,2878,2878,5,2880,2880,7,2887,2888,7,2893,2893,5,2903,2903,5,2946,2946,5,3007,3007,7,3009,3010,7,3018,3020,7,3031,3031,5,3073,3075,7,3132,3132,5,3137,3140,7,3146,3149,5,3170,3171,5,3202,3203,7,3262,3262,7,3264,3265,7,3267,3268,7,3271,3272,7,3276,3277,5,3298,3299,5,3330,3331,7,3390,3390,5,3393,3396,5,3402,3404,7,3406,3406,1,3426,3427,5,3458,3459,7,3535,3535,5,3538,3540,5,3544,3550,7,3570,3571,7,3635,3635,7,3655,3662,5,3763,3763,7,3784,3789,5,3893,3893,5,3897,3897,5,3953,3966,5,3968,3972,5,3981,3991,5,4038,4038,5,4145,4145,7,4153,4154,5,4157,4158,5,4184,4185,5,4209,4212,5,4228,4228,7,4237,4237,5,4352,4447,8,4520,4607,10,5906,5908,5,5938,5939,5,5970,5971,5,6068,6069,5,6071,6077,5,6086,6086,5,6089,6099,5,6155,6157,5,6159,6159,5,6313,6313,5,6435,6438,7,6441,6443,7,6450,6450,5,6457,6459,5,6681,6682,7,6741,6741,7,6743,6743,7,6752,6752,5,6757,6764,5,6771,6780,5,6832,6845,5,6847,6862,5,6916,6916,7,6965,6965,5,6971,6971,7,6973,6977,7,6979,6980,7,7040,7041,5,7073,7073,7,7078,7079,7,7082,7082,7,7142,7142,5,7144,7145,5,7149,7149,5,7151,7153,5,7204,7211,7,7220,7221,7,7376,7378,5,7393,7393,7,7405,7405,5,7415,7415,7,7616,7679,5,8204,8204,5,8206,8207,4,8233,8233,4,8252,8252,14,8288,8292,4,8294,8303,4,8413,8416,5,8418,8420,5,8482,8482,14,8596,8601,14,8986,8987,14,9096,9096,14,9193,9196,14,9199,9199,14,9201,9202,14,9208,9210,14,9642,9643,14,9664,9664,14,9728,9729,14,9732,9732,14,9735,9741,14,9743,9744,14,9746,9746,14,9750,9751,14,9753,9756,14,9758,9759,14,9761,9761,14,9764,9765,14,9767,9769,14,9771,9773,14,9775,9775,14,9784,9785,14,9787,9791,14,9793,9793,14,9795,9799,14,9812,9822,14,9824,9824,14,9827,9827,14,9829,9830,14,9832,9832,14,9851,9851,14,9854,9854,14,9856,9861,14,9874,9874,14,9876,9876,14,9878,9879,14,9881,9881,14,9883,9884,14,9888,9889,14,9895,9895,14,9898,9899,14,9904,9905,14,9917,9918,14,9924,9925,14,9928,9928,14,9934,9934,14,9936,9936,14,9938,9938,14,9940,9940,14,9961,9961,14,9963,9967,14,9970,9971,14,9973,9973,14,9975,9977,14,9979,9980,14,9982,9985,14,9987,9988,14,9992,9996,14,9998,9998,14,10000,10001,14,10004,10004,14,10013,10013,14,10024,10024,14,10052,10052,14,10060,10060,14,10067,10069,14,10083,10083,14,10085,10087,14,10145,10145,14,10175,10175,14,11013,11015,14,11088,11088,14,11503,11505,5,11744,11775,5,12334,12335,5,12349,12349,14,12951,12951,14,42607,42607,5,42612,42621,5,42736,42737,5,43014,43014,5,43043,43044,7,43047,43047,7,43136,43137,7,43204,43205,5,43263,43263,5,43335,43345,5,43360,43388,8,43395,43395,7,43444,43445,7,43450,43451,7,43454,43456,7,43561,43566,5,43569,43570,5,43573,43574,5,43596,43596,5,43644,43644,5,43698,43700,5,43710,43711,5,43755,43755,7,43758,43759,7,43766,43766,5,44005,44005,5,44008,44008,5,44012,44012,7,44032,44032,11,44060,44060,11,44088,44088,11,44116,44116,11,44144,44144,11,44172,44172,11,44200,44200,11,44228,44228,11,44256,44256,11,44284,44284,11,44312,44312,11,44340,44340,11,44368,44368,11,44396,44396,11,44424,44424,11,44452,44452,11,44480,44480,11,44508,44508,11,44536,44536,11,44564,44564,11,44592,44592,11,44620,44620,11,44648,44648,11,44676,44676,11,44704,44704,11,44732,44732,11,44760,44760,11,44788,44788,11,44816,44816,11,44844,44844,11,44872,44872,11,44900,44900,11,44928,44928,11,44956,44956,11,44984,44984,11,45012,45012,11,45040,45040,11,45068,45068,11,45096,45096,11,45124,45124,11,45152,45152,11,45180,45180,11,45208,45208,11,45236,45236,11,45264,45264,11,45292,45292,11,45320,45320,11,45348,45348,11,45376,45376,11,45404,45404,11,45432,45432,11,45460,45460,11,45488,45488,11,45516,45516,11,45544,45544,11,45572,45572,11,45600,45600,11,45628,45628,11,45656,45656,11,45684,45684,11,45712,45712,11,45740,45740,11,45768,45768,11,45796,45796,11,45824,45824,11,45852,45852,11,45880,45880,11,45908,45908,11,45936,45936,11,45964,45964,11,45992,45992,11,46020,46020,11,46048,46048,11,46076,46076,11,46104,46104,11,46132,46132,11,46160,46160,11,46188,46188,11,46216,46216,11,46244,46244,11,46272,46272,11,46300,46300,11,46328,46328,11,46356,46356,11,46384,46384,11,46412,46412,11,46440,46440,11,46468,46468,11,46496,46496,11,46524,46524,11,46552,46552,11,46580,46580,11,46608,46608,11,46636,46636,11,46664,46664,11,46692,46692,11,46720,46720,11,46748,46748,11,46776,46776,11,46804,46804,11,46832,46832,11,46860,46860,11,46888,46888,11,46916,46916,11,46944,46944,11,46972,46972,11,47000,47000,11,47028,47028,11,47056,47056,11,47084,47084,11,47112,47112,11,47140,47140,11,47168,47168,11,47196,47196,11,47224,47224,11,47252,47252,11,47280,47280,11,47308,47308,11,47336,47336,11,47364,47364,11,47392,47392,11,47420,47420,11,47448,47448,11,47476,47476,11,47504,47504,11,47532,47532,11,47560,47560,11,47588,47588,11,47616,47616,11,47644,47644,11,47672,47672,11,47700,47700,11,47728,47728,11,47756,47756,11,47784,47784,11,47812,47812,11,47840,47840,11,47868,47868,11,47896,47896,11,47924,47924,11,47952,47952,11,47980,47980,11,48008,48008,11,48036,48036,11,48064,48064,11,48092,48092,11,48120,48120,11,48148,48148,11,48176,48176,11,48204,48204,11,48232,48232,11,48260,48260,11,48288,48288,11,48316,48316,11,48344,48344,11,48372,48372,11,48400,48400,11,48428,48428,11,48456,48456,11,48484,48484,11,48512,48512,11,48540,48540,11,48568,48568,11,48596,48596,11,48624,48624,11,48652,48652,11,48680,48680,11,48708,48708,11,48736,48736,11,48764,48764,11,48792,48792,11,48820,48820,11,48848,48848,11,48876,48876,11,48904,48904,11,48932,48932,11,48960,48960,11,48988,48988,11,49016,49016,11,49044,49044,11,49072,49072,11,49100,49100,11,49128,49128,11,49156,49156,11,49184,49184,11,49212,49212,11,49240,49240,11,49268,49268,11,49296,49296,11,49324,49324,11,49352,49352,11,49380,49380,11,49408,49408,11,49436,49436,11,49464,49464,11,49492,49492,11,49520,49520,11,49548,49548,11,49576,49576,11,49604,49604,11,49632,49632,11,49660,49660,11,49688,49688,11,49716,49716,11,49744,49744,11,49772,49772,11,49800,49800,11,49828,49828,11,49856,49856,11,49884,49884,11,49912,49912,11,49940,49940,11,49968,49968,11,49996,49996,11,50024,50024,11,50052,50052,11,50080,50080,11,50108,50108,11,50136,50136,11,50164,50164,11,50192,50192,11,50220,50220,11,50248,50248,11,50276,50276,11,50304,50304,11,50332,50332,11,50360,50360,11,50388,50388,11,50416,50416,11,50444,50444,11,50472,50472,11,50500,50500,11,50528,50528,11,50556,50556,11,50584,50584,11,50612,50612,11,50640,50640,11,50668,50668,11,50696,50696,11,50724,50724,11,50752,50752,11,50780,50780,11,50808,50808,11,50836,50836,11,50864,50864,11,50892,50892,11,50920,50920,11,50948,50948,11,50976,50976,11,51004,51004,11,51032,51032,11,51060,51060,11,51088,51088,11,51116,51116,11,51144,51144,11,51172,51172,11,51200,51200,11,51228,51228,11,51256,51256,11,51284,51284,11,51312,51312,11,51340,51340,11,51368,51368,11,51396,51396,11,51424,51424,11,51452,51452,11,51480,51480,11,51508,51508,11,51536,51536,11,51564,51564,11,51592,51592,11,51620,51620,11,51648,51648,11,51676,51676,11,51704,51704,11,51732,51732,11,51760,51760,11,51788,51788,11,51816,51816,11,51844,51844,11,51872,51872,11,51900,51900,11,51928,51928,11,51956,51956,11,51984,51984,11,52012,52012,11,52040,52040,11,52068,52068,11,52096,52096,11,52124,52124,11,52152,52152,11,52180,52180,11,52208,52208,11,52236,52236,11,52264,52264,11,52292,52292,11,52320,52320,11,52348,52348,11,52376,52376,11,52404,52404,11,52432,52432,11,52460,52460,11,52488,52488,11,52516,52516,11,52544,52544,11,52572,52572,11,52600,52600,11,52628,52628,11,52656,52656,11,52684,52684,11,52712,52712,11,52740,52740,11,52768,52768,11,52796,52796,11,52824,52824,11,52852,52852,11,52880,52880,11,52908,52908,11,52936,52936,11,52964,52964,11,52992,52992,11,53020,53020,11,53048,53048,11,53076,53076,11,53104,53104,11,53132,53132,11,53160,53160,11,53188,53188,11,53216,53216,11,53244,53244,11,53272,53272,11,53300,53300,11,53328,53328,11,53356,53356,11,53384,53384,11,53412,53412,11,53440,53440,11,53468,53468,11,53496,53496,11,53524,53524,11,53552,53552,11,53580,53580,11,53608,53608,11,53636,53636,11,53664,53664,11,53692,53692,11,53720,53720,11,53748,53748,11,53776,53776,11,53804,53804,11,53832,53832,11,53860,53860,11,53888,53888,11,53916,53916,11,53944,53944,11,53972,53972,11,54000,54000,11,54028,54028,11,54056,54056,11,54084,54084,11,54112,54112,11,54140,54140,11,54168,54168,11,54196,54196,11,54224,54224,11,54252,54252,11,54280,54280,11,54308,54308,11,54336,54336,11,54364,54364,11,54392,54392,11,54420,54420,11,54448,54448,11,54476,54476,11,54504,54504,11,54532,54532,11,54560,54560,11,54588,54588,11,54616,54616,11,54644,54644,11,54672,54672,11,54700,54700,11,54728,54728,11,54756,54756,11,54784,54784,11,54812,54812,11,54840,54840,11,54868,54868,11,54896,54896,11,54924,54924,11,54952,54952,11,54980,54980,11,55008,55008,11,55036,55036,11,55064,55064,11,55092,55092,11,55120,55120,11,55148,55148,11,55176,55176,11,55216,55238,9,64286,64286,5,65056,65071,5,65438,65439,5,65529,65531,4,66272,66272,5,68097,68099,5,68108,68111,5,68159,68159,5,68900,68903,5,69446,69456,5,69632,69632,7,69634,69634,7,69744,69744,5,69759,69761,5,69808,69810,7,69815,69816,7,69821,69821,1,69837,69837,1,69927,69931,5,69933,69940,5,70003,70003,5,70018,70018,7,70070,70078,5,70082,70083,1,70094,70094,7,70188,70190,7,70194,70195,7,70197,70197,7,70206,70206,5,70368,70370,7,70400,70401,5,70459,70460,5,70463,70463,7,70465,70468,7,70475,70477,7,70498,70499,7,70512,70516,5,70712,70719,5,70722,70724,5,70726,70726,5,70832,70832,5,70835,70840,5,70842,70842,5,70845,70845,5,70847,70848,5,70850,70851,5,71088,71089,7,71096,71099,7,71102,71102,7,71132,71133,5,71219,71226,5,71229,71229,5,71231,71232,5,71340,71340,7,71342,71343,7,71350,71350,7,71453,71455,5,71462,71462,7,71724,71726,7,71736,71736,7,71984,71984,5,71991,71992,7,71997,71997,7,71999,71999,1,72001,72001,1,72003,72003,5,72148,72151,5,72156,72159,7,72164,72164,7,72243,72248,5,72250,72250,1,72263,72263,5,72279,72280,7,72324,72329,1,72343,72343,7,72751,72751,7,72760,72765,5,72767,72767,5,72873,72873,7,72881,72881,7,72884,72884,7,73009,73014,5,73020,73021,5,73030,73030,1,73098,73102,7,73107,73108,7,73110,73110,7,73459,73460,5,78896,78904,4,92976,92982,5,94033,94087,7,94180,94180,5,113821,113822,5,118528,118573,5,119141,119141,5,119143,119145,5,119150,119154,5,119163,119170,5,119210,119213,5,121344,121398,5,121461,121461,5,121499,121503,5,122880,122886,5,122907,122913,5,122918,122922,5,123566,123566,5,125136,125142,5,126976,126979,14,126981,127182,14,127184,127231,14,127279,127279,14,127344,127345,14,127374,127374,14,127405,127461,14,127489,127490,14,127514,127514,14,127538,127546,14,127561,127567,14,127570,127743,14,127757,127758,14,127760,127760,14,127762,127762,14,127766,127768,14,127770,127770,14,127772,127772,14,127775,127776,14,127778,127779,14,127789,127791,14,127794,127795,14,127798,127798,14,127819,127819,14,127824,127824,14,127868,127868,14,127870,127871,14,127892,127893,14,127896,127896,14,127900,127901,14,127904,127940,14,127942,127942,14,127944,127944,14,127946,127946,14,127951,127955,14,127968,127971,14,127973,127984,14,127987,127987,14,127989,127989,14,127991,127991,14,127995,127999,5,128008,128008,14,128012,128014,14,128017,128018,14,128020,128020,14,128022,128022,14,128042,128042,14,128063,128063,14,128065,128065,14,128101,128101,14,128108,128109,14,128173,128173,14,128182,128183,14,128236,128237,14,128239,128239,14,128245,128245,14,128248,128248,14,128253,128253,14,128255,128258,14,128260,128263,14,128265,128265,14,128277,128277,14,128300,128301,14,128326,128328,14,128331,128334,14,128336,128347,14,128360,128366,14,128369,128370,14,128378,128378,14,128391,128391,14,128394,128397,14,128400,128400,14,128405,128406,14,128420,128420,14,128422,128423,14,128425,128432,14,128435,128443,14,128445,128449,14,128453,128464,14,128468,128475,14,128479,128480,14,128482,128482,14,128484,128487,14,128489,128494,14,128496,128498,14,128500,128505,14,128507,128511,14,128513,128518,14,128521,128525,14,128527,128527,14,128529,128529,14,128533,128533,14,128535,128535,14,128537,128537,14]")}function ta(e,t){if(e===0)return 0;const n=Rr(e,t);if(n!==void 0)return n;const s=new Lt(t,e);return s.prevCodePoint(),s.offset}function Rr(e,t){const n=new Lt(t,e);let s=n.prevCodePoint();for(;Pr(s)||s===65039||s===8419;){if(n.offset===0)return;s=n.prevCodePoint()}if(!Er(s))return;let i=n.offset;return i>0&&n.prevCodePoint()===8205&&(i=n.offset),i}function Pr(e){return 127995<=e&&e<=127999}const na=" ";class z{constructor(t){this.confusableDictionary=t}static getInstance(t){return z.cache.get(Array.from(t))}static getLocales(){return z._locales.getValue()}isAmbiguous(t){return this.confusableDictionary.has(t)}getPrimaryConfusable(t){return this.confusableDictionary.get(t)}getConfusableCodePoints(){return new Set(this.confusableDictionary.keys())}}X1=z;z.ambiguousCharacterData=new J1(()=>JSON.parse('{"_common":[8232,32,8233,32,5760,32,8192,32,8193,32,8194,32,8195,32,8196,32,8197,32,8198,32,8200,32,8201,32,8202,32,8287,32,8199,32,8239,32,2042,95,65101,95,65102,95,65103,95,8208,45,8209,45,8210,45,65112,45,1748,45,8259,45,727,45,8722,45,10134,45,11450,45,1549,44,1643,44,8218,44,184,44,42233,44,894,59,2307,58,2691,58,1417,58,1795,58,1796,58,5868,58,65072,58,6147,58,6153,58,8282,58,1475,58,760,58,42889,58,8758,58,720,58,42237,58,451,33,11601,33,660,63,577,63,2429,63,5038,63,42731,63,119149,46,8228,46,1793,46,1794,46,42510,46,68176,46,1632,46,1776,46,42232,46,1373,96,65287,96,8219,96,8242,96,1370,96,1523,96,8175,96,65344,96,900,96,8189,96,8125,96,8127,96,8190,96,697,96,884,96,712,96,714,96,715,96,756,96,699,96,701,96,700,96,702,96,42892,96,1497,96,2036,96,2037,96,5194,96,5836,96,94033,96,94034,96,65339,91,10088,40,10098,40,12308,40,64830,40,65341,93,10089,41,10099,41,12309,41,64831,41,10100,123,119060,123,10101,125,65342,94,8270,42,1645,42,8727,42,66335,42,5941,47,8257,47,8725,47,8260,47,9585,47,10187,47,10744,47,119354,47,12755,47,12339,47,11462,47,20031,47,12035,47,65340,92,65128,92,8726,92,10189,92,10741,92,10745,92,119311,92,119355,92,12756,92,20022,92,12034,92,42872,38,708,94,710,94,5869,43,10133,43,66203,43,8249,60,10094,60,706,60,119350,60,5176,60,5810,60,5120,61,11840,61,12448,61,42239,61,8250,62,10095,62,707,62,119351,62,5171,62,94015,62,8275,126,732,126,8128,126,8764,126,65372,124,65293,45,120784,50,120794,50,120804,50,120814,50,120824,50,130034,50,42842,50,423,50,1000,50,42564,50,5311,50,42735,50,119302,51,120785,51,120795,51,120805,51,120815,51,120825,51,130035,51,42923,51,540,51,439,51,42858,51,11468,51,1248,51,94011,51,71882,51,120786,52,120796,52,120806,52,120816,52,120826,52,130036,52,5070,52,71855,52,120787,53,120797,53,120807,53,120817,53,120827,53,130037,53,444,53,71867,53,120788,54,120798,54,120808,54,120818,54,120828,54,130038,54,11474,54,5102,54,71893,54,119314,55,120789,55,120799,55,120809,55,120819,55,120829,55,130039,55,66770,55,71878,55,2819,56,2538,56,2666,56,125131,56,120790,56,120800,56,120810,56,120820,56,120830,56,130040,56,547,56,546,56,66330,56,2663,57,2920,57,2541,57,3437,57,120791,57,120801,57,120811,57,120821,57,120831,57,130041,57,42862,57,11466,57,71884,57,71852,57,71894,57,9082,97,65345,97,119834,97,119886,97,119938,97,119990,97,120042,97,120094,97,120146,97,120198,97,120250,97,120302,97,120354,97,120406,97,120458,97,593,97,945,97,120514,97,120572,97,120630,97,120688,97,120746,97,65313,65,119808,65,119860,65,119912,65,119964,65,120016,65,120068,65,120120,65,120172,65,120224,65,120276,65,120328,65,120380,65,120432,65,913,65,120488,65,120546,65,120604,65,120662,65,120720,65,5034,65,5573,65,42222,65,94016,65,66208,65,119835,98,119887,98,119939,98,119991,98,120043,98,120095,98,120147,98,120199,98,120251,98,120303,98,120355,98,120407,98,120459,98,388,98,5071,98,5234,98,5551,98,65314,66,8492,66,119809,66,119861,66,119913,66,120017,66,120069,66,120121,66,120173,66,120225,66,120277,66,120329,66,120381,66,120433,66,42932,66,914,66,120489,66,120547,66,120605,66,120663,66,120721,66,5108,66,5623,66,42192,66,66178,66,66209,66,66305,66,65347,99,8573,99,119836,99,119888,99,119940,99,119992,99,120044,99,120096,99,120148,99,120200,99,120252,99,120304,99,120356,99,120408,99,120460,99,7428,99,1010,99,11429,99,43951,99,66621,99,128844,67,71922,67,71913,67,65315,67,8557,67,8450,67,8493,67,119810,67,119862,67,119914,67,119966,67,120018,67,120174,67,120226,67,120278,67,120330,67,120382,67,120434,67,1017,67,11428,67,5087,67,42202,67,66210,67,66306,67,66581,67,66844,67,8574,100,8518,100,119837,100,119889,100,119941,100,119993,100,120045,100,120097,100,120149,100,120201,100,120253,100,120305,100,120357,100,120409,100,120461,100,1281,100,5095,100,5231,100,42194,100,8558,68,8517,68,119811,68,119863,68,119915,68,119967,68,120019,68,120071,68,120123,68,120175,68,120227,68,120279,68,120331,68,120383,68,120435,68,5024,68,5598,68,5610,68,42195,68,8494,101,65349,101,8495,101,8519,101,119838,101,119890,101,119942,101,120046,101,120098,101,120150,101,120202,101,120254,101,120306,101,120358,101,120410,101,120462,101,43826,101,1213,101,8959,69,65317,69,8496,69,119812,69,119864,69,119916,69,120020,69,120072,69,120124,69,120176,69,120228,69,120280,69,120332,69,120384,69,120436,69,917,69,120492,69,120550,69,120608,69,120666,69,120724,69,11577,69,5036,69,42224,69,71846,69,71854,69,66182,69,119839,102,119891,102,119943,102,119995,102,120047,102,120099,102,120151,102,120203,102,120255,102,120307,102,120359,102,120411,102,120463,102,43829,102,42905,102,383,102,7837,102,1412,102,119315,70,8497,70,119813,70,119865,70,119917,70,120021,70,120073,70,120125,70,120177,70,120229,70,120281,70,120333,70,120385,70,120437,70,42904,70,988,70,120778,70,5556,70,42205,70,71874,70,71842,70,66183,70,66213,70,66853,70,65351,103,8458,103,119840,103,119892,103,119944,103,120048,103,120100,103,120152,103,120204,103,120256,103,120308,103,120360,103,120412,103,120464,103,609,103,7555,103,397,103,1409,103,119814,71,119866,71,119918,71,119970,71,120022,71,120074,71,120126,71,120178,71,120230,71,120282,71,120334,71,120386,71,120438,71,1292,71,5056,71,5107,71,42198,71,65352,104,8462,104,119841,104,119945,104,119997,104,120049,104,120101,104,120153,104,120205,104,120257,104,120309,104,120361,104,120413,104,120465,104,1211,104,1392,104,5058,104,65320,72,8459,72,8460,72,8461,72,119815,72,119867,72,119919,72,120023,72,120179,72,120231,72,120283,72,120335,72,120387,72,120439,72,919,72,120494,72,120552,72,120610,72,120668,72,120726,72,11406,72,5051,72,5500,72,42215,72,66255,72,731,105,9075,105,65353,105,8560,105,8505,105,8520,105,119842,105,119894,105,119946,105,119998,105,120050,105,120102,105,120154,105,120206,105,120258,105,120310,105,120362,105,120414,105,120466,105,120484,105,618,105,617,105,953,105,8126,105,890,105,120522,105,120580,105,120638,105,120696,105,120754,105,1110,105,42567,105,1231,105,43893,105,5029,105,71875,105,65354,106,8521,106,119843,106,119895,106,119947,106,119999,106,120051,106,120103,106,120155,106,120207,106,120259,106,120311,106,120363,106,120415,106,120467,106,1011,106,1112,106,65322,74,119817,74,119869,74,119921,74,119973,74,120025,74,120077,74,120129,74,120181,74,120233,74,120285,74,120337,74,120389,74,120441,74,42930,74,895,74,1032,74,5035,74,5261,74,42201,74,119844,107,119896,107,119948,107,120000,107,120052,107,120104,107,120156,107,120208,107,120260,107,120312,107,120364,107,120416,107,120468,107,8490,75,65323,75,119818,75,119870,75,119922,75,119974,75,120026,75,120078,75,120130,75,120182,75,120234,75,120286,75,120338,75,120390,75,120442,75,922,75,120497,75,120555,75,120613,75,120671,75,120729,75,11412,75,5094,75,5845,75,42199,75,66840,75,1472,108,8739,73,9213,73,65512,73,1633,108,1777,73,66336,108,125127,108,120783,73,120793,73,120803,73,120813,73,120823,73,130033,73,65321,73,8544,73,8464,73,8465,73,119816,73,119868,73,119920,73,120024,73,120128,73,120180,73,120232,73,120284,73,120336,73,120388,73,120440,73,65356,108,8572,73,8467,108,119845,108,119897,108,119949,108,120001,108,120053,108,120105,73,120157,73,120209,73,120261,73,120313,73,120365,73,120417,73,120469,73,448,73,120496,73,120554,73,120612,73,120670,73,120728,73,11410,73,1030,73,1216,73,1493,108,1503,108,1575,108,126464,108,126592,108,65166,108,65165,108,1994,108,11599,73,5825,73,42226,73,93992,73,66186,124,66313,124,119338,76,8556,76,8466,76,119819,76,119871,76,119923,76,120027,76,120079,76,120131,76,120183,76,120235,76,120287,76,120339,76,120391,76,120443,76,11472,76,5086,76,5290,76,42209,76,93974,76,71843,76,71858,76,66587,76,66854,76,65325,77,8559,77,8499,77,119820,77,119872,77,119924,77,120028,77,120080,77,120132,77,120184,77,120236,77,120288,77,120340,77,120392,77,120444,77,924,77,120499,77,120557,77,120615,77,120673,77,120731,77,1018,77,11416,77,5047,77,5616,77,5846,77,42207,77,66224,77,66321,77,119847,110,119899,110,119951,110,120003,110,120055,110,120107,110,120159,110,120211,110,120263,110,120315,110,120367,110,120419,110,120471,110,1400,110,1404,110,65326,78,8469,78,119821,78,119873,78,119925,78,119977,78,120029,78,120081,78,120185,78,120237,78,120289,78,120341,78,120393,78,120445,78,925,78,120500,78,120558,78,120616,78,120674,78,120732,78,11418,78,42208,78,66835,78,3074,111,3202,111,3330,111,3458,111,2406,111,2662,111,2790,111,3046,111,3174,111,3302,111,3430,111,3664,111,3792,111,4160,111,1637,111,1781,111,65359,111,8500,111,119848,111,119900,111,119952,111,120056,111,120108,111,120160,111,120212,111,120264,111,120316,111,120368,111,120420,111,120472,111,7439,111,7441,111,43837,111,959,111,120528,111,120586,111,120644,111,120702,111,120760,111,963,111,120532,111,120590,111,120648,111,120706,111,120764,111,11423,111,4351,111,1413,111,1505,111,1607,111,126500,111,126564,111,126596,111,65259,111,65260,111,65258,111,65257,111,1726,111,64428,111,64429,111,64427,111,64426,111,1729,111,64424,111,64425,111,64423,111,64422,111,1749,111,3360,111,4125,111,66794,111,71880,111,71895,111,66604,111,1984,79,2534,79,2918,79,12295,79,70864,79,71904,79,120782,79,120792,79,120802,79,120812,79,120822,79,130032,79,65327,79,119822,79,119874,79,119926,79,119978,79,120030,79,120082,79,120134,79,120186,79,120238,79,120290,79,120342,79,120394,79,120446,79,927,79,120502,79,120560,79,120618,79,120676,79,120734,79,11422,79,1365,79,11604,79,4816,79,2848,79,66754,79,42227,79,71861,79,66194,79,66219,79,66564,79,66838,79,9076,112,65360,112,119849,112,119901,112,119953,112,120005,112,120057,112,120109,112,120161,112,120213,112,120265,112,120317,112,120369,112,120421,112,120473,112,961,112,120530,112,120544,112,120588,112,120602,112,120646,112,120660,112,120704,112,120718,112,120762,112,120776,112,11427,112,65328,80,8473,80,119823,80,119875,80,119927,80,119979,80,120031,80,120083,80,120187,80,120239,80,120291,80,120343,80,120395,80,120447,80,929,80,120504,80,120562,80,120620,80,120678,80,120736,80,11426,80,5090,80,5229,80,42193,80,66197,80,119850,113,119902,113,119954,113,120006,113,120058,113,120110,113,120162,113,120214,113,120266,113,120318,113,120370,113,120422,113,120474,113,1307,113,1379,113,1382,113,8474,81,119824,81,119876,81,119928,81,119980,81,120032,81,120084,81,120188,81,120240,81,120292,81,120344,81,120396,81,120448,81,11605,81,119851,114,119903,114,119955,114,120007,114,120059,114,120111,114,120163,114,120215,114,120267,114,120319,114,120371,114,120423,114,120475,114,43847,114,43848,114,7462,114,11397,114,43905,114,119318,82,8475,82,8476,82,8477,82,119825,82,119877,82,119929,82,120033,82,120189,82,120241,82,120293,82,120345,82,120397,82,120449,82,422,82,5025,82,5074,82,66740,82,5511,82,42211,82,94005,82,65363,115,119852,115,119904,115,119956,115,120008,115,120060,115,120112,115,120164,115,120216,115,120268,115,120320,115,120372,115,120424,115,120476,115,42801,115,445,115,1109,115,43946,115,71873,115,66632,115,65331,83,119826,83,119878,83,119930,83,119982,83,120034,83,120086,83,120138,83,120190,83,120242,83,120294,83,120346,83,120398,83,120450,83,1029,83,1359,83,5077,83,5082,83,42210,83,94010,83,66198,83,66592,83,119853,116,119905,116,119957,116,120009,116,120061,116,120113,116,120165,116,120217,116,120269,116,120321,116,120373,116,120425,116,120477,116,8868,84,10201,84,128872,84,65332,84,119827,84,119879,84,119931,84,119983,84,120035,84,120087,84,120139,84,120191,84,120243,84,120295,84,120347,84,120399,84,120451,84,932,84,120507,84,120565,84,120623,84,120681,84,120739,84,11430,84,5026,84,42196,84,93962,84,71868,84,66199,84,66225,84,66325,84,119854,117,119906,117,119958,117,120010,117,120062,117,120114,117,120166,117,120218,117,120270,117,120322,117,120374,117,120426,117,120478,117,42911,117,7452,117,43854,117,43858,117,651,117,965,117,120534,117,120592,117,120650,117,120708,117,120766,117,1405,117,66806,117,71896,117,8746,85,8899,85,119828,85,119880,85,119932,85,119984,85,120036,85,120088,85,120140,85,120192,85,120244,85,120296,85,120348,85,120400,85,120452,85,1357,85,4608,85,66766,85,5196,85,42228,85,94018,85,71864,85,8744,118,8897,118,65366,118,8564,118,119855,118,119907,118,119959,118,120011,118,120063,118,120115,118,120167,118,120219,118,120271,118,120323,118,120375,118,120427,118,120479,118,7456,118,957,118,120526,118,120584,118,120642,118,120700,118,120758,118,1141,118,1496,118,71430,118,43945,118,71872,118,119309,86,1639,86,1783,86,8548,86,119829,86,119881,86,119933,86,119985,86,120037,86,120089,86,120141,86,120193,86,120245,86,120297,86,120349,86,120401,86,120453,86,1140,86,11576,86,5081,86,5167,86,42719,86,42214,86,93960,86,71840,86,66845,86,623,119,119856,119,119908,119,119960,119,120012,119,120064,119,120116,119,120168,119,120220,119,120272,119,120324,119,120376,119,120428,119,120480,119,7457,119,1121,119,1309,119,1377,119,71434,119,71438,119,71439,119,43907,119,71919,87,71910,87,119830,87,119882,87,119934,87,119986,87,120038,87,120090,87,120142,87,120194,87,120246,87,120298,87,120350,87,120402,87,120454,87,1308,87,5043,87,5076,87,42218,87,5742,120,10539,120,10540,120,10799,120,65368,120,8569,120,119857,120,119909,120,119961,120,120013,120,120065,120,120117,120,120169,120,120221,120,120273,120,120325,120,120377,120,120429,120,120481,120,5441,120,5501,120,5741,88,9587,88,66338,88,71916,88,65336,88,8553,88,119831,88,119883,88,119935,88,119987,88,120039,88,120091,88,120143,88,120195,88,120247,88,120299,88,120351,88,120403,88,120455,88,42931,88,935,88,120510,88,120568,88,120626,88,120684,88,120742,88,11436,88,11613,88,5815,88,42219,88,66192,88,66228,88,66327,88,66855,88,611,121,7564,121,65369,121,119858,121,119910,121,119962,121,120014,121,120066,121,120118,121,120170,121,120222,121,120274,121,120326,121,120378,121,120430,121,120482,121,655,121,7935,121,43866,121,947,121,8509,121,120516,121,120574,121,120632,121,120690,121,120748,121,1199,121,4327,121,71900,121,65337,89,119832,89,119884,89,119936,89,119988,89,120040,89,120092,89,120144,89,120196,89,120248,89,120300,89,120352,89,120404,89,120456,89,933,89,978,89,120508,89,120566,89,120624,89,120682,89,120740,89,11432,89,1198,89,5033,89,5053,89,42220,89,94019,89,71844,89,66226,89,119859,122,119911,122,119963,122,120015,122,120067,122,120119,122,120171,122,120223,122,120275,122,120327,122,120379,122,120431,122,120483,122,7458,122,43923,122,71876,122,66293,90,71909,90,65338,90,8484,90,8488,90,119833,90,119885,90,119937,90,119989,90,120041,90,120197,90,120249,90,120301,90,120353,90,120405,90,120457,90,918,90,120493,90,120551,90,120609,90,120667,90,120725,90,5059,90,42204,90,71849,90,65282,34,65284,36,65285,37,65286,38,65290,42,65291,43,65294,46,65295,47,65296,48,65297,49,65298,50,65299,51,65300,52,65301,53,65302,54,65303,55,65304,56,65305,57,65308,60,65309,61,65310,62,65312,64,65316,68,65318,70,65319,71,65324,76,65329,81,65330,82,65333,85,65334,86,65335,87,65343,95,65346,98,65348,100,65350,102,65355,107,65357,109,65358,110,65361,113,65362,114,65364,116,65365,117,65367,119,65370,122,65371,123,65373,125],"_default":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"cs":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"de":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"es":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"fr":[65374,126,65306,58,65281,33,8216,96,8245,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"it":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ja":[8211,45,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65292,44,65307,59],"ko":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pl":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pt-BR":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"qps-ploc":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ru":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,305,105,921,73,1009,112,215,120,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"tr":[160,32,8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"zh-hans":[65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41],"zh-hant":[8211,45,65374,126,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65307,59]}'));z.cache=new gr(e=>{function t(u){const h=new Map;for(let f=0;f<u.length;f+=2)h.set(u[f],u[f+1]);return h}function n(u,h){const f=new Map(u);for(const[d,w]of h)f.set(d,w);return f}function s(u,h){if(!u)return h;const f=new Map;for(const[d,w]of u)h.has(d)&&f.set(d,w);return f}const i=X1.ambiguousCharacterData.getValue();let a=e.filter(u=>!u.startsWith("_")&&u in i);a.length===0&&(a=["_default"]);let o;for(const u of a){const h=t(i[u]);o=s(o,h)}const l=t(i._common),c=n(l,o);return new z(c)});z._locales=new J1(()=>Object.keys(z.ambiguousCharacterData.getValue()).filter(e=>!e.startsWith("_")));class re{static getRawData(){return JSON.parse("[9,10,11,12,13,32,127,160,173,847,1564,4447,4448,6068,6069,6155,6156,6157,6158,7355,7356,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8203,8204,8205,8206,8207,8234,8235,8236,8237,8238,8239,8287,8288,8289,8290,8291,8292,8293,8294,8295,8296,8297,8298,8299,8300,8301,8302,8303,10240,12288,12644,65024,65025,65026,65027,65028,65029,65030,65031,65032,65033,65034,65035,65036,65037,65038,65039,65279,65440,65520,65521,65522,65523,65524,65525,65526,65527,65528,65532,78844,119155,119156,119157,119158,119159,119160,119161,119162,917504,917505,917506,917507,917508,917509,917510,917511,917512,917513,917514,917515,917516,917517,917518,917519,917520,917521,917522,917523,917524,917525,917526,917527,917528,917529,917530,917531,917532,917533,917534,917535,917536,917537,917538,917539,917540,917541,917542,917543,917544,917545,917546,917547,917548,917549,917550,917551,917552,917553,917554,917555,917556,917557,917558,917559,917560,917561,917562,917563,917564,917565,917566,917567,917568,917569,917570,917571,917572,917573,917574,917575,917576,917577,917578,917579,917580,917581,917582,917583,917584,917585,917586,917587,917588,917589,917590,917591,917592,917593,917594,917595,917596,917597,917598,917599,917600,917601,917602,917603,917604,917605,917606,917607,917608,917609,917610,917611,917612,917613,917614,917615,917616,917617,917618,917619,917620,917621,917622,917623,917624,917625,917626,917627,917628,917629,917630,917631,917760,917761,917762,917763,917764,917765,917766,917767,917768,917769,917770,917771,917772,917773,917774,917775,917776,917777,917778,917779,917780,917781,917782,917783,917784,917785,917786,917787,917788,917789,917790,917791,917792,917793,917794,917795,917796,917797,917798,917799,917800,917801,917802,917803,917804,917805,917806,917807,917808,917809,917810,917811,917812,917813,917814,917815,917816,917817,917818,917819,917820,917821,917822,917823,917824,917825,917826,917827,917828,917829,917830,917831,917832,917833,917834,917835,917836,917837,917838,917839,917840,917841,917842,917843,917844,917845,917846,917847,917848,917849,917850,917851,917852,917853,917854,917855,917856,917857,917858,917859,917860,917861,917862,917863,917864,917865,917866,917867,917868,917869,917870,917871,917872,917873,917874,917875,917876,917877,917878,917879,917880,917881,917882,917883,917884,917885,917886,917887,917888,917889,917890,917891,917892,917893,917894,917895,917896,917897,917898,917899,917900,917901,917902,917903,917904,917905,917906,917907,917908,917909,917910,917911,917912,917913,917914,917915,917916,917917,917918,917919,917920,917921,917922,917923,917924,917925,917926,917927,917928,917929,917930,917931,917932,917933,917934,917935,917936,917937,917938,917939,917940,917941,917942,917943,917944,917945,917946,917947,917948,917949,917950,917951,917952,917953,917954,917955,917956,917957,917958,917959,917960,917961,917962,917963,917964,917965,917966,917967,917968,917969,917970,917971,917972,917973,917974,917975,917976,917977,917978,917979,917980,917981,917982,917983,917984,917985,917986,917987,917988,917989,917990,917991,917992,917993,917994,917995,917996,917997,917998,917999]")}static getData(){return this._data||(this._data=new Set(re.getRawData())),this._data}static isInvisibleCharacter(t){return re.getData().has(t)}static get codePoints(){return re.getData()}}re._data=void 0;var N1;(function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=4]="Right",e[e.Full=7]="Full"})(N1||(N1={}));var S1;(function(e){e[e.Inline=1]="Inline",e[e.Gutter=2]="Gutter"})(S1||(S1={}));var v1;(function(e){e[e.Both=0]="Both",e[e.Right=1]="Right",e[e.Left=2]="Left",e[e.None=3]="None"})(v1||(v1={}));class ra{constructor(t){this._textModelResolvedOptionsBrand=void 0,this.tabSize=Math.max(1,t.tabSize|0),this.indentSize=t.tabSize|0,this.insertSpaces=!!t.insertSpaces,this.defaultEOL=t.defaultEOL|0,this.trimAutoWhitespace=!!t.trimAutoWhitespace,this.bracketPairColorizationOptions=t.bracketPairColorizationOptions}equals(t){return this.tabSize===t.tabSize&&this.indentSize===t.indentSize&&this.insertSpaces===t.insertSpaces&&this.defaultEOL===t.defaultEOL&&this.trimAutoWhitespace===t.trimAutoWhitespace&&Re(this.bracketPairColorizationOptions,t.bracketPairColorizationOptions)}createChangeEvent(t){return{tabSize:this.tabSize!==t.tabSize,indentSize:this.indentSize!==t.indentSize,insertSpaces:this.insertSpaces!==t.insertSpaces,trimAutoWhitespace:this.trimAutoWhitespace!==t.trimAutoWhitespace}}}class ct{constructor(t,n){this._findMatchBrand=void 0,this.range=t,this.matches=n}}class sa{constructor(t,n,s,i,a,o){this.identifier=t,this.range=n,this.text=s,this.forceMoveMarkers=i,this.isAutoWhitespaceEdit=a,this._isTracked=o}}class Vr{constructor(t,n,s){this.regex=t,this.wordSeparators=n,this.simpleSearch=s}}class ia{constructor(t,n,s){this.reverseEdits=t,this.changes=n,this.trimAutoWhitespaceLineNumbers=s}}function aa(e){return!e.isTooLargeForSyncing()&&!e.isForSimpleWidget}const Br="$initialize";let A1=!1;function oa(e){bn&&(A1||(A1=!0,console.warn("Could not create web worker(s). Falling back to loading web worker code in main thread, which might cause UI freezes. Please see https://github.com/microsoft/monaco-editor#faq")),console.warn(e.message))}class Ir{constructor(t,n,s,i){this.vsWorker=t,this.req=n,this.method=s,this.args=i,this.type=0}}class y1{constructor(t,n,s,i){this.vsWorker=t,this.seq=n,this.res=s,this.err=i,this.type=1}}class Ur{constructor(t,n,s,i){this.vsWorker=t,this.req=n,this.eventName=s,this.arg=i,this.type=2}}class Tr{constructor(t,n,s){this.vsWorker=t,this.req=n,this.event=s,this.type=3}}class Wr{constructor(t,n){this.vsWorker=t,this.req=n,this.type=4}}class qr{constructor(t){this._workerId=-1,this._handler=t,this._lastSentReq=0,this._pendingReplies=Object.create(null),this._pendingEmitters=new Map,this._pendingEvents=new Map}setWorkerId(t){this._workerId=t}sendMessage(t,n){const s=String(++this._lastSentReq);return new Promise((i,a)=>{this._pendingReplies[s]={resolve:i,reject:a},this._send(new Ir(this._workerId,s,t,n))})}listen(t,n){let s=null;const i=new $({onFirstListenerAdd:()=>{s=String(++this._lastSentReq),this._pendingEmitters.set(s,i),this._send(new Ur(this._workerId,s,t,n))},onLastListenerRemove:()=>{this._pendingEmitters.delete(s),this._send(new Wr(this._workerId,s)),s=null}});return i.event}handleMessage(t){!t||!t.vsWorker||this._workerId!==-1&&t.vsWorker!==this._workerId||this._handleMessage(t)}_handleMessage(t){switch(t.type){case 1:return this._handleReplyMessage(t);case 0:return this._handleRequestMessage(t);case 2:return this._handleSubscribeEventMessage(t);case 3:return this._handleEventMessage(t);case 4:return this._handleUnsubscribeEventMessage(t)}}_handleReplyMessage(t){if(!this._pendingReplies[t.seq]){console.warn("Got reply to unknown seq");return}let n=this._pendingReplies[t.seq];if(delete this._pendingReplies[t.seq],t.err){let s=t.err;t.err.$isError&&(s=new Error,s.name=t.err.name,s.message=t.err.message,s.stack=t.err.stack),n.reject(s);return}n.resolve(t.res)}_handleRequestMessage(t){let n=t.req;this._handler.handleMessage(t.method,t.args).then(i=>{this._send(new y1(this._workerId,n,i,void 0))},i=>{i.detail instanceof Error&&(i.detail=xt(i.detail)),this._send(new y1(this._workerId,n,void 0,xt(i)))})}_handleSubscribeEventMessage(t){const n=t.req,s=this._handler.handleEvent(t.eventName,t.arg)(i=>{this._send(new Tr(this._workerId,n,i))});this._pendingEvents.set(n,s)}_handleEventMessage(t){if(!this._pendingEmitters.has(t.req)){console.warn("Got event for unknown req");return}this._pendingEmitters.get(t.req).fire(t.event)}_handleUnsubscribeEventMessage(t){if(!this._pendingEvents.has(t.req)){console.warn("Got unsubscribe for unknown req");return}this._pendingEvents.get(t.req).dispose(),this._pendingEvents.delete(t.req)}_send(t){let n=[];if(t.type===0)for(let s=0;s<t.args.length;s++)t.args[s]instanceof ArrayBuffer&&n.push(t.args[s]);else t.type===1&&t.res instanceof ArrayBuffer&&n.push(t.res);this._handler.sendMessage(t,n)}}class la extends ve{constructor(t,n,s){super();let i=null;this._worker=this._register(t.create("vs/base/common/worker/simpleWorker",u=>{this._protocol.handleMessage(u)},u=>{i&&i(u)})),this._protocol=new qr({sendMessage:(u,h)=>{this._worker.postMessage(u,h)},handleMessage:(u,h)=>{if(typeof s[u]!="function")return Promise.reject(new Error("Missing method "+u+" on main thread host."));try{return Promise.resolve(s[u].apply(s,h))}catch(f){return Promise.reject(f)}},handleEvent:(u,h)=>{if(sn(u)){const f=s[u].call(s,h);if(typeof f!="function")throw new Error(`Missing dynamic event ${u} on main thread host.`);return f}if(rn(u)){const f=s[u];if(typeof f!="function")throw new Error(`Missing event ${u} on main thread host.`);return f}throw new Error(`Malformed event name ${u}`)}}),this._protocol.setWorkerId(this._worker.getId());let a=null;typeof V.require!="undefined"&&typeof V.require.getConfig=="function"?a=V.require.getConfig():typeof V.requirejs!="undefined"&&(a=V.requirejs.s.contexts._.config);const o=T1(s);this._onModuleLoaded=this._protocol.sendMessage(Br,[this._worker.getId(),JSON.parse(JSON.stringify(a)),n,o]);const l=(u,h)=>this._request(u,h),c=(u,h)=>this._protocol.listen(u,h);this._lazyProxy=new Promise((u,h)=>{i=h,this._onModuleLoaded.then(f=>{u(Hr(f,l,c))},f=>{h(f),this._onError("Worker failed to load "+n,f)})})}getProxyObject(){return this._lazyProxy}_request(t,n){return new Promise((s,i)=>{this._onModuleLoaded.then(()=>{this._protocol.sendMessage(t,n).then(s,i)},i)})}_onError(t,n){console.error(t),console.info(n)}}function rn(e){return e[0]==="o"&&e[1]==="n"&&en(e.charCodeAt(2))}function sn(e){return/^onDynamic/.test(e)&&en(e.charCodeAt(9))}function Hr(e,t,n){const s=o=>function(){const l=Array.prototype.slice.call(arguments,0);return t(o,l)},i=o=>function(l){return n(o,l)};let a={};for(const o of e){if(sn(o)){a[o]=i(o);continue}if(rn(o)){a[o]=n(o,void 0);continue}a[o]=s(o)}return a}class X{constructor(t,n,s,i){this.originalStart=t,this.originalLength=n,this.modifiedStart=s,this.modifiedLength=i}getOriginalEnd(){return this.originalStart+this.originalLength}getModifiedEnd(){return this.modifiedStart+this.modifiedLength}}function ua(e){return Nt(e,0)}function Nt(e,t){switch(typeof e){case"object":return e===null?Q(349,t):Array.isArray(e)?Or(e,t):$r(e,t);case"string":return St(e,t);case"boolean":return zr(e,t);case"number":return Q(e,t);case"undefined":return Q(937,t);default:return Q(617,t)}}function Q(e,t){return(t<<5)-t+e|0}function zr(e,t){return Q(e?433:863,t)}function St(e,t){t=Q(149417,t);for(let n=0,s=e.length;n<s;n++)t=Q(e.charCodeAt(n),t);return t}function Or(e,t){return t=Q(104579,t),e.reduce((n,s)=>Nt(s,n),t)}function $r(e,t){return t=Q(181387,t),Object.keys(e).sort().reduce((n,s)=>(n=St(s,n),Nt(e[s],n)),t)}function Qe(e,t,n=32){const s=n-t,i=~((1<<s)-1);return(e<<t|(i&e)>>>s)>>>0}function M1(e,t=0,n=e.byteLength,s=0){for(let i=0;i<n;i++)e[t+i]=s}function Gr(e,t,n="0"){for(;e.length<t;)e=n+e;return e}function de(e,t=32){return e instanceof ArrayBuffer?Array.from(new Uint8Array(e)).map(n=>n.toString(16).padStart(2,"0")).join(""):Gr((e>>>0).toString(16),t/4)}class vt{constructor(){this._h0=1732584193,this._h1=4023233417,this._h2=2562383102,this._h3=271733878,this._h4=3285377520,this._buff=new Uint8Array(64+3),this._buffDV=new DataView(this._buff.buffer),this._buffLen=0,this._totalLen=0,this._leftoverHighSurrogate=0,this._finished=!1}update(t){const n=t.length;if(n===0)return;const s=this._buff;let i=this._buffLen,a=this._leftoverHighSurrogate,o,l;for(a!==0?(o=a,l=-1,a=0):(o=t.charCodeAt(0),l=0);;){let c=o;if(pe(o))if(l+1<n){const u=t.charCodeAt(l+1);Le(u)?(l++,c=pt(o,u)):c=65533}else{a=o;break}else Le(o)&&(c=65533);if(i=this._push(s,i,c),l++,l<n)o=t.charCodeAt(l);else break}this._buffLen=i,this._leftoverHighSurrogate=a}_push(t,n,s){return s<128?t[n++]=s:s<2048?(t[n++]=192|(s&1984)>>>6,t[n++]=128|(s&63)>>>0):s<65536?(t[n++]=224|(s&61440)>>>12,t[n++]=128|(s&4032)>>>6,t[n++]=128|(s&63)>>>0):(t[n++]=240|(s&1835008)>>>18,t[n++]=128|(s&258048)>>>12,t[n++]=128|(s&4032)>>>6,t[n++]=128|(s&63)>>>0),n>=64&&(this._step(),n-=64,this._totalLen+=64,t[0]=t[64+0],t[1]=t[64+1],t[2]=t[64+2]),n}digest(){return this._finished||(this._finished=!0,this._leftoverHighSurrogate&&(this._leftoverHighSurrogate=0,this._buffLen=this._push(this._buff,this._buffLen,65533)),this._totalLen+=this._buffLen,this._wrapUp()),de(this._h0)+de(this._h1)+de(this._h2)+de(this._h3)+de(this._h4)}_wrapUp(){this._buff[this._buffLen++]=128,M1(this._buff,this._buffLen),this._buffLen>56&&(this._step(),M1(this._buff));const t=8*this._totalLen;this._buffDV.setUint32(56,Math.floor(t/4294967296),!1),this._buffDV.setUint32(60,t%4294967296,!1),this._step()}_step(){const t=vt._bigBlock32,n=this._buffDV;for(let f=0;f<64;f+=4)t.setUint32(f,n.getUint32(f,!1),!1);for(let f=64;f<320;f+=4)t.setUint32(f,Qe(t.getUint32(f-12,!1)^t.getUint32(f-32,!1)^t.getUint32(f-56,!1)^t.getUint32(f-64,!1),1),!1);let s=this._h0,i=this._h1,a=this._h2,o=this._h3,l=this._h4,c,u,h;for(let f=0;f<80;f++)f<20?(c=i&a|~i&o,u=1518500249):f<40?(c=i^a^o,u=1859775393):f<60?(c=i&a|i&o|a&o,u=2400959708):(c=i^a^o,u=3395469782),h=Qe(s,5)+c+l+u+t.getUint32(f*4,!1)&4294967295,l=o,o=a,a=Qe(i,30),i=s,s=h;this._h0=this._h0+s&4294967295,this._h1=this._h1+i&4294967295,this._h2=this._h2+a&4294967295,this._h3=this._h3+o&4294967295,this._h4=this._h4+l&4294967295}}vt._bigBlock32=new DataView(new ArrayBuffer(320));class x1{constructor(t){this.source=t}getElements(){const t=this.source,n=new Int32Array(t.length);for(let s=0,i=t.length;s<i;s++)n[s]=t.charCodeAt(s);return n}}function jr(e,t,n){return new K(new x1(e),new x1(t)).ComputeDiff(n).changes}class oe{static Assert(t,n){if(!t)throw new Error(n)}}class le{static Copy(t,n,s,i,a){for(let o=0;o<a;o++)s[i+o]=t[n+o]}static Copy2(t,n,s,i,a){for(let o=0;o<a;o++)s[i+o]=t[n+o]}}class F1{constructor(){this.m_changes=[],this.m_originalStart=1073741824,this.m_modifiedStart=1073741824,this.m_originalCount=0,this.m_modifiedCount=0}MarkNextChange(){(this.m_originalCount>0||this.m_modifiedCount>0)&&this.m_changes.push(new X(this.m_originalStart,this.m_originalCount,this.m_modifiedStart,this.m_modifiedCount)),this.m_originalCount=0,this.m_modifiedCount=0,this.m_originalStart=1073741824,this.m_modifiedStart=1073741824}AddOriginalElement(t,n){this.m_originalStart=Math.min(this.m_originalStart,t),this.m_modifiedStart=Math.min(this.m_modifiedStart,n),this.m_originalCount++}AddModifiedElement(t,n){this.m_originalStart=Math.min(this.m_originalStart,t),this.m_modifiedStart=Math.min(this.m_modifiedStart,n),this.m_modifiedCount++}getChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes}getReverseChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes.reverse(),this.m_changes}}class K{constructor(t,n,s=null){this.ContinueProcessingPredicate=s,this._originalSequence=t,this._modifiedSequence=n;const[i,a,o]=K._getElements(t),[l,c,u]=K._getElements(n);this._hasStrings=o&&u,this._originalStringElements=i,this._originalElementsOrHash=a,this._modifiedStringElements=l,this._modifiedElementsOrHash=c,this.m_forwardHistory=[],this.m_reverseHistory=[]}static _isStringArray(t){return t.length>0&&typeof t[0]=="string"}static _getElements(t){const n=t.getElements();if(K._isStringArray(n)){const s=new Int32Array(n.length);for(let i=0,a=n.length;i<a;i++)s[i]=St(n[i],0);return[n,s,!0]}return n instanceof Int32Array?[[],n,!1]:[[],new Int32Array(n),!1]}ElementsAreEqual(t,n){return this._originalElementsOrHash[t]!==this._modifiedElementsOrHash[n]?!1:this._hasStrings?this._originalStringElements[t]===this._modifiedStringElements[n]:!0}ElementsAreStrictEqual(t,n){if(!this.ElementsAreEqual(t,n))return!1;const s=K._getStrictElement(this._originalSequence,t),i=K._getStrictElement(this._modifiedSequence,n);return s===i}static _getStrictElement(t,n){return typeof t.getStrictElement=="function"?t.getStrictElement(n):null}OriginalElementsAreEqual(t,n){return this._originalElementsOrHash[t]!==this._originalElementsOrHash[n]?!1:this._hasStrings?this._originalStringElements[t]===this._originalStringElements[n]:!0}ModifiedElementsAreEqual(t,n){return this._modifiedElementsOrHash[t]!==this._modifiedElementsOrHash[n]?!1:this._hasStrings?this._modifiedStringElements[t]===this._modifiedStringElements[n]:!0}ComputeDiff(t){return this._ComputeDiff(0,this._originalElementsOrHash.length-1,0,this._modifiedElementsOrHash.length-1,t)}_ComputeDiff(t,n,s,i,a){const o=[!1];let l=this.ComputeDiffRecursive(t,n,s,i,o);return a&&(l=this.PrettifyChanges(l)),{quitEarly:o[0],changes:l}}ComputeDiffRecursive(t,n,s,i,a){for(a[0]=!1;t<=n&&s<=i&&this.ElementsAreEqual(t,s);)t++,s++;for(;n>=t&&i>=s&&this.ElementsAreEqual(n,i);)n--,i--;if(t>n||s>i){let f;return s<=i?(oe.Assert(t===n+1,"originalStart should only be one more than originalEnd"),f=[new X(t,0,s,i-s+1)]):t<=n?(oe.Assert(s===i+1,"modifiedStart should only be one more than modifiedEnd"),f=[new X(t,n-t+1,s,0)]):(oe.Assert(t===n+1,"originalStart should only be one more than originalEnd"),oe.Assert(s===i+1,"modifiedStart should only be one more than modifiedEnd"),f=[]),f}const o=[0],l=[0],c=this.ComputeRecursionPoint(t,n,s,i,o,l,a),u=o[0],h=l[0];if(c!==null)return c;if(!a[0]){const f=this.ComputeDiffRecursive(t,u,s,h,a);let d=[];return a[0]?d=[new X(u+1,n-(u+1)+1,h+1,i-(h+1)+1)]:d=this.ComputeDiffRecursive(u+1,n,h+1,i,a),this.ConcatenateChanges(f,d)}return[new X(t,n-t+1,s,i-s+1)]}WALKTRACE(t,n,s,i,a,o,l,c,u,h,f,d,w,S,v,M,E,C){let p=null,g=null,m=new F1,L=n,_=s,b=w[0]-M[0]-i,N=-1073741824,F=this.m_forwardHistory.length-1;do{const y=b+t;y===L||y<_&&u[y-1]<u[y+1]?(f=u[y+1],S=f-b-i,f<N&&m.MarkNextChange(),N=f,m.AddModifiedElement(f+1,S),b=y+1-t):(f=u[y-1]+1,S=f-b-i,f<N&&m.MarkNextChange(),N=f-1,m.AddOriginalElement(f,S+1),b=y-1-t),F>=0&&(u=this.m_forwardHistory[F],t=u[0],L=1,_=u.length-1)}while(--F>=-1);if(p=m.getReverseChanges(),C[0]){let y=w[0]+1,A=M[0]+1;if(p!==null&&p.length>0){const G=p[p.length-1];y=Math.max(y,G.getOriginalEnd()),A=Math.max(A,G.getModifiedEnd())}g=[new X(y,d-y+1,A,v-A+1)]}else{m=new F1,L=o,_=l,b=w[0]-M[0]-c,N=1073741824,F=E?this.m_reverseHistory.length-1:this.m_reverseHistory.length-2;do{const y=b+a;y===L||y<_&&h[y-1]>=h[y+1]?(f=h[y+1]-1,S=f-b-c,f>N&&m.MarkNextChange(),N=f+1,m.AddOriginalElement(f+1,S+1),b=y+1-a):(f=h[y-1],S=f-b-c,f>N&&m.MarkNextChange(),N=f,m.AddModifiedElement(f+1,S+1),b=y-1-a),F>=0&&(h=this.m_reverseHistory[F],a=h[0],L=1,_=h.length-1)}while(--F>=-1);g=m.getChanges()}return this.ConcatenateChanges(p,g)}ComputeRecursionPoint(t,n,s,i,a,o,l){let c=0,u=0,h=0,f=0,d=0,w=0;t--,s--,a[0]=0,o[0]=0,this.m_forwardHistory=[],this.m_reverseHistory=[];const S=n-t+(i-s),v=S+1,M=new Int32Array(v),E=new Int32Array(v),C=i-s,p=n-t,g=t-s,m=n-i,_=(p-C)%2===0;M[C]=t,E[p]=n,l[0]=!1;for(let b=1;b<=S/2+1;b++){let N=0,F=0;h=this.ClipDiagonalBound(C-b,b,C,v),f=this.ClipDiagonalBound(C+b,b,C,v);for(let A=h;A<=f;A+=2){A===h||A<f&&M[A-1]<M[A+1]?c=M[A+1]:c=M[A-1]+1,u=c-(A-C)-g;const G=c;for(;c<n&&u<i&&this.ElementsAreEqual(c+1,u+1);)c++,u++;if(M[A]=c,c+u>N+F&&(N=c,F=u),!_&&Math.abs(A-p)<=b-1&&c>=E[A])return a[0]=c,o[0]=u,G<=E[A]&&1447>0&&b<=1447+1?this.WALKTRACE(C,h,f,g,p,d,w,m,M,E,c,n,a,u,i,o,_,l):null}const y=(N-t+(F-s)-b)/2;if(this.ContinueProcessingPredicate!==null&&!this.ContinueProcessingPredicate(N,y))return l[0]=!0,a[0]=N,o[0]=F,y>0&&1447>0&&b<=1447+1?this.WALKTRACE(C,h,f,g,p,d,w,m,M,E,c,n,a,u,i,o,_,l):(t++,s++,[new X(t,n-t+1,s,i-s+1)]);d=this.ClipDiagonalBound(p-b,b,p,v),w=this.ClipDiagonalBound(p+b,b,p,v);for(let A=d;A<=w;A+=2){A===d||A<w&&E[A-1]>=E[A+1]?c=E[A+1]-1:c=E[A-1],u=c-(A-p)-m;const G=c;for(;c>t&&u>s&&this.ElementsAreEqual(c,u);)c--,u--;if(E[A]=c,_&&Math.abs(A-C)<=b&&c<=M[A])return a[0]=c,o[0]=u,G>=M[A]&&1447>0&&b<=1447+1?this.WALKTRACE(C,h,f,g,p,d,w,m,M,E,c,n,a,u,i,o,_,l):null}if(b<=1447){let A=new Int32Array(f-h+2);A[0]=C-h+1,le.Copy2(M,h,A,1,f-h+1),this.m_forwardHistory.push(A),A=new Int32Array(w-d+2),A[0]=p-d+1,le.Copy2(E,d,A,1,w-d+1),this.m_reverseHistory.push(A)}}return this.WALKTRACE(C,h,f,g,p,d,w,m,M,E,c,n,a,u,i,o,_,l)}PrettifyChanges(t){for(let n=0;n<t.length;n++){const s=t[n],i=n<t.length-1?t[n+1].originalStart:this._originalElementsOrHash.length,a=n<t.length-1?t[n+1].modifiedStart:this._modifiedElementsOrHash.length,o=s.originalLength>0,l=s.modifiedLength>0;for(;s.originalStart+s.originalLength<i&&s.modifiedStart+s.modifiedLength<a&&(!o||this.OriginalElementsAreEqual(s.originalStart,s.originalStart+s.originalLength))&&(!l||this.ModifiedElementsAreEqual(s.modifiedStart,s.modifiedStart+s.modifiedLength));){const u=this.ElementsAreStrictEqual(s.originalStart,s.modifiedStart);if(this.ElementsAreStrictEqual(s.originalStart+s.originalLength,s.modifiedStart+s.modifiedLength)&&!u)break;s.originalStart++,s.modifiedStart++}let c=[null];if(n<t.length-1&&this.ChangesOverlap(t[n],t[n+1],c)){t[n]=c[0],t.splice(n+1,1),n--;continue}}for(let n=t.length-1;n>=0;n--){const s=t[n];let i=0,a=0;if(n>0){const f=t[n-1];i=f.originalStart+f.originalLength,a=f.modifiedStart+f.modifiedLength}const o=s.originalLength>0,l=s.modifiedLength>0;let c=0,u=this._boundaryScore(s.originalStart,s.originalLength,s.modifiedStart,s.modifiedLength);for(let f=1;;f++){const d=s.originalStart-f,w=s.modifiedStart-f;if(d<i||w<a||o&&!this.OriginalElementsAreEqual(d,d+s.originalLength)||l&&!this.ModifiedElementsAreEqual(w,w+s.modifiedLength))break;const v=(d===i&&w===a?5:0)+this._boundaryScore(d,s.originalLength,w,s.modifiedLength);v>u&&(u=v,c=f)}s.originalStart-=c,s.modifiedStart-=c;const h=[null];if(n>0&&this.ChangesOverlap(t[n-1],t[n],h)){t[n-1]=h[0],t.splice(n,1),n++;continue}}if(this._hasStrings)for(let n=1,s=t.length;n<s;n++){const i=t[n-1],a=t[n],o=a.originalStart-i.originalStart-i.originalLength,l=i.originalStart,c=a.originalStart+a.originalLength,u=c-l,h=i.modifiedStart,f=a.modifiedStart+a.modifiedLength,d=f-h;if(o<5&&u<20&&d<20){const w=this._findBetterContiguousSequence(l,u,h,d,o);if(w){const[S,v]=w;(S!==i.originalStart+i.originalLength||v!==i.modifiedStart+i.modifiedLength)&&(i.originalLength=S-i.originalStart,i.modifiedLength=v-i.modifiedStart,a.originalStart=S+o,a.modifiedStart=v+o,a.originalLength=c-a.originalStart,a.modifiedLength=f-a.modifiedStart)}}}return t}_findBetterContiguousSequence(t,n,s,i,a){if(n<a||i<a)return null;const o=t+n-a+1,l=s+i-a+1;let c=0,u=0,h=0;for(let f=t;f<o;f++)for(let d=s;d<l;d++){const w=this._contiguousSequenceScore(f,d,a);w>0&&w>c&&(c=w,u=f,h=d)}return c>0?[u,h]:null}_contiguousSequenceScore(t,n,s){let i=0;for(let a=0;a<s;a++){if(!this.ElementsAreEqual(t+a,n+a))return 0;i+=this._originalStringElements[t+a].length}return i}_OriginalIsBoundary(t){return t<=0||t>=this._originalElementsOrHash.length-1?!0:this._hasStrings&&/^\s*$/.test(this._originalStringElements[t])}_OriginalRegionIsBoundary(t,n){if(this._OriginalIsBoundary(t)||this._OriginalIsBoundary(t-1))return!0;if(n>0){const s=t+n;if(this._OriginalIsBoundary(s-1)||this._OriginalIsBoundary(s))return!0}return!1}_ModifiedIsBoundary(t){return t<=0||t>=this._modifiedElementsOrHash.length-1?!0:this._hasStrings&&/^\s*$/.test(this._modifiedStringElements[t])}_ModifiedRegionIsBoundary(t,n){if(this._ModifiedIsBoundary(t)||this._ModifiedIsBoundary(t-1))return!0;if(n>0){const s=t+n;if(this._ModifiedIsBoundary(s-1)||this._ModifiedIsBoundary(s))return!0}return!1}_boundaryScore(t,n,s,i){const a=this._OriginalRegionIsBoundary(t,n)?1:0,o=this._ModifiedRegionIsBoundary(s,i)?1:0;return a+o}ConcatenateChanges(t,n){let s=[];if(t.length===0||n.length===0)return n.length>0?n:t;if(this.ChangesOverlap(t[t.length-1],n[0],s)){const i=new Array(t.length+n.length-1);return le.Copy(t,0,i,0,t.length-1),i[t.length-1]=s[0],le.Copy(n,1,i,t.length,n.length-1),i}else{const i=new Array(t.length+n.length);return le.Copy(t,0,i,0,t.length),le.Copy(n,0,i,t.length,n.length),i}}ChangesOverlap(t,n,s){if(oe.Assert(t.originalStart<=n.originalStart,"Left change is not less than or equal to right change"),oe.Assert(t.modifiedStart<=n.modifiedStart,"Left change is not less than or equal to right change"),t.originalStart+t.originalLength>=n.originalStart||t.modifiedStart+t.modifiedLength>=n.modifiedStart){const i=t.originalStart;let a=t.originalLength;const o=t.modifiedStart;let l=t.modifiedLength;return t.originalStart+t.originalLength>=n.originalStart&&(a=n.originalStart+n.originalLength-t.originalStart),t.modifiedStart+t.modifiedLength>=n.modifiedStart&&(l=n.modifiedStart+n.modifiedLength-t.modifiedStart),s[0]=new X(i,a,o,l),!0}else return s[0]=null,!1}ClipDiagonalBound(t,n,s,i){if(t>=0&&t<i)return t;const a=s,o=i-s-1,l=n%2===0;if(t<0){const c=a%2===0;return l===c?0:1}else{const c=o%2===0;return l===c?i-1:i-2}}}const Qr=3;function an(e,t,n,s){return new K(e,t,n).ComputeDiff(s)}class k1{constructor(t){const n=[],s=[];for(let i=0,a=t.length;i<a;i++)n[i]=ft(t[i],1),s[i]=ht(t[i],1);this.lines=t,this._startColumns=n,this._endColumns=s}getElements(){const t=[];for(let n=0,s=this.lines.length;n<s;n++)t[n]=this.lines[n].substring(this._startColumns[n]-1,this._endColumns[n]-1);return t}getStrictElement(t){return this.lines[t]}getStartLineNumber(t){return t+1}getEndLineNumber(t){return t+1}createCharSequence(t,n,s){const i=[],a=[],o=[];let l=0;for(let c=n;c<=s;c++){const u=this.lines[c],h=t?this._startColumns[c]:1,f=t?this._endColumns[c]:u.length+1;for(let d=h;d<f;d++)i[l]=u.charCodeAt(d-1),a[l]=c+1,o[l]=d,l++}return new Zr(i,a,o)}}class Zr{constructor(t,n,s){this._charCodes=t,this._lineNumbers=n,this._columns=s}getElements(){return this._charCodes}getStartLineNumber(t){return this._lineNumbers[t]}getStartColumn(t){return this._columns[t]}getEndLineNumber(t){return this._lineNumbers[t]}getEndColumn(t){return this._columns[t]+1}}class Ne{constructor(t,n,s,i,a,o,l,c){this.originalStartLineNumber=t,this.originalStartColumn=n,this.originalEndLineNumber=s,this.originalEndColumn=i,this.modifiedStartLineNumber=a,this.modifiedStartColumn=o,this.modifiedEndLineNumber=l,this.modifiedEndColumn=c}static createFromDiffChange(t,n,s){let i,a,o,l,c,u,h,f;return t.originalLength===0?(i=0,a=0,o=0,l=0):(i=n.getStartLineNumber(t.originalStart),a=n.getStartColumn(t.originalStart),o=n.getEndLineNumber(t.originalStart+t.originalLength-1),l=n.getEndColumn(t.originalStart+t.originalLength-1)),t.modifiedLength===0?(c=0,u=0,h=0,f=0):(c=s.getStartLineNumber(t.modifiedStart),u=s.getStartColumn(t.modifiedStart),h=s.getEndLineNumber(t.modifiedStart+t.modifiedLength-1),f=s.getEndColumn(t.modifiedStart+t.modifiedLength-1)),new Ne(i,a,o,l,c,u,h,f)}}function Yr(e){if(e.length<=1)return e;const t=[e[0]];let n=t[0];for(let s=1,i=e.length;s<i;s++){const a=e[s],o=a.originalStart-(n.originalStart+n.originalLength),l=a.modifiedStart-(n.modifiedStart+n.modifiedLength);Math.min(o,l)<Qr?(n.originalLength=a.originalStart+a.originalLength-n.originalStart,n.modifiedLength=a.modifiedStart+a.modifiedLength-n.modifiedStart):(t.push(a),n=a)}return t}class Ce{constructor(t,n,s,i,a){this.originalStartLineNumber=t,this.originalEndLineNumber=n,this.modifiedStartLineNumber=s,this.modifiedEndLineNumber=i,this.charChanges=a}static createFromDiffResult(t,n,s,i,a,o,l){let c,u,h,f,d;if(n.originalLength===0?(c=s.getStartLineNumber(n.originalStart)-1,u=0):(c=s.getStartLineNumber(n.originalStart),u=s.getEndLineNumber(n.originalStart+n.originalLength-1)),n.modifiedLength===0?(h=i.getStartLineNumber(n.modifiedStart)-1,f=0):(h=i.getStartLineNumber(n.modifiedStart),f=i.getEndLineNumber(n.modifiedStart+n.modifiedLength-1)),o&&n.originalLength>0&&n.originalLength<20&&n.modifiedLength>0&&n.modifiedLength<20&&a()){const w=s.createCharSequence(t,n.originalStart,n.originalStart+n.originalLength-1),S=i.createCharSequence(t,n.modifiedStart,n.modifiedStart+n.modifiedLength-1);let v=an(w,S,a,!0).changes;l&&(v=Yr(v)),d=[];for(let M=0,E=v.length;M<E;M++)d.push(Ne.createFromDiffChange(v[M],w,S))}return new Ce(c,u,h,f,d)}}class Jr{constructor(t,n,s){this.shouldComputeCharChanges=s.shouldComputeCharChanges,this.shouldPostProcessCharChanges=s.shouldPostProcessCharChanges,this.shouldIgnoreTrimWhitespace=s.shouldIgnoreTrimWhitespace,this.shouldMakePrettyDiff=s.shouldMakePrettyDiff,this.originalLines=t,this.modifiedLines=n,this.original=new k1(t),this.modified=new k1(n),this.continueLineDiff=E1(s.maxComputationTime),this.continueCharDiff=E1(s.maxComputationTime===0?0:Math.min(s.maxComputationTime,5e3))}computeDiff(){if(this.original.lines.length===1&&this.original.lines[0].length===0)return this.modified.lines.length===1&&this.modified.lines[0].length===0?{quitEarly:!1,changes:[]}:{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:1,modifiedStartLineNumber:1,modifiedEndLineNumber:this.modified.lines.length,charChanges:[{modifiedEndColumn:0,modifiedEndLineNumber:0,modifiedStartColumn:0,modifiedStartLineNumber:0,originalEndColumn:0,originalEndLineNumber:0,originalStartColumn:0,originalStartLineNumber:0}]}]};if(this.modified.lines.length===1&&this.modified.lines[0].length===0)return{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:this.original.lines.length,modifiedStartLineNumber:1,modifiedEndLineNumber:1,charChanges:[{modifiedEndColumn:0,modifiedEndLineNumber:0,modifiedStartColumn:0,modifiedStartLineNumber:0,originalEndColumn:0,originalEndLineNumber:0,originalStartColumn:0,originalStartLineNumber:0}]}]};const t=an(this.original,this.modified,this.continueLineDiff,this.shouldMakePrettyDiff),n=t.changes,s=t.quitEarly;if(this.shouldIgnoreTrimWhitespace){const l=[];for(let c=0,u=n.length;c<u;c++)l.push(Ce.createFromDiffResult(this.shouldIgnoreTrimWhitespace,n[c],this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges));return{quitEarly:s,changes:l}}const i=[];let a=0,o=0;for(let l=-1,c=n.length;l<c;l++){const u=l+1<c?n[l+1]:null,h=u?u.originalStart:this.originalLines.length,f=u?u.modifiedStart:this.modifiedLines.length;for(;a<h&&o<f;){const d=this.originalLines[a],w=this.modifiedLines[o];if(d!==w){{let S=ft(d,1),v=ft(w,1);for(;S>1&&v>1;){const M=d.charCodeAt(S-2),E=w.charCodeAt(v-2);if(M!==E)break;S--,v--}(S>1||v>1)&&this._pushTrimWhitespaceCharChange(i,a+1,1,S,o+1,1,v)}{let S=ht(d,1),v=ht(w,1);const M=d.length+1,E=w.length+1;for(;S<M&&v<E;){const C=d.charCodeAt(S-1),p=d.charCodeAt(v-1);if(C!==p)break;S++,v++}(S<M||v<E)&&this._pushTrimWhitespaceCharChange(i,a+1,S,M,o+1,v,E)}}a++,o++}u&&(i.push(Ce.createFromDiffResult(this.shouldIgnoreTrimWhitespace,u,this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges)),a+=u.originalLength,o+=u.modifiedLength)}return{quitEarly:s,changes:i}}_pushTrimWhitespaceCharChange(t,n,s,i,a,o,l){if(this._mergeTrimWhitespaceCharChange(t,n,s,i,a,o,l))return;let c;this.shouldComputeCharChanges&&(c=[new Ne(n,s,n,i,a,o,a,l)]),t.push(new Ce(n,n,a,a,c))}_mergeTrimWhitespaceCharChange(t,n,s,i,a,o,l){const c=t.length;if(c===0)return!1;const u=t[c-1];return u.originalEndLineNumber===0||u.modifiedEndLineNumber===0?!1:u.originalEndLineNumber+1===n&&u.modifiedEndLineNumber+1===a?(u.originalEndLineNumber=n,u.modifiedEndLineNumber=a,this.shouldComputeCharChanges&&u.charChanges&&u.charChanges.push(new Ne(n,s,n,i,a,o,a,l)),!0):!1}}function ft(e,t){const n=Lr(e);return n===-1?t:n+1}function ht(e,t){const n=Nr(e);return n===-1?t:n+2}function E1(e){if(e===0)return()=>!0;const t=Date.now();return()=>Date.now()-t<e}function D1(e){return e<0?0:e>255?255:e|0}function ue(e){return e<0?0:e>4294967295?4294967295:e|0}class Xr{constructor(t){this.values=t,this.prefixSum=new Uint32Array(t.length),this.prefixSumValidIndex=new Int32Array(1),this.prefixSumValidIndex[0]=-1}insertValues(t,n){t=ue(t);const s=this.values,i=this.prefixSum,a=n.length;return a===0?!1:(this.values=new Uint32Array(s.length+a),this.values.set(s.subarray(0,t),0),this.values.set(s.subarray(t),t+a),this.values.set(n,t),t-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=t-1),this.prefixSum=new Uint32Array(this.values.length),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(i.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}setValue(t,n){return t=ue(t),n=ue(n),this.values[t]===n?!1:(this.values[t]=n,t-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=t-1),!0)}removeValues(t,n){t=ue(t),n=ue(n);const s=this.values,i=this.prefixSum;if(t>=s.length)return!1;const a=s.length-t;return n>=a&&(n=a),n===0?!1:(this.values=new Uint32Array(s.length-n),this.values.set(s.subarray(0,t),0),this.values.set(s.subarray(t+n),t),this.prefixSum=new Uint32Array(this.values.length),t-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=t-1),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(i.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}getTotalSum(){return this.values.length===0?0:this._getPrefixSum(this.values.length-1)}getPrefixSum(t){return t<0?0:(t=ue(t),this._getPrefixSum(t))}_getPrefixSum(t){if(t<=this.prefixSumValidIndex[0])return this.prefixSum[t];let n=this.prefixSumValidIndex[0]+1;n===0&&(this.prefixSum[0]=this.values[0],n++),t>=this.values.length&&(t=this.values.length-1);for(let s=n;s<=t;s++)this.prefixSum[s]=this.prefixSum[s-1]+this.values[s];return this.prefixSumValidIndex[0]=Math.max(this.prefixSumValidIndex[0],t),this.prefixSum[t]}getIndexOf(t){t=Math.floor(t),this.getTotalSum();let n=0,s=this.values.length-1,i=0,a=0,o=0;for(;n<=s;)if(i=n+(s-n)/2|0,a=this.prefixSum[i],o=a-this.values[i],t<o)s=i-1;else if(t>=a)n=i+1;else break;return new on(i,t-o)}}class ca{constructor(t){this._values=t,this._isValid=!1,this._validEndIndex=-1,this._prefixSum=[],this._indexBySum=[]}getTotalSum(){return this._ensureValid(),this._indexBySum.length}getPrefixSum(t){return this._ensureValid(),t===0?0:this._prefixSum[t-1]}getIndexOf(t){this._ensureValid();const n=this._indexBySum[t],s=n>0?this._prefixSum[n-1]:0;return new on(n,t-s)}removeValues(t,n){this._values.splice(t,n),this._invalidate(t)}insertValues(t,n){this._values=vn(this._values,t,n),this._invalidate(t)}_invalidate(t){this._isValid=!1,this._validEndIndex=Math.min(this._validEndIndex,t-1)}_ensureValid(){if(!this._isValid){for(let t=this._validEndIndex+1,n=this._values.length;t<n;t++){const s=this._values[t],i=t>0?this._prefixSum[t-1]:0;this._prefixSum[t]=i+s;for(let a=0;a<s;a++)this._indexBySum[i+a]=t}this._prefixSum.length=this._values.length,this._indexBySum.length=this._prefixSum[this._prefixSum.length-1],this._isValid=!0,this._validEndIndex=this._values.length-1}}setValue(t,n){this._values[t]!==n&&(this._values[t]=n,this._invalidate(t))}}class on{constructor(t,n){this.index=t,this.remainder=n,this._prefixSumIndexOfResultBrand=void 0,this.index=t,this.remainder=n}}class Kr{constructor(t,n,s,i){this._uri=t,this._lines=n,this._eol=s,this._versionId=i,this._lineStarts=null,this._cachedTextValue=null}dispose(){this._lines.length=0}get version(){return this._versionId}getText(){return this._cachedTextValue===null&&(this._cachedTextValue=this._lines.join(this._eol)),this._cachedTextValue}onEvents(t){t.eol&&t.eol!==this._eol&&(this._eol=t.eol,this._lineStarts=null);const n=t.changes;for(const s of n)this._acceptDeleteRange(s.range),this._acceptInsertText(new U(s.range.startLineNumber,s.range.startColumn),s.text);this._versionId=t.versionId,this._cachedTextValue=null}_ensureLineStarts(){if(!this._lineStarts){const t=this._eol.length,n=this._lines.length,s=new Uint32Array(n);for(let i=0;i<n;i++)s[i]=this._lines[i].length+t;this._lineStarts=new Xr(s)}}_setLineText(t,n){this._lines[t]=n,this._lineStarts&&this._lineStarts.setValue(t,this._lines[t].length+this._eol.length)}_acceptDeleteRange(t){if(t.startLineNumber===t.endLineNumber){if(t.startColumn===t.endColumn)return;this._setLineText(t.startLineNumber-1,this._lines[t.startLineNumber-1].substring(0,t.startColumn-1)+this._lines[t.startLineNumber-1].substring(t.endColumn-1));return}this._setLineText(t.startLineNumber-1,this._lines[t.startLineNumber-1].substring(0,t.startColumn-1)+this._lines[t.endLineNumber-1].substring(t.endColumn-1)),this._lines.splice(t.startLineNumber,t.endLineNumber-t.startLineNumber),this._lineStarts&&this._lineStarts.removeValues(t.startLineNumber,t.endLineNumber-t.startLineNumber)}_acceptInsertText(t,n){if(n.length===0)return;const s=pr(n);if(s.length===1){this._setLineText(t.lineNumber-1,this._lines[t.lineNumber-1].substring(0,t.column-1)+s[0]+this._lines[t.lineNumber-1].substring(t.column-1));return}s[s.length-1]+=this._lines[t.lineNumber-1].substring(t.column-1),this._setLineText(t.lineNumber-1,this._lines[t.lineNumber-1].substring(0,t.column-1)+s[0]);const i=new Uint32Array(s.length-1);for(let a=1;a<s.length;a++)this._lines.splice(t.lineNumber+a-1,0,s[a]),i[a-1]=s[a].length+this._eol.length;this._lineStarts&&this._lineStarts.insertValues(t.lineNumber,i)}}class ye{constructor(t){const n=D1(t);this._defaultValue=n,this._asciiMap=ye._createAsciiMap(n),this._map=new Map}static _createAsciiMap(t){const n=new Uint8Array(256);for(let s=0;s<256;s++)n[s]=t;return n}set(t,n){const s=D1(n);t>=0&&t<256?this._asciiMap[t]=s:this._map.set(t,s)}get(t){return t>=0&&t<256?this._asciiMap[t]:this._map.get(t)||this._defaultValue}}class fa{constructor(){this._actual=new ye(0)}add(t){this._actual.set(t,1)}has(t){return this._actual.get(t)===1}}class es{constructor(t,n,s){const i=new Uint8Array(t*n);for(let a=0,o=t*n;a<o;a++)i[a]=s;this._data=i,this.rows=t,this.cols=n}get(t,n){return this._data[t*this.cols+n]}set(t,n,s){this._data[t*this.cols+n]=s}}class ts{constructor(t){let n=0,s=0;for(let a=0,o=t.length;a<o;a++){const[l,c,u]=t[a];c>n&&(n=c),l>s&&(s=l),u>s&&(s=u)}n++,s++;const i=new es(s,n,0);for(let a=0,o=t.length;a<o;a++){const[l,c,u]=t[a];i.set(l,c,u)}this._states=i,this._maxCharCode=n}nextState(t,n){return n<0||n>=this._maxCharCode?0:this._states.get(t,n)}}let Ze=null;function ns(){return Ze===null&&(Ze=new ts([[1,104,2],[1,72,2],[1,102,6],[1,70,6],[2,116,3],[2,84,3],[3,116,4],[3,84,4],[4,112,5],[4,80,5],[5,115,9],[5,83,9],[5,58,10],[6,105,7],[6,73,7],[7,108,8],[7,76,8],[8,101,9],[8,69,9],[9,58,10],[10,47,11],[11,47,12]])),Ze}let me=null;function rs(){if(me===null){me=new ye(0);const e=` 	<>'"、。｡､，．：；‘〈「『〔（［｛｢｣｝］）〕』」〉’｀～…`;for(let n=0;n<e.length;n++)me.set(e.charCodeAt(n),1);const t=".,;";for(let n=0;n<t.length;n++)me.set(t.charCodeAt(n),2)}return me}class ze{static _createLink(t,n,s,i,a){let o=a-1;do{const l=n.charCodeAt(o);if(t.get(l)!==2)break;o--}while(o>i);if(i>0){const l=n.charCodeAt(i-1),c=n.charCodeAt(o);(l===40&&c===41||l===91&&c===93||l===123&&c===125)&&o--}return{range:{startLineNumber:s,startColumn:i+1,endLineNumber:s,endColumn:o+2},url:n.substring(i,o+1)}}static computeLinks(t,n=ns()){const s=rs(),i=[];for(let a=1,o=t.getLineCount();a<=o;a++){const l=t.getLineContent(a),c=l.length;let u=0,h=0,f=0,d=1,w=!1,S=!1,v=!1,M=!1;for(;u<c;){let E=!1;const C=l.charCodeAt(u);if(d===13){let p;switch(C){case 40:w=!0,p=0;break;case 41:p=w?0:1;break;case 91:v=!0,S=!0,p=0;break;case 93:v=!1,p=S?0:1;break;case 123:M=!0,p=0;break;case 125:p=M?0:1;break;case 39:p=f===34||f===96?0:1;break;case 34:p=f===39||f===96?0:1;break;case 96:p=f===39||f===34?0:1;break;case 42:p=f===42?1:0;break;case 124:p=f===124?1:0;break;case 32:p=v?0:1;break;default:p=s.get(C)}p===1&&(i.push(ze._createLink(s,l,a,h,u)),E=!0)}else if(d===12){let p;C===91?(S=!0,p=0):p=s.get(C),p===1?E=!0:d=13}else d=n.nextState(d,C),d===0&&(E=!0);E&&(d=1,w=!1,S=!1,M=!1,h=u+1,f=C),u++}d===13&&i.push(ze._createLink(s,l,a,h,c))}return i}}function ss(e){return!e||typeof e.getLineCount!="function"||typeof e.getLineContent!="function"?[]:ze.computeLinks(e)}class dt{constructor(){this._defaultValueSet=[["true","false"],["True","False"],["Private","Public","Friend","ReadOnly","Partial","Protected","WriteOnly"],["public","protected","private"]]}navigateValueSet(t,n,s,i,a){if(t&&n){const o=this.doNavigateValueSet(n,a);if(o)return{range:t,value:o}}if(s&&i){const o=this.doNavigateValueSet(i,a);if(o)return{range:s,value:o}}return null}doNavigateValueSet(t,n){const s=this.numberReplace(t,n);return s!==null?s:this.textReplace(t,n)}numberReplace(t,n){const s=Math.pow(10,t.length-(t.lastIndexOf(".")+1));let i=Number(t),a=parseFloat(t);return!isNaN(i)&&!isNaN(a)&&i===a?i===0&&!n?null:(i=Math.floor(i*s),i+=n?s:-s,String(i/s)):null}textReplace(t,n){return this.valueSetsReplace(this._defaultValueSet,t,n)}valueSetsReplace(t,n,s){let i=null;for(let a=0,o=t.length;i===null&&a<o;a++)i=this.valueSetReplace(t[a],n,s);return i}valueSetReplace(t,n,s){let i=t.indexOf(n);return i>=0?(i+=s?1:-1,i<0?i=t.length-1:i%=t.length,t[i]):null}}dt.INSTANCE=new dt;class is extends ye{constructor(t){super(0);for(let n=0,s=t.length;n<s;n++)this.set(t.charCodeAt(n),2);this.set(32,1),this.set(9,1)}}function as(e){const t={};return n=>(t.hasOwnProperty(n)||(t[n]=e(n)),t[n])}const os=as(e=>new is(e)),ls=999;class ha{constructor(t,n,s,i){this.searchString=t,this.isRegex=n,this.matchCase=s,this.wordSeparators=i}parseSearchRequest(){if(this.searchString==="")return null;let t;this.isRegex?t=us(this.searchString):t=this.searchString.indexOf(`
`)>=0;let n=null;try{n=_r(this.searchString,this.isRegex,{matchCase:this.matchCase,wholeWord:!1,multiline:t,global:!0,unicode:!0})}catch(i){return null}if(!n)return null;let s=!this.isRegex&&!t;return s&&this.searchString.toLowerCase()!==this.searchString.toUpperCase()&&(s=this.matchCase),new Vr(n,this.wordSeparators?os(this.wordSeparators):null,s?this.searchString:null)}}function us(e){if(!e||e.length===0)return!1;for(let t=0,n=e.length;t<n;t++)if(e.charCodeAt(t)===92){if(t++,t>=n)break;const i=e.charCodeAt(t);if(i===110||i===114||i===87)return!0}return!1}function ge(e,t,n){if(!n)return new ct(e,null);const s=[];for(let i=0,a=t.length;i<a;i++)s[i]=t[i];return new ct(e,s)}class R1{constructor(t){const n=[];let s=0;for(let i=0,a=t.length;i<a;i++)t.charCodeAt(i)===10&&(n[s++]=i);this._lineFeedsOffsets=n}findLineFeedCountBeforeOffset(t){const n=this._lineFeedsOffsets;let s=0,i=n.length-1;if(i===-1||t<=n[0])return 0;for(;s<i;){const a=s+((i-s)/2>>0);n[a]>=t?i=a-1:n[a+1]>=t?(s=a,i=a):s=a+1}return s+1}}class da{static findMatches(t,n,s,i,a){const o=n.parseSearchRequest();return o?o.regex.multiline?this._doFindMatchesMultiline(t,s,new we(o.wordSeparators,o.regex),i,a):this._doFindMatchesLineByLine(t,s,o,i,a):[]}static _getMultilineMatchRange(t,n,s,i,a,o){let l,c=0;i?(c=i.findLineFeedCountBeforeOffset(a),l=n+a+c):l=n+a;let u;if(i){const w=i.findLineFeedCountBeforeOffset(a+o.length)-c;u=l+o.length+w}else u=l+o.length;const h=t.getPositionAt(l),f=t.getPositionAt(u);return new k(h.lineNumber,h.column,f.lineNumber,f.column)}static _doFindMatchesMultiline(t,n,s,i,a){const o=t.getOffsetAt(n.getStartPosition()),l=t.getValueInRange(n,1),c=t.getEOL()===`\r
`?new R1(l):null,u=[];let h=0,f;for(s.reset(0);f=s.next(l);)if(u[h++]=ge(this._getMultilineMatchRange(t,o,l,c,f.index,f[0]),f,i),h>=a)return u;return u}static _doFindMatchesLineByLine(t,n,s,i,a){const o=[];let l=0;if(n.startLineNumber===n.endLineNumber){const u=t.getLineContent(n.startLineNumber).substring(n.startColumn-1,n.endColumn-1);return l=this._findMatchesInLine(s,u,n.startLineNumber,n.startColumn-1,l,o,i,a),o}const c=t.getLineContent(n.startLineNumber).substring(n.startColumn-1);l=this._findMatchesInLine(s,c,n.startLineNumber,n.startColumn-1,l,o,i,a);for(let u=n.startLineNumber+1;u<n.endLineNumber&&l<a;u++)l=this._findMatchesInLine(s,t.getLineContent(u),u,0,l,o,i,a);if(l<a){const u=t.getLineContent(n.endLineNumber).substring(0,n.endColumn-1);l=this._findMatchesInLine(s,u,n.endLineNumber,0,l,o,i,a)}return o}static _findMatchesInLine(t,n,s,i,a,o,l,c){const u=t.wordSeparators;if(!l&&t.simpleSearch){const d=t.simpleSearch,w=d.length,S=n.length;let v=-w;for(;(v=n.indexOf(d,v+w))!==-1;)if((!u||ln(u,n,S,v,w))&&(o[a++]=new ct(new k(s,v+1+i,s,v+1+w+i),null),a>=c))return a;return a}const h=new we(t.wordSeparators,t.regex);let f;h.reset(0);do if(f=h.next(n),f&&(o[a++]=ge(new k(s,f.index+1+i,s,f.index+1+f[0].length+i),f,l),a>=c))return a;while(f);return a}static findNextMatch(t,n,s,i){const a=n.parseSearchRequest();if(!a)return null;const o=new we(a.wordSeparators,a.regex);return a.regex.multiline?this._doFindNextMatchMultiline(t,s,o,i):this._doFindNextMatchLineByLine(t,s,o,i)}static _doFindNextMatchMultiline(t,n,s,i){const a=new U(n.lineNumber,1),o=t.getOffsetAt(a),l=t.getLineCount(),c=t.getValueInRange(new k(a.lineNumber,a.column,l,t.getLineMaxColumn(l)),1),u=t.getEOL()===`\r
`?new R1(c):null;s.reset(n.column-1);let h=s.next(c);return h?ge(this._getMultilineMatchRange(t,o,c,u,h.index,h[0]),h,i):n.lineNumber!==1||n.column!==1?this._doFindNextMatchMultiline(t,new U(1,1),s,i):null}static _doFindNextMatchLineByLine(t,n,s,i){const a=t.getLineCount(),o=n.lineNumber,l=t.getLineContent(o),c=this._findFirstMatchInLine(s,l,o,n.column,i);if(c)return c;for(let u=1;u<=a;u++){const h=(o+u-1)%a,f=t.getLineContent(h+1),d=this._findFirstMatchInLine(s,f,h+1,1,i);if(d)return d}return null}static _findFirstMatchInLine(t,n,s,i,a){t.reset(i-1);const o=t.next(n);return o?ge(new k(s,o.index+1,s,o.index+1+o[0].length),o,a):null}static findPreviousMatch(t,n,s,i){const a=n.parseSearchRequest();if(!a)return null;const o=new we(a.wordSeparators,a.regex);return a.regex.multiline?this._doFindPreviousMatchMultiline(t,s,o,i):this._doFindPreviousMatchLineByLine(t,s,o,i)}static _doFindPreviousMatchMultiline(t,n,s,i){const a=this._doFindMatchesMultiline(t,new k(1,1,n.lineNumber,n.column),s,i,10*ls);if(a.length>0)return a[a.length-1];const o=t.getLineCount();return n.lineNumber!==o||n.column!==t.getLineMaxColumn(o)?this._doFindPreviousMatchMultiline(t,new U(o,t.getLineMaxColumn(o)),s,i):null}static _doFindPreviousMatchLineByLine(t,n,s,i){const a=t.getLineCount(),o=n.lineNumber,l=t.getLineContent(o).substring(0,n.column-1),c=this._findLastMatchInLine(s,l,o,i);if(c)return c;for(let u=1;u<=a;u++){const h=(a+o-u-1)%a,f=t.getLineContent(h+1),d=this._findLastMatchInLine(s,f,h+1,i);if(d)return d}return null}static _findLastMatchInLine(t,n,s,i){let a=null,o;for(t.reset(0);o=t.next(n);)a=ge(new k(s,o.index+1,s,o.index+1+o[0].length),o,i);return a}}function cs(e,t,n,s,i){if(s===0)return!0;const a=t.charCodeAt(s-1);if(e.get(a)!==0||a===13||a===10)return!0;if(i>0){const o=t.charCodeAt(s);if(e.get(o)!==0)return!0}return!1}function fs(e,t,n,s,i){if(s+i===n)return!0;const a=t.charCodeAt(s+i);if(e.get(a)!==0||a===13||a===10)return!0;if(i>0){const o=t.charCodeAt(s+i-1);if(e.get(o)!==0)return!0}return!1}function ln(e,t,n,s,i){return cs(e,t,n,s,i)&&fs(e,t,n,s,i)}class we{constructor(t,n){this._wordSeparators=t,this._searchRegex=n,this._prevMatchStartIndex=-1,this._prevMatchLength=0}reset(t){this._searchRegex.lastIndex=t,this._prevMatchStartIndex=-1,this._prevMatchLength=0}next(t){const n=t.length;let s;do{if(this._prevMatchStartIndex+this._prevMatchLength===n||(s=this._searchRegex.exec(t),!s))return null;const i=s.index,a=s[0].length;if(i===this._prevMatchStartIndex&&a===this._prevMatchLength){if(a===0){tn(t,n,this._searchRegex.lastIndex)>65535?this._searchRegex.lastIndex+=2:this._searchRegex.lastIndex+=1;continue}return null}if(this._prevMatchStartIndex=i,this._prevMatchLength=a,!this._wordSeparators||ln(this._wordSeparators,t,n,i,a))return s}while(s);return null}}class hs{static computeUnicodeHighlights(t,n,s){const i=s?s.startLineNumber:1,a=s?s.endLineNumber:t.getLineCount(),o=new P1(n),l=o.getCandidateCodePoints();let c;l==="allNonBasicAscii"?c=new RegExp("[^\\t\\n\\r\\x20-\\x7E]","g"):c=new RegExp(`${ds(Array.from(l))}`,"g");const u=new we(null,c),h=[];let f=!1,d,w=0,S=0,v=0;e:for(let M=i,E=a;M<=E;M++){const C=t.getLineContent(M),p=C.length;u.reset(0);do if(d=u.next(C),d){let g=d.index,m=d.index+d[0].length;if(g>0){const N=C.charCodeAt(g-1);pe(N)&&g--}if(m+1<p){const N=C.charCodeAt(m-1);pe(N)&&m++}const L=C.substring(g,m),_=bt(g+1,I1,C,0),b=o.shouldHighlightNonBasicASCII(L,_?_.word:null);if(b!==0){b===3?w++:b===2?S++:b===1?v++:Pn();const N=1e3;if(h.length>=N){f=!0;break e}h.push(new k(M,g+1,M,m+1))}}while(d)}return{ranges:h,hasMore:f,ambiguousCharacterCount:w,invisibleCharacterCount:S,nonBasicAsciiCharacterCount:v}}static computeUnicodeHighlightReason(t,n){const s=new P1(n);switch(s.shouldHighlightNonBasicASCII(t,null)){case 0:return null;case 2:return{kind:1};case 3:{const a=t.codePointAt(0),o=s.ambiguousCharacters.getPrimaryConfusable(a),l=z.getLocales().filter(c=>!z.getInstance(new Set([...n.allowedLocales,c])).isAmbiguous(a));return{kind:0,confusableWith:String.fromCodePoint(o),notAmbiguousInLocales:l}}case 1:return{kind:2}}}}function ds(e,t){return`[${K1(e.map(s=>String.fromCodePoint(s)).join(""))}]`}class P1{constructor(t){this.options=t,this.allowedCodePoints=new Set(t.allowedCodePoints),this.ambiguousCharacters=z.getInstance(new Set(t.allowedLocales))}getCandidateCodePoints(){if(this.options.nonBasicASCII)return"allNonBasicAscii";const t=new Set;if(this.options.invisibleCharacters)for(const n of re.codePoints)V1(String.fromCodePoint(n))||t.add(n);if(this.options.ambiguousCharacters)for(const n of this.ambiguousCharacters.getConfusableCodePoints())t.add(n);for(const n of this.allowedCodePoints)t.delete(n);return t}shouldHighlightNonBasicASCII(t,n){const s=t.codePointAt(0);if(this.allowedCodePoints.has(s))return 0;if(this.options.nonBasicASCII)return 1;let i=!1,a=!1;if(n)for(let o of n){const l=o.codePointAt(0),c=Fr(o);i=i||c,!c&&!this.ambiguousCharacters.isAmbiguous(l)&&!re.isInvisibleCharacter(l)&&(a=!0)}return!i&&a?0:this.options.invisibleCharacters&&!V1(t)&&re.isInvisibleCharacter(s)?2:this.options.ambiguousCharacters&&this.ambiguousCharacters.isAmbiguous(s)?3:0}}function V1(e){return e===" "||e===`
`||e==="	"}var se=globalThis&&globalThis.__awaiter||function(e,t,n,s){function i(a){return a instanceof n?a:new n(function(o){o(a)})}return new(n||(n=Promise))(function(a,o){function l(h){try{u(s.next(h))}catch(f){o(f)}}function c(h){try{u(s.throw(h))}catch(f){o(f)}}function u(h){h.done?a(h.value):i(h.value).then(l,c)}u((s=s.apply(e,t||[])).next())})};class ms extends Kr{get uri(){return this._uri}get eol(){return this._eol}getValue(){return this.getText()}getLinesContent(){return this._lines.slice(0)}getLineCount(){return this._lines.length}getLineContent(t){return this._lines[t-1]}getWordAtPosition(t,n){const s=bt(t.column,pn(n),this._lines[t.lineNumber-1],0);return s?new k(t.lineNumber,s.startColumn,t.lineNumber,s.endColumn):null}words(t){const n=this._lines,s=this._wordenize.bind(this);let i=0,a="",o=0,l=[];return{*[Symbol.iterator](){for(;;)if(o<l.length){const c=a.substring(l[o].start,l[o].end);o+=1,yield c}else if(i<n.length)a=n[i],l=s(a,t),o=0,i+=1;else break}}}getLineWords(t,n){const s=this._lines[t-1],i=this._wordenize(s,n),a=[];for(const o of i)a.push({word:s.substring(o.start,o.end),startColumn:o.start+1,endColumn:o.end+1});return a}_wordenize(t,n){const s=[];let i;for(n.lastIndex=0;(i=n.exec(t))&&i[0].length!==0;)s.push({start:i.index,end:i.index+i[0].length});return s}getValueInRange(t){if(t=this._validateRange(t),t.startLineNumber===t.endLineNumber)return this._lines[t.startLineNumber-1].substring(t.startColumn-1,t.endColumn-1);const n=this._eol,s=t.startLineNumber-1,i=t.endLineNumber-1,a=[];a.push(this._lines[s].substring(t.startColumn-1));for(let o=s+1;o<i;o++)a.push(this._lines[o]);return a.push(this._lines[i].substring(0,t.endColumn-1)),a.join(n)}offsetAt(t){return t=this._validatePosition(t),this._ensureLineStarts(),this._lineStarts.getPrefixSum(t.lineNumber-2)+(t.column-1)}positionAt(t){t=Math.floor(t),t=Math.max(0,t),this._ensureLineStarts();const n=this._lineStarts.getIndexOf(t),s=this._lines[n.index].length;return{lineNumber:1+n.index,column:1+Math.min(n.remainder,s)}}_validateRange(t){const n=this._validatePosition({lineNumber:t.startLineNumber,column:t.startColumn}),s=this._validatePosition({lineNumber:t.endLineNumber,column:t.endColumn});return n.lineNumber!==t.startLineNumber||n.column!==t.startColumn||s.lineNumber!==t.endLineNumber||s.column!==t.endColumn?{startLineNumber:n.lineNumber,startColumn:n.column,endLineNumber:s.lineNumber,endColumn:s.column}:t}_validatePosition(t){if(!U.isIPosition(t))throw new Error("bad position");let{lineNumber:n,column:s}=t,i=!1;if(n<1)n=1,s=1,i=!0;else if(n>this._lines.length)n=this._lines.length,s=this._lines[n-1].length+1,i=!0;else{const a=this._lines[n-1].length+1;s<1?(s=1,i=!0):s>a&&(s=a,i=!0)}return i?{lineNumber:n,column:s}:t}}class Se{constructor(t,n){this._host=t,this._models=Object.create(null),this._foreignModuleFactory=n,this._foreignModule=null}dispose(){this._models=Object.create(null)}_getModel(t){return this._models[t]}_getModels(){const t=[];return Object.keys(this._models).forEach(n=>t.push(this._models[n])),t}acceptNewModel(t){this._models[t.url]=new ms(ne.parse(t.url),t.lines,t.EOL,t.versionId)}acceptModelChanged(t,n){if(!this._models[t])return;this._models[t].onEvents(n)}acceptRemovedModel(t){this._models[t]&&delete this._models[t]}computeUnicodeHighlights(t,n,s){return se(this,void 0,void 0,function*(){const i=this._getModel(t);return i?hs.computeUnicodeHighlights(i,n,s):{ranges:[],hasMore:!1,ambiguousCharacterCount:0,invisibleCharacterCount:0,nonBasicAsciiCharacterCount:0}})}computeDiff(t,n,s,i){return se(this,void 0,void 0,function*(){const a=this._getModel(t),o=this._getModel(n);if(!a||!o)return null;const l=a.getLinesContent(),c=o.getLinesContent(),h=new Jr(l,c,{shouldComputeCharChanges:!0,shouldPostProcessCharChanges:!0,shouldIgnoreTrimWhitespace:s,shouldMakePrettyDiff:!0,maxComputationTime:i}).computeDiff(),f=h.changes.length>0?!1:this._modelsAreIdentical(a,o);return{quitEarly:h.quitEarly,identical:f,changes:h.changes}})}_modelsAreIdentical(t,n){const s=t.getLineCount(),i=n.getLineCount();if(s!==i)return!1;for(let a=1;a<=s;a++){const o=t.getLineContent(a),l=n.getLineContent(a);if(o!==l)return!1}return!0}computeMoreMinimalEdits(t,n){return se(this,void 0,void 0,function*(){const s=this._getModel(t);if(!s)return n;const i=[];let a;n=n.slice(0).sort((o,l)=>{if(o.range&&l.range)return k.compareRangesUsingStarts(o.range,l.range);const c=o.range?0:1,u=l.range?0:1;return c-u});for(let{range:o,text:l,eol:c}of n){if(typeof c=="number"&&(a=c),k.isEmpty(o)&&!l)continue;const u=s.getValueInRange(o);if(l=l.replace(/\r\n|\n|\r/g,s.eol),u===l)continue;if(Math.max(l.length,u.length)>Se._diffLimit){i.push({range:o,text:l});continue}const h=jr(u,l,!1),f=s.offsetAt(k.lift(o).getStartPosition());for(const d of h){const w=s.positionAt(f+d.originalStart),S=s.positionAt(f+d.originalStart+d.originalLength),v={text:l.substr(d.modifiedStart,d.modifiedLength),range:{startLineNumber:w.lineNumber,startColumn:w.column,endLineNumber:S.lineNumber,endColumn:S.column}};s.getValueInRange(v.range)!==v.text&&i.push(v)}}return typeof a=="number"&&i.push({eol:a,text:"",range:{startLineNumber:0,startColumn:0,endLineNumber:0,endColumn:0}}),i})}computeLinks(t){return se(this,void 0,void 0,function*(){const n=this._getModel(t);return n?ss(n):null})}textualSuggest(t,n,s,i){return se(this,void 0,void 0,function*(){const a=new $e(!0),o=new RegExp(s,i),l=new Set;e:for(let c of t){const u=this._getModel(c);if(u){for(let h of u.words(o))if(!(h===n||!isNaN(Number(h)))&&(l.add(h),l.size>Se._suggestionsLimit))break e}}return{words:Array.from(l),duration:a.elapsed()}})}computeWordRanges(t,n,s,i){return se(this,void 0,void 0,function*(){const a=this._getModel(t);if(!a)return Object.create(null);const o=new RegExp(s,i),l=Object.create(null);for(let c=n.startLineNumber;c<n.endLineNumber;c++){const u=a.getLineWords(c,o);for(const h of u){if(!isNaN(Number(h.word)))continue;let f=l[h.word];f||(f=[],l[h.word]=f),f.push({startLineNumber:c,startColumn:h.startColumn,endLineNumber:c,endColumn:h.endColumn})}}return l})}navigateValueSet(t,n,s,i,a){return se(this,void 0,void 0,function*(){const o=this._getModel(t);if(!o)return null;const l=new RegExp(i,a);n.startColumn===n.endColumn&&(n={startLineNumber:n.startLineNumber,startColumn:n.startColumn,endLineNumber:n.endLineNumber,endColumn:n.endColumn+1});const c=o.getValueInRange(n),u=o.getWordAtPosition({lineNumber:n.startLineNumber,column:n.startColumn},l);if(!u)return null;const h=o.getValueInRange(u);return dt.INSTANCE.navigateValueSet(n,c,u,h,s)})}loadForeignModule(t,n,s){const o={host:Rn(s,(l,c)=>this._host.fhr(l,c)),getMirrorModels:()=>this._getModels()};return this._foreignModuleFactory?(this._foreignModule=this._foreignModuleFactory(o,n),Promise.resolve(T1(this._foreignModule))):Promise.reject(new Error("Unexpected usage"))}fmr(t,n){if(!this._foreignModule||typeof this._foreignModule[t]!="function")return Promise.reject(new Error("Missing requestHandler or method: "+t));try{return Promise.resolve(this._foreignModule[t].apply(this._foreignModule,n))}catch(s){return Promise.reject(s)}}}Se._diffLimit=1e5;Se._suggestionsLimit=1e4;typeof importScripts=="function"&&(V.monaco=mr());export{ws as $,Ti as A,z1 as B,jn as C,ve as D,$ as E,he as F,T1 as G,Mi as H,Lr as I,Nr as J,Yi as K,Fr as L,Qi as M,Ei as N,Xi as O,pr as P,bs as Q,k as R,$e as S,xi as T,Cn as U,li as V,Ft as W,Qn as X,_e as Y,ne as Z,bn as _,gn as a,ln as a$,Cs as a0,ti as a1,Xe as a2,Je as a3,As as a4,Ss as a5,wn as a6,vs as a7,Di as a8,xn as a9,Qs as aA,Xs as aB,fi as aC,D1 as aD,Wn as aE,S1 as aF,ji as aG,js as aH,Os as aI,Gs as aJ,Oe as aK,Pi as aL,ea as aM,ci as aN,di as aO,q as aP,Oi as aQ,vi as aR,Ci as aS,rt as aT,qi as aU,Li as aV,pi as aW,_i as aX,zi as aY,ge as aZ,we as a_,be as aa,Rt as ab,Ls as ac,ni as ad,Ks as ae,U as af,Vn as ag,ys as ah,Pn as ai,Er as aj,nn as ak,tn as al,H as am,$i as an,Gi as ao,os as ap,yr as aq,Ar as ar,ta as as,p1 as at,en as au,Fn as av,Js as aw,Zs as ax,r as ay,Bs as az,xs as b,Xt as b$,ct as b0,Zi as b1,ia as b2,Ji as b3,vn as b4,ra as b5,Hn as b6,kr as b7,da as b8,ha as b9,ai as bA,vt as bB,Ri as bC,Ts as bD,yi as bE,ii as bF,Fs as bG,bi as bH,Ws as bI,qs as bJ,K as bK,zs as bL,Ms as bM,wi as bN,Ys as bO,Gn as bP,ui as bQ,gi as bR,Rs as bS,aa as bT,mi as bU,qt as bV,Gt as bW,jt as bX,Qt as bY,Yt as bZ,Jt as b_,sa as ba,bt as bb,N1 as bc,ca as bd,v1 as be,ye as bf,Le as bg,kn as bh,hi as bi,Sr as bj,_t as bk,Hi as bl,Nt as bm,ri as bn,Us as bo,$1 as bp,Ni as bq,Si as br,Ui as bs,Ds as bt,Is as bu,Bi as bv,wr as bw,Cr as bx,H1 as by,qe as bz,pe as c,Kt as c0,s1 as c1,i1 as c2,a1 as c3,o1 as c4,u1 as c5,c1 as c6,h1 as c7,f1 as c8,b1 as c9,ks as cA,Mn as cB,fa as cC,Vt as cD,Es as cE,Pt as cF,Mt as cG,yn as cH,$s as cI,Tt as cJ,Wt as cK,Bt as cL,ei as cM,_s as cN,hs as cO,re as cP,It as cQ,Ii as cR,Vi as cS,Ut as cT,w1 as ca,C1 as cb,_1 as cc,t1 as cd,l1 as ce,Zt as cf,zt as cg,Ot as ch,Ht as ci,m1 as cj,g1 as ck,e1 as cl,$t as cm,d1 as cn,n1 as co,r1 as cp,fe as cq,Hs as cr,mr as cs,Jn as ct,J1 as cu,ua as cv,na as cw,ki as cx,si as cy,Ki as cz,Ns as d,Re as e,_r as f,K1 as g,Vs as h,gs as i,Wi as j,I1 as k,pn as l,Bn as m,dr as n,Un as o,Fi as p,Tn as q,We as r,ps as s,Te as t,oa as u,V as v,Ps as w,la as x,Se as y,oi as z};
