var j=(_,w,l)=>new Promise((f,o)=>{var g=r=>{try{m(l.next(r))}catch(d){o(d)}},p=r=>{try{m(l.throw(r))}catch(d){o(d)}},m=r=>r.done?f(r.value):Promise.resolve(r.value).then(g,p);m((l=l.apply(_,w)).next())});import{d as L,Z as B,l as R,aJ as z,L as Q,r as n,o as O,E as J,w as t,e,q as H,f as c,t as v,u as a,j as A,b8 as P,m as Z,a0 as K,cr as W,cs as X,c as Y,cR as $,a7 as F,bh as ee}from"./index-bb2cbf17.js";import{i as te}from"./icon-f36697ff.js";import{S as T}from"./SettingItem-7fe1cfec.js";import{S as I}from"./SettingItemBox-500aaf18.js";import"./chartEditStore-55fbe93c.js";/* empty css                                                                      */import{R as ae,a as oe}from"./pondIndex.vue_vue_type_style_index_0_scoped_de860d6d_lang-a2489be1.js";import{u as ne}from"./useTargetData.hook-a16b3b4d.js";import"./index-819682d7.js";import"./EditorWorker-54a88558.js";import"./editorWorker-43a98755.js";import{c as se}from"./http-36f53bd1.js";import{C as le}from"./index-922b8da1.js";import"./plugin-3ef0fcec.js";import"./index-56351f34.js";import"./fileTypeEnum-21359a08.js";const re={class:"go-pr-3"},ue=L({__name:"index",props:{modelShow:Boolean,targetData:Object,saveBtnText:String||null},emits:["update:modelShow","sendHandle"],setup(_,{emit:w}){const l=_,f=w,{chartConfig:o}=B(l.targetData),{requestContentType:g}=B(l.targetData.request),p=R(!1),m={[z.DEFAULT]:"普通请求",[z.SQL]:"SQL 请求"};Q(()=>l.modelShow,d=>{p.value=d},{immediate:!0});const r=()=>{f("update:modelShow",!1),f("sendHandle")};return(d,i)=>{const h=n("n-space"),b=n("n-scrollbar"),q=n("n-text"),x=n("n-tag"),U=n("n-button"),y=n("n-card"),k=n("n-modal");return O(),J(k,{class:"go-chart-data-request",show:p.value,"onUpdate:show":i[0]||(i[0]=s=>p.value=s),"mask-closable":!1,closeOnEsc:!1},{default:t(()=>[e(y,{bordered:!1,role:"dialog",size:"small","aria-modal":"true",style:{width:"1000px",height:"800px"}},{header:t(()=>[]),"header-extra":t(()=>[]),action:t(()=>[e(h,{justify:"space-between"},{default:t(()=>[H("div",null,[e(q,null,{default:t(()=>[c("「 "+v(a(o).categoryName)+" 」",1)]),_:1}),e(q,null,{default:t(()=>[c("—— ")]),_:1}),e(x,{type:"primary",bordered:!1,style:{"border-radius":"5px"}},{default:t(()=>[c(v(m[a(g)]),1)]),_:1})]),e(U,{type:"primary",onClick:r},{default:t(()=>[c(v(_.saveBtnText||"保存 & 发送请求"),1)]),_:1})]),_:1})]),default:t(()=>[e(b,{style:{"max-height":"718px"}},{default:t(()=>[H("div",re,[e(h,{vertical:""},{default:t(()=>{var s;return[e(a(ae)),e(a(oe),{"target-data-request":(s=_.targetData)==null?void 0:s.request},null,8,["target-data-request"])]}),_:1})])]),_:1})]),_:1})]),_:1},8,["show"])}}}),ce=A(ue,[["__scopeId","data-v-6ace1718"]]),de={class:"go-chart-configurations-data-ajax"},ie={class:"go-absolute-center"},pe=L({__name:"index",setup(_){P(s=>({"272613bc":k.value}));const{HelpOutlineIcon:w,FlashIcon:l,PulseIcon:f}=te.ionicons5,{targetData:o,chartEditStore:g}=ne(),{requestOriginUrl:p,requestInterval:m,requestIntervalUnit:r}=B(g.getRequestGlobalConfig),d=Z(),i=R(!1),h=R(!1),b=R(!1);let q=0,x;const U=()=>{h.value=!0},y=()=>j(this,null,function*(){var s;if((s=o.value)!=null&&s.request){i.value=!0;try{const u=yield se(F(o.value.request),F(g.getRequestGlobalConfig));if(i.value=!1,u){const{data:C}=u;if(!C&&!o.value.filter){window.$message.warning("您的数据不符合默认格式，请配置过滤器！"),b.value=!0;return}o.value.option.dataset=ee(C,u,o.value.filter),b.value=!0;return}window.$message.warning("没有拿到返回值，请检查接口！")}catch(u){console.error(u),i.value=!1,window.$message.warning("数据异常，请检查参数！")}}}),k=K(()=>d.getAppTheme);return W(()=>{var u;const s=(u=o.value)==null?void 0:u.filter;x!==s&&q&&(x=s,y()),q++}),X(()=>{x=null}),(s,u)=>{const C=n("n-tag"),S=n("n-input"),D=n("n-icon"),E=n("n-button"),G=n("n-card"),N=n("n-tooltip"),V=n("go-skeleton");return O(),Y("div",de,[e(G,{class:"n-card-shallow"},{default:t(()=>[e(a(I),{name:"请求配置"},{default:t(()=>[e(a(T),{name:"类型"},{default:t(()=>[e(C,{bordered:!1,type:"primary",style:{"border-radius":"5px"}},{default:t(()=>[c(v(a(o).request.requestContentType===a(z).DEFAULT?"普通请求":"SQL请求"),1)]),_:1})]),_:1}),e(a(T),{name:"方式"},{default:t(()=>[e(S,{size:"small",placeholder:a(o).request.requestHttpType||"暂无",disabled:!0},null,8,["placeholder"])]),_:1}),e(a(T),{name:"组件间隔"},{default:t(()=>[e(S,{size:"small",placeholder:`${a(o).request.requestInterval||"暂无"}`,disabled:!0},{suffix:t(()=>[c(v(a($)[a(o).request.requestIntervalUnit]),1)]),_:1},8,["placeholder"])]),_:1}),e(a(T),{name:"全局间隔（默认）"},{default:t(()=>[e(S,{size:"small",placeholder:`${a(m)||"暂无"} `,disabled:!0},{suffix:t(()=>[c(v(a($)[a(r)]),1)]),_:1},8,["placeholder"])]),_:1})]),_:1}),e(a(I),{name:"源地址",alone:!0},{default:t(()=>[e(S,{size:"small",placeholder:a(p)||"暂无",disabled:!0},{prefix:t(()=>[e(D,{component:a(f)},null,8,["component"])]),_:1},8,["placeholder"])]),_:1}),e(a(I),{name:"组件地址",alone:!0},{default:t(()=>[e(S,{size:"small",placeholder:a(o).request.requestUrl||"暂无",disabled:!0},{prefix:t(()=>[e(D,{component:a(l)},null,8,["component"])]),_:1},8,["placeholder"])]),_:1}),H("div",{class:"edit-text",onClick:U},[H("div",ie,[e(E,{type:"primary",secondary:""},{default:t(()=>[c("编辑配置")]),_:1})])])]),_:1}),e(a(I),{alone:!0},{name:t(()=>[c(" 测试 "),e(N,{trigger:"hover"},{trigger:t(()=>[e(D,{size:"21",depth:3},{default:t(()=>[e(a(w))]),_:1})]),default:t(()=>[c(" 默认赋值给 dataset 字段 ")]),_:1})]),default:t(()=>[e(E,{type:"primary",ghost:"",onClick:y},{icon:t(()=>[e(D,null,{default:t(()=>[e(a(l))]),_:1})]),default:t(()=>[c(" 发送请求 ")]),_:1})]),_:1}),e(a(le),{show:b.value&&!i.value,ajax:!0},null,8,["show"]),e(V,{load:i.value,repeat:3},null,8,["load"]),e(a(ce),{modelShow:h.value,"onUpdate:modelShow":u[0]||(u[0]=M=>h.value=M),targetData:a(o),onSendHandle:y},null,8,["modelShow","targetData"])])}}});const He=A(pe,[["__scopeId","data-v-688badbe"]]);export{He as default};
