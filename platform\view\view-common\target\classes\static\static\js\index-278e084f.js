import{C as t}from"./index-922b8da1.js";import{d as o,o as e,c as a,e as r,u as s,j as i}from"./index-bb2cbf17.js";import"./chartEditStore-55fbe93c.js";import"./plugin-3ef0fcec.js";import"./icon-f36697ff.js";import"./index-819682d7.js";import"./index-56351f34.js";/* empty css                                                                      */import"./SettingItemBox-500aaf18.js";import"./useTargetData.hook-a16b3b4d.js";import"./EditorWorker-54a88558.js";import"./editorWorker-43a98755.js";import"./http-36f53bd1.js";import"./fileTypeEnum-21359a08.js";const p={class:"go-chart-configurations-data-static"},n=o({__name:"index",setup(_){return(c,m)=>(e(),a("div",p,[r(s(t),{show:!1,ajax:!1})]))}});const B=i(n,[["__scopeId","data-v-27759b05"]]);export{B as default};
