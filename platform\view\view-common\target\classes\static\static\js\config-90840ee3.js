var m=Object.defineProperty;var e=(r,o,f)=>o in r?m(r,o,{enumerable:!0,configurable:!0,writable:!0,value:f}):r[o]=f;var i=(r,o,f)=>(e(r,typeof o!="symbol"?o+"":o,f),f);import{a8 as t}from"./index-bb2cbf17.js";import{d as a}from"./chartEditStore-55fbe93c.js";import{C as p}from"./index-0ec04aee.js";import"./plugin-3ef0fcec.js";import"./icon-f36697ff.js";import"./SettingItem-7fe1cfec.js";/* empty css                                                                      */import"./SettingItemBox-500aaf18.js";import"./CollapseItem.vue_vue_type_script_setup_true_lang-9b6d8958.js";import"./index.esm.min-f1367f57.js";import"./http-36f53bd1.js";import"./lodash-d17632fd.js";import"./fileTypeEnum-21359a08.js";const s={border:6,color:"#ffffff",bgColor:"#84a5e9",borderColor:"#ffffff"};class j extends a{constructor(){super(...arguments);i(this,"key",p.key);i(this,"chartConfig",t(p));i(this,"option",t(s))}}export{j as default,s as option};
