import{d as I,l as D,H as V,I as L,J as z,P as g,L as F,r as n,o as a,E as _,w as e,e as t,f as x,t as E,G as S,u as o,c as H,s as G,F as R,M as q,N as J,O as Q,j as C,m as X,q as w,Q as j,R as T,U as Y,V as Z,p as ee,W as A,X as B,Y as O,Z as te,$ as oe,C as ne,a0 as ae,n as se,a1 as le,a2 as ce,a3 as _e,K as de}from"./index-bb2cbf17.js";import{i as M}from"./icon-f36697ff.js";import{_ as ie}from"./index.vue_vue_type_script_setup_true_lang-825e1dc4.js";import"./index-56351f34.js";const re=I({__name:"index",props:{show:Boolean},emits:["close"],setup(c,{emit:s}){const{FishIcon:r,CloseIcon:l}=M.ionicons5,{StoreIcon:d,ObjectStorageIcon:p}=M.carbon,m=D(!1),f=s,v=c,i=V([{title:L("project.new_project"),key:z.CHART_HOME_NAME,icon:r,disabled:!1},{title:L("project.my_templete"),key:g.BASE_HOME_TEMPLATE_NAME,icon:p,disabled:!0},{title:L("project.template_market"),key:g.BASE_HOME_TEMPLATE_MARKET_NAME,icon:d,disabled:!0}]);F(v,h=>{m.value=h.show});const u=()=>{f("close",!1)},y=h=>{u();const b=q(),$=J(z.CHART_HOME_NAME,"href");Q($,[b],void 0,!0)};return(h,b)=>{const $=n("n-text"),N=n("n-icon"),K=n("n-button"),P=n("n-space"),W=n("n-card"),U=n("n-modal");return a(),_(U,{show:m.value,"onUpdate:show":b[0]||(b[0]=k=>m.value=k),class:"go-create-modal",onAfterLeave:u},{default:e(()=>[t(P,{size:"large"},{default:e(()=>[t(W,{class:"card-box",hoverable:""},{header:e(()=>[t($,{class:"card-box-tite"},{default:e(()=>[x(E(h.$t("project.create_tip")),1)]),_:1})]),"header-extra":e(()=>[t($,{onClick:u},{default:e(()=>[t(N,{size:"20"},{default:e(()=>[(a(),_(S(o(l))))]),_:1})]),_:1})]),action:e(()=>[]),default:e(()=>[t(P,{class:"card-box-content",justify:"center"},{default:e(()=>[(a(!0),H(R,null,G(i.value,k=>(a(),_(K,{size:"large",disabled:k.disabled,key:k.key,onClick:y},{icon:e(()=>[t(N,{size:"18"},{default:e(()=>[(a(),_(S(k.icon)))]),_:2},1024)]),default:e(()=>[(a(),_(S(k.title)))]),_:2},1032,["disabled"]))),128))]),_:1})]),_:1})]),_:1})]),_:1},8,["show"])}}});const ue=C(re,[["__scopeId","data-v-d724be6b"]]),pe=I({__name:"index",props:{collapsed:Boolean},setup(c){const{DuplicateIcon:s,DuplicateOutlineIcon:r}=M.ionicons5,l=X(),d=D(!1),p=()=>{d.value=!0},m=()=>{d.value=!1};return(f,v)=>{const i=n("n-icon"),u=n("n-button"),y=n("n-tooltip");return a(),H(R,null,[w("div",{onClick:p},[c.collapsed?(a(),_(y,{key:0,placement:"right",trigger:"hover"},{trigger:e(()=>[t(u,{ghost:"",type:"primary",size:"small"},{icon:e(()=>[t(i,null,{default:e(()=>[j(t(o(r),null,null,512),[[T,o(l).getDarkTheme]]),j(t(o(s),null,null,512),[[T,!o(l).getDarkTheme]])]),_:1})]),_:1})]),default:e(()=>[w("span",null,E(f.$t("project.create_btn")),1)]),_:1})):(a(),_(u,{key:1,ghost:"",type:"primary"},{icon:e(()=>[t(i,null,{default:e(()=>[j(t(o(r),null,null,512),[[T,o(l).getDarkTheme]]),j(t(o(s),null,null,512),[[T,!o(l).getDarkTheme]])]),_:1})]),default:e(()=>[w("span",null,E(f.$t("project.create_btn")),1)]),_:1}))]),t(o(ue),{show:d.value,onClose:m},null,8,["show"])],64)}}}),me={class:"go-aside-footer"},fe=I({__name:"index",props:{collapsed:Boolean},setup(c){const{DocumentTextIcon:s,CodeSlashIcon:r}=M.ionicons5,l=()=>{Y()},d=()=>{Z()};return(p,m)=>{const f=n("n-divider"),v=n("n-icon"),i=n("n-button"),u=n("n-text"),y=n("n-tooltip"),h=n("n-space");return a(),H("div",me,[t(f,{class:"go-mt-0"}),t(h,{justify:"space-around"},{default:e(()=>[c.collapsed?(a(),_(y,{key:0,placement:"right",trigger:"hover"},{trigger:e(()=>[t(i,{secondary:"",onClick:l},{icon:e(()=>[t(v,{size:"18"},{default:e(()=>[t(o(s))]),_:1})]),_:1})]),default:e(()=>[t(u,null,{default:e(()=>[x(E(p.$t("global.doc")),1)]),_:1})]),_:1})):(a(),_(i,{key:1,secondary:"",onClick:l},{icon:e(()=>[t(v,{size:"18"},{default:e(()=>[t(o(s))]),_:1})]),default:e(()=>[t(u,null,{default:e(()=>[x(E(p.$t("global.doc")),1)]),_:1})]),_:1})),c.collapsed?(a(),_(y,{key:2,placement:"right",trigger:"hover"},{trigger:e(()=>[t(i,{secondary:"",onClick:l},{icon:e(()=>[t(v,{size:"18"},{default:e(()=>[t(o(r))]),_:1})]),_:1})]),default:e(()=>[t(u,null,{default:e(()=>[x(E(p.$t("global.code_addr")),1)]),_:1})]),_:1})):(a(),_(i,{key:3,secondary:"",onClick:d},{icon:e(()=>[t(v,{size:"18"},{default:e(()=>[t(o(r))]),_:1})]),default:e(()=>[j(t(u,null,{default:e(()=>[x(E(p.$t("global.code_addr")),1)]),_:1},512),[[T,!c.collapsed]])]),_:1}))]),_:1})])}}});const ve=C(fe,[["__scopeId","data-v-b5a02cb8"]]),{GridIcon:Oe,TvOutlineIcon:ye}=M.ionicons5,{StoreIcon:he,ObjectStorageIcon:ge,DevicesIcon:Ee}=M.carbon,be=()=>["all-project"],ke=()=>{const c=window.$t;return ee([{key:"divider-1",type:"divider"},{label:()=>A("span",null,{default:()=>c("project.project")}),key:"all-project",icon:B(Ee),children:[{type:"group",label:()=>A("span",null,{default:()=>c("project.my")}),key:"my-project",children:[{label:()=>A(O,{to:{name:g.BASE_HOME_ITEMS_NAME}},{default:()=>c("project.all_project")}),key:g.BASE_HOME_ITEMS_NAME,icon:B(ye)},{label:()=>A(O,{to:{name:g.BASE_HOME_TEMPLATE_NAME}},{default:()=>c("project.my_templete")}),key:g.BASE_HOME_TEMPLATE_NAME,icon:B(ge)}]}]},{key:"divider-2",type:"divider"},{label:()=>A(O,{to:{name:g.BASE_HOME_TEMPLATE_MARKET_NAME}},{default:()=>c("project.template_market")}),key:g.BASE_HOME_TEMPLATE_MARKET_NAME,icon:B(he)}])},we={class:"go-project-sider-flex"},Me={class:"sider-bottom"},Ae=I({__name:"index",setup(c){const s=D(!1),{getAsideCollapsedWidth:r}=te(oe()),l=ne(),d=ae(()=>l.name),p=ke(),m=be(),f=()=>{document.body.clientWidth<=950?s.value=!0:s.value=!1};return se(()=>{window.addEventListener("resize",f)}),le(()=>{window.removeEventListener("resize",f)}),(v,i)=>{const u=n("n-space"),y=n("n-menu"),h=n("n-layout-sider");return a(),_(h,{class:"go-project-sider",bordered:"","collapse-mode":"width","show-trigger":"bar",collapsed:s.value,"native-scrollbar":!1,"collapsed-width":o(r),width:o(ce),onCollapse:i[0]||(i[0]=b=>s.value=!0),onExpand:i[1]||(i[1]=b=>s.value=!1)},{default:e(()=>[w("div",we,[w("aside",null,[t(u,{vertical:"",class:"go-project-sider-top"},{default:e(()=>[t(o(pe),{collapsed:s.value},null,8,["collapsed"])]),_:1}),t(y,{value:d.value,options:o(p),"collapsed-width":o(r),"collapsed-icon-size":22,"default-expanded-keys":o(m)},null,8,["value","options","collapsed-width","default-expanded-keys"])]),w("div",Me,[t(o(ve),{collapsed:s.value},null,8,["collapsed"])])])]),_:1},8,["collapsed","collapsed-width","width"])}}});const xe=C(Ae,[["__scopeId","data-v-58606bc9"]]),je={};function Te(c,s){const r=n("router-view");return a(),_(r,null,{default:e(({Component:l,route:d})=>[t(_e,{name:"fade",mode:"out-in",appear:""},{default:e(()=>[d.meta.noKeepAlive?(a(),_(S(l),{key:d.fullPath})):(a(),_(de,{key:1},[(a(),_(S(l),{key:d.fullPath}))],1024))]),_:2},1024)]),_:1})}const Se=C(je,[["render",Te]]),Ie={class:"go-project"},Ce=I({__name:"index",setup(c){return(s,r)=>{const l=n("n-space"),d=n("router-view"),p=n("n-layout-content"),m=n("n-layout");return a(),H("div",Ie,[t(m,{"has-sider":"",position:"absolute"},{default:e(()=>[t(l,{vertical:""},{default:e(()=>[t(o(xe))]),_:1}),t(m,null,{default:e(()=>[t(o(ie)),t(m,{id:"go-project-content-top",class:"content-top",position:"absolute","native-scrollbar":!1},{default:e(()=>[t(p,null,{default:e(()=>[t(o(Se),null,{default:e(()=>[t(d)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})])}}});const De=C(Ce,[["__scopeId","data-v-300e47bd"]]);export{De as default};
