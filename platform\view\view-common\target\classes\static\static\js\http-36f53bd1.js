var E=Object.defineProperty;var T=Object.getOwnPropertySymbols;var F=Object.prototype.hasOwnProperty,S=Object.prototype.propertyIsEnumerable;var q=(e,t,r)=>t in e?E(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,m=(e,t)=>{for(var r in t||(t={}))F.call(t,r)&&q(e,r,t[r]);if(T)for(var r of T(t))S.call(t,r)&&q(e,r,t[r]);return e};import{aG as N,aK as i,cn as y,aJ as h,co as L}from"./index-bb2cbf17.js";const O="javascript:",a=e=>{if(typeof e=="string")if(e.startsWith(O)){const t=e.split(O)[1];let r;try{r=new Function(`${t}`)()}catch(l){console.log(l),window.$message.error("js内容解析有误！")}return r}else return e;for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)){const r=e[t];e[t]=a(r)}return e},M=(e,t)=>{if(!e||!t)return;const{requestOriginUrl:r,requestParams:l}=t,{requestUrl:f,requestContentType:w,requestDataType:C,requestHttpType:R,requestParamsBodyType:D,requestSQLContent:b,requestParams:c}=e;if(C===N.STATIC||!f)return;let o=m(m({},l.Header),c.Header);o=a(o);let s={},d=m({},c.Params);d=a(d);let p=new FormData;switch(D){case i.NONE:break;case i.JSON:o["Content-Type"]=y.JSON,s=a(c.Body.json),typeof s=="string"&&(s=JSON.parse(s));break;case i.XML:o["Content-Type"]=y.XML,s=a(c.Body.xml);break;case i.X_WWW_FORM_URLENCODED:{o["Content-Type"]=y.FORM_URLENCODED;const n=c.Body["x-www-form-urlencoded"];for(const u in n)p.set(u,a(n[u]));s=p;break}case i.FORM_DATA:{o["Content-Type"]=y.FORM_DATA;const n=c.Body["form-data"];for(const u in n)p.set(u,a(n[u]));s=p;break}}w===h.SQL&&(o["Content-Type"]=y.JSON,s=b);try{const n=new Function("return `"+`${r}${f}`.trim()+"`")();return L({url:n,method:R,data:s,params:d,headers:o})}catch(n){console.log(n),window.$message.error("URL地址格式有误！")}};export{M as c};
