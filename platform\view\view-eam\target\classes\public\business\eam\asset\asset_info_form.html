<!--
/**
 * 资产 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2021-09-07 16:11:55
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
	<title th:text="${lang.translate('资产')}">资产</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/ASSET_DEFAULT_OWN_COMPANYtoast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" th:href="@{/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css}" rel="stylesheet"/>
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon"> <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
    <style>
        .layui-required{
            margin-right: 0px;
        }
    </style>
</head>

<body style="overflow-y: hidden; background-color:white">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="display:none;height:800px">
        <input name="id" id="id"  type="hidden"/>
        <div style="display: inline-block;" class="layui-col-xs12">
            <div class="layui-tab" style="padding-right: 8px;padding-left: 8px;">
                <ul class="layui-tab-title">
                    <li id="attribution-tab" th:text="${lang.translate('基本信息')}" class="layui-this">基本信息</li>
                    <li id="maintainer-tab"  th:text="${lang.translate('维保信息')}">维保信息</li>
                    <li id="financial-tab" th:text="${lang.translate('财务信息')}"> 财务信息</li>
                    <li id="equipment-tab" > <div th:text="${lang.translate('设备信息')}">设备信息</div></li>
                    <li id="partRcd-tab" > <div th:text="${lang.translate('备品配件')}">备品配件</div></li>
                    <li id="repairRcd-tab" > <div th:text="${lang.translate('维修记录')}">维修记录</div></li>
                    <li id="maintainRcd-tab" > <div th:text="${lang.translate('保养记录')}">保养记录</div></li>
                    <li id="insepectionRcd-tab" > <div th:text="${lang.translate('巡检记录')}">巡检记录</div></li>
                    <li id="history-tab" > <div th:text="${lang.translate('历史记录')}">历史记录</div></li>
                </ul>
                <div class="layui-tab-content">
                    <div class="layui-tab-item layui-show" id="attribution-tab-ct">
                        <!--开始：3栏模式 循环-->
                        <div class="layui-row form-row" >
                            <div class="layui-col-xs4 form-column">
                                <div class="layui-form-item" th:each="item:${attributeData3Column1.?[dimension == 'attribution']}">
                                    <div th:replace="business/eam/asset/asset_column :: (${item.attribute.code})"></div>
                                </div>
                            </div>
                            <div class="layui-col-xs4 form-column">
                                <div class="layui-form-item"  th:each="item:${attributeData3Column2.?[dimension == 'attribution']}">
                                    <div th:replace="business/eam/asset/asset_column :: (${item.attribute.code})"></div>
                                </div>
                            </div>
                            <div class="layui-col-xs4 form-column">
                                <div class="layui-form-item"  th:each="item:${attributeData3Column3.?[dimension == 'attribution']}">
                                    <div th:replace="business/eam/asset/asset_column :: (${item.attribute.code})"></div>
                                </div>
                            </div>
                        </div>
                        <!--开始：单栏模式 循环-->
                        <div class="layui-row form-row" >
                            <div class="layui-col-xs12 form-column">
                                <div class="layui-form-item" th:each="item:${attributeData1Column1.?[dimension == 'attribution']}">
                                    <div th:replace="business/eam/asset/asset_column :: (${item.attribute.code})"></div>
                                </div>
                            </div>
                        </div>
                        <div id="pcmExtColumn">
                        </div>
                    </div>
                    <div class="layui-tab-item" id="maintainer-tab-ct">
                        <!--开始：3栏模式 循环-->
                        <div class="layui-row form-row" >
                            <div class="layui-col-xs4 form-column">
                                <div class="layui-form-item" th:each="item:${attributeData3Column1.?[dimension == 'maintainer']}">
                                    <div th:replace="business/eam/asset/asset_column :: (${item.attribute.code})"></div>
                                </div>
                            </div>
                            <div class="layui-col-xs4 form-column">
                                <div class="layui-form-item"  th:each="item:${attributeData3Column2.?[dimension == 'maintainer']}">
                                    <div th:replace="business/eam/asset/asset_column :: (${item.attribute.code})"></div>
                                </div>
                            </div>
                            <div class="layui-col-xs4 form-column">
                                <div class="layui-form-item"  th:each="item:${attributeData3Column3.?[dimension == 'maintainer']}">
                                    <div th:replace="business/eam/asset/asset_column :: (${item.attribute.code})"></div>
                                </div>
                            </div>
                        </div>
                        <!--开始：单栏模式 循环-->
                        <div class="layui-row form-row" >
                            <div class="layui-col-xs12 form-column">
                                <div class="layui-form-item" th:each="item:${attributeData1Column1.?[dimension == 'maintainer']}">
                                    <div th:replace="business/eam/asset/asset_column :: (${item.attribute.code})"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-tab-item" id="financial-tab-ct">
                        <!--开始：3栏模式 循环-->
                        <div class="layui-row form-row" >
                            <div class="layui-col-xs4 form-column">
                                <div class="layui-form-item" th:each="item:${attributeData3Column1.?[dimension == 'financial']}">
                                    <div th:replace="business/eam/asset/asset_column :: (${item.attribute.code})"></div>
                                </div>
                            </div>
                            <div class="layui-col-xs4 form-column">
                                <div class="layui-form-item"  th:each="item:${attributeData3Column2.?[dimension == 'financial']}">
                                    <div th:replace="business/eam/asset/asset_column :: (${item.attribute.code})"></div>
                                </div>
                            </div>
                            <div class="layui-col-xs4 form-column">
                                <div class="layui-form-item"  th:each="item:${attributeData3Column3.?[dimension == 'financial']}">
                                    <div th:replace="business/eam/asset/asset_column :: (${item.attribute.code})"></div>
                                </div>
                            </div>
                        </div>
                        <!--开始：单栏模式 循环-->
                        <div class="layui-row form-row" >
                            <div class="layui-col-xs12 form-column">
                                <div class="layui-form-item" th:each="item:${attributeData1Column1.?[dimension == 'financial']}">
                                    <div th:replace="business/eam/asset/asset_column :: (${item.attribute.code})"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-tab-item" id="equipment-tab-ct">
                        <!--开始：3栏模式 循环-->
                        <div class="layui-row form-row" >
                            <div class="layui-col-xs4 form-column">
                                <div class="layui-form-item" th:each="item:${attributeData3Column1.?[dimension == 'equipment']}">
                                    <div th:replace="business/eam/asset/asset_column :: (${item.attribute.code})"></div>
                                </div>
                            </div>
                            <div class="layui-col-xs4 form-column">
                                <div class="layui-form-item"  th:each="item:${attributeData3Column2.?[dimension == 'equipment']}">
                                    <div th:replace="business/eam/asset/asset_column :: (${item.attribute.code})"></div>
                                </div>
                            </div>
                            <div class="layui-col-xs4 form-column">
                                <div class="layui-form-item"  th:each="item:${attributeData3Column3.?[dimension == 'equipment']}">
                                    <div th:replace="business/eam/asset/asset_column :: (${item.attribute.code})"></div>
                                </div>
                            </div>
                        </div>
                        <!--开始：单栏模式 循环-->
                        <div class="layui-row form-row" >
                            <div class="layui-col-xs12 form-column">
                                <div class="layui-form-item" th:each="item:${attributeData1Column1.?[dimension == 'equipment']}">
                                    <div th:replace="business/eam/asset/asset_column :: (${item.attribute.code})"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div  class="layui-tab-item" id="partRcd-tab-ct">
                        <div style="display: inline-block;padding-right: 8px;padding-left: 8px" class="layui-col-xs12">
                            <iframe id="partRcdList" js-fn="partRcdList" class="form-iframe" frameborder="0" style="width: 100%"></iframe>
                        </div>
                    </div>

                    <div  class="layui-tab-item" id="repairRcd-tab-ct">
                        <div style="display: inline-block;padding-right: 8px;padding-left: 8px" class="layui-col-xs12">
                            <iframe id="repairRcdList" js-fn="repairRcdList" class="form-iframe" frameborder="0" style="width: 100%"></iframe>
                        </div>
                    </div>

                    <div  class="layui-tab-item" id="maintainRcd-tab-ct">
                        <div style="display: inline-block;padding-right: 8px;padding-left: 8px" class="layui-col-xs12">
                            <iframe id="maintainRcdList" js-fn="maintainRcdList" class="form-iframe" frameborder="0" style="width: 100%"></iframe>
                        </div>
                    </div>

                    <div  class="layui-tab-item" id="insepectionRcd-tab-ct">
                        <div style="display: inline-block;padding-right: 8px;padding-left: 8px" class="layui-col-xs12">
                            <iframe id="insepectionRcdList" js-fn="insepectionRcdList" class="form-iframe" frameborder="0" style="width: 100%"></iframe>
                        </div>
                    </div>

                    <div  class="layui-tab-item" id="history-tab-ct">
                        <div style="display: inline-block;padding-right: 8px;padding-left: 8px" class="layui-col-xs12">
                            <iframe id="historyList" js-fn="historyList" class="form-iframe" frameborder="0" style="width: 100%"></iframe>
                        </div>
                    </div>

                </div>
            </div>

        </div>

        <div style="height: 20px"></div>
        <div style="height: 80px"></div>

    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消')}" >取消</button>
    <button class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>

<script th:inline="javascript">

    var PAGE_TYPE =  [[${pageType}]];
    var OWNER_CODE =  [[${ownerCode}]];
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}]];
    var SELECT_ASSETSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetStatusEnum')}]];
    var RADIO_EQUIPMENTSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetEquipmentStatusEnum')}]];
    var AUTH_PREFIX="eam_asset";
    var ASSET_CODE_AUTO_CREATE =  [[${assetCodeAutoCreate}]];
    var VALIDATE_CONFIG =  [[${attributeRequiredData}]];
    var ASSET_CATEGORY_DATA =  [[${assetCategoryData}]];
    var ASSET_DIRECT_UPDATE_MODE =  [[${assetDirectUpdateMode}]];
    var ASSET_STATUS_COLUMN_DISABLE =  [[${assetStatusColumnDisable}]];
    var ASSET_DEFAULT_OWN_COMPANY =  [[${assetDefaultOwnCompany}]];
    var INTERNAL_CONTROL_LABEL =  [[${internalControlLabel}]];
    var TRACE_ID =  [[${traceId}]];

</script>





<script th:src="'/business/eam/asset/asset_info_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset/asset_info_form.js?'+${cacheKey}"></script>

</body>
</html>
