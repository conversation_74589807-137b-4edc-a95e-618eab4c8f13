import{L as Y}from"./index-56351f34.js";import{d as H,$ as J,l as x,p as Q,ab as w,ac as G,L as $,r as o,o as l,E as u,w as e,e as t,f as a,u as m,c as B,s as W,F as V,t as U,a5 as L,q as M,j as z,ad as Z,I as N,X as R,ae as ee,b as T}from"./index-bb2cbf17.js";import{i as D}from"./icon-f36697ff.js";const te=H({__name:"index",props:{modelShow:Boolean},emits:["update:modelShow"],setup(C,{emit:r}){const k=C,A=r,{HelpOutlineIcon:I,CloseIcon:v}=D.ionicons5,s=J(),i=x(!1),b=Q([{key:w.ASIDE_ALL_COLLAPSED,value:s.getAsideAllCollapsed,type:"switch",name:"菜单折叠",desc:"首页菜单折叠时隐藏至界面外"},{key:w.HIDE_PACKAGE_ONE_CATEGORY,value:s.getHidePackageOneCategory,type:"switch",name:"隐藏分类",desc:"工作空间表单分类只有单项时隐藏"},{key:w.CHANGE_LANG_RELOAD,value:s.getChangeLangReload,type:"switch",name:"切换语言",desc:"切换语言重新加载页面",tip:"若遇到部分区域语言切换失败，则建议开启"},{key:"divider1",type:"divider",name:"",desc:"",value:""},{key:w.CHART_TOOLS_STATUS_HIDE,value:s.getChartToolsStatusHide,type:"switch",name:"隐藏工具栏",desc:"鼠标移入时，会展示切换到展开模式"},{key:w.CHART_TOOLS_STATUS,value:s.getChartToolsStatus,type:"select",name:"工具栏展示",desc:"工作空间工具栏展示方式",options:[{label:"侧边栏",value:G.ASIDE},{label:"底部 Dock",value:G.DOCK}]},{key:"divider0",type:"divider",name:"",desc:"",value:""},{key:w.CHART_MOVE_DISTANCE,value:s.getChartMoveDistance,type:"number",name:"移动距离",min:1,step:1,suffix:"px",desc:"工作空间方向键控制移动距离"},{key:w.CHART_ALIGN_RANGE,value:s.getChartAlignRange,type:"number",name:"吸附距离",min:10,step:2,suffix:"px",desc:"工作空间移动图表时的吸附距离"}]);$(()=>k.modelShow,g=>{i.value=g});const y=()=>{A("update:modelShow",!1)},h=(g,_)=>{s.setItem(_.key,_.value)};return(g,_)=>{const c=o("n-h3"),f=o("n-icon"),p=o("n-space"),S=o("n-divider"),E=o("n-text"),O=o("n-switch"),P=o("n-input-number"),j=o("n-select"),F=o("n-tooltip"),K=o("n-list-item"),q=o("n-list"),X=o("n-modal");return l(),u(X,{show:i.value,"onUpdate:show":_[0]||(_[0]=n=>i.value=n),onAfterLeave:y},{default:e(()=>[t(q,{bordered:"",class:"go-system-setting"},{header:e(()=>[t(p,{justify:"space-between"},{default:e(()=>[t(c,{class:"go-mb-0"},{default:e(()=>[a("系统设置")]),_:1}),t(f,{size:"20",class:"go-cursor-pointer",onClick:y},{default:e(()=>[t(m(v))]),_:1})]),_:1})]),default:e(()=>[(l(!0),B(V,null,W(b,n=>(l(),u(K,{key:n.key},{default:e(()=>[n.type==="divider"?(l(),u(S,{key:0,style:{margin:"0"}})):(l(),u(p,{key:1,size:40},{default:e(()=>[t(p,null,{default:e(()=>[t(E,{class:"item-left"},{default:e(()=>[a(U(n.name),1)]),_:2},1024),n.type==="switch"?(l(),u(O,{key:0,value:n.value,"onUpdate:value":[d=>n.value=d,d=>h(d,n)],size:"small"},null,8,["value","onUpdate:value"])):n.type==="number"?(l(),u(P,{key:1,value:n.value,"onUpdate:value":[d=>n.value=d,d=>h(d,n)],class:"input-num-width",size:"small",step:n.step||null,suffix:n.suffix||null,min:n.min||0},null,8,["value","onUpdate:value","step","suffix","min"])):n.type==="select"?(l(),u(j,{key:2,class:"select-min-width",value:n.value,"onUpdate:value":[d=>n.value=d,d=>h(d,n)],size:"small",options:n.options},null,8,["value","onUpdate:value","options"])):L("",!0)]),_:2},1024),t(p,null,{default:e(()=>[t(E,{class:"item-right"},{default:e(()=>[a(U(n.desc),1)]),_:2},1024),n.tip?(l(),u(F,{key:0,trigger:"hover"},{trigger:e(()=>[t(f,{size:"21"},{default:e(()=>[t(m(I))]),_:1})]),default:e(()=>[M("span",null,U(n.tip),1)]),_:2},1024)):L("",!0)]),_:2},1024)]),_:2},1024))]),_:2},1024))),128))]),_:1})]),_:1},8,["show"])}}});const ne=z(te,[["__scopeId","data-v-fa104537"]]),oe=H({__name:"index",props:{modelShow:Boolean},emits:["update:modelShow"],setup(C,{emit:r}){const k=C,A=r,{HelpOutlineIcon:I,CloseIcon:v}=D.ionicons5,s=x(!1);$(()=>k.modelShow,b=>{s.value=b});const i=()=>{A("update:modelShow",!1)};return(b,y)=>{const h=o("n-h3"),g=o("n-icon"),_=o("n-space"),c=o("n-text"),f=o("n-a"),p=o("n-list-item"),S=o("n-list"),E=o("n-modal");return l(),u(E,{show:s.value,"onUpdate:show":y[0]||(y[0]=O=>s.value=O),onAfterLeave:i},{default:e(()=>[t(S,{bordered:"",class:"go-system-info"},{header:e(()=>[t(_,{justify:"space-between"},{default:e(()=>[t(h,{class:"go-mb-0"},{default:e(()=>[a("关于我们")]),_:1}),t(g,{size:"20",class:"go-cursor-pointer",onClick:i},{default:e(()=>[t(m(v))]),_:1})]),_:1})]),default:e(()=>[t(p,null,{default:e(()=>[t(_,{class:"go-my-2",size:20},{default:e(()=>[t(c,{class:"item-left"},{default:e(()=>[a("版权声明：")]),_:1}),t(c,null,{default:e(()=>[a(" GoView 版权属于 "),t(f,{href:"https://gitee.com/MTrun/go-view",target:"_blank"},{default:e(()=>[a("https://gitee.com/MTrun/go-view")]),_:1}),a(" 项目作者 ")]),_:1})]),_:1})]),_:1}),t(p,null,{default:e(()=>[t(_,{class:"go-my-2",size:20},{default:e(()=>[t(c,{class:"item-left"},{default:e(()=>[a("协议备注：")]),_:1}),t(c,null,{default:e(()=>[a(" 请遵守开源 MIT 协议，以上声明 "),t(c,{type:"error"},{default:e(()=>[a("不可删除")]),_:1}),a("，否则视作侵权行为，后果自负！ ")]),_:1})]),_:1})]),_:1}),t(p,null,{default:e(()=>[t(_,{class:"go-mt-2",size:20},{default:e(()=>[t(c,{class:"item-left"},{default:e(()=>[a("商业授权：")]),_:1}),t(c,null,{default:e(()=>[a(" 若不想保留版权声明，请通过仓库/交流群 联系项目作者，进行授权 ")]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["show"])}}});const se=z(oe,[["__scopeId","data-v-a1946442"]]),ae="/static/png/person.png",le={class:"user-info-box"},ce=H({__name:"index",setup(C){const{ChatboxEllipsesIcon:r,PersonIcon:k,LogOutOutlineIcon:A,SettingsSharpIcon:I}=D.ionicons5,v=x(!1),s=x(!1),i=x(!1),b=x([{label:N("global.sys_set"),key:"sysSet",icon:R(I)},{label:N("global.contact"),key:"contact",icon:R(r)}]),y=c=>{i.value=!0},h=()=>{s.value=!0},g=()=>{v.value=!0},_=c=>{switch(c){case"contact":g();break;case"sysSet":h();break;case"logout":ee();break}};return(c,f)=>{const p=o("n-dropdown");return l(),B(V,null,[t(p,{trigger:"hover",onSelect:_,"show-arrow":!0,options:b.value},{default:e(()=>[M("div",le,[i.value?(l(),u(m(k),{key:0})):L("",!0),i.value?L("",!0):(l(),u(m(Z),{key:1,round:"","object-fit":"cover",size:"medium",src:m(ae),onError:y},null,8,["src"]))])]),_:1},8,["options"]),t(m(ne),{modelShow:s.value,"onUpdate:modelShow":f[0]||(f[0]=S=>s.value=S)},null,8,["modelShow"]),t(m(se),{modelShow:v.value,"onUpdate:modelShow":f[1]||(f[1]=S=>v.value=S)},null,8,["modelShow"])],64)}}});const _e=z(ce,[["__scopeId","data-v-3ad3c581"]]),ie=H({__name:"index",setup(C){return(r,k)=>(l(),u(m(Y),null,{left:e(()=>[T(r.$slots,"left")]),center:e(()=>[T(r.$slots,"center")]),"ri-left":e(()=>[T(r.$slots,"ri-left")]),"ri-right":e(()=>[t(m(_e)),T(r.$slots,"ri-right")]),_:3}))}});export{ne as G,ie as _};
