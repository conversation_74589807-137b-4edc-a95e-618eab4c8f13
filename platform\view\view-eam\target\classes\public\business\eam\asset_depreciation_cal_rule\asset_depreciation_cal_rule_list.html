<!--
/**
 * 计算方法 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2023-10-18 06:50:18
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('计算方法')}">计算方法</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 折旧方案 , depreciationId ,typeName=select_box, isHideInSearch=true -->
                    <!-- 状态 , status ,typeName=radio_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('状态')}" class="search-label status-label">状态</span><span class="search-colon">:</span></div>


                        <div id="status" th:data="${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}" style="width:140px"></div>
                    </div>
                    <!-- 规则编号 , ruleNumber ,typeName=number_input, isHideInSearch=true -->
                    <!-- 字段值 , columnValue ,typeName=text_input, isHideInSearch=true -->
                    <!-- 计算类型 , calculationType ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 计算方法 , methodContent ,typeName=text_area, isHideInSearch=true -->
                    <!-- 方法描述 , methodContentInfo ,typeName=text_input, isHideInSearch=true -->
                    <!-- 返回类型 , returnType ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 备注 , notes ,typeName=text_input, isHideInSearch=true -->
                    <!-- 修改人ID , updateBy ,typeName=text_input, isHideInSearch=true -->
                    <!-- 动作 , actionCode ,typeName=radio_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('动作')}" class="search-label actionCode-label">动作</span><span class="search-colon">:</span></div>


                        <div id="actionCode" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetDepreciationRuleActionCodeEnum')}" style="width:140px"></div>
                    </div>
                    <!-- 字段名称 , columnName ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('字段名称')}" class="search-label columnName-label">字段名称</span><span class="search-colon">:</span></div>
                        <input id="columnName" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索','','cmp:table.search')}">搜索</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 42px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${perm.checkAuth('eam_asset_depreciation_cal_rule:create')}" id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建','','cmp:table.button')}">新建</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${perm.checkAuth('eam_asset_depreciation_cal_rule:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view"  data-id="{{d.id}}"> <span th:text="${lang.translate('查看','','cmp:table.ops')}">查看</span></button>
    <button th:if="${perm.checkAnyAuth('eam_asset_depreciation_cal_rule:update','eam_asset_depreciation_cal_rule:save')}" class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit"data-id="{{d.id}}"><span th:text="${lang.translate('修改','','cmp:table.ops')}">修改</span></button>


    <button th:if="${perm.checkAuth('eam_asset_depreciation_cal_rule:delete')}" class="layui-btn layui-btn-xs layui-btn-danger ops-delete-button " lay-event="del" data-id="{{d.id}}"><span th:text="${lang.translate('删除','','cmp:table.ops')}">删除</span></button>


</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var RADIO_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_ACTIONCODE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetDepreciationRuleActionCodeEnum')}]];
    var RADIO_CALCULATIONTYPE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetDepreciationCalculationMethodTypeEnum')}]];
    var RADIO_RETURNTYPE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetDepreciationCalculationReturnTypeEnum')}]];
    var AUTH_PREFIX="eam_asset_depreciation_cal_rule";

    // depreciationId
    var DEPRECIATION_ID = [[${depreciationId}]] ;

</script>

<script th:src="'/business/eam/asset_depreciation_cal_rule/asset_depreciation_cal_rule_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset_depreciation_cal_rule/asset_depreciation_cal_rule_list.js?'+${cacheKey}"></script>

</body>
</html>