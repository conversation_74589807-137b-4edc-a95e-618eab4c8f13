(function(e){function t(t){for(var n,s,a=t[0],r=t[1],d=t[2],u=0,f=[];u<a.length;u++)s=a[u],Object.prototype.hasOwnProperty.call(o,s)&&o[s]&&f.push(o[s][0]),o[s]=0;for(n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n]);c&&c(t);while(f.length)f.shift()();return l.push.apply(l,d||[]),i()}function i(){for(var e,t=0;t<l.length;t++){for(var i=l[t],n=!0,a=1;a<i.length;a++){var r=i[a];0!==o[r]&&(n=!1)}n&&(l.splice(t--,1),e=s(s.s=i[0]))}return e}var n={},o={app:0},l=[];function s(t){if(n[t])return n[t].exports;var i=n[t]={i:t,l:!1,exports:{}};return e[t].call(i.exports,i,i.exports,s),i.l=!0,i.exports}s.m=e,s.c=n,s.d=function(e,t,i){s.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},s.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},s.t=function(e,t){if(1&t&&(e=s(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(s.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)s.d(i,n,function(t){return e[t]}.bind(null,n));return i},s.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return s.d(t,"a",t),t},s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},s.p="";var a=window["webpackJsonp"]=window["webpackJsonp"]||[],r=a.push.bind(a);a.push=t,a=a.slice();for(var d=0;d<a.length;d++)t(a[d]);var c=r;l.push([0,"chunk-vendors"]),i()})({0:function(e,t,i){e.exports=i("56d7")},"0085":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.widgetSize")}},[i("el-select",{model:{value:e.optionModel.size,callback:function(t){e.$set(e.optionModel,"size",t)},expression:"optionModel.size"}},e._l(e.widgetSizes,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)},o=[],l=i("79fa"),s={name:"size-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},data:function(){return{widgetSizes:[{label:"default",value:""},{label:"large",value:"large"},{label:"medium",value:"medium"},{label:"small",value:"small"},{label:"mini",value:"mini"}]}}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"08040a34",null);t["default"]=d.exports},"0086":function(e,t,i){"use strict";i("9a48")},"01ea":function(e,t,i){"use strict";i.d(t,"d",(function(){return n})),i.d(t,"c",(function(){return o})),i.d(t,"a",(function(){return l})),i.d(t,"b",(function(){return s}));var n="2.2.9",o="https://ks3-cn-beijing.ksyuncs.com/vform-static/vcase/",l="https://ks3-cn-beijing.ksyun.com/vform2021/ace-mini",s="https://ks3-cn-beijing.ksyun.com/vform2021/js-beautify/1.14.0/beautifier.min.js"},"028e":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.defaultValue")}},[i("el-time-picker",{staticStyle:{width:"100%"},attrs:{format:e.optionModel.format,"value-format":"HH:mm:ss"},on:{change:e.emitDefaultValueChange},model:{value:e.optionModel.defaultValue,callback:function(t){e.$set(e.optionModel,"defaultValue",t)},expression:"optionModel.defaultValue"}})],1)},o=[],l=i("79fa"),s=i("b2bf"),a={name:"time-defaultValue-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"988a5246",null);t["default"]=c.exports},"0353":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.multipleLimit")}},[i("el-input-number",{staticClass:"hide-spin-button",staticStyle:{width:"100%"},attrs:{min:0},model:{value:e.optionModel.multipleLimit,callback:function(t){e.$set(e.optionModel,"multipleLimit",t)},expression:"optionModel.multipleLimit"}})],1)},o=[],l=i("79fa"),s={name:"multipleLimit-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"088d9185",null);t["default"]=d.exports},"04fd":function(e,t,i){},"05af":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{prop:"name",rules:e.nameRequiredRule}},[i("span",{attrs:{slot:"label"},slot:"label"},[e._v(e._s(e.i18nt("designer.setting.uniqueName"))+" "),i("el-tooltip",{attrs:{effect:"light",content:e.i18nt("designer.setting.editNameHelp")}},[i("i",{staticClass:"el-icon-info"})])],1),e.selectedWidget.category&&"sub-form"!==e.selectedWidget.type||e.noFieldList?[i("el-input",{attrs:{type:"text",readonly:e.widgetNameReadonly},on:{change:e.updateWidgetNameAndRef},model:{value:e.optionModel.name,callback:function(t){e.$set(e.optionModel,"name",t)},expression:"optionModel.name"}})]:[i("el-select",{attrs:{"allow-create":"",filterable:"",disabled:e.widgetNameReadonly,title:e.i18nt("designer.setting.editNameHelp")},on:{change:e.updateWidgetNameAndRef},model:{value:e.optionModel.name,callback:function(t){e.$set(e.optionModel,"name",t)},expression:"optionModel.name"}},e._l(e.serverFieldList,(function(e,t){return i("el-option",{key:t,attrs:{label:e.label,value:e.name}})})),1)]],2)},o=[],l=(i("b0c0"),i("79fa")),s=i("ca00"),a={name:"name-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},inject:["serverFieldList","getDesignerConfig"],data:function(){return{nameRequiredRule:[{required:!0,message:"name required"}]}},computed:{noFieldList:function(){return!this.serverFieldList||this.serverFieldList.length<=0},widgetNameReadonly:function(){return!!this.getDesignerConfig().widgetNameReadonly}},methods:{updateWidgetNameAndRef:function(e){var t=this.designer.selectedWidgetName;if(Object(s["l"])(e))return this.selectedWidget.options.name=t,void this.$message.info(this.i18nt("designer.hint.nameRequired"));if(this.designer.formWidget){var i=this.designer.formWidget.getWidgetRef(e);if(i)return this.selectedWidget.options.name=t,void this.$message.info(this.i18nt("designer.hint.duplicateName")+e);var n=this.designer.formWidget.getWidgetRef(t);if(n&&n.registerToRefList){n.registerToRefList(t);var o=this.getLabelByFieldName(e);this.designer.updateSelectedWidgetNameAndLabel(this.selectedWidget,e,o)}}},getLabelByFieldName:function(e){for(var t=0;t<this.serverFieldList.length;t++)if(this.serverFieldList[t].name===e)return this.serverFieldList[t].label;return null}}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"4487ff70",null);t["default"]=c.exports},"0654":function(e,t,i){var n={"./allowCreate-editor.vue":"fe5d","./appendButton-editor.vue":"ab136","./appendButtonDisabled-editor.vue":"dca80","./automaticDropdown-editor.vue":"30ac","./border-editor.vue":"131c","./buttonIcon-editor.vue":"7ceb","./buttonStyle-editor.vue":"22a7","./clearable-editor.vue":"1e67","./columnWidth-editor.vue":"f388","./container-grid-col/grid-col-offset-editor.vue":"329d","./container-grid-col/grid-col-pull-editor.vue":"ce37","./container-grid-col/grid-col-push-editor.vue":"fa85","./container-grid-col/grid-col-responsive-editor.vue":"c3f1","./container-grid-col/grid-col-span-editor.vue":"ffcf","./container-grid/colHeight-editor.vue":"6732","./container-grid/gutter-editor.vue":"e447","./container-sub-form/showBlankRow-editor.vue":"b6b9","./container-sub-form/showRowNumber-editor.vue":"2b47","./container-sub-form/sub-form-labelAlign-editor.vue":"3e10","./container-tab/tab-customClass-editor.vue":"d2d9","./container-table-cell/cellHeight-editor.vue":"fcbf","./container-table-cell/cellWidth-editor.vue":"5674","./container-table-cell/wordBreak-editor.vue":"937f","./customClass-editor.vue":"cc78","./defaultValue-editor.vue":"c610","./disabled-editor.vue":"87ca","./displayStyle-editor.vue":"7c77","./editable-editor.vue":"a867","./endPlaceholder-editor.vue":"df90","./event-handler/onAppendButtonClick-editor.vue":"9d09","./event-handler/onBeforeUpload-editor.vue":"839e","./event-handler/onBlur-editor.vue":"4e3d","./event-handler/onChange-editor.vue":"6c26","./event-handler/onClick-editor.vue":"93b5","./event-handler/onCreated-editor.vue":"b90a","./event-handler/onFileRemove.vue":"c391","./event-handler/onFocus-editor.vue":"f4a6","./event-handler/onInput-editor.vue":"8029","./event-handler/onMounted-editor.vue":"b46f","./event-handler/onRemoteQuery-editor.vue":"955b","./event-handler/onSubFormRowAdd-editor.vue":"c67a","./event-handler/onSubFormRowChange-editor.vue":"1973","./event-handler/onSubFormRowDelete-editor.vue":"d773","./event-handler/onSubFormRowInsert-editor.vue":"c3f8","./event-handler/onUploadError-editor.vue":"3f28","./event-handler/onUploadSuccess-editor.vue":"7331","./event-handler/onValidate-editor.vue":"e072","./field-button/button-type-editor.vue":"61e5","./field-button/circle-editor.vue":"6f49","./field-button/icon-editor.vue":"bc16","./field-button/plain-editor.vue":"8299","./field-button/round-editor.vue":"5905","./field-cascader/cascader-defaultValue-editor.vue":"9381","./field-cascader/cascader-multiple-editor.vue":"bb60","./field-cascader/checkStrictly-editor.vue":"29a3","./field-cascader/showAllLevels-editor.vue":"e8d7","./field-checkbox/checkbox-defaultValue-editor.vue":"37ab","./field-color/color-defaultValue-editor.vue":"bae2","./field-date-range/date-range-defaultValue-editor.vue":"79ad","./field-date-range/date-range-format-editor.vue":"4e94","./field-date-range/date-range-type-editor.vue":"feea","./field-date-range/date-range-valueFormat-editor.vue":"4be5","./field-date/date-defaultValue-editor.vue":"6c62","./field-date/date-format-editor.vue":"109b","./field-date/date-type-editor.vue":"cc5f","./field-date/date-valueFormat-editor.vue":"6656","./field-divider/contentPosition-editor.vue":"35c2","./field-file-upload/file-upload-fileTypes-editor.vue":"f584","./field-html-text/htmlContent-editor.vue":"df12","./field-number/controlsPosition-editor.vue":"54d3","./field-picture-upload/picture-upload-fileTypes-editor.vue":"2305","./field-radio/radio-defaultValue-editor.vue":"b205","./field-rate/allowHalf-editor.vue":"b674","./field-rate/highThreshold-editor.vue":"91df","./field-rate/lowThreshold-editor.vue":"fc8b","./field-rate/rate-defaultValue-editor.vue":"653d","./field-rate/rate-max-editor.vue":"3bc5","./field-rate/showScore-editor.vue":"28ed","./field-rate/showText-editor.vue":"44fb","./field-select/select-defaultValue-editor.vue":"5f2d","./field-slider/range-editor.vue":"9dcd","./field-slider/showStops-editor.vue":"d3eb","./field-slider/vertical-editor.vue":"cbdd","./field-static-text/fontSize-editor.vue":"8745","./field-static-text/preWrap-editor.vue":"acb0","./field-static-text/textContent-editor.vue":"b2b6","./field-switch/activeColor-editor.vue":"0a43","./field-switch/activeText-editor.vue":"e96c","./field-switch/inactiveColor-editor.vue":"1795","./field-switch/inactiveText-editor.vue":"31ab","./field-switch/switch-defaultValue-editor.vue":"e0b7","./field-switch/switchWidth-editor.vue":"663b","./field-time-range/time-range-defaultValue-editor.vue":"b302","./field-time-range/time-range-format-editor.vue":"a979","./field-time/time-defaultValue-editor.vue":"028e","./field-time/time-format-editor.vue":"7de6","./fileMaxSize-editor.vue":"9b00","./filterable-editor.vue":"6caf","./hidden-editor.vue":"2c6d","./label-editor.vue":"c061","./labelAlign-editor.vue":"8aa9","./labelHidden-editor.vue":"36f6","./labelIconClass-editor.vue":"732a","./labelIconPosition-editor.vue":"9000","./labelTooltip-editor.vue":"f319","./labelWidth-editor.vue":"e801","./limit-editor.vue":"2dc5","./max-editor.vue":"ff09","./maxLength-editor.vue":"e090","./min-editor.vue":"a982","./minLength-editor.vue":"791d","./multiple-editor.vue":"e86f","./multipleLimit-editor.vue":"0353","./multipleSelect-editor.vue":"aa43","./name-editor.vue":"05af","./optionItems-editor.vue":"5ce3","./placeholder-editor.vue":"4595","./precision-editor.vue":"ff66","./prefixIcon-editor.vue":"16f3","./readonly-editor.vue":"a0f1","./remote-editor.vue":"5112","./required-editor.vue":"6038","./requiredHint-editor.vue":"6bcd","./rows-editor.vue":"54cf","./showFileList-editor.vue":"401c","./showPassword-editor.vue":"a4ae","./showWordLimit-editor.vue":"8f6d","./size-editor.vue":"0085","./startPlaceholder-editor.vue":"c6d2","./step-editor.vue":"566c","./suffixIcon-editor.vue":"6873","./textAlign-editor.vue":"c635","./type-editor.vue":"9164","./uploadTip-editor.vue":"a2c6","./uploadURL-editor.vue":"31b7","./validation-editor.vue":"65d0","./validationHint-editor.vue":"99c0","./withCredentials-editor.vue":"d741"};function o(e){var t=l(e);return i(t)}function l(e){if(!i.o(n,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}o.keys=function(){return Object.keys(n)},o.resolve=l,e.exports=o,o.id="0654"},"090f":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("static-content-wrapper",{attrs:{designer:e.designer,field:e.field,"design-state":e.designState,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList,"sub-form-row-index":e.subFormRowIndex,"sub-form-col-index":e.subFormColIndex,"sub-form-row-id":e.subFormRowId}},[i("div",{ref:"fieldEditor",domProps:{innerHTML:e._s(e.field.options.htmlContent)}})])},o=[],l=(i("a9e3"),i("828b")),s=i("c6e3"),a=i("79fa"),r=i("2d11"),d={name:"html-text-widget",componentName:"FieldWidget",mixins:[s["a"],r["a"],a["b"]],props:{field:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object,designState:{type:Boolean,default:!1},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""}},components:{StaticContentWrapper:l["default"]},computed:{},beforeCreate:function(){},created:function(){this.registerToRefList(),this.initEventHandler(),this.handleOnCreated()},mounted:function(){this.handleOnMounted()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{}},c=d,u=(i("8f10"),i("2877")),f=Object(u["a"])(c,n,o,!1,null,"5b64c2ea",null);t["default"]=f.exports},"09c3":function(e,t,i){"use strict";i("43b3")},"0a43":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.activeColor")}},[i("el-color-picker",{model:{value:e.optionModel.activeColor,callback:function(t){e.$set(e.optionModel,"activeColor",t)},expression:"optionModel.activeColor"}})],1)},o=[],l=i("79fa"),s={name:"activeColor-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"01693e0b",null);t["default"]=d.exports},"0b6a":function(e,t,i){},"0dee":function(e,t,i){"use strict";i("36c7")},"0f59":function(e,t,i){},"109b":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.format")}},[i("el-select",{attrs:{filterable:"","allow-create":""},model:{value:e.optionModel.format,callback:function(t){e.$set(e.optionModel,"format",t)},expression:"optionModel.format"}},[i("el-option",{attrs:{label:"yyyy-MM-dd",value:"yyyy-MM-dd"}}),i("el-option",{attrs:{label:"yyyy/MM/dd",value:"yyyy/MM/dd"}}),i("el-option",{attrs:{label:"yyyy年MM月dd日",value:"yyyy年MM月dd日"}}),i("el-option",{attrs:{label:"yyyy-MM-dd HH:mm:ss",value:"yyyy-MM-dd HH:mm:ss"}}),i("el-option",{attrs:{label:"yyyy-MM-dd hh:mm:ss",value:"yyyy-MM-dd hh:mm:ss"}})],1)],1)},o=[],l=i("79fa"),s={name:"date-format-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"095e5f5d",null);t["default"]=d.exports},"10ae":function(e,t,i){var n={"./container-item-wrapper.vue":"90c2","./grid-col-item.vue":"a93f","./grid-item.vue":"f746","./sub-form-item.vue":"efdc","./tab-item.vue":"c9d4","./table-cell-item.vue":"6a79","./table-item.vue":"8921"};function o(e){var t=l(e);return i(t)}function l(e){if(!i.o(n,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}o.keys=function(){return Object.keys(n)},o.resolve=l,e.exports=o,o.id="10ae"},"112c":function(e,t,i){},"131c":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.border")}},[i("el-switch",{model:{value:e.optionModel.border,callback:function(t){e.$set(e.optionModel,"border",t)},expression:"optionModel.border"}})],1)},o=[],l=i("79fa"),s={name:"border-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"f579a26a",null);t["default"]=d.exports},"13f0":function(e,t,i){"use strict";i("7962")},1415:function(e,t,i){"use strict";i("67ab")},1516:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("td",{staticClass:"table-cell",class:[e.selected?"selected":"",e.customClass],style:{width:e.widget.options.cellWidth+"!important"||!1,height:e.widget.options.cellHeight+"!important"||!1,"word-break":e.widget.options.wordBreak?"break-all":"normal"},attrs:{colspan:e.widget.options.colspan||1,rowspan:e.widget.options.rowspan||1},on:{click:function(t){return t.stopPropagation(),e.selectWidget(e.widget)}}},[i("draggable",e._b({staticClass:"draggable-div",attrs:{list:e.widget.widgetList,handle:".drag-handler",move:e.checkContainerMove},on:{end:function(t){return e.onTableDragEnd(t,e.widget.widgetList)},add:function(t){return e.onTableDragAdd(t,e.widget.widgetList)},update:e.onTableDragUpdate}},"draggable",{group:"dragGroup",ghostClass:"ghost",animation:200},!1),[i("transition-group",{staticClass:"form-widget-list",attrs:{name:"fade",tag:"div"}},[e._l(e.widget.widgetList,(function(t,n){return["container"===t.category?[i(t.type+"-widget",{key:t.id,tag:"component",attrs:{widget:t,designer:e.designer,"parent-list":e.widget.widgetList,"index-of-parent-list":n,"parent-widget":e.widget}})]:[i(t.type+"-widget",{key:t.id,tag:"component",attrs:{field:t,designer:e.designer,"parent-list":e.widget.widgetList,"index-of-parent-list":n,"parent-widget":e.widget,"design-state":!0}})]]}))],2)],1),e.designer.selectedId===e.widget.id&&"table-cell"===e.widget.type?i("div",{staticClass:"table-cell-action"},[i("i",{staticClass:"el-icon-back",attrs:{title:e.i18nt("designer.hint.selectParentWidget")},on:{click:function(t){return t.stopPropagation(),e.selectParentWidget()}}}),i("el-dropdown",{attrs:{trigger:"click",size:"small"},on:{command:e.handleTableCellCommand}},[i("i",{staticClass:"el-icon-menu",attrs:{title:e.i18nt("designer.hint.cellSetting")}}),i("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[i("el-dropdown-item",{attrs:{command:"insertLeftCol"}},[e._v(e._s(e.i18nt("designer.setting.insertColumnToLeft")))]),i("el-dropdown-item",{attrs:{command:"insertRightCol"}},[e._v(e._s(e.i18nt("designer.setting.insertColumnToRight")))]),i("el-dropdown-item",{attrs:{command:"insertAboveRow"}},[e._v(e._s(e.i18nt("designer.setting.insertRowAbove")))]),i("el-dropdown-item",{attrs:{command:"insertBelowRow"}},[e._v(e._s(e.i18nt("designer.setting.insertRowBelow")))]),i("el-dropdown-item",{attrs:{command:"mergeLeftCol",disabled:e.mergeLeftColDisabled,divided:""}},[e._v(e._s(e.i18nt("designer.setting.mergeLeftColumn")))]),i("el-dropdown-item",{attrs:{command:"mergeRightCol",disabled:e.mergeRightColDisabled}},[e._v(e._s(e.i18nt("designer.setting.mergeRightColumn")))]),i("el-dropdown-item",{attrs:{command:"mergeWholeRow",disabled:e.mergeWholeRowDisabled}},[e._v(e._s(e.i18nt("designer.setting.mergeEntireRow")))]),i("el-dropdown-item",{attrs:{command:"mergeAboveRow",disabled:e.mergeAboveRowDisabled,divided:""}},[e._v(e._s(e.i18nt("designer.setting.mergeRowAbove")))]),i("el-dropdown-item",{attrs:{command:"mergeBelowRow",disabled:e.mergeBelowRowDisabled}},[e._v(e._s(e.i18nt("designer.setting.mergeRowBelow")))]),i("el-dropdown-item",{attrs:{command:"mergeWholeCol",disabled:e.mergeWholeColDisabled}},[e._v(e._s(e.i18nt("designer.setting.mergeEntireColumn")))]),i("el-dropdown-item",{attrs:{command:"undoMergeRow",disabled:e.undoMergeRowDisabled,divided:""}},[e._v(e._s(e.i18nt("designer.setting.undoMergeRow")))]),i("el-dropdown-item",{attrs:{command:"undoMergeCol",disabled:e.undoMergeColDisabled}},[e._v(e._s(e.i18nt("designer.setting.undoMergeCol")))]),i("el-dropdown-item",{attrs:{command:"deleteWholeCol",disabled:e.deleteWholeColDisabled,divided:""}},[e._v(e._s(e.i18nt("designer.setting.deleteEntireCol")))]),i("el-dropdown-item",{attrs:{command:"deleteWholeRow",disabled:e.deleteWholeRowDisabled}},[e._v(e._s(e.i18nt("designer.setting.deleteEntireRow")))])],1)],1)],1):e._e(),e.designer.selectedId===e.widget.id&&"table-cell"===e.widget.type?i("div",{staticClass:"table-cell-handler"},[i("i",[e._v(e._s(e.i18nt("designer.widgetLabel."+e.widget.type)))])]):e._e()],1)},o=[],l=i("5530"),s=(i("a9e3"),i("b76a")),a=i.n(s),r=i("79fa"),d=i("c029"),c=i("26a6"),u={name:"TableCellWidget",componentName:"TableCellWidget",mixins:[r["b"],c["a"]],inject:["refList"],components:Object(l["a"])({Draggable:a.a},d["a"]),props:{widget:Object,parentWidget:Object,parentList:Array,rowIndex:Number,colIndex:Number,rowLength:Number,colLength:Number,colArray:Array,rowArray:Array,designer:Object},computed:{selected:function(){return this.widget.id===this.designer.selectedId},customClass:function(){return this.widget.options.customClass||""},mergeLeftColDisabled:function(){return this.colIndex<=0||this.colArray[this.colIndex-1].options.rowspan!==this.widget.options.rowspan},mergeRightColDisabled:function(){var e=this.colIndex+this.widget.options.colspan;return this.colIndex>=this.colLength-1||e>this.colLength-1||this.colArray[e].options.rowspan!==this.widget.options.rowspan},mergeWholeRowDisabled:function(){return this.colLength<=1||this.colLength===this.widget.options.colspan},mergeAboveRowDisabled:function(){return this.rowIndex<=0||this.rowArray[this.rowIndex-1].cols[this.colIndex].options.colspan!==this.widget.options.colspan},mergeBelowRowDisabled:function(){var e=this.rowIndex+this.widget.options.rowspan;return this.rowIndex>=this.rowLength-1||e>this.rowLength-1||this.rowArray[e].cols[this.colIndex].options.colspan!==this.widget.options.colspan},mergeWholeColDisabled:function(){return this.rowLength<=1||this.rowLength===this.widget.options.rowspan},undoMergeColDisabled:function(){return this.widget.merged||this.widget.options.colspan<=1},undoMergeRowDisabled:function(){return this.widget.merged||this.widget.options.rowspan<=1},deleteWholeColDisabled:function(){return 1===this.colLength||this.widget.options.colspan===this.colLength},deleteWholeRowDisabled:function(){return 1===this.rowLength||this.widget.options.rowspan===this.rowLength}},watch:{},created:function(){this.initRefList()},methods:{selectWidget:function(e){this.designer.setSelected(e)},checkContainerMove:function(e){return this.designer.checkWidgetMove(e)},onTableDragEnd:function(e,t){},onTableDragAdd:function(e,t){var i=e.newIndex;t[i]&&this.designer.setSelected(t[i]),this.designer.emitHistoryChange(),this.designer.emitEvent("field-selected",this.widget)},onTableDragUpdate:function(){this.designer.emitHistoryChange()},selectParentWidget:function(){this.parentWidget?this.designer.setSelected(this.parentWidget):this.designer.clearSelected()},handleTableCellCommand:function(e){"insertLeftCol"===e?this.insertLeftCol():"insertRightCol"===e?this.insertRightCol():"insertAboveRow"===e?this.insertAboveRow():"insertBelowRow"===e?this.insertBelowRow():"mergeLeftCol"===e?this.mergeLeftCol():"mergeRightCol"===e?this.mergeRightCol():"mergeWholeCol"===e?this.mergeWholeCol():"mergeAboveRow"===e?this.mergeAboveRow():"mergeBelowRow"===e?this.mergeBelowRow():"mergeWholeRow"===e?this.mergeWholeRow():"undoMergeCol"===e?this.undoMergeCol():"undoMergeRow"===e?this.undoMergeRow():"deleteWholeCol"===e?this.deleteWholeCol():"deleteWholeRow"===e&&this.deleteWholeRow()},insertLeftCol:function(){this.designer.insertTableCol(this.parentWidget,this.colIndex,this.rowIndex,!0)},insertRightCol:function(){this.designer.insertTableCol(this.parentWidget,this.colIndex,this.rowIndex,!1)},insertAboveRow:function(){this.designer.insertTableRow(this.parentWidget,this.rowIndex,this.rowIndex,this.colIndex,!0)},insertBelowRow:function(){this.designer.insertTableRow(this.parentWidget,this.rowIndex,this.rowIndex,this.colIndex,!1)},mergeLeftCol:function(){this.designer.mergeTableCol(this.rowArray,this.colArray,this.rowIndex,this.colIndex,!0,this.widget)},mergeRightCol:function(){this.designer.mergeTableCol(this.rowArray,this.colArray,this.rowIndex,this.colIndex,!1,this.widget)},mergeWholeRow:function(){this.designer.mergeTableWholeRow(this.rowArray,this.colArray,this.rowIndex,this.colIndex)},mergeAboveRow:function(){this.designer.mergeTableRow(this.rowArray,this.rowIndex,this.colIndex,!0,this.widget)},mergeBelowRow:function(){this.designer.mergeTableRow(this.rowArray,this.rowIndex,this.colIndex,!1,this.widget)},mergeWholeCol:function(){this.designer.mergeTableWholeCol(this.rowArray,this.colArray,this.rowIndex,this.colIndex)},undoMergeCol:function(){this.designer.undoMergeTableCol(this.rowArray,this.rowIndex,this.colIndex,this.widget.options.colspan,this.widget.options.rowspan)},undoMergeRow:function(){this.designer.undoMergeTableRow(this.rowArray,this.rowIndex,this.colIndex,this.widget.options.colspan,this.widget.options.rowspan)},deleteWholeCol:function(){this.designer.deleteTableWholeCol(this.rowArray,this.colIndex)},deleteWholeRow:function(){this.designer.deleteTableWholeRow(this.rowArray,this.rowIndex)}}},f=u,p=(i("fb56"),i("2877")),m=Object(p["a"])(f,n,o,!1,null,"18cb4f2a",null);t["default"]=m.exports},"15c6":function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-picture-upload-field",use:"icon-picture-upload-field-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-picture-upload-field"><defs><style type="text/css"></style></defs><path d="M896 1024 128 1024c-70.688 0-128-57.312-128-128L0 128c0-70.688 57.312-128 128-128l768 0c70.688 0 128 57.312 128 128l0 768C1024 966.688 966.688 1024 896 1024zM896 960c35.328 0 64-28.672 64-64l0-256.032-192-192-273.184 273.152L730.624 960 896 960zM64 896c0 35.328 28.672 64 64 64l512.032 0L318.24 638.208 64 865.952 64 896zM960 128c0-35.328-28.672-64-64-64L128 64C92.672 64 64 92.672 64 128l0 650.752L320 544l129.856 131.552L768 352l192 196.096L960 128zM256 384c-70.688 0-128-57.312-128-128s57.312-128 128-128 128 57.312 128 128S326.688 384 256 384zM256 192C220.672 192 192 220.672 192 256s28.672 64 64 64 64-28.672 64-64S291.328 192 256 192z" p-id="64598" /></symbol>'});s.a.add(a);t["default"]=a},1697:function(e,t,i){"use strict";i("714d")},"16f3":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.prefixIcon")}},[i("el-input",{attrs:{type:"text"},model:{value:e.optionModel.prefixIcon,callback:function(t){e.$set(e.optionModel,"prefixIcon",t)},expression:"optionModel.prefixIcon"}})],1)},o=[],l=i("79fa"),s={name:"prefixIcon-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"28d62b7a",null);t["default"]=d.exports},1795:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.inactiveColor")}},[i("el-color-picker",{model:{value:e.optionModel.inactiveColor,callback:function(t){e.$set(e.optionModel,"inactiveColor",t)},expression:"optionModel.inactiveColor"}})],1)},o=[],l=i("79fa"),s={name:"inactiveColor-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"03b49a61",null);t["default"]=d.exports},"17f0":function(e,t,i){},1895:function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-switch-field",use:"icon-switch-field-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-switch-field"><defs><style type="text/css"></style></defs><path d="M692 792H332C182 792 62 672 62 522s120-270 270-270h360c150 0 270 120 270 270 0 147-120 270-270 270zM332 312c-117 0-210 93-210 210s93 210 210 210h360c117 0 210-93 210-210s-93-210-210-210H332z" p-id="48564" /><path d="M191 522a150 150 0 1 0 300 0 150 150 0 1 0-300 0z" p-id="48565" /></symbol>'});s.a.add(a);t["default"]=a},"18ba":function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-redo",use:"icon-redo-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-redo"><defs><style type="text/css"></style></defs><path d="M412.081 346.3h443.415l-215.328-212.429c-18.973-18.973-18.973-46.064 0-65.038s44.325-19.884 63.381-0.83l291.385 284.591c18.973 18.973 18.973 44.159 0 63.132l-291.385 284.923c-18.973 18.973-44.325 18.973-63.381-0.083-18.973-18.973-18.973-43.91 0-62.883l215.328-208.534h-443.415c-177.3 0-314.335 138.359-314.335 309.364v44.325c0 25.354-16.074 44.325-41.425 44.325s-41.425-18.973-41.425-44.325v-44.325c0-221.709 169.181-392.213 397.185-392.213z" p-id="3522" /></symbol>'});s.a.add(a);t["default"]=a},1973:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:"onSubFormRowChange","label-width":"150px"}},[i("el-button",{attrs:{type:"info",icon:"el-icon-edit",plain:"",round:""},on:{click:function(t){return e.editEventHandler("onSubFormRowChange",e.eventParams)}}},[e._v(" "+e._s(e.i18nt("designer.setting.addEventHandler")))])],1)},o=[],l=i("79fa"),s=i("7d6c"),a={name:"onSubFormRowChange-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},data:function(){return{eventParams:["subFormData"]}}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"7109dd9a",null);t["default"]=c.exports},"1bdc":function(e,t,i){},"1d42":function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-time-field",use:"icon-time-field-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-time-field"><defs><style type="text/css"></style></defs><path d="M512 39.384615a472.615385 472.615385 0 1 0 472.615385 472.615385A472.615385 472.615385 0 0 0 512 39.384615z m0 866.461539a393.846154 393.846154 0 1 1 393.846154-393.846154 393.846154 393.846154 0 0 1-393.846154 393.846154z m75.854769-373.720616A77.154462 77.154462 0 0 0 590.769231 512a78.454154 78.454154 0 0 0-39.384616-67.859692V196.923077a39.384615 39.384615 0 0 0-78.76923 0v247.217231a78.336 78.336 0 0 0 59.549538 143.714461l70.144 70.144a39.384615 39.384615 0 0 0 55.689846-55.689846zM512 551.384615a39.384615 39.384615 0 1 1 39.384615-39.384615 39.384615 39.384615 0 0 1-39.384615 39.384615z m315.076923-78.76923a39.384615 39.384615 0 1 0 39.384615 39.384615 39.384615 39.384615 0 0 0-39.384615-39.384615zM196.923077 472.615385a39.384615 39.384615 0 1 0 39.384615 39.384615 39.384615 39.384615 0 0 0-39.384615-39.384615z m509.991385 234.299077a39.384615 39.384615 0 1 0 55.689846 0 39.384615 39.384615 0 0 0-55.689846 0z m-389.907693-389.907693a39.384615 39.384615 0 1 0-55.729231 0 39.384615 39.384615 0 0 0 55.808 0.039385zM512 787.692308a39.384615 39.384615 0 1 0 39.384615 39.384615 39.384615 39.384615 0 0 0-39.384615-39.384615z m-250.604308-80.777846a39.384615 39.384615 0 1 0 55.689846 0 39.384615 39.384615 0 0 0-55.689846-0.039385zM706.914462 261.395692a39.384615 39.384615 0 1 0 55.689846 0 39.384615 39.384615 0 0 0-55.689846-0.039384z" p-id="45070" /></symbol>'});s.a.add(a);t["default"]=a},"1e67":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.clearable")}},[i("el-switch",{model:{value:e.optionModel.clearable,callback:function(t){e.$set(e.optionModel,"clearable",t)},expression:"optionModel.clearable"}})],1)},o=[],l=i("79fa"),s={name:"clearable-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"5534f7e5",null);t["default"]=d.exports},"20c0":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("form-item-wrapper",{attrs:{designer:e.designer,field:e.field,rules:e.rules,"design-state":e.designState,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList,"sub-form-row-index":e.subFormRowIndex,"sub-form-col-index":e.subFormColIndex,"sub-form-row-id":e.subFormRowId}},[i("el-time-picker",{ref:"fieldEditor",staticClass:"full-width-input",attrs:{disabled:e.field.options.disabled,readonly:e.field.options.readonly,size:e.field.options.size,clearable:e.field.options.clearable,editable:e.field.options.editable,format:e.field.options.format,"value-format":"HH:mm:ss",placeholder:e.field.options.placeholder||e.i18nt("render.hint.timePlaceholder")},on:{focus:e.handleFocusCustomEvent,blur:e.handleBlurCustomEvent,change:e.handleChangeEvent},model:{value:e.fieldModel,callback:function(t){e.fieldModel=t},expression:"fieldModel"}})],1)},o=[],l=(i("a9e3"),i("9eeb")),s=i("c6e3"),a=i("79fa"),r=i("2d11"),d={name:"time-widget",componentName:"FieldWidget",mixins:[s["a"],r["a"],a["b"]],props:{field:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object,designState:{type:Boolean,default:!1},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""}},components:{FormItemWrapper:l["default"]},inject:["refList","formConfig","globalOptionData","globalModel"],data:function(){return{oldFieldValue:null,fieldModel:null,rules:[]}},computed:{},beforeCreate:function(){},created:function(){this.initFieldModel(),this.registerToRefList(),this.initEventHandler(),this.buildFieldRules(),this.handleOnCreated()},mounted:function(){this.handleOnMounted()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{}},c=d,u=(i("9ebd"),i("2877")),f=Object(u["a"])(c,n,o,!1,null,"0761446e",null);t["default"]=f.exports},"20e0":function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-slot-component",use:"icon-slot-component-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-slot-component"><defs><style type="text/css"></style></defs><path d="M512 102.4c-212.48 0-384 171.52-384 384s171.52 384 384 384 384-171.52 384-384-171.52-384-384-384z m25.6 716.8v-128c0-15.36-10.24-25.6-25.6-25.6s-25.6 10.24-25.6 25.6v128C322.56 806.4 192 675.84 179.2 512h128c15.36 0 25.6-10.24 25.6-25.6s-10.24-25.6-25.6-25.6h-128C192 296.96 322.56 166.4 486.4 156.16V281.6c0 15.36 10.24 25.6 25.6 25.6s25.6-10.24 25.6-25.6V156.16C701.44 168.96 832 299.52 844.8 460.8h-128c-15.36 0-25.6 10.24-25.6 25.6s10.24 25.6 25.6 25.6h128C832 675.84 701.44 806.4 537.6 819.2z" p-id="71130" /></symbol>'});s.a.add(a);t["default"]=a},"22a7":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.buttonStyle")}},[i("el-switch",{model:{value:e.optionModel.buttonStyle,callback:function(t){e.$set(e.optionModel,"buttonStyle",t)},expression:"optionModel.buttonStyle"}})],1)},o=[],l=i("79fa"),s={name:"buttonStyle-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"e14fae56",null);t["default"]=d.exports},2305:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",[i("span",{attrs:{slot:"label"},slot:"label"},[e._v(e._s(e.i18nt("designer.setting.fileTypes"))+" "),i("el-tooltip",{attrs:{effect:"light",content:e.i18nt("designer.setting.fileTypesHelp")}},[i("i",{staticClass:"el-icon-info"})])],1),i("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"","allow-create":"",filterable:"","default-first-option":""},model:{value:e.optionModel.fileTypes,callback:function(t){e.$set(e.optionModel,"fileTypes",t)},expression:"optionModel.fileTypes"}},e._l(e.uploadPictureTypes,(function(e,t){return i("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1)},o=[],l=i("79fa"),s={name:"picture-upload-fileTypes-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},data:function(){return{uploadPictureTypes:[{value:"jpg",label:"jpg"},{value:"jpeg",label:"jpeg"},{value:"png",label:"png"},{value:"gif",label:"gif"}]}}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"651805a2",null);t["default"]=d.exports},2445:function(e,t,i){"use strict";i("e905")},"25e2":function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-radio-field",use:"icon-radio-field-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-radio-field"><defs><style type="text/css"></style></defs><path d="M512 65.983389c-245.919634 0-446.016611 200.095256-446.016611 446.016611 0 245.952318 200.064292 446.016611 446.016611 446.016611S958.016611 757.952318 958.016611 512C958.016611 266.080366 757.952318 65.983389 512 65.983389zM512 894.016611c-210.655557 0-382.016611-171.392017-382.016611-382.016611 0-210.655557 171.359333-382.016611 382.016611-382.016611 210.624593 0 382.016611 171.359333 382.016611 382.016611C894.016611 722.624593 722.624593 894.016611 512 894.016611zM512 352.00086c-88.223841 0-160.00086 71.775299-160.00086 159.99914s71.775299 160.00086 160.00086 160.00086 160.00086-71.775299 160.00086-160.00086S600.223841 352.00086 512 352.00086z" p-id="24993" /></symbol>'});s.a.add(a);t["default"]=a},"25fa":function(e,t,i){"use strict";i("f0c7")},"26a6":function(e,t,i){"use strict";i("b0c0");t["a"]={methods:{initRefList:function(){null!==this.refList&&this.widget.options.name&&(this.refList[this.widget.options.name]=this)},getWidgetRef:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=this.refList[e];return!i&&t&&this.$message.error(this.i18nt("render.hint.refNotFound")+e),i},registerToRefList:function(e){null!==this.refList&&this.widget.options.name&&(e&&delete this.refList[e],this.refList[this.widget.options.name]=this)}}}},"27a8":function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-date-field",use:"icon-date-field-usage",viewBox:"0 0 1132 1024",content:'<symbol class="icon" viewBox="0 0 1132 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-date-field"><defs><style type="text/css"></style></defs><path d="M1023.99488 1023.99488C1023.99488 1023.99488 107.788935 1023.99488 107.788935 1023.99488 48.262496 1023.99488 0 975.732384 0 916.205945 0 916.205945 0 350.314037 0 350.314037 0 350.314037 0 296.419571 0 296.419571 0 296.419571 0 188.630636 0 188.630636 0 129.104196 48.262496 80.841701 107.788935 80.841701 107.788935 80.841701 188.630636 80.841701 188.630636 80.841701 188.630636 80.841701 188.630636 134.736168 188.630636 134.736168 188.630636 134.736168 107.788935 134.736168 107.788935 134.736168 78.012242 134.736168 53.894468 158.853943 53.894468 188.630636 53.894468 188.630636 53.894468 296.419571 53.894468 296.419571 53.894468 296.419571 1077.889348 296.419571 1077.889348 296.419571 1077.889348 296.419571 1077.889348 188.630636 1077.889348 188.630636 1077.889348 158.853943 1053.771573 134.736168 1023.99488 134.736168 1023.99488 134.736168 943.153179 134.736168 943.153179 134.736168 943.153179 134.736168 943.153179 80.841701 943.153179 80.841701 943.153179 80.841701 1023.99488 80.841701 1023.99488 80.841701 1083.521319 80.841701 1131.783815 129.104196 1131.783815 188.630636 1131.783815 188.630636 1131.783815 296.419571 1131.783815 296.419571 1131.783815 296.419571 1131.783815 350.314037 1131.783815 350.314037 1131.783815 350.314037 1131.783815 916.205945 1131.783815 916.205945 1131.783815 975.732384 1083.521319 1023.99488 1023.99488 1023.99488ZM1077.889348 350.314037C1077.889348 350.314037 53.894468 350.314037 53.894468 350.314037 53.894468 350.314037 53.894468 916.205945 53.894468 916.205945 53.894468 945.982638 78.012242 970.100412 107.788935 970.100412 107.788935 970.100412 1023.99488 970.100412 1023.99488 970.100412 1053.771573 970.100412 1077.889348 945.982638 1077.889348 916.205945 1077.889348 916.205945 1077.889348 350.314037 1077.889348 350.314037ZM794.943393 628.086123C794.943393 628.086123 791.359411 633.718094 791.359411 633.718094 788.044902 640.023747 784.27229 645.871297 780.122415 651.449375 780.122415 651.449375 646.733608 861.476114 646.733608 861.476114 646.733608 861.476114 589.120422 861.476114 589.120422 861.476114 589.120422 861.476114 687.477826 700.601129 687.477826 700.601129 687.370037 700.601129 687.262248 700.628076 687.154459 700.628076 620.190583 700.628076 565.891908 640.29322 565.891908 565.891908 565.891908 491.490596 620.190583 431.155739 687.154459 431.155739 754.118334 431.155739 808.417011 491.490596 808.417011 565.891908 808.417011 588.312005 803.027564 609.142217 794.296661 627.735808 794.296661 627.735808 794.943393 628.086123 794.943393 628.086123ZM687.154459 485.050207C649.940329 485.050207 619.786375 521.240341 619.786375 565.891908 619.786375 610.543473 649.940329 646.733608 687.154459 646.733608 706.206153 646.733608 723.317646 637.16734 735.578638 621.915206 735.578638 621.915206 745.360484 605.908549 745.360484 605.908549 751.019403 594.078714 754.522543 580.524255 754.522543 565.891908 754.522543 521.240341 724.368589 485.050207 687.154459 485.050207ZM376.937904 506.93136C376.937904 506.93136 296.203993 579.257736 296.203993 579.257736 296.203993 579.257736 296.203993 512.266913 296.203993 512.266913 296.203993 512.266913 378.608633 431.856367 378.608633 431.856367 378.608633 431.856367 430.670689 431.856367 430.670689 431.856367 430.670689 431.856367 430.670689 861.583903 430.670689 861.583903 430.670689 861.583903 376.937904 861.583903 376.937904 861.583903 376.937904 861.583903 376.937904 506.93136 376.937904 506.93136ZM889.258712 215.577869C874.383839 215.577869 862.311477 203.505508 862.311477 188.630636 862.311477 188.630636 862.311477 26.947233 862.311477 26.947233 862.311477 12.072361 874.383839 0 889.258712 0 904.133584 0 916.205945 12.072361 916.205945 26.947233 916.205945 26.947233 916.205945 188.630636 916.205945 188.630636 916.205945 203.505508 904.133584 215.577869 889.258712 215.577869ZM296.419571 80.841701C296.419571 80.841701 835.364244 80.841701 835.364244 80.841701 835.364244 80.841701 835.364244 134.736168 835.364244 134.736168 835.364244 134.736168 296.419571 134.736168 296.419571 134.736168 296.419571 134.736168 296.419571 80.841701 296.419571 80.841701ZM242.525103 215.577869C227.65023 215.577869 215.577869 203.505508 215.577869 188.630636 215.577869 188.630636 215.577869 26.947233 215.577869 26.947233 215.577869 12.072361 227.65023 0 242.525103 0 257.399976 0 269.472337 12.072361 269.472337 26.947233 269.472337 26.947233 269.472337 188.630636 269.472337 188.630636 269.472337 203.505508 257.399976 215.577869 242.525103 215.577869Z" p-id="47756" /></symbol>'});s.a.add(a);t["default"]=a},"28ce":function(e,t,i){"use strict";i("b574")},"28ed":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.showScore")}},[i("el-switch",{model:{value:e.optionModel.showScore,callback:function(t){e.$set(e.optionModel,"showScore",t)},expression:"optionModel.showScore"}})],1)},o=[],l=i("79fa"),s={name:"showScore-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"1bb4aaaf",null);t["default"]=d.exports},"29a3":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.checkStrictly")}},[i("el-switch",{model:{value:e.optionModel.checkStrictly,callback:function(t){e.$set(e.optionModel,"checkStrictly",t)},expression:"optionModel.checkStrictly"}})],1)},o=[],l=i("79fa"),s={name:"checkStrictly-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"615c8dbf",null);t["default"]=d.exports},"2b47":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.showRowNumber")}},[i("el-switch",{model:{value:e.optionModel.showRowNumber,callback:function(t){e.$set(e.optionModel,"showRowNumber",t)},expression:"optionModel.showRowNumber"}})],1)},o=[],l=i("79fa"),s={name:"showRowNumber-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"148dc69b",null);t["default"]=d.exports},"2c2c":function(e,t,i){"use strict";i("af9a")},"2c6d":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.hidden")}},[i("el-switch",{model:{value:e.optionModel.hidden,callback:function(t){e.$set(e.optionModel,"hidden",t)},expression:"optionModel.hidden"}})],1)},o=[],l=i("79fa"),s={name:"hidden-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"d798e8e2",null);t["default"]=d.exports},"2d11":function(e,t,i){"use strict";i("b0c0"),i("a434"),i("d3b7"),i("159b"),i("d81d");var n=i("ca00"),o=i("a00a");t["a"]={inject:["refList","formConfig","getGlobalDsv","globalOptionData","globalModel","getOptionData"],computed:{subFormName:function(){return this.parentWidget?this.parentWidget.options.name:""},subFormItemFlag:function(){return!!this.parentWidget&&"sub-form"===this.parentWidget.type},formModel:{cache:!1,get:function(){return this.globalModel.formModel}}},methods:{getPropName:function(){return this.subFormItemFlag&&!this.designState?this.subFormName+"."+this.subFormRowIndex+"."+this.field.options.name:this.field.options.name},initFieldModel:function(){var e=this;if(this.field.formItemFlag){if(this.subFormItemFlag&&!this.designState){var t=this.formModel[this.subFormName];return void 0!==t&&void 0!==t[this.subFormRowIndex]&&void 0!==t[this.subFormRowIndex][this.field.options.name]||void 0===this.field.options.defaultValue?void 0===t[this.subFormRowIndex][this.field.options.name]?(this.fieldModel=null,t[this.subFormRowIndex][this.field.options.name]=null):this.fieldModel=t[this.subFormRowIndex][this.field.options.name]:(this.fieldModel=this.field.options.defaultValue,t[this.subFormRowIndex][this.field.options.name]=this.field.options.defaultValue),setTimeout((function(){e.handleOnChangeForSubForm(e.fieldModel,e.oldFieldValue,t,e.subFormRowId)}),800),this.oldFieldValue=Object(n["d"])(this.fieldModel),void this.initFileList()}void 0===this.formModel[this.field.options.name]&&void 0!==this.field.options.defaultValue?this.fieldModel=this.field.options.defaultValue:void 0===this.formModel[this.field.options.name]?this.formModel[this.field.options.name]=null:this.fieldModel=this.formModel[this.field.options.name],this.oldFieldValue=Object(n["d"])(this.fieldModel),this.initFileList()}},initFileList:function(){"picture-upload"!==this.field.type&&"file-upload"!==this.field.type||!0===this.designState||this.fieldModel&&(Array.isArray(this.fieldModel)?this.fileList=Object(n["d"])(this.fieldModel):this.fileList.splice(0,0,Object(n["d"])(this.fieldModel)))},initEventHandler:function(){var e=this;this.$on("setFormData",(function(t){e.subFormItemFlag||e.setValue(t[e.field.options.name])})),this.$on("field-value-changed",(function(t){if(e.subFormItemFlag){var i=e.formModel[e.subFormName];e.handleOnChangeForSubForm(t[0],t[1],i,e.subFormRowId)}else e.handleOnChange(t[0],t[1])})),this.$on("reloadOptionItems",(function(t){(0===t.length||t.indexOf(e.field.options.name)>-1)&&e.initOptionItems(!0)}))},handleOnCreated:function(){if(this.field.options.onCreated){var e=new Function(this.field.options.onCreated);e.call(this)}},handleOnMounted:function(){if(this.field.options.onMounted){var e=new Function(this.field.options.onMounted);e.call(this)}},registerToRefList:function(e){null!==this.refList&&this.field.options.name&&(this.subFormItemFlag&&!this.designState?(e&&delete this.refList[e+"@row"+this.subFormRowId],this.refList[this.field.options.name+"@row"+this.subFormRowId]=this):(e&&delete this.refList[e],this.refList[this.field.options.name]=this))},unregisterFromRefList:function(){if(null!==this.refList&&this.field.options.name){var e=this.field.options.name;this.subFormItemFlag&&!this.designState?delete this.refList[e+"@row"+this.subFormRowId]:delete this.refList[e]}},initOptionItems:function(e){if(!this.designState&&("radio"===this.field.type||"checkbox"===this.field.type||"select"===this.field.type||"cascader"===this.field.type)){var t=this.getOptionData();t&&t.hasOwnProperty(this.field.options.name)&&(e?this.reloadOptions(t[this.field.options.name]):this.loadOptions(t[this.field.options.name]))}},refreshDefaultValue:function(){!0===this.designState&&void 0!==this.field.options.defaultValue&&(this.fieldModel=this.field.options.defaultValue)},clearFieldRules:function(){this.field.formItemFlag&&this.rules.splice(0,this.rules.length)},buildFieldRules:function(){var e=this;if(this.field.formItemFlag||!this.field.options.hidden){if(this.rules.splice(0,this.rules.length),this.field.options.required&&this.rules.push({required:!0,trigger:["blur"],message:this.field.options.requiredHint||this.i18nt("render.hint.fieldRequired")}),this.field.options.validation){var t=this.field.options.validation;o["a"][t]?this.rules.push({validator:o["a"][t],trigger:["blur","change"],label:this.field.options.label,errorMsg:this.field.options.validationHint}):this.rules.push({validator:o["a"]["regExp"],trigger:["blur","change"],regExp:t,label:this.field.options.label,errorMsg:this.field.options.validationHint})}if(this.field.options.onValidate){var i=function(t,i,n){var o=new Function("rule","value","callback",e.field.options.onValidate);return o.call(e,t,i,n)};this.rules.push({validator:i,trigger:["blur","change"],label:this.field.options.label})}}},disableChangeValidate:function(){this.rules&&this.rules.forEach((function(e){e.trigger&&e.trigger.splice(0,e.trigger.length)}))},enableChangeValidate:function(){this.rules&&this.rules.forEach((function(e){e.trigger&&(e.trigger.push("blur"),e.trigger.push("change"))}))},disableOptionOfList:function(e,t){e&&e.length>0&&e.forEach((function(e){e.value===t&&(e.disabled=!0)}))},enableOptionOfList:function(e,t){e&&e.length>0&&e.forEach((function(e){e.value===t&&(e.disabled=!1)}))},emitFieldDataChange:function(e,t){this.$emit("field-value-changed",[e,t]),this.dispatch("VFormRender","fieldChange",[this.field.options.name,e,t,this.subFormName,this.subFormRowIndex])},syncUpdateFormModel:function(e){if(!this.designState)if(this.subFormItemFlag){var t=this.formModel[this.subFormName]||[{}],i=t[this.subFormRowIndex];i[this.field.options.name]=e}else this.formModel[this.field.options.name]=e},handleChangeEvent:function(e){this.syncUpdateFormModel(e),this.emitFieldDataChange(e,this.oldFieldValue),this.oldFieldValue=Object(n["d"])(e),this.dispatch("VFormRender","fieldValidation",[this.getPropName()])},handleFocusCustomEvent:function(e){if(this.oldFieldValue=Object(n["d"])(this.fieldModel),this.field.options.onFocus){var t=new Function("event",this.field.options.onFocus);t.call(this,e)}},handleBlurCustomEvent:function(e){if(this.field.options.onBlur){var t=new Function("event",this.field.options.onBlur);t.call(this,e)}},handleInputCustomEvent:function(e){if(this.syncUpdateFormModel(e),this.dispatch("VFormRender","fieldValidation",[this.getPropName()]),this.field.options.onInput){var t=new Function("value",this.field.options.onInput);t.call(this,e)}},emitAppendButtonClick:function(){if(!this.designState)if(this.field.options.onAppendButtonClick){var e=new Function(this.field.options.onAppendButtonClick);e.call(this)}else this.dispatch("VFormRender","appendButtonClick",[this])},handleOnChange:function(e,t){if(this.field.options.onChange){var i=new Function("value","oldValue",this.field.options.onChange);i.call(this,e,t)}},handleOnChangeForSubForm:function(e,t,i,n){if(this.field.options.onChange){var o=new Function("value","oldValue","subFormData","rowId",this.field.options.onChange);o.call(this,e,t,i,n)}},handleButtonWidgetClick:function(){if(!this.designState)if(this.field.options.onClick){var e=new Function(this.field.options.onClick);e.call(this)}else this.dispatch("VFormRender","buttonClick",[this])},remoteQuery:function(e){if(this.field.options.onRemoteQuery){var t=new Function("keyword",this.field.options.onRemoteQuery);t.call(this,e)}},getFormRef:function(){return this.refList["v_form_ref"]},getWidgetRef:function(e,t){var i=this.refList[e];return!i&&t&&this.$message.error(this.i18nt("render.hint.refNotFound")+e),i},getFieldEditor:function(){return this.$refs["fieldEditor"]},setValue:function(e){if(this.field.formItemFlag){var t=Object(n["d"])(this.fieldModel);this.fieldModel=e,this.initFileList(),this.syncUpdateFormModel(e),this.emitFieldDataChange(e,t)}},getValue:function(){return this.fieldModel},resetField:function(){if(!this.subFormItemFlag){var e=this.field.options.defaultValue;this.setValue(e),"picture-upload"!==this.field.type&&"file-upload"!==this.field.type||(this.$refs["fieldEditor"].clearFiles(),this.fileList.splice(0,this.fileList.length))}},setWidgetOption:function(e,t){this.field.options.hasOwnProperty(e)&&(this.field.options[e]=t)},setReadonly:function(e){this.field.options.readonly=e},setDisabled:function(e){this.field.options.disabled=e},setAppendButtonVisible:function(e){this.field.options.appendButton=e},setAppendButtonDisabled:function(e){this.field.options.appendButtonDisabled=e},setHidden:function(e){this.field.options.hidden=e,e?this.clearFieldRules():this.buildFieldRules()},setRequired:function(e){this.field.options.required=e,this.buildFieldRules()},setLabel:function(e){this.field.options.label=e},focus:function(){this.getFieldEditor()&&this.getFieldEditor().focus&&this.getFieldEditor().focus()},clearSelectedOptions:function(){"checkbox"!==this.field.type&&"radio"!==this.field.type&&"select"!==this.field.type||("checkbox"===this.field.type||"select"===this.field.type&&this.field.options.multiple?this.fieldModel=[]:this.fieldModel="")},loadOptions:function(e){this.field.options.optionItems=Object(n["d"])(e)},reloadOptions:function(e){this.field.options.optionItems=Object(n["d"])(e)},getOptions:function(){return this.field.options.optionItems},disableOption:function(e){this.disableOptionOfList(this.field.options.optionItems,e)},enableOption:function(e){this.enableOptionOfList(this.field.options.optionItems,e)},setUploadHeader:function(e,t){this.$set(this.uploadHeaders,e,t)},setUploadData:function(e,t){this.$set(this.uploadData,e,t)},setToolbar:function(e){this.customToolbar=e},isSubFormItem:function(){return!!this.parentWidget&&"sub-form"===this.parentWidget.type},addCssClass:function(e){this.field.options.customClass?this.field.options.customClass.push(e):this.field.options.customClass=[e]},removeCssClass:function(e){if(this.field.options.customClass){var t=-1;this.field.options.customClass.map((function(i,n){i===e&&(t=n)})),t>-1&&this.field.options.customClass.splice(t,1)}}}}},"2d9e":function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-card",use:"icon-card-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-card"><defs><style type="text/css"></style></defs><path d="M858.656 864H165.344C109.472 864 64 818.56 64 762.688V261.312C64 205.44 109.472 160 165.344 160h693.312C914.528 160 960 205.44 960 261.312v501.376C960 818.56 914.528 864 858.656 864zM165.344 224C144.736 224 128 240.736 128 261.312v501.376C128 783.264 144.736 800 165.344 800h693.312C879.264 800 896 783.264 896 762.688V261.312C896 240.736 879.264 224 858.656 224H165.344zM800 416H224c-17.664 0-32-14.336-32-32s14.336-32 32-32h576c17.696 0 32 14.336 32 32s-14.304 32-32 32zM320 736h-96c-17.664 0-32-14.304-32-32s14.336-32 32-32h96c17.664 0 32 14.304 32 32s-14.336 32-32 32z" p-id="4166" /></symbol>'});s.a.add(a);t["default"]=a},"2dc5":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.limit")}},[i("el-input-number",{staticClass:"hide-spin-button",staticStyle:{width:"100%"},attrs:{min:1},model:{value:e.optionModel.limit,callback:function(t){e.$set(e.optionModel,"limit",t)},expression:"optionModel.limit"}})],1)},o=[],l=i("79fa"),s={name:"limit-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"41408032",null);t["default"]=d.exports},"2dd9":function(e,t,i){},"2e46":function(e,t,i){"use strict";i("ad7e")},"2ec9":function(e,t,i){"use strict";i("04fd")},"2faa":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("form-item-wrapper",{attrs:{designer:e.designer,field:e.field,rules:e.rules,"design-state":e.designState,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList,"sub-form-row-index":e.subFormRowIndex,"sub-form-col-index":e.subFormColIndex,"sub-form-row-id":e.subFormRowId}},[i("el-rate",{ref:"fieldEditor",attrs:{disabled:e.field.options.disabled,max:e.field.options.max,"low-threshold":e.field.options.lowThreshold,"high-threshold":e.field.options.highThreshold,"allow-half":e.field.options.allowHalf,"show-text":e.field.options.showText,"show-score":e.field.options.showScore},on:{change:e.handleChangeEvent},model:{value:e.fieldModel,callback:function(t){e.fieldModel=t},expression:"fieldModel"}})],1)},o=[],l=(i("a9e3"),i("9eeb")),s=i("c6e3"),a=i("79fa"),r=i("2d11"),d={name:"rate-widget",componentName:"FieldWidget",mixins:[s["a"],r["a"],a["b"]],props:{field:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object,designState:{type:Boolean,default:!1},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""}},components:{FormItemWrapper:l["default"]},inject:["refList","formConfig","globalOptionData","globalModel"],data:function(){return{oldFieldValue:null,fieldModel:null,rules:[]}},computed:{},beforeCreate:function(){},created:function(){this.initFieldModel(),this.registerToRefList(),this.initEventHandler(),this.buildFieldRules(),this.handleOnCreated()},mounted:function(){this.handleOnMounted()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{}},c=d,u=(i("623d"),i("2877")),f=Object(u["a"])(c,n,o,!1,null,"02bf17e4",null);t["default"]=f.exports},"30ac":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.automaticDropdown")}},[i("el-switch",{model:{value:e.optionModel.automaticDropdown,callback:function(t){e.$set(e.optionModel,"automaticDropdown",t)},expression:"optionModel.automaticDropdown"}})],1)},o=[],l=i("79fa"),s={name:"automaticDropdown-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"82662fa2",null);t["default"]=d.exports},"31ab":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.inactiveText")}},[i("el-input",{model:{value:e.optionModel.inactiveText,callback:function(t){e.$set(e.optionModel,"inactiveText",t)},expression:"optionModel.inactiveText"}})],1)},o=[],l=i("79fa"),s={name:"inactiveText-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"03f5012d",null);t["default"]=d.exports},"31b7":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("el-form-item",{attrs:{"label-width":"0"}},[i("el-divider",{staticClass:"custom-divider"},[e._v(e._s(e.i18nt("designer.setting.uploadSetting")))])],1),i("el-form-item",{attrs:{label:e.i18nt("designer.setting.uploadURL")}},[i("el-input",{attrs:{type:"text"},model:{value:e.optionModel.uploadURL,callback:function(t){e.$set(e.optionModel,"uploadURL",t)},expression:"optionModel.uploadURL"}})],1)],1)},o=[],l=i("79fa"),s={name:"uploadURL-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"2b299867",null);t["default"]=d.exports},"329d":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.colOffsetTitle")}},[i("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0,max:24},model:{value:e.optionModel.offset,callback:function(t){e.$set(e.optionModel,"offset",e._n(t))},expression:"optionModel.offset"}})],1)},o=[],l=i("79fa"),s={name:"grid-col-offset-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"b3fd22fe",null);t["default"]=d.exports},"34c5":function(e,t,i){"use strict";i("baf1")},"34f0":function(e,t,i){"use strict";i("b0c0"),i("d3b7"),i("159b"),i("a434"),i("d81d");var n=i("ca00");t["a"]={inject:["getGlobalDsv"],computed:{customClass:function(){return this.widget.options.customClass||""},formModel:{cache:!1,get:function(){return this.globalModel.formModel}}},mounted:function(){this.callSetHidden()},methods:{unregisterFromRefList:function(){if(null!==this.refList&&this.widget.options.name){var e=this.widget.options.name;delete this.refList[e]}},callSetHidden:function(){!0===this.widget.options.hidden&&this.setHidden(!0)},setHidden:function(e){var t=this;this.widget.options.hidden=e;var i=function(i){var n=i.options.name,o=t.getWidgetRef(n);e&&o&&o.clearFieldRules&&o.clearFieldRules(),!e&&o&&o.buildFieldRules&&o.buildFieldRules()};Object(n["t"])(this.widget,i)},activeTab:function(e){var t=this;e>=0&&e<this.widget.tabs.length&&this.widget.tabs.forEach((function(i,n){i.options.active=n===e,n===e&&(t.activeTabName=i.options.name)}))},disableTab:function(e){e>=0&&e<this.widget.tabs.length&&(this.widget.tabs[e].options.disabled=!0)},enableTab:function(e){e>=0&&e<this.widget.tabs.length&&(this.widget.tabs[e].options.disabled=!1)},hideTab:function(e){e>=0&&e<this.widget.tabs.length&&(this.widget.tabs[e].options.hidden=!0)},showTab:function(e){e>=0&&e<this.widget.tabs.length&&(this.widget.tabs[e].options.hidden=!1)},setWidgetOption:function(e,t){this.widget.options.hasOwnProperty(e)&&(this.widget.options[e]=t)},getSubFormRowCount:function(){return this.rowIdData?this.rowIdData.length:0},disableSubFormRow:function(e){var t=this;this.widget.widgetList.forEach((function(i){var n=i.options.name+"@row"+t.rowIdData[e],o=t.getWidgetRef(n);o&&o.setDisabled(!0)}))},enableSubFormRow:function(e){var t=this;this.widget.widgetList.forEach((function(i){var n=i.options.name+"@row"+t.rowIdData[e],o=t.getWidgetRef(n);o&&o.setDisabled(!1)}))},disableSubForm:function(){var e=this;this.rowIdData.length>0&&this.rowIdData.forEach((function(t,i){e.disableSubFormRow(i)})),this.actionDisabled=!0},enableSubForm:function(){var e=this;this.rowIdData.length>0&&this.rowIdData.forEach((function(t,i){e.enableSubFormRow(i)})),this.actionDisabled=!1},resetSubForm:function(){if("sub-form"===this.widget.type){var e=this.formModel[this.widget.options.name];e&&(e.splice(0,e.length),this.rowIdData.splice(0,this.rowIdData.length)),this.widget.options.showBlankRow&&this.addSubFormRow()}},getSubFormValues:function(){if("sub-form"===this.widget.type)return this.formModel[this.widget.options.name];this.$message.error(this.i18nt("render.hint.nonSubFormType"))},setSubFormValues:function(e){},addCssClass:function(e){this.widget.options.customClass?this.widget.options.customClass.push(e):this.widget.options.customClass=[e]},removeCssClass:function(e){if(this.widget.options.customClass){var t=-1;this.widget.options.customClass.map((function(i,n){i===e&&(t=n)})),t>-1&&this.widget.options.customClass.splice(t,1)}}}}},3559:function(e,t,i){},"35c2":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.contentPosition")}},[i("el-select",{model:{value:e.optionModel.contentPosition,callback:function(t){e.$set(e.optionModel,"contentPosition",t)},expression:"optionModel.contentPosition"}},[i("el-option",{attrs:{label:"center",value:"center"}}),i("el-option",{attrs:{label:"left",value:"left"}}),i("el-option",{attrs:{label:"right",value:"right"}})],1)],1)},o=[],l=i("79fa"),s={name:"contentPosition-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"d4bc4820",null);t["default"]=d.exports},"36c7":function(e,t,i){},"36f6":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.labelHidden")}},[i("el-switch",{model:{value:e.optionModel.labelHidden,callback:function(t){e.$set(e.optionModel,"labelHidden",t)},expression:"optionModel.labelHidden"}})],1)},o=[],l=i("79fa"),s={name:"labelHidden-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"086f4433",null);t["default"]=d.exports},"37ab":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticStyle:{display:"none"}})},o=[],l={name:"checkbox-defaultValue-editor",props:{designer:Object,selectedWidget:Object,optionModel:Object}},s=l,a=i("2877"),r=Object(a["a"])(s,n,o,!1,null,"b77e89ba",null);t["default"]=r.exports},"38b9":function(e,t,i){"use strict";i("d6e6")},3918:function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-time-range-field",use:"icon-time-range-field-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-time-range-field"><defs><style type="text/css"></style></defs><path d="M498.596 482.29H345.42v57.308h210.478V274.197h-57.301V482.29z m0 0M577.685 644.985h379.88v57.302h-379.88v-57.302z m0 0M577.685 773.765h379.88v57.307h-379.88v-57.307z m0 0M577.685 902.55h379.88v57.307h-379.88V902.55z m0 0" p-id="46153" /><path d="M102.523 382.29a28.668 28.668 0 0 0 23.367 2.56l190.81-61.886c15.053-4.883 23.298-21.04 18.415-36.09-4.882-15.052-21.04-23.297-36.093-18.415l-123.346 40c15.994-26.117 35.17-50.538 57.37-72.745 73.768-73.767 171.847-114.388 276.169-114.388 104.32 0 202.395 40.622 276.161 114.388S899.77 407.56 899.77 511.882c0 26.428-2.616 52.45-7.71 77.78h58.303c4.465-25.499 6.709-51.47 6.709-77.78 0-60.45-11.846-119.102-35.205-174.336-22.56-53.335-54.85-101.227-95.969-142.35-41.122-41.122-89.017-73.408-142.348-95.968-55.233-23.361-113.89-35.207-174.334-35.207-60.45 0-119.107 11.846-174.337 35.208-53.335 22.56-101.23 54.846-142.35 95.969-23.98 23.98-44.933 50.278-62.727 78.6l-20.738-105.654c-3.043-15.528-18.105-25.642-33.632-22.6-15.528 3.048-25.643 18.105-22.6 33.637l36.103 183.932a28.666 28.666 0 0 0 13.588 19.178z m0 0M126.02 587.942H67.768c5.76 33.679 15.368 66.544 28.79 98.278 22.56 53.334 54.85 101.225 95.972 142.348 41.123 41.123 89.014 73.409 142.349 95.969 54.112 22.888 111.518 34.711 170.668 35.182v-57.324c-102.95-0.941-199.595-41.446-272.5-114.349-55.501-55.502-92.237-124.77-107.027-200.104z m0 0" p-id="46154" /></symbol>'});s.a.add(a);t["default"]=a},3952:function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-rich-editor-field",use:"icon-rich-editor-field-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-rich-editor-field"><defs><style type="text/css"></style></defs><path d="M313.359894 448.093505l319.271534 0 0 31.927153-319.271534 0 0-31.927153Z" p-id="66103" /><path d="M313.359894 583.783907l223.490074 0 0 31.927153-223.490074 0 0-31.927153Z" p-id="66104" /><path d="M313.359894 719.474308l127.708614 0 0 31.927153-127.708614 0 0-31.927153Z" p-id="66105" /><path d="M889.411699 554.808992l-39.954991-39.971363-39.957037-39.940664c-7.357572-7.357572-19.284205-7.357572-26.641777 0L453.157847 804.590871c-2.113127 1.730411-3.749394 4.068665-4.591575 6.873548l-36.488029 121.394814c-1.544169 5.160533-0.016373 10.523681 3.539616 14.078647l0.989537 0.763386 0.77055 0.981351c3.554966 3.554966 8.902764 5.082762 14.062274 3.554966l121.394814-36.494169c2.844791-0.857531 5.214768-2.52552 6.938016-4.677533l329.638649-329.630463C896.761084 574.093197 896.761084 562.152237 889.411699 554.808992zM541.135574 889.75553l-95.197152 28.622898 28.622898-95.235015 255.020184-255.020184 66.598814 66.598814L541.135574 889.75553zM856.112292 574.779835l-46.611597 46.611597-66.590628-66.598814 46.605457-46.596248c3.677762-3.679809 9.641591-3.679809 13.319353-0.016373l26.892487 26.892487 26.383904 26.393113C859.791078 565.145407 859.791078 571.116399 856.112292 574.779835z" p-id="66106" /><path d="M671.874197 224.898143l0-28.933983c0-22.004153-17.904789-39.908942-39.908942-39.908942L314.026066 156.055219c-22.004153 0-39.908942 17.904789-39.908942 39.908942l0 28.933983L169.687704 224.898143l0 643.563408c0 35.261085 28.591175 63.854307 63.854307 63.854307l127.708614 0 0-47.89073-111.745037 0c-17.631566 0-31.927153-14.297634-31.927153-31.927153L217.578434 272.788873l56.538691 0 0 10.974959c0 22.004153 17.904789 39.908942 39.908942 39.908942l317.938166 0c22.004153 0 39.908942-17.904789 39.908942-39.908942l0-10.974959 56.538691 0 0 164.662247 47.89073 0L776.302595 224.898143 671.874197 224.898143zM623.983467 275.782044 322.007855 275.782044l0-71.836095 301.974589 0L623.982444 275.782044z" p-id="66107" /></symbol>'});s.a.add(a);t["default"]=a},"3ad3":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("form-item-wrapper",{attrs:{designer:e.designer,field:e.field,rules:e.rules,"design-state":e.designState,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList,"sub-form-row-index":e.subFormRowIndex,"sub-form-col-index":e.subFormColIndex,"sub-form-row-id":e.subFormRowId}},[i("el-color-picker",{ref:"fieldEditor",attrs:{size:e.field.options.size,disabled:e.field.options.disabled},on:{change:e.handleChangeEvent},model:{value:e.fieldModel,callback:function(t){e.fieldModel=t},expression:"fieldModel"}})],1)},o=[],l=(i("a9e3"),i("9eeb")),s=i("c6e3"),a=i("79fa"),r=i("2d11"),d={name:"color-widget",componentName:"FieldWidget",mixins:[s["a"],r["a"],a["b"]],props:{field:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object,designState:{type:Boolean,default:!1},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""}},components:{FormItemWrapper:l["default"]},inject:["refList","formConfig","globalOptionData","globalModel"],data:function(){return{oldFieldValue:null,fieldModel:null,rules:[]}},computed:{},beforeCreate:function(){},created:function(){this.initFieldModel(),this.registerToRefList(),this.initEventHandler(),this.buildFieldRules(),this.handleOnCreated()},mounted:function(){this.handleOnMounted()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{}},c=d,u=(i("a906"),i("2877")),f=Object(u["a"])(c,n,o,!1,null,"53ad0c08",null);t["default"]=f.exports},"3bc5":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.maxStars")}},[i("el-input-number",{staticClass:"hide-spin-button",staticStyle:{width:"100%"},attrs:{min:1,max:10},model:{value:e.optionModel.max,callback:function(t){e.$set(e.optionModel,"max",t)},expression:"optionModel.max"}})],1)},o=[],l=i("79fa"),s={name:"rate-max-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"59c5a4f2",null);t["default"]=d.exports},"3c10":function(e,t,i){"use strict";i("848c")},"3cd5":function(e,t,i){},"3e10":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.labelAlign")}},[i("el-radio-group",{staticClass:"radio-group-custom",model:{value:e.optionModel.labelAlign,callback:function(t){e.$set(e.optionModel,"labelAlign",t)},expression:"optionModel.labelAlign"}},[i("el-radio-button",{attrs:{label:"label-left-align"}},[e._v(" "+e._s(e.i18nt("designer.setting.leftAlign")))]),i("el-radio-button",{attrs:{label:"label-center-align"}},[e._v(" "+e._s(e.i18nt("designer.setting.centerAlign")))]),i("el-radio-button",{attrs:{label:"label-right-align"}},[e._v(" "+e._s(e.i18nt("designer.setting.rightAlign")))])],1)],1)},o=[],l=i("79fa"),s={name:"sub-form-labelAlign-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=(i("0dee"),i("2877")),d=Object(r["a"])(a,n,o,!1,null,"66fb0270",null);t["default"]=d.exports},"3f28":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:"onUploadError","label-width":"150px"}},[i("el-button",{attrs:{type:"info",icon:"el-icon-edit",plain:"",round:""},on:{click:function(t){return e.editEventHandler("onUploadError",e.eventParams)}}},[e._v(" "+e._s(e.i18nt("designer.setting.addEventHandler")))])],1)},o=[],l=i("79fa"),s=i("7d6c"),a={name:"onUploadError-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},data:function(){return{eventParams:["error","file","fileList"]}}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"a1332e98",null);t["default"]=c.exports},"401c":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.showFileList")}},[i("el-switch",{model:{value:e.optionModel.showFileList,callback:function(t){e.$set(e.optionModel,"showFileList",t)},expression:"optionModel.showFileList"}})],1)},o=[],l=i("79fa"),s={name:"showFileList-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"6901fcd5",null);t["default"]=d.exports},"40fa":function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-text-field",use:"icon-text-field-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-text-field"><defs><style type="text/css"></style></defs><path d="M896 224H128c-35.2 0-64 28.8-64 64v448c0 35.2 28.8 64 64 64h768c35.2 0 64-28.8 64-64V288c0-35.2-28.8-64-64-64z m0 480c0 19.2-12.8 32-32 32H160c-19.2 0-32-12.8-32-32V320c0-19.2 12.8-32 32-32h704c19.2 0 32 12.8 32 32v384z" p-id="12704" /><path d="M224 352c-19.2 0-32 12.8-32 32v256c0 16 12.8 32 32 32s32-12.8 32-32V384c0-16-12.8-32-32-32z" p-id="12705" /></symbol>'});s.a.add(a);t["default"]=a},4120:function(e,t,i){"use strict";i("1bdc")},"423c":function(e,t,i){},"43b3":function(e,t,i){},4415:function(e,t,i){},"447a":function(e,t,i){"use strict";i("6043")},"44fb":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.showText")}},[i("el-switch",{model:{value:e.optionModel.showText,callback:function(t){e.$set(e.optionModel,"showText",t)},expression:"optionModel.showText"}})],1)},o=[],l=i("79fa"),s={name:"showText-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"ed97a906",null);t["default"]=d.exports},4595:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.placeholder")}},[i("el-input",{attrs:{type:"text"},model:{value:e.optionModel.placeholder,callback:function(t){e.$set(e.optionModel,"placeholder",t)},expression:"optionModel.placeholder"}})],1)},o=[],l=i("79fa"),s=i("b2bf"),a={name:"placeholder-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"03f971ba",null);t["default"]=c.exports},"47f1":function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-table",use:"icon-table-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-table"><defs><style type="text/css"></style></defs><path d="M925.586 0H101.369C69.885 0 42.24 28.924 42.24 62.328v902.8c0 33.403 27.645 58.872 59.129 58.872h824.217c31.484 0 56.057-25.469 56.057-58.873V62.328C981.643 28.924 957.198 0 925.586 0zM373.719 735.908V543.932h276.445v191.976z m276.445 42.235v203.494H373.719V778.143z m287.964-276.446h-244.45V298.203h244.45z m-287.964 0H373.719V298.203h276.445z m-319.96 0H85.754V298.203h244.45z m-244.45 42.235h244.45v191.976H85.754z m607.925 0h244.449v191.976h-244.45zM101.369 42.235h824.217c7.807 0 12.542 10.366 12.542 20.093v193.64H85.755V62.328c0-9.727 7.807-20.093 15.614-20.093zM85.755 964.999V778.143h244.449v203.494H101.369c-7.807 0-15.614-6.91-15.614-16.51z m839.83 16.638H693.68V778.143h244.449v186.856c0 9.727-4.607 16.638-12.542 16.638z" p-id="7727" /></symbol>'});s.a.add(a);t["default"]=a},"491c":function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-divider",use:"icon-divider-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-divider"><defs><style type="text/css"></style></defs><path d="M62.5 491.773h899v74.918h-899v-74.918z" p-id="63667" /></symbol>'});s.a.add(a);t["default"]=a},"4a70":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("form-item-wrapper",{attrs:{designer:e.designer,field:e.field,rules:e.rules,"design-state":e.designState,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList,"sub-form-row-index":e.subFormRowIndex,"sub-form-col-index":e.subFormColIndex,"sub-form-row-id":e.subFormRowId}},[i("el-upload",{ref:"fieldEditor",staticClass:"dynamicPseudoAfter",class:{hideUploadDiv:e.uploadBtnHidden},style:e.styleVariables,attrs:{disabled:e.field.options.disabled,action:e.field.options.uploadURL,headers:e.uploadHeaders,data:e.uploadData,"with-credentials":e.field.options.withCredentials,multiple:e.field.options.multipleSelect,"file-list":e.fileList,"show-file-list":e.field.options.showFileList,limit:e.field.options.limit,"on-exceed":e.handleFileExceed,"before-upload":e.beforeFileUpload,"on-success":e.handleFileUpload,"on-error":e.handleUploadError},scopedSlots:e._u([{key:"file",fn:function(t){var n=t.file;return[i("div",{staticClass:"upload-file-list"},[i("span",{staticClass:"upload-file-name",attrs:{title:n.name}},[e._v(e._s(n.name))]),i("a",{attrs:{href:n.url,download:"",target:"_blank"}},[i("i",{staticClass:"el-icon-download file-action",attrs:{title:e.i18nt("render.hint.downloadFile")}})]),e.field.options.disabled?e._e():i("i",{staticClass:"el-icon-delete file-action",attrs:{title:e.i18nt("render.hint.removeFile")},on:{click:function(t){return e.removeUploadFile(n.name,n.url,n.uid)}}})])]}}])},[e.field.options.uploadTip?i("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v(e._s(e.field.options.uploadTip))]):e._e(),i("i",{staticClass:"el-icon-plus avatar-uploader-icon",attrs:{slot:"default"},slot:"default"})])],1)},o=[],l=(i("a9e3"),i("ac1f"),i("5319"),i("b0c0"),i("d3b7"),i("a434"),i("159b"),i("9eeb")),s=i("c6e3"),a=i("79fa"),r=i("ca00"),d=i("2d11"),c="'"+Object(a["c"])("render.hint.selectFile")+"'",u={name:"file-upload-widget",componentName:"FieldWidget",mixins:[s["a"],d["a"],a["b"]],props:{field:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object,designState:{type:Boolean,default:!1},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""}},components:{FormItemWrapper:l["default"]},inject:["refList","formConfig","globalOptionData","globalModel"],data:function(){return{oldFieldValue:null,fieldModel:[],rules:[],uploadHeaders:{},uploadData:{key:""},fileList:[],uploadBtnHidden:!1,styleVariables:{"--select-file-action":c}}},computed:{},beforeCreate:function(){},created:function(){this.initFieldModel(),this.registerToRefList(),this.initEventHandler(),this.buildFieldRules(),this.handleOnCreated()},mounted:function(){this.handleOnMounted()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{handleFileExceed:function(){var e=this.field.options.limit;this.$message.warning(this.i18nt("render.hint.uploadExceed").replace("${uploadLimit}",e))},beforeFileUpload:function(e){var t=!1,i=e.name.substring(e.name.lastIndexOf(".")+1);if(this.field.options&&this.field.options.fileTypes){var n=this.field.options.fileTypes;n.length>0&&(t=n.some((function(e){return i.toLowerCase()===e.toLowerCase()})))}if(!t)return this.$message.error(this.i18nt("render.hint.unsupportedFileType")+i),!1;var o=!1,l=5;return this.field.options&&this.field.options.fileMaxSize&&(l=this.field.options.fileMaxSize),o=e.size/1024/1024<=l,o?(this.uploadData.key=e.name,this.handleOnBeforeUpload(e)):(this.$message.error(this.i18nt("render.hint.fileSizeExceed")+l+"MB"),!1)},handleOnBeforeUpload:function(e){if(this.field.options.onBeforeUpload){var t=new Function("file",this.field.options.onBeforeUpload),i=t.call(this,e);return"boolean"!==typeof i||i}return!0},updateFieldModelAndEmitDataChangeForUpload:function(e,t,i){var n=Object(r["d"])(this.fieldModel);t&&t.name&&t.url?this.fieldModel.push({name:t.name,url:t.url}):i&&i.name&&i.url?this.fieldModel.push({name:i.name,url:i.url}):this.fieldModel=Object(r["d"])(e),this.syncUpdateFormModel(this.fieldModel),this.emitFieldDataChange(this.fieldModel,n)},handleFileUpload:function(e,t,i){if("success"===t.status){var n=null;if(this.field.options.onUploadSuccess){var o=new Function("result","file","fileList",this.field.options.onUploadSuccess);n=o.call(this,e,t,i)}this.updateFieldModelAndEmitDataChangeForUpload(i,n,e),n&&n.name?t.name=n.name:t.name=t.name||e.name||e.fileName||e.filename,n&&n.url?t.url=n.url:t.url=t.url||e.url,this.fileList=Object(r["d"])(i),this.uploadBtnHidden=i.length>=this.field.options.limit}},updateFieldModelAndEmitDataChangeForRemove:function(e,t){var i=Object(r["d"])(this.fieldModel);this.fieldModel.splice(e,1),this.syncUpdateFormModel(this.fieldModel),this.emitFieldDataChange(this.fieldModel,i)},removeUploadFile:function(e,t,i){var n=-1,o=null;if(this.fileList.forEach((function(l,s){l.name===e&&(l.url===t||i&&l.uid===i)&&(n=s,o=l)})),n>=0&&(this.fileList.splice(n,1),this.updateFieldModelAndEmitDataChangeForRemove(n,this.fileList),this.uploadBtnHidden=this.fileList.length>=this.field.options.limit,this.field.options.onFileRemove)){var l=new Function("file","fileList",this.field.options.onFileRemove);l.call(this,o,this.fileList)}},handleUploadError:function(e,t,i){if(this.field.options.onUploadError){var n=new Function("error","file","fileList",this.field.options.onUploadError);n.call(this,e,t,i)}else this.$message({message:this.i18nt("render.hint.uploadError")+e,duration:3e3,type:"error"})}}},f=u,p=(i("b30d"),i("2877")),m=Object(p["a"])(f,n,o,!1,null,"10d86f80",null);t["default"]=m.exports},"4be5":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.valueFormat")}},[i("el-select",{attrs:{filterable:"","allow-create":""},model:{value:e.optionModel.valueFormat,callback:function(t){e.$set(e.optionModel,"valueFormat",t)},expression:"optionModel.valueFormat"}},[i("el-option",{attrs:{label:"yyyy-MM-dd",value:"yyyy-MM-dd"}}),i("el-option",{attrs:{label:"yyyy-MM-dd HH:mm:ss",value:"yyyy-MM-dd HH:mm:ss"}})],1)],1)},o=[],l=i("79fa"),s={name:"date-range-valueFormat-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"584a5ccb",null);t["default"]=d.exports},"4cde":function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-static-text",use:"icon-static-text-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-static-text"><defs><style type="text/css"></style></defs><path d="M213.333333 160c-4.821333 0-9.472 0.64-13.824 1.792a32 32 0 0 1-16.554666-61.824c9.728-2.56 19.925333-3.968 30.378666-3.968h33.194667a32 32 0 0 1 0 64H213.333333zM347.264 128a32 32 0 0 1 32-32h66.346667a32 32 0 1 1 0 64H379.306667a32 32 0 0 1-32-32z m199.125333 0a32 32 0 0 1 32-32h66.346667a32 32 0 0 1 0 64h-66.346667a32 32 0 0 1-32-32z m199.082667 0a32 32 0 0 1 32-32H810.666667c10.453333 0 20.650667 1.365333 30.378666 3.968a32 32 0 1 1-16.554666 61.866667A53.418667 53.418667 0 0 0 810.666667 160h-33.194667a32 32 0 0 1-32-32z m-606.293333 32.341333a32 32 0 0 1 22.613333 39.168A53.461333 53.461333 0 0 0 160 213.333333v33.194667a32 32 0 0 1-64 0V213.333333c0-10.453333 1.365333-20.650667 3.968-30.378666a32 32 0 0 1 39.168-22.613334z m745.685333 0a32 32 0 0 1 39.168 22.613334c2.56 9.728 3.968 19.925333 3.968 30.378666v33.194667a32 32 0 0 1-64 0V213.333333c0-4.821333-0.64-9.472-1.792-13.824a32 32 0 0 1 22.613333-39.168zM128 347.221333a32 32 0 0 1 32 32v66.389334a32 32 0 1 1-64 0V379.306667a32 32 0 0 1 32-32z m768 0a32 32 0 0 1 32 32v66.389334a32 32 0 1 1-64 0V379.306667a32 32 0 0 1 32-32zM128 546.432a32 32 0 0 1 32 32v66.346667a32 32 0 0 1-64 0v-66.346667a32 32 0 0 1 32-32z m768 0a32 32 0 0 1 32 32v66.346667a32 32 0 0 1-64 0v-66.346667a32 32 0 0 1 32-32z m0 199.082667a32 32 0 0 1 32 32V810.666667c0 10.453333-1.365333 20.650667-3.968 30.378666a32 32 0 1 1-61.866667-16.554666c1.194667-4.352 1.834667-8.96 1.834667-13.824v-33.194667a32 32 0 0 1 32-32z m-768 0a32 32 0 0 1 32 32V810.666667c0 4.821333 0.64 9.472 1.792 13.824a32 32 0 0 1-61.824 16.512A117.461333 117.461333 0 0 1 96 810.666667v-33.194667a32 32 0 0 1 32-32z m32.341333 139.392a32 32 0 0 1 39.168-22.656c4.352 1.152 8.96 1.792 13.824 1.792h33.194667a32 32 0 0 1 0 64H213.333333c-10.453333 0-20.650667-1.365333-30.378666-3.968a32 32 0 0 1-22.613334-39.168z m703.317334 0a32 32 0 0 1-22.613334 39.168c-9.728 2.56-19.925333 3.968-30.378666 3.968h-33.194667a32 32 0 0 1 0-64H810.666667c4.821333 0 9.472-0.64 13.824-1.792a32 32 0 0 1 39.168 22.613333zM347.306667 896a32 32 0 0 1 32-32h66.346666a32 32 0 1 1 0 64H379.306667a32 32 0 0 1-32-32z m199.125333 0a32 32 0 0 1 32-32h66.346667a32 32 0 0 1 0 64h-66.346667a32 32 0 0 1-32-32zM341.333333 352a32 32 0 0 0 0 64h138.666667V682.666667a32 32 0 0 0 64 0V416H682.666667a32 32 0 0 0 0-64H341.333333z" p-id="55447" /></symbol>'});s.a.add(a);t["default"]=a},"4daf":function(e,t,i){},"4e3d":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:"onBlur","label-width":"150px"}},[i("el-button",{attrs:{type:"info",icon:"el-icon-edit",plain:"",round:""},on:{click:function(t){return e.editEventHandler("onBlur",e.eventParams)}}},[e._v(" "+e._s(e.i18nt("designer.setting.addEventHandler")))])],1)},o=[],l=i("79fa"),s=i("7d6c"),a={name:"onBlur-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},data:function(){return{eventParams:["event"]}}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"ab3f95fc",null);t["default"]=c.exports},"4e94":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.format")}},[i("el-select",{attrs:{filterable:"","allow-create":""},model:{value:e.optionModel.format,callback:function(t){e.$set(e.optionModel,"format",t)},expression:"optionModel.format"}},[i("el-option",{attrs:{label:"yyyy-MM-dd",value:"yyyy-MM-dd"}}),i("el-option",{attrs:{label:"yyyy/MM/dd",value:"yyyy/MM/dd"}}),i("el-option",{attrs:{label:"yyyy年MM月dd日",value:"yyyy年MM月dd日"}}),i("el-option",{attrs:{label:"yyyy-MM-dd HH:mm:ss",value:"yyyy-MM-dd HH:mm:ss"}}),i("el-option",{attrs:{label:"yyyy-MM-dd hh:mm:ss",value:"yyyy-MM-dd hh:mm:ss"}})],1)],1)},o=[],l=i("79fa"),s={name:"date-range-format-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"04dfb3ed",null);t["default"]=d.exports},"4ed4":function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-button",use:"icon-button-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-button"><defs><style type="text/css"></style></defs><path d="M912 176v416h-179.52v-32h147.52v-352h-736v352h175.488v32H112v-416z" p-id="63465" /><path d="M436.384 788.512l0.544 2.688a16 16 0 0 0 27.776 5.504l44.032-54.336 56.768 97.664a16 16 0 0 0 21.792 5.856l68.672-39.392 2.368-1.664a16 16 0 0 0 3.52-20.256l-55.904-96.16 68.8-12.064a16 16 0 0 0 6.464-28.8l-256-180.64a16 16 0 0 0-25.12 14.976l36.288 306.624z" p-id="63466" /></symbol>'});s.a.add(a);t["default"]=a},"4ef4":function(e,t,i){},"4eff":function(e,t,i){},5112:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.remote")}},[i("el-switch",{on:{change:e.onRemoteChange},model:{value:e.optionModel.remote,callback:function(t){e.$set(e.optionModel,"remote",t)},expression:"optionModel.remote"}})],1)},o=[],l=i("79fa"),s=i("b2bf"),a={name:"remote-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"83c35cae",null);t["default"]=c.exports},"51ff":function(e,t,i){var n={"./alert.svg":"f2db","./button.svg":"4ed4","./card.svg":"2d9e","./cascader-field.svg":"5fe5","./checkbox-field.svg":"f582","./color-field.svg":"5bc5","./custom-component.svg":"c885","./data-table.svg":"6592","./date-field.svg":"27a8","./date-range-field.svg":"6947","./divider.svg":"491c","./document.svg":"fd28","./drag.svg":"9bbf","./file-upload-field.svg":"d119","./github.svg":"558d","./grid.svg":"d8ec","./html-text.svg":"89ef","./node-tree.svg":"e486","./number-field.svg":"9b6e","./picture-upload-field.svg":"15c6","./radio-field.svg":"25e2","./rate-field.svg":"f250","./redo.svg":"18ba","./rich-editor-field.svg":"3952","./section.svg":"e934","./select-field.svg":"b8d7","./slider-field.svg":"7007","./slot-component.svg":"20e0","./slot-field.svg":"ecf2","./static-text.svg":"4cde","./sub-form.svg":"db13","./switch-field.svg":"1895","./tab.svg":"8fb7","./table.svg":"47f1","./text-field.svg":"40fa","./textarea-field.svg":"d2e4","./time-field.svg":"1d42","./time-range-field.svg":"3918","./undo.svg":"612b","./vue-sfc.svg":"8740"};function o(e){var t=l(e);return i(t)}function l(e){if(!i.o(n,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}o.keys=function(){return Object.keys(n)},o.resolve=l,e.exports=o,o.id="51ff"},5479:function(e,t,i){"use strict";i("f355")},"54cf":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.rows")}},[i("el-input-number",{staticStyle:{width:"100%"},model:{value:e.optionModel.rows,callback:function(t){e.$set(e.optionModel,"rows",t)},expression:"optionModel.rows"}})],1)},o=[],l=i("79fa"),s={name:"rows-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"99e99dee",null);t["default"]=d.exports},"54d3":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.controlsPosition")}},[i("el-select",{model:{value:e.optionModel.controlsPosition,callback:function(t){e.$set(e.optionModel,"controlsPosition",t)},expression:"optionModel.controlsPosition"}},[i("el-option",{attrs:{label:"default",value:""}}),i("el-option",{attrs:{label:"right",value:"right"}})],1)],1)},o=[],l=i("79fa"),s={name:"controlsPosition-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"320a7676",null);t["default"]=d.exports},"558d":function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-github",use:"icon-github-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-github"><defs><style type="text/css"></style></defs><path d="M512 0C229.283787 0 0.142041 234.942803 0.142041 524.867683c0 231.829001 146.647305 428.553077 350.068189 497.952484 25.592898 4.819996 34.976961-11.38884 34.976961-25.294314 0-12.45521-0.469203-45.470049-0.725133-89.276559-142.381822 31.735193-172.453477-70.380469-172.453477-70.380469-23.246882-60.569859-56.816233-76.693384-56.816234-76.693385-46.493765-32.58829 3.540351-31.948468 3.540351-31.948467 51.356415 3.71097 78.356923 54.086324 78.356923 54.086324 45.683323 80.19108 119.817417 57.072162 148.993321 43.593236 4.649376-33.91059 17.915029-57.029508 32.50298-70.167195-113.675122-13.222997-233.151301-58.223843-233.1513-259.341366 0-57.285437 19.919806-104.163095 52.678715-140.846248-5.246544-13.265652-22.820334-66.626844 4.990615-138.884127 0 0 42.996069-14.076094 140.760939 53.787741 40.863327-11.644769 84.627183-17.445825 128.177764-17.6591 43.465272 0.213274 87.271782 6.014331 128.135109 17.6591 97.679561-67.906489 140.59032-53.787741 140.59032-53.787741 27.938914 72.257282 10.407779 125.618474 5.118579 138.884127 32.844219 36.683154 52.593405 83.560812 52.593405 140.846248 0 201.586726-119.646798 245.990404-233.663158 258.957473 18.341577 16.208835 34.721032 48.199958 34.721032 97.210357 0 70.167195-0.639822 126.7275-0.639823 143.960051 0 14.033439 9.213443 30.370239 35.190235 25.209005 203.250265-69.527373 349.769606-266.123484 349.769605-497.867175C1023.857959 234.942803 794.673558 0 512 0" fill="#3E75C3" p-id="2491" /></symbol>'});s.a.add(a);t["default"]=a},"566c":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.step")}},[i("el-input-number",{staticClass:"hide-spin-button",staticStyle:{width:"100%"},model:{value:e.optionModel.step,callback:function(t){e.$set(e.optionModel,"step",t)},expression:"optionModel.step"}})],1)},o=[],l=i("79fa"),s={name:"step-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"3dde34f4",null);t["default"]=d.exports},5674:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.cellWidth")}},[i("el-input",{attrs:{type:"text"},model:{value:e.optionModel.cellWidth,callback:function(t){e.$set(e.optionModel,"cellWidth",t)},expression:"optionModel.cellWidth"}})],1)},o=[],l=i("79fa"),s={name:"cellWidth-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"8ddfb426",null);t["default"]=d.exports},"56d7":function(e,t,i){"use strict";i.r(t);i("e260"),i("e6cf"),i("cca6"),i("a79d"),i("db4d"),i("768f");var n=i("a026"),o=i("bc3a"),l=i.n(o),s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{attrs:{id:"app"}},[i("VFormDesigner",{ref:"vfDesignerRef",attrs:{"designer-config":e.designerConfig,"global-dsv":e.globalDsv},scopedSlots:e._u([{key:"customToolButtons",fn:function(){return[i("el-button",{attrs:{type:"text"},on:{click:e.saveJson}},[e._v("保存")])]},proxy:!0}])})],1)},a=[],r=(i("e9c4"),function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-container",{staticClass:"main-container full-height"},[n("el-header",{staticClass:"main-header"},[n("div",{staticClass:"float-left main-title"},[n("img",{attrs:{src:i("d3fb")},on:{click:e.openHome}}),n("span",{staticClass:"bold"},[e._v("VForm")]),e._v(" "+e._s(e.i18nt("application.productTitle"))+" "),n("span",{staticClass:"version-span"},[e._v("Ver "+e._s(e.vFormVersion))])]),n("div",{staticClass:"float-right external-link"},[e.showLink("languageMenu")?n("el-dropdown",{attrs:{"hide-timeout":2e3},on:{command:e.handleLanguageChanged}},[n("span",{staticClass:"el-dropdown-link"},[e._v(e._s(e.curLangName)),n("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),n("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[n("el-dropdown-item",{attrs:{command:"zh-CN"}},[e._v(e._s(e.i18nt("application.zh-CN")))]),n("el-dropdown-item",{attrs:{command:"en-US"}},[e._v(e._s(e.i18nt("application.en-US")))])],1)],1):e._e(),e.showLink("externalLink")?n("a",{attrs:{href:"javascript:void(0)",target:"_blank"},on:{click:function(t){return e.openUrl(t,e.gitUrl)}}},[n("svg-icon",{attrs:{"icon-class":"github"}}),e._v(e._s(e.i18nt("application.github")))],1):e._e(),e.showLink("externalLink")?n("a",{attrs:{href:"javascript:void(0)",target:"_blank"},on:{click:function(t){return e.openUrl(t,e.docUrl)}}},[n("svg-icon",{attrs:{"icon-class":"document"}}),e._v(e._s(e.i18nt("application.document")))],1):e._e(),e.showLink("externalLink")?n("a",{attrs:{href:"javascript:void(0)",target:"_blank"},on:{click:function(t){return e.openUrl(t,e.chatUrl)}}},[e._v(e._s(e.i18nt("application.qqGroup")))]):e._e(),e.showLink("externalLink")?n("a",{attrs:{href:"javascript:void(0)",target:"_blank"},on:{click:function(t){return e.openUrl(t,e.subScribeUrl)}}},[e._v(" "+e._s(e.i18nt("application.subscription"))),n("i",{staticClass:"el-icon-top-right"})]):e._e()],1)]),n("el-container",[n("el-aside",{staticClass:"side-panel"},[n("widget-panel",{attrs:{designer:e.designer}})],1),n("el-container",{staticClass:"center-layout-container"},[n("el-header",{staticClass:"toolbar-header"},[n("toolbar-panel",{ref:"toolbarRef",attrs:{designer:e.designer,"global-dsv":e.globalDsv},scopedSlots:e._u([e._l(e.$slots,(function(t,i){return{key:i,fn:function(){return[e._t(i)]},proxy:!0}}))],null,!0)})],1),n("el-main",{staticClass:"form-widget-main"},[n("el-scrollbar",{staticClass:"container-scroll-bar",style:{height:e.scrollerHeight}},[n("v-form-widget",{ref:"formRef",attrs:{designer:e.designer,"form-config":e.designer.formConfig,"global-dsv":e.globalDsv}})],1)],1)],1),n("el-aside",[n("setting-panel",{attrs:{designer:e.designer,"selected-widget":e.designer.selectedWidget,"form-config":e.designer.formConfig,"global-dsv":e.globalDsv}})],1)],1)],1)}),d=[],c=(i("a434"),i("d3b7"),i("159b"),function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-scrollbar",{staticClass:"side-scroll-bar",style:{height:e.scrollerHeight}},[i("div",{staticClass:"panel-container"},[i("el-tabs",{staticClass:"no-bottom-margin indent-left-margin",model:{value:e.firstTab,callback:function(t){e.firstTab=t},expression:"firstTab"}},[i("el-tab-pane",{attrs:{name:"componentLib"}},[i("span",{attrs:{slot:"label"},slot:"label"},[i("i",{staticClass:"el-icon-set-up"}),e._v(" "+e._s(e.i18nt("designer.componentLib")))]),i("el-collapse",{staticClass:"widget-collapse",model:{value:e.activeNames,callback:function(t){e.activeNames=t},expression:"activeNames"}},[i("el-collapse-item",{attrs:{name:"1",title:e.i18nt("designer.containerTitle")}},[i("draggable",{attrs:{tag:"ul",list:e.containers,group:{name:"dragGroup",pull:"clone",put:!1},clone:e.handleContainerWidgetClone,"ghost-class":"ghost",sort:!1,move:e.checkContainerMove},on:{end:e.onContainerDragEnd}},e._l(e.containers,(function(t,n){return i("li",{key:n,staticClass:"container-widget-item",attrs:{title:t.displayName},on:{dblclick:function(i){return e.addContainerByDbClick(t)}}},[i("span",[i("svg-icon",{attrs:{"icon-class":t.icon,"class-name":"color-svg-icon"}}),e._v(e._s(e.i18n2t("designer.widgetLabel."+t.type,"extension.widgetLabel."+t.type)))],1)])})),0)],1),i("el-collapse-item",{attrs:{name:"2",title:e.i18nt("designer.basicFieldTitle")}},[i("draggable",{attrs:{tag:"ul",list:e.basicFields,group:{name:"dragGroup",pull:"clone",put:!1},move:e.checkFieldMove,clone:e.handleFieldWidgetClone,"ghost-class":"ghost",sort:!1}},e._l(e.basicFields,(function(t,n){return i("li",{key:n,staticClass:"field-widget-item",attrs:{title:t.displayName},on:{dblclick:function(i){return e.addFieldByDbClick(t)}}},[i("span",[i("svg-icon",{attrs:{"icon-class":t.icon,"class-name":"color-svg-icon"}}),e._v(e._s(e.i18n2t("designer.widgetLabel."+t.type,"extension.widgetLabel."+t.type)))],1)])})),0)],1),i("el-collapse-item",{attrs:{name:"3",title:e.i18nt("designer.advancedFieldTitle")}},[i("draggable",{attrs:{tag:"ul",list:e.advancedFields,group:{name:"dragGroup",pull:"clone",put:!1},move:e.checkFieldMove,clone:e.handleFieldWidgetClone,"ghost-class":"ghost",sort:!1}},e._l(e.advancedFields,(function(t,n){return i("li",{key:n,staticClass:"field-widget-item",attrs:{title:t.displayName},on:{dblclick:function(i){return e.addFieldByDbClick(t)}}},[i("span",[i("svg-icon",{attrs:{"icon-class":t.icon,"class-name":"color-svg-icon"}}),e._v(e._s(e.i18n2t("designer.widgetLabel."+t.type,"extension.widgetLabel."+t.type)))],1)])})),0)],1),i("el-collapse-item",{attrs:{name:"4",title:e.i18nt("designer.customFieldTitle")}},[i("draggable",{attrs:{tag:"ul",list:e.customFields,group:{name:"dragGroup",pull:"clone",put:!1},move:e.checkFieldMove,clone:e.handleFieldWidgetClone,"ghost-class":"ghost",sort:!1}},e._l(e.customFields,(function(t,n){return i("li",{key:n,staticClass:"field-widget-item",attrs:{title:t.displayName},on:{dblclick:function(i){return e.addFieldByDbClick(t)}}},[i("span",[i("svg-icon",{attrs:{"icon-class":t.icon,"class-name":"color-svg-icon"}}),e._v(e._s(e.i18n2t("designer.widgetLabel."+t.type,"extension.widgetLabel."+t.type)))],1)])})),0)],1)],1)],1),e.showFormTemplates()?i("el-tab-pane",{staticStyle:{padding:"8px"},attrs:{name:"formLib"}},[i("span",{attrs:{slot:"label"},slot:"label"},[i("i",{staticClass:"el-icon-c-scale-to-original"}),e._v(" "+e._s(e.i18nt("designer.formLib")))]),e._l(e.formTemplates,(function(t,n){return[i("el-card",{key:n,staticClass:"ft-card",attrs:{"bord-style":{padding:"0"},shadow:"hover"}},[i("el-popover",{attrs:{placement:"right",trigger:"hover"}},[i("img",{staticStyle:{width:"200px"},attrs:{slot:"reference",src:t.imgUrl},slot:"reference"}),i("img",{staticStyle:{height:"600px",width:"720px"},attrs:{src:t.imgUrl}})]),i("div",{staticClass:"bottom clear-fix"},[i("span",{staticClass:"ft-title"},[e._v("#"+e._s(n+1)+" "+e._s(t.title))]),i("el-button",{staticClass:"right-button",attrs:{type:"text"},on:{click:function(i){return e.loadFormTemplate(t.jsonUrl)}}},[e._v(" "+e._s(e.i18nt("designer.hint.loadFormTemplate")))])],1)],1)]}))],2):e._e()],1)],1)])}),u=[],f=i("5530"),p=(i("4de4"),i("d81d"),i("b76a")),m=i.n(p),g=[{type:"grid",category:"container",icon:"grid",cols:[],options:{name:"",hidden:!1,gutter:12,colHeight:null,customClass:""}},{type:"table",category:"container",icon:"table",rows:[],options:{name:"",hidden:!1,customClass:""}},{type:"tab",category:"container",icon:"tab",displayType:"border-card",tabs:[],options:{name:"",hidden:!1,customClass:""}},{type:"grid-col",category:"container",icon:"grid-col",internal:!0,widgetList:[],options:{name:"",hidden:!1,span:12,offset:0,push:0,pull:0,responsive:!1,md:12,sm:12,xs:12,customClass:""}},{type:"table-cell",category:"container",icon:"table-cell",internal:!0,widgetList:[],merged:!1,options:{name:"",cellWidth:"",cellHeight:"",colspan:1,rowspan:1,wordBreak:!1,customClass:""}},{type:"tab-pane",category:"container",icon:"tab-pane",internal:!0,widgetList:[],options:{name:"",label:"",hidden:!1,active:!1,disabled:!1,customClass:""}}],h=[{type:"input",icon:"text-field",formItemFlag:!0,options:{name:"",label:"",labelAlign:"",type:"text",defaultValue:"",placeholder:"",columnWidth:"200px",size:"",labelWidth:null,labelHidden:!1,readonly:!1,disabled:!1,hidden:!1,clearable:!0,showPassword:!1,required:!1,requiredHint:"",validation:"",validationHint:"",customClass:"",labelIconClass:null,labelIconPosition:"rear",labelTooltip:null,minLength:null,maxLength:null,showWordLimit:!1,prefixIcon:"",suffixIcon:"",appendButton:!1,appendButtonDisabled:!1,buttonIcon:"el-icon-search",onCreated:"",onMounted:"",onInput:"",onChange:"",onFocus:"",onBlur:"",onValidate:"",onAppendButtonClick:""}},{type:"textarea",icon:"textarea-field",formItemFlag:!0,options:{name:"",label:"",labelAlign:"",rows:3,defaultValue:"",placeholder:"",columnWidth:"200px",size:"",labelWidth:null,labelHidden:!1,readonly:!1,disabled:!1,hidden:!1,required:!1,requiredHint:"",validation:"",validationHint:"",customClass:"",labelIconClass:null,labelIconPosition:"rear",labelTooltip:null,minLength:null,maxLength:null,showWordLimit:!1,onCreated:"",onMounted:"",onInput:"",onChange:"",onFocus:"",onBlur:"",onValidate:""}},{type:"number",icon:"number-field",formItemFlag:!0,options:{name:"",label:"",labelAlign:"",defaultValue:0,placeholder:"",columnWidth:"200px",size:"",labelWidth:null,labelHidden:!1,disabled:!1,hidden:!1,required:!1,requiredHint:"",validation:"",validationHint:"",customClass:"",labelIconClass:null,labelIconPosition:"rear",labelTooltip:null,min:-1e11,max:1e11,precision:0,step:1,controlsPosition:"right",onCreated:"",onMounted:"",onChange:"",onFocus:"",onBlur:"",onValidate:""}},{type:"radio",icon:"radio-field",formItemFlag:!0,options:{name:"",label:"",labelAlign:"",defaultValue:null,columnWidth:"200px",size:"",displayStyle:"inline",buttonStyle:!1,border:!1,labelWidth:null,labelHidden:!1,disabled:!1,hidden:!1,optionItems:[{label:"radio 1",value:1},{label:"radio 2",value:2},{label:"radio 3",value:3}],required:!1,requiredHint:"",validation:"",validationHint:"",customClass:"",labelIconClass:null,labelIconPosition:"rear",labelTooltip:null,onCreated:"",onMounted:"",onChange:"",onValidate:""}},{type:"checkbox",icon:"checkbox-field",formItemFlag:!0,options:{name:"",label:"",labelAlign:"",defaultValue:[],columnWidth:"200px",size:"",displayStyle:"inline",buttonStyle:!1,border:!1,labelWidth:null,labelHidden:!1,disabled:!1,hidden:!1,optionItems:[{label:"check 1",value:1},{label:"check 2",value:2},{label:"check 3",value:3}],required:!1,requiredHint:"",validation:"",validationHint:"",customClass:"",labelIconClass:null,labelIconPosition:"rear",labelTooltip:null,onCreated:"",onMounted:"",onChange:"",onValidate:""}},{type:"select",icon:"select-field",formItemFlag:!0,options:{name:"",label:"",labelAlign:"",defaultValue:"",placeholder:"",columnWidth:"200px",size:"",labelWidth:null,labelHidden:!1,disabled:!1,hidden:!1,clearable:!0,filterable:!1,allowCreate:!1,remote:!1,automaticDropdown:!1,multiple:!1,multipleLimit:0,optionItems:[{label:"select 1",value:1},{label:"select 2",value:2},{label:"select 3",value:3}],required:!1,requiredHint:"",validation:"",validationHint:"",customClass:"",labelIconClass:null,labelIconPosition:"rear",labelTooltip:null,onCreated:"",onMounted:"",onRemoteQuery:"",onChange:"",onFocus:"",onBlur:"",onValidate:""}},{type:"time",icon:"time-field",formItemFlag:!0,options:{name:"",label:"",labelAlign:"",defaultValue:null,placeholder:"",columnWidth:"200px",size:"",labelWidth:null,labelHidden:!1,readonly:!1,disabled:!1,hidden:!1,clearable:!0,editable:!1,format:"HH:mm:ss",required:!1,requiredHint:"",validation:"",validationHint:"",customClass:"",labelIconClass:null,labelIconPosition:"rear",labelTooltip:null,onCreated:"",onMounted:"",onChange:"",onFocus:"",onBlur:"",onValidate:""}},{type:"time-range",icon:"time-range-field",formItemFlag:!0,options:{name:"",label:"",labelAlign:"",defaultValue:null,startPlaceholder:"",endPlaceholder:"",columnWidth:"200px",size:"",labelWidth:null,labelHidden:!1,readonly:!1,disabled:!1,hidden:!1,clearable:!0,editable:!1,format:"HH:mm:ss",required:!1,requiredHint:"",validation:"",validationHint:"",customClass:"",labelIconClass:null,labelIconPosition:"rear",labelTooltip:null,onCreated:"",onMounted:"",onChange:"",onFocus:"",onBlur:"",onValidate:""}},{type:"date",icon:"date-field",formItemFlag:!0,options:{name:"",label:"",labelAlign:"",type:"date",defaultValue:null,placeholder:"",columnWidth:"200px",size:"",labelWidth:null,labelHidden:!1,readonly:!1,disabled:!1,hidden:!1,clearable:!0,editable:!1,format:"yyyy-MM-dd",valueFormat:"yyyy-MM-dd",required:!1,requiredHint:"",validation:"",validationHint:"",customClass:"",labelIconClass:null,labelIconPosition:"rear",labelTooltip:null,onCreated:"",onMounted:"",onChange:"",onFocus:"",onBlur:"",onValidate:""}},{type:"date-range",icon:"date-range-field",formItemFlag:!0,options:{name:"",label:"",labelAlign:"",type:"daterange",defaultValue:null,startPlaceholder:"",endPlaceholder:"",columnWidth:"200px",size:"",labelWidth:null,labelHidden:!1,readonly:!1,disabled:!1,hidden:!1,clearable:!0,editable:!1,format:"yyyy-MM-dd",valueFormat:"yyyy-MM-dd",required:!1,requiredHint:"",validation:"",validationHint:"",customClass:"",labelIconClass:null,labelIconPosition:"rear",labelTooltip:null,onCreated:"",onMounted:"",onChange:"",onFocus:"",onBlur:"",onValidate:""}},{type:"switch",icon:"switch-field",formItemFlag:!0,options:{name:"",label:"",labelAlign:"",defaultValue:null,columnWidth:"200px",labelWidth:null,labelHidden:!1,disabled:!1,hidden:!1,customClass:"",labelIconClass:null,labelIconPosition:"rear",labelTooltip:null,switchWidth:40,activeText:"",inactiveText:"",activeColor:null,inactiveColor:null,onCreated:"",onMounted:"",onChange:"",onValidate:""}},{type:"rate",icon:"rate-field",formItemFlag:!0,options:{name:"",label:"",labelAlign:"",defaultValue:null,columnWidth:"200px",labelWidth:null,labelHidden:!1,disabled:!1,hidden:!1,required:!1,requiredHint:"",validation:"",validationHint:"",customClass:"",labelIconClass:null,labelIconPosition:"rear",labelTooltip:null,max:5,lowThreshold:2,highThreshold:4,allowHalf:!1,showText:!1,showScore:!1,onCreated:"",onMounted:"",onChange:"",onValidate:""}},{type:"color",icon:"color-field",formItemFlag:!0,options:{name:"",label:"",labelAlign:"",defaultValue:null,columnWidth:"200px",size:"",labelWidth:null,labelHidden:!1,disabled:!1,hidden:!1,required:!1,requiredHint:"",validation:"",validationHint:"",customClass:"",labelIconClass:null,labelIconPosition:"rear",labelTooltip:null,onCreated:"",onMounted:"",onChange:"",onValidate:""}},{type:"slider",icon:"slider-field",formItemFlag:!0,options:{name:"",label:"",labelAlign:"",columnWidth:"200px",showStops:!0,size:"",labelWidth:null,labelHidden:!1,disabled:!1,hidden:!1,required:!1,requiredHint:"",validation:"",validationHint:"",customClass:"",labelIconClass:null,labelIconPosition:"rear",labelTooltip:null,min:0,max:100,step:10,range:!1,height:null,onCreated:"",onMounted:"",onChange:"",onValidate:""}},{type:"static-text",icon:"static-text",formItemFlag:!1,options:{name:"",columnWidth:"200px",hidden:!1,textContent:"static text",textAlign:"left",fontSize:"13px",preWrap:!1,customClass:"",onCreated:"",onMounted:""}},{type:"html-text",icon:"html-text",formItemFlag:!1,options:{name:"",columnWidth:"200px",hidden:!1,htmlContent:"<b>html text</b>",customClass:"",onCreated:"",onMounted:""}},{type:"button",icon:"button",formItemFlag:!1,options:{name:"",label:"",columnWidth:"200px",size:"",displayStyle:"block",disabled:!1,hidden:!1,type:"",plain:!1,round:!1,circle:!1,icon:null,customClass:"",onCreated:"",onMounted:"",onClick:""}},{type:"divider",icon:"divider",formItemFlag:!1,options:{name:"",label:"",columnWidth:"200px",direction:"horizontal",contentPosition:"center",hidden:!1,customClass:"",onCreated:"",onMounted:""}}],b=[{type:"picture-upload",icon:"picture-upload-field",formItemFlag:!0,options:{name:"",label:"",labelAlign:"",labelWidth:null,labelHidden:!1,columnWidth:"200px",disabled:!1,hidden:!1,required:!1,requiredHint:"",customRule:"",customRuleHint:"",uploadURL:"",uploadTip:"",withCredentials:!1,multipleSelect:!1,showFileList:!0,limit:3,fileMaxSize:5,fileTypes:["jpg","jpeg","png"],customClass:"",labelIconClass:null,labelIconPosition:"rear",labelTooltip:null,onCreated:"",onMounted:"",onBeforeUpload:"",onUploadSuccess:"",onUploadError:"",onFileRemove:"",onValidate:""}},{type:"file-upload",icon:"file-upload-field",formItemFlag:!0,options:{name:"",label:"",labelAlign:"",labelWidth:null,labelHidden:!1,columnWidth:"200px",disabled:!1,hidden:!1,required:!1,requiredHint:"",customRule:"",customRuleHint:"",uploadURL:"",uploadTip:"",withCredentials:!1,multipleSelect:!1,showFileList:!0,limit:3,fileMaxSize:5,fileTypes:["doc","docx","xls","xlsx"],customClass:"",labelIconClass:null,labelIconPosition:"rear",labelTooltip:null,onCreated:"",onMounted:"",onBeforeUpload:"",onUploadSuccess:"",onUploadError:"",onFileRemove:"",onValidate:""}},{type:"rich-editor",icon:"rich-editor-field",formItemFlag:!0,options:{name:"",label:"",labelAlign:"",placeholder:"",labelWidth:null,labelHidden:!1,columnWidth:"200px",disabled:!1,hidden:!1,required:!1,requiredHint:"",customRule:"",customRuleHint:"",customClass:"",labelIconClass:null,labelIconPosition:"rear",labelTooltip:null,minLength:null,maxLength:null,showWordLimit:!1,onCreated:"",onMounted:"",onValidate:""}},{type:"cascader",icon:"cascader-field",formItemFlag:!0,options:{name:"",label:"",labelAlign:"",defaultValue:"",placeholder:"",size:"",labelWidth:null,labelHidden:!1,columnWidth:"200px",disabled:!1,hidden:!1,clearable:!0,filterable:!1,multiple:!1,checkStrictly:!1,showAllLevels:!0,optionItems:[{label:"select 1",value:1,children:[{label:"child 1",value:11}]},{label:"select 2",value:2},{label:"select 3",value:3}],required:!1,requiredHint:"",customRule:"",customRuleHint:"",customClass:"",labelIconClass:null,labelIconPosition:"rear",labelTooltip:null,onCreated:"",onMounted:"",onChange:"",onFocus:"",onBlur:"",onValidate:""}}],v=[];function w(e){g.push(e)}function y(e){v.push(e)}var x=[{title:"单列表单",imgUrl:"https://ks3-cn-beijing.ksyuncs.com/vform-static/form-samples/t1.png",jsonUrl:"https://ks3-cn-beijing.ksyuncs.com/vform-static/form-samples/json1.txt",description:"表单模板详细说明..."},{title:"多列表单",imgUrl:"https://ks3-cn-beijing.ksyuncs.com/vform-static/form-samples/t2.png",jsonUrl:"https://ks3-cn-beijing.ksyuncs.com/vform-static/form-samples/json2.txt",description:"表单模板详细说明..."},{title:"分组表单",imgUrl:"https://ks3-cn-beijing.ksyuncs.com/vform-static/form-samples/t3.png",jsonUrl:"https://ks3-cn-beijing.ksyuncs.com/vform-static/form-samples/json3.txt",description:"表单模板详细说明..."},{title:"标签页表单",imgUrl:"https://ks3-cn-beijing.ksyuncs.com/vform-static/form-samples/t4.png",jsonUrl:"https://ks3-cn-beijing.ksyuncs.com/vform-static/form-samples/json4.txt",description:"表单模板详细说明..."},{title:"主从表单",imgUrl:"https://ks3-cn-beijing.ksyuncs.com/vform-static/form-samples/t5.png",jsonUrl:"https://ks3-cn-beijing.ksyuncs.com/vform-static/form-samples/json5.txt",description:"表单模板详细说明..."},{title:"响应式表单",imgUrl:"https://ks3-cn-beijing.ksyuncs.com/vform-static/form-samples/t6.png",jsonUrl:"https://ks3-cn-beijing.ksyuncs.com/vform-static/form-samples/json6.txt",description:"表单模板详细说明..."},{title:"问卷调查表",imgUrl:"https://ks3-cn-beijing.ksyuncs.com/vform-static/form-samples/t7.png",jsonUrl:"https://ks3-cn-beijing.ksyuncs.com/vform-static/form-samples/json7.txt",description:"表单模板详细说明..."},{title:"固定表格表单",imgUrl:"https://ks3-cn-beijing.ksyuncs.com/vform-static/form-samples/t8.png",jsonUrl:"https://ks3-cn-beijing.ksyuncs.com/vform-static/form-samples/json8.txt",description:"表单模板详细说明..."}],C=i("ca00"),_=i("79fa"),O=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("svg",{class:e.svgClass,attrs:{"aria-hidden":"true"}},[i("use",{attrs:{"xlink:href":e.iconName}}),e.title?i("title",[e._v(e._s(e.title))]):e._e()])},F=[],M={name:"SvgIcon",props:{iconClass:{type:String,required:!0},className:{type:String},title:{type:String,default:""}},computed:{iconName:function(){return"#icon-".concat(this.iconClass)},svgClass:function(){return this.className?"svg-icon "+this.className:"svg-icon"}}},j=M,L=(i("d53b"),i("2877")),k=Object(L["a"])(j,O,F,!1,null,"1a7581a2",null),W=k.exports,S={name:"FieldPanel",mixins:[_["b"]],components:{Draggable:m.a,SvgIcon:W},props:{designer:Object},inject:["getBannedWidgets","getDesignerConfig"],data:function(){return{designerConfig:this.getDesignerConfig(),firstTab:"componentLib",scrollerHeight:0,activeNames:["1","2","3","4"],containers:g,basicFields:h,advancedFields:b,customFields:v,formTemplates:x}},computed:{},mounted:function(){var e=this;this.loadWidgets(),this.scrollerHeight=window.innerHeight-56+"px",Object(C["a"])((function(){e.$nextTick((function(){e.scrollerHeight=window.innerHeight-56+"px"}))}))},methods:{isBanned:function(e){return this.getBannedWidgets().indexOf(e)>-1},showFormTemplates:function(){return void 0===this.designerConfig["formTemplates"]||!!this.designerConfig["formTemplates"]},loadWidgets:function(){var e=this;this.containers=this.containers.map((function(t){return Object(f["a"])(Object(f["a"])({},t),{},{displayName:e.i18n2t("designer.widgetLabel.".concat(t.type),"extension.widgetLabel.".concat(t.type))})})).filter((function(t){return!t.internal&&!e.isBanned(t.type)})),this.basicFields=this.basicFields.map((function(t){return Object(f["a"])(Object(f["a"])({},t),{},{displayName:e.i18n2t("designer.widgetLabel.".concat(t.type),"extension.widgetLabel.".concat(t.type))})})).filter((function(t){return!e.isBanned(t.type)})),this.advancedFields=this.advancedFields.map((function(t){return Object(f["a"])(Object(f["a"])({},t),{},{displayName:e.i18n2t("designer.widgetLabel.".concat(t.type),"extension.widgetLabel.".concat(t.type))})})).filter((function(t){return!e.isBanned(t.type)})),this.customFields=this.customFields.map((function(t){return Object(f["a"])(Object(f["a"])({},t),{},{displayName:e.i18n2t("designer.widgetLabel.".concat(t.type),"extension.widgetLabel.".concat(t.type))})})).filter((function(t){return!e.isBanned(t.type)}))},handleContainerWidgetClone:function(e){return this.designer.copyNewContainerWidget(e)},handleFieldWidgetClone:function(e){return this.designer.copyNewFieldWidget(e)},checkContainerMove:function(e){return this.designer.checkWidgetMove(e)},checkFieldMove:function(e){return this.designer.checkFieldMove(e)},onContainerDragEnd:function(e){},addContainerByDbClick:function(e){this.designer.addContainerByDbClick(e)},addFieldByDbClick:function(e){this.designer.addFieldByDbClick(e)},loadFormTemplate:function(e){var t=this;this.$confirm(this.i18nt("designer.hint.loadFormTemplateHint"),this.i18nt("render.hint.prompt"),{confirmButtonText:this.i18nt("render.hint.confirm"),cancelButtonText:this.i18nt("render.hint.cancel")}).then((function(){l.a.get(e).then((function(e){var i=!1;"string"===typeof e.data?i=t.designer.loadFormJson(JSON.parse(e.data)):e.data.constructor===Object&&(i=t.designer.loadFormJson(e.data)),i&&t.designer.emitHistoryChange(),t.$message.success(t.i18nt("designer.hint.loadFormTemplateSuccess"))})).catch((function(e){t.$message.error(t.i18nt("designer.hint.loadFormTemplateFailed")+":"+e)}))})).catch((function(e){console.error(e)}))}}},E=S,D=(i("6425"),Object(L["a"])(E,c,u,!1,null,"7c54b0c6",null)),I=D.exports,R=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"toolbar-container"},[i("div",{staticClass:"left-toolbar"},[i("el-button",{attrs:{type:"text",disabled:e.undoDisabled,title:e.i18nt("designer.toolbar.undoHint")},on:{click:e.undoHistory}},[i("svg-icon",{attrs:{"icon-class":"undo"}})],1),i("el-button",{attrs:{type:"text",disabled:e.redoDisabled,title:e.i18nt("designer.toolbar.redoHint")},on:{click:e.redoHistory}},[i("svg-icon",{attrs:{"icon-class":"redo"}})],1),i("el-button-group",{staticStyle:{"margin-left":"20px"}},[i("el-button",{attrs:{type:"PC"===e.layoutType?"info":""},on:{click:function(t){return e.changeLayoutType("PC")}}},[e._v(" "+e._s(e.i18nt("designer.toolbar.pcLayout")))]),i("el-button",{attrs:{type:"Pad"===e.layoutType?"info":""},on:{click:function(t){return e.changeLayoutType("Pad")}}},[e._v(" "+e._s(e.i18nt("designer.toolbar.padLayout")))]),i("el-button",{attrs:{type:"H5"===e.layoutType?"info":""},on:{click:function(t){return e.changeLayoutType("H5")}}},[e._v(" "+e._s(e.i18nt("designer.toolbar.mobileLayout")))])],1),i("el-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"",title:e.i18nt("designer.toolbar.nodeTreeHint")},on:{click:e.showNodeTreeDrawer}},[i("svg-icon",{attrs:{"icon-class":"node-tree"}})],1)],1),i("el-drawer",{staticClass:"node-tree-drawer",attrs:{title:e.i18nt("designer.toolbar.nodeTreeTitle"),direction:"ltr",visible:e.showNodeTreeDrawerFlag,modal:!1,size:280,"destroy-on-close":!0},on:{"update:visible":function(t){e.showNodeTreeDrawerFlag=t}}},[i("el-tree",{ref:"nodeTree",staticClass:"node-tree",attrs:{data:e.nodeTreeData,"node-key":"id","default-expand-all":"","highlight-current":"","icon-class":"el-icon-arrow-right"},on:{"node-click":e.onNodeTreeClick}})],1),i("div",{staticClass:"right-toolbar",style:{width:e.toolbarWidth+"px"}},[i("div",{staticClass:"right-toolbar-con"},[e.showToolButton("clearDesignerButton")?i("el-button",{attrs:{type:"text"},on:{click:e.clearFormWidget}},[i("i",{staticClass:"el-icon-delete"}),e._v(e._s(e.i18nt("designer.toolbar.clear")))]):e._e(),e.showToolButton("previewFormButton")?i("el-button",{attrs:{type:"text"},on:{click:e.previewForm}},[i("i",{staticClass:"el-icon-view"}),e._v(e._s(e.i18nt("designer.toolbar.preview")))]):e._e(),e.showToolButton("importJsonButton")?i("el-button",{attrs:{type:"text"},on:{click:e.importJson}},[e._v(" "+e._s(e.i18nt("designer.toolbar.importJson")))]):e._e(),e.showToolButton("exportJsonButton")?i("el-button",{attrs:{type:"text"},on:{click:e.exportJson}},[e._v(" "+e._s(e.i18nt("designer.toolbar.exportJson")))]):e._e(),e.showToolButton("exportCodeButton")?i("el-button",{attrs:{type:"text"},on:{click:e.exportCode}},[e._v(" "+e._s(e.i18nt("designer.toolbar.exportCode")))]):e._e(),e.showToolButton("generateSFCButton")?i("el-button",{attrs:{type:"text"},on:{click:e.generateSFC}},[i("svg-icon",{attrs:{"icon-class":"vue-sfc"}}),e._v(e._s(e.i18nt("designer.toolbar.generateSFC")))],1):e._e(),e._l(e.$slots,(function(t,i){return[e._t(i)]}))],2)]),e.showPreviewDialogFlag?i("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],staticClass:"small-padding-dialog",attrs:{title:e.i18nt("designer.toolbar.preview"),visible:e.showPreviewDialogFlag,"show-close":!0,"close-on-click-modal":!1,"close-on-press-escape":!1,center:"","destroy-on-close":!0,"append-to-body":!0,width:"75%",fullscreen:"H5"===e.layoutType||"Pad"===e.layoutType},on:{"update:visible":function(t){e.showPreviewDialogFlag=t}}},[i("div",[i("div",{staticClass:"form-render-wrapper",class:["H5"===e.layoutType?"h5-layout":"Pad"===e.layoutType?"pad-layout":""]},[i("VFormRender",{ref:"preForm",attrs:{"form-json":e.formJson,"form-data":e.testFormData,"preview-state":!0,"option-data":e.testOptionData,"global-dsv":e.designerDsv},on:{appendButtonClick:e.testOnAppendButtonClick,buttonClick:e.testOnButtonClick,formChange:e.handleFormChange}})],1)]),i("code-editor",{staticStyle:{display:"none"},model:{value:e.testFunc,callback:function(t){e.testFunc=t},expression:"testFunc"}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:e.getFormData}},[e._v(e._s(e.i18nt("designer.hint.getFormData")))]),i("el-button",{attrs:{type:"primary"},on:{click:e.resetForm}},[e._v(e._s(e.i18nt("designer.hint.resetForm")))]),i("el-button",{attrs:{type:"primary"},on:{click:e.setFormDisabled}},[e._v(e._s(e.i18nt("designer.hint.disableForm")))]),i("el-button",{attrs:{type:"primary"},on:{click:e.setFormEnabled}},[e._v(e._s(e.i18nt("designer.hint.enableForm")))]),i("el-button",{attrs:{type:""},on:{click:function(t){e.showPreviewDialogFlag=!1}}},[e._v(e._s(e.i18nt("designer.hint.closePreview")))]),e._e(),e._e(),e._e(),e._e()],1)],1):e._e(),e.showImportJsonDialogFlag?i("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],staticClass:"small-padding-dialog",attrs:{title:e.i18nt("designer.toolbar.importJson"),visible:e.showImportJsonDialogFlag,"show-close":!0,center:"","append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.showImportJsonDialogFlag=t}}},[i("el-alert",{staticClass:"alert-padding",attrs:{type:"info",title:e.i18nt("designer.hint.importJsonHint"),"show-icon":""}}),i("code-editor",{attrs:{mode:"json",readonly:!1},model:{value:e.importTemplate,callback:function(t){e.importTemplate=t},expression:"importTemplate"}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:e.doJsonImport}},[e._v(" "+e._s(e.i18nt("designer.hint.import")))]),i("el-button",{on:{click:function(t){e.showImportJsonDialogFlag=!1}}},[e._v(" "+e._s(e.i18nt("designer.hint.cancel")))])],1)],1):e._e(),e.showExportJsonDialogFlag?i("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],staticClass:"small-padding-dialog",attrs:{title:e.i18nt("designer.toolbar.exportJson"),visible:e.showExportJsonDialogFlag,"show-close":!0,center:"","append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.showExportJsonDialogFlag=t}}},[i("code-editor",{attrs:{mode:"json",readonly:!0},model:{value:e.jsonContent,callback:function(t){e.jsonContent=t},expression:"jsonContent"}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{staticClass:"copy-json-btn",attrs:{type:"primary","data-clipboard-text":e.jsonRawContent},on:{click:e.copyFormJson}},[e._v(" "+e._s(e.i18nt("designer.hint.copyJson")))]),i("el-button",{on:{click:e.saveFormJson}},[e._v(e._s(e.i18nt("designer.hint.saveFormJson")))]),i("el-button",{attrs:{type:""},on:{click:function(t){e.showExportJsonDialogFlag=!1}}},[e._v(" "+e._s(e.i18nt("designer.hint.closePreview")))])],1)],1):e._e(),e.showExportCodeDialogFlag?i("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],staticClass:"small-padding-dialog",attrs:{title:e.i18nt("designer.toolbar.exportCode"),visible:e.showExportCodeDialogFlag,"show-close":!0,center:"","append-to-body":"",width:"65%","close-on-click-modal":!1,"close-on-press-escape":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.showExportCodeDialogFlag=t}}},[i("el-tabs",{staticClass:"no-box-shadow no-padding",attrs:{type:"border-card"},model:{value:e.activeCodeTab,callback:function(t){e.activeCodeTab=t},expression:"activeCodeTab"}},[i("el-tab-pane",{attrs:{label:"Vue",name:"vue"}},[i("code-editor",{attrs:{mode:"html",readonly:!0,"user-worker":!1},model:{value:e.vueCode,callback:function(t){e.vueCode=t},expression:"vueCode"}})],1),i("el-tab-pane",{attrs:{label:"HTML",name:"html"}},[i("code-editor",{attrs:{mode:"html",readonly:!0,"user-worker":!1},model:{value:e.htmlCode,callback:function(t){e.htmlCode=t},expression:"htmlCode"}})],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{staticClass:"copy-vue-btn",attrs:{type:"primary","data-clipboard-text":e.vueCode},on:{click:e.copyVueCode}},[e._v(" "+e._s(e.i18nt("designer.hint.copyVueCode")))]),i("el-button",{staticClass:"copy-html-btn",attrs:{type:"primary","data-clipboard-text":e.htmlCode},on:{click:e.copyHtmlCode}},[e._v(" "+e._s(e.i18nt("designer.hint.copyHtmlCode")))]),i("el-button",{on:{click:e.saveVueCode}},[e._v(e._s(e.i18nt("designer.hint.saveVueCode")))]),i("el-button",{on:{click:e.saveHtmlCode}},[e._v(e._s(e.i18nt("designer.hint.saveHtmlCode")))]),i("el-button",{attrs:{type:""},on:{click:function(t){e.showExportCodeDialogFlag=!1}}},[e._v(" "+e._s(e.i18nt("designer.hint.closePreview")))])],1)],1):e._e(),e.showFormDataDialogFlag?i("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],staticClass:"dialog-title-light-bg",attrs:{title:e.i18nt("designer.hint.exportFormData"),visible:e.showFormDataDialogFlag,"show-close":!0,center:"","close-on-click-modal":!1,"close-on-press-escape":!1,"destroy-on-close":!0,"append-to-body":!0},on:{"update:visible":function(t){e.showFormDataDialogFlag=t}}},[i("div",{staticStyle:{border:"1px solid #DCDFE6"}},[i("code-editor",{attrs:{mode:"json",readonly:!0},model:{value:e.formDataJson,callback:function(t){e.formDataJson=t},expression:"formDataJson"}})],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{staticClass:"copy-form-data-json-btn",attrs:{type:"primary","data-clipboard-text":e.formDataRawJson},on:{click:e.copyFormDataJson}},[e._v(" "+e._s(e.i18nt("designer.hint.copyFormData")))]),i("el-button",{on:{click:e.saveFormData}},[e._v(e._s(e.i18nt("designer.hint.saveFormData")))]),i("el-button",{attrs:{type:""},on:{click:function(t){e.showFormDataDialogFlag=!1}}},[e._v(" "+e._s(e.i18nt("designer.hint.closePreview")))])],1)]):e._e(),e.showExportSFCDialogFlag?i("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],staticClass:"small-padding-dialog",attrs:{title:e.i18nt("designer.toolbar.generateSFC"),visible:e.showExportSFCDialogFlag,"show-close":!0,center:"","append-to-body":"",width:"65%","close-on-click-modal":!1,"close-on-press-escape":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.showExportSFCDialogFlag=t}}},[i("el-tabs",{staticClass:"no-box-shadow no-padding",attrs:{type:"border-card"},model:{value:e.activeSFCTab,callback:function(t){e.activeSFCTab=t},expression:"activeSFCTab"}},[i("el-tab-pane",{attrs:{label:"Vue2",name:"vue2"}},[i("code-editor",{attrs:{mode:"html",readonly:!0,"user-worker":!1},model:{value:e.sfcCode,callback:function(t){e.sfcCode=t},expression:"sfcCode"}})],1),i("el-tab-pane",{attrs:{label:"Vue3",name:"vue3"}},[i("code-editor",{attrs:{mode:"html",readonly:!0,"user-worker":!1},model:{value:e.sfcCodeV3,callback:function(t){e.sfcCodeV3=t},expression:"sfcCodeV3"}})],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{staticClass:"copy-vue2-sfc-btn",attrs:{type:"primary","data-clipboard-text":e.sfcCode},on:{click:e.copyV2SFC}},[e._v(" "+e._s(e.i18nt("designer.hint.copyVue2SFC")))]),i("el-button",{staticClass:"copy-vue3-sfc-btn",attrs:{type:"primary","data-clipboard-text":e.sfcCodeV3},on:{click:e.copyV3SFC}},[e._v(" "+e._s(e.i18nt("designer.hint.copyVue3SFC")))]),i("el-button",{on:{click:e.saveV2SFC}},[e._v(e._s(e.i18nt("designer.hint.saveVue2SFC")))]),i("el-button",{on:{click:e.saveV3SFC}},[e._v(e._s(e.i18nt("designer.hint.saveVue3SFC")))]),i("el-button",{attrs:{type:""},on:{click:function(t){e.showExportSFCDialogFlag=!1}}},[e._v(" "+e._s(e.i18nt("designer.hint.closePreview")))])],1)],1):e._e()],1)},T=[],P=(i("b0c0"),function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form",{ref:"renderForm",staticClass:"render-form",class:[e.customClass],attrs:{"label-position":e.labelPosition,size:e.size,"label-width":e.labelWidth,"validate-on-rule-change":!1,model:e.formDataModel},nativeOn:{submit:function(e){e.preventDefault()}}},[e._l(e.widgetList,(function(t,n){return["container"===t.category?[i(e.getContainerWidgetName(t),{key:t.id,tag:"component",attrs:{widget:t,"parent-list":e.widgetList,"index-of-parent-list":n,"parent-widget":null},scopedSlots:e._u([e._l(Object.keys(e.$scopedSlots),(function(t){return{key:t,fn:function(i){return[e._t(t,null,null,i)]}}}))],null,!0)})]:[i(e.getWidgetName(t),{key:t.id,tag:"component",attrs:{field:t,"form-model":e.formDataModel,designer:null,"parent-list":e.widgetList,"index-of-parent-list":n,"parent-widget":null},scopedSlots:e._u([e._l(Object.keys(e.$scopedSlots),(function(t){return{key:t,fn:function(i){return[e._t(t,null,null,i)]}}}))],null,!0)})]]}))],2)}),H=[],N=i("2909"),$=(i("b64b"),i("c6e3")),V=(i("ddb0"),i("10ae"));V.keys().map((function(e){var t=V(e).default;n["default"].component(t.name,t)}));var A,B=i("c029"),z={name:"VFormRender",componentName:"VFormRender",mixins:[$["a"],_["b"]],components:Object(f["a"])({},B["a"]),props:{formJson:{type:Object,default:function(){return Object(C["b"])()}},formData:{type:Object,default:function(){return{}}},optionData:{type:Object,default:function(){return{}}},previewState:{type:Boolean,default:!1},globalDsv:{type:Object,default:function(){return{}}}},provide:function(){var e=this;return{refList:this.widgetRefList,sfRefList:this.subFormRefList,formConfig:this.formConfig,getGlobalDsv:function(){return e.globalDsv},globalOptionData:this.optionData,getOptionData:function(){return e.optionData},globalModel:{formModel:this.formDataModel},previewState:this.previewState}},data:function(){return{formJsonObj:this.formJson,formDataModel:{},widgetRefList:{},subFormRefList:{},formId:null,externalComponents:{}}},computed:{formConfig:function(){return this.formJsonObj.formConfig},widgetList:function(){return this.formJsonObj.widgetList},labelPosition:function(){return this.formConfig&&this.formConfig.labelPosition?this.formConfig.labelPosition:"left"},labelWidth:function(){return this.formConfig&&this.formConfig.labelWidth?this.formConfig.labelWidth+"px":"80px"},size:function(){return this.formConfig&&this.formConfig.size?this.formConfig.size:"medium"},customClass:function(){return this.formConfig&&this.formConfig.customClass?this.formConfig.customClass:""}},watch:{},created:function(){this.buildFormModel(this.formJsonObj?this.formJsonObj.widgetList:null),this.initFormObject()},mounted:function(){this.initLocale(),this.handleOnMounted()},methods:{initFormObject:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.formId="vfRender"+Object(C["e"])(),e&&this.insertCustomStyleAndScriptNode(),this.addFieldChangeEventHandler(),this.addFieldValidateEventHandler(),this.registerFormToRefList(),this.handleOnCreated()},getContainerWidgetName:function(e){return"grid"===e.type?"vf-grid-item":e.type+"-item"},getWidgetName:function(e){return e.type+"-widget"},initLocale:function(){var e=localStorage.getItem("v_form_locale")||"zh-CN";this.changeLanguage(e)},insertCustomStyleAndScriptNode:function(){this.formConfig&&this.formConfig.cssCode&&Object(C["j"])(this.formConfig.cssCode,this.previewState?"":this.formId),this.formConfig&&this.formConfig.functions&&Object(C["k"])(this.formConfig.functions,this.previewState?"":this.formId)},buildFormModel:function(e){var t=this;e&&e.length>0&&e.forEach((function(e){t.buildDataFromWidget(e)}))},buildDataFromWidget:function(e){var t=this;if("container"===e.category)if("grid"===e.type)e.cols&&e.cols.length>0&&e.cols.forEach((function(e){t.buildDataFromWidget(e)}));else if("table"===e.type)e.rows&&e.rows.length>0&&e.rows.forEach((function(e){e.cols&&e.cols.length>0&&e.cols.forEach((function(e){t.buildDataFromWidget(e)}))}));else if("tab"===e.type)e.tabs&&e.tabs.length>0&&e.tabs.forEach((function(e){e.widgetList&&e.widgetList.length>0&&e.widgetList.forEach((function(e){t.buildDataFromWidget(e)}))}));else if("sub-form"===e.type){var i=e.options.name;if(this.formData.hasOwnProperty(i)){var n=this.formData[i];this.$set(this.formDataModel,i,Object(C["d"])(n))}else{var o={};e.options.showBlankRow?(e.widgetList.forEach((function(e){e.formItemFlag&&(o[e.options.name]=e.options.defaultValue)})),this.$set(this.formDataModel,i,[o])):this.$set(this.formDataModel,i,[])}}else"grid-col"===e.type||e.type,e.widgetList&&e.widgetList.length>0&&e.widgetList.forEach((function(e){t.buildDataFromWidget(e)}));else if(e.formItemFlag)if(this.formData.hasOwnProperty(e.options.name)){var l=this.formData[e.options.name];this.$set(this.formDataModel,e.options.name,Object(C["d"])(l))}else this.$set(this.formDataModel,e.options.name,e.options.defaultValue)},addFieldChangeEventHandler:function(){var e=this;this.$off("fieldChange"),this.$on("fieldChange",(function(t,i,n,o,l){e.handleFieldDataChange(t,i,n,o,l),e.$emit("formChange",t,i,n,e.formDataModel,o,l)}))},addFieldValidateEventHandler:function(){var e=this;this.$off("fieldValidation"),this.$on("fieldValidation",(function(t){e.$refs.renderForm.validateField(t)}))},registerFormToRefList:function(){this.widgetRefList["v_form_ref"]=this},handleFieldDataChange:function(e,t,i,n,o){if(this.formConfig&&this.formConfig.onFormDataChange){var l=new Function("fieldName","newValue","oldValue","formModel","subFormName","subFormRowIndex",this.formConfig.onFormDataChange);l.call(this,e,t,i,this.formDataModel,n,o)}},handleOnCreated:function(){if(this.formConfig&&this.formConfig.onFormCreated){var e=new Function(this.formConfig.onFormCreated);e.call(this)}},handleOnMounted:function(){if(this.formConfig&&this.formConfig.onFormMounted){var e=new Function(this.formConfig.onFormMounted);e.call(this)}},findWidgetAndSetDisabled:function(e,t){var i=this.getWidgetRef(e);i?i.setDisabled(t):this.findWidgetOfSubFormAndSetDisabled(e,t)},findWidgetOfSubFormAndSetDisabled:function(e,t){var i=this;this.findWidgetNameInSubForm(e).forEach((function(e){var n=i.getWidgetRef(e);n&&n.setDisabled(t)}))},findWidgetAndSetHidden:function(e,t){var i=this.getWidgetRef(e);i?i.setHidden(t):this.findWidgetOfSubFormAndSetHidden(e,t)},findWidgetOfSubFormAndSetHidden:function(e,t){var i=this;this.findWidgetNameInSubForm(e).forEach((function(e){var n=i.getWidgetRef(e);n&&n.setHidden(t)}))},findWidgetNameInSubForm:function(e){var t=[],i=null,n=function(t,n){t.options&&t.options.name===e&&(i=n.options.name)};if(Object(C["s"])(this.widgetList,n),i){var o=this.getWidgetRef(i);if(o){var l=o.getRowIdData();l&&l.length>0&&l.forEach((function(i){t.push(e+"@row"+i)}))}}return t},changeLanguage:function(e){Object(_["a"])(e)},getNativeForm:function(){return this.$refs["renderForm"]},getFormRef:function(){return this},getWidgetRef:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=this.widgetRefList[e];return!i&&t&&this.$message.error(this.i18nt("render.hint.refNotFound")+e),i},clearFormDataModel:function(){for(var e in this.formDataModel)delete this.formDataModel[e]},setFormJson:function(e){var t=this;if(e)if("string"===typeof e||e.constructor===Object){var i=null;if(i="string"===typeof e?JSON.parse(e):e,!i.formConfig||!i.widgetList)return void this.$message.error("Invalid format of form json.");this.clearFormDataModel(),this.buildFormModel(i.widgetList),this.$set(this.formJsonObj,"formConfig",i.formConfig),this._provided.formConfig=i.formConfig,this.$set(this.formJsonObj,"widgetList",i.widgetList),this.insertCustomStyleAndScriptNode(),this.$nextTick((function(){t.initFormObject(!1),t.handleOnMounted()}))}else this.$message.error("Set form json failed.")},reloadOptionData:function(e){var t=[];e&&"string"===typeof e?t=[e]:e&&Array.isArray(e)&&(t=Object(N["a"])(e)),this.broadcast("FieldWidget","reloadOptionItems",[t])},getFormData:function(){var e=this,t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(!t)return this.formDataModel;var i=function(){},n=new window.Promise((function(e,t){i=function(i,n){n?t(n):e(i)}}));return this.$refs["renderForm"].validate((function(t){t?i(e.formDataModel):i(e.formDataModel,e.i18nt("render.hint.validationFailed"))})),n},setFormData:function(e){var t=this;Object.keys(this.formDataModel).forEach((function(i){e&&e.hasOwnProperty(i)&&(t.formDataModel[i]=Object(C["d"])(e[i]))})),this.broadcast("ContainerItem","setFormData",this.formDataModel),this.broadcast("FieldWidget","setFormData",this.formDataModel)},getFieldValue:function(e){var t=this,i=this.getWidgetRef(e);if(i&&i.getValue)return i.getValue();if(!i){var n=[];return this.findWidgetNameInSubForm(e).forEach((function(e){var i=t.getWidgetRef(e);i&&i.getValue&&n.push(i.getValue())})),n}},setFieldValue:function(e,t){var i=this,n=this.getWidgetRef(e);n&&n.setValue&&n.setValue(t),n||this.findWidgetNameInSubForm(e).forEach((function(e){var n=i.getWidgetRef(e);n&&n.setValue&&n.setValue(t)}))},getSubFormValues:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=this.subFormRefList[e];return i.getSubFormValues(t)},setSubFormValues:function(e,t){},disableForm:function(){var e=this,t=Object.keys(this.widgetRefList);t.forEach((function(t){var i=e.getWidgetRef(t);i&&(i.widget&&"sub-form"===i.widget.type?i.disableSubForm():i.setDisabled&&i.setDisabled(!0))}))},enableForm:function(){var e=this,t=Object.keys(this.widgetRefList);t.forEach((function(t){var i=e.getWidgetRef(t);i&&(i.widget&&"sub-form"===i.widget.type?i.enableSubForm():i.setDisabled&&i.setDisabled(!1))}))},resetForm:function(){var e=this,t=Object.keys(this.subFormRefList);t.forEach((function(t){e.subFormRefList[t].resetSubForm&&e.subFormRefList[t].resetSubForm()}));var i=Object.keys(this.widgetRefList);i.forEach((function(t){var i=e.getWidgetRef(t);i&&!i.subFormItemFlag&&i.resetField&&i.resetField()})),this.$nextTick((function(){e.clearValidate()}))},clearValidate:function(e){this.$refs.renderForm.clearValidate(e)},validateForm:function(e){this.$refs["renderForm"].validate((function(t){e(t)}))},validateFields:function(){},disableWidgets:function(e){var t=this;e&&("string"===typeof e?this.findWidgetAndSetDisabled(e,!0):Array.isArray(e)&&e.forEach((function(e){t.findWidgetAndSetDisabled(e,!0)})))},enableWidgets:function(e){var t=this;e&&("string"===typeof e?this.findWidgetAndSetDisabled(e,!1):Array.isArray(e)&&e.forEach((function(e){t.findWidgetAndSetDisabled(e,!1)})))},hideWidgets:function(e){var t=this;e&&("string"===typeof e?this.findWidgetAndSetHidden(e,!0):Array.isArray(e)&&e.forEach((function(e){t.findWidgetAndSetHidden(e,!0)})))},showWidgets:function(e){var t=this;e&&("string"===typeof e?this.findWidgetAndSetHidden(e,!1):Array.isArray(e)&&e.forEach((function(e){t.findWidgetAndSetHidden(e,!1)})))},getFieldWidgets:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return e?Object(C["g"])(e):Object(C["g"])(this.formJsonObj.widgetList)},getContainerWidgets:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return e?Object(C["f"])(e):Object(C["f"])(this.formJsonObj.widgetList)},addEC:function(e,t){this.externalComponents[e]=t},hasEC:function(e){return this.externalComponents.hasOwnProperty(e)},getEC:function(e){return this.externalComponents[e]},getGlobalDsv:function(){return this.globalDsv}}},U=z,J=(i("d312"),Object(L["a"])(U,P,H,!1,null,"5fc8e448",null)),q=J.exports,G=i("9470"),K=i("b311"),Z=i.n(K),Q=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"vue",i=JSON.stringify(e);return"html"===t?'<!DOCTYPE html>\n<html>\n<head>\n\t<meta charset="UTF-8">\n\t<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no" />\n\t<title>VForm Demo</title>\n\t<link rel="stylesheet" href="https://cdn.staticfile.org/element-ui/2.15.7/theme-chalk/index.min.css">\n\t<link rel="stylesheet" href="https://ks3-cn-beijing.ksyun.com/vform2021/VFormRender.css?t=20210720">\n\t<style type="text/css">\n\t</style>\n</head>\n<body>\n\n  <div id="app">\n\t  <v-form-render :form-json="formJson" :form-data="formData" :option-data="optionData" ref="vFormRef">\n    </v-form-render>\n\t  <el-button type="primary" @click="submitForm">Submit</el-button>\n  </div>\n\n<script type="text/javascript">\n  if (!!window.ActiveXObject || "ActiveXObject" in window) { //IE load polyfill.js for Promise\n    var scriptEle = document.createElement("script");\n    scriptEle.type = "text/javascript";\n    scriptEle.src = "https://cdn.bootcss.com/babel-polyfill/6.23.0/polyfill.min.js"\n    document.body.appendChild(scriptEle)\n  }\n<\/script>\n<script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js"><\/script>\n<script src="https://cdn.staticfile.org/element-ui/2.15.7/index.min.js"><\/script>\n<script src="https://ks3-cn-beijing.ksyun.com/vform2021/VFormRender.umd.min.js?t=20210720"><\/script>\n<script>\n\tnew Vue({\n      el: \'#app\',\n      data: {\n        formJson: '.concat(i,",\n        formData: {},\n        optionData: {}\n      },\n      methods: {\n        submitForm() {\n          this.$refs.vFormRef.getFormData().then( function(formData) {\n            // Form Validation OK\n            alert( JSON.stringify(formData) )\n          }).catch( function(error) {\n            // Form Validation Failed\n            alert(error)\n          })\n        }\n      }\n\t});\n<\/script>\n</body>\n</html>"):'<template>\n  <div>\n    <v-form-render :form-json="formJson" :form-data="formData" :option-data="optionData" ref="vFormRef">\n    </v-form-render>\n    <el-button type="primary" @click="submitForm">Submit</el-button>\n  </div>\n</template>\n<script>\n  export default {\n    data() {\n      return {\n        formJson: '.concat(i,",\n        formData: {},\n        optionData: {}\n      }\n    },\n    methods: {\n      submitForm() {\n        this.$refs.vFormRef.getFormData().then(formData => {\n          // Form Validation OK\n          alert( JSON.stringify(formData) )\n        }).catch(error => {\n          // Form Validation failed\n          this.$message.error(error)\n        })\n      }\n    }\n  }\n<\/script>")},X=(i("a15b"),i("99af"),i("71bc")),Y=i("01ea"),ee={html:{indent_size:"2",indent_char:" ",max_preserve_newlines:"-1",preserve_newlines:!1,keep_array_indentation:!1,break_chained_methods:!1,indent_scripts:"separate",brace_style:"end-expand",space_before_conditional:!0,unescape_strings:!1,jslint_happy:!1,end_with_newline:!0,wrap_line_length:"110",indent_inner_html:!0,comma_first:!1,e4x:!0,indent_empty_lines:!0},js:{indent_size:"2",indent_char:" ",max_preserve_newlines:"-1",preserve_newlines:!1,keep_array_indentation:!1,break_chained_methods:!1,indent_scripts:"normal",brace_style:"end-expand",space_before_conditional:!0,unescape_strings:!1,jslint_happy:!0,end_with_newline:!0,wrap_line_length:"110",indent_inner_html:!0,comma_first:!1,e4x:!0,indent_empty_lines:!0},css:{indent_size:"2",indent_char:" ",max_preserve_newlines:"-1",preserve_newlines:!1,keep_array_indentation:!1,break_chained_methods:!1,indent_scripts:"normal",brace_style:"end-expand",space_before_conditional:!0,unescape_strings:!1,jslint_happy:!0,end_with_newline:!0,wrap_line_length:"110",indent_inner_html:!0,comma_first:!1,e4x:!0,indent_empty_lines:!0}};function te(e){A?e(A):Object(C["o"])(Y["b"],(function(){A=beautifier,e(A)}))}var ie=function(e,t){var i=[],n=[],o=[],l=[];Object(C["s"])(t,(function(s){Object(X["b"])(e,t,i)(s),Object(X["d"])(e,t,n)(s),Object(X["c"])(e,t,o)(s),Object(X["e"])(e,t,l)(s)}));var s=Object(X["a"])(e,t),a="  import { defineComponent, toRefs, reactive, getCurrentInstance } from 'vue'\n  \n  export default defineComponent({\n    components: {},\n    props: {},\n    setup() {\n      const state = reactive({\n        ".concat(e.modelName,": {\n          ").concat(i.join("\n"),"\n        },\n        \n        ").concat(e.rulesName,": {\n          ").concat(n.join("\n"),"\n        },\n        \n        ").concat(s.join("\n"),"\n        \n        ").concat(o.join("\n"),"\n        \n        ").concat(l.join("\n"),"\n      })\n    \n      const instance = getCurrentInstance()\n      \n      const submitForm = () => {\n        instance.proxy.$refs['vForm'].validate(valid => {\n          if (!valid) return\n          \n          //TODO: 提交表单\n        })\n      }\n      \n      const resetForm = () => {\n        instance.proxy.$refs['vForm'].resetFields()\n      }\n      \n      return {\n        ...toRefs(state),\n        submitForm,\n        resetForm\n      }\n    }\n  })");return a};function ne(e,t){var i=e.options,n=[];return t&&n.push(t),i.customClass&&i.customClass.length>0&&n.push(i.customClass.join(" ")),n.length>0?'class="'.concat(n.join(" "),'"'):""}var oe={grid:function(e,t){var i=ne(e),n="<el-row ".concat(i,">\n").concat(e.cols.map((function(e){var i=e.options,n=i.responsive?"":':span="'.concat(i.span,'"'),o=i.responsive?':md="'.concat(i.md,'"'):"",l=i.responsive?':sm="'.concat(i.sm,'"'):"",s=i.responsive?':xs="'.concat(i.xs,'"'):"",a=i.offset?':offset="'.concat(i.offset,'"'):"",r=i.push?':push="'.concat(i.push,'"'):"",d=i.pull?':pull="'.concat(i.pull,'"'):"",c=ne(e,"grid-cell");return"<el-col ".concat(n," ").concat(o," ").concat(l," ").concat(s," ").concat(a," ").concat(r," ").concat(d," ").concat(c,">\n    ").concat(e.widgetList.map((function(e){return"container"===e.category?le(e,t):ue(e,t)})).join(""),"\n    </el-col>")})).join(""),"\n</el-row>");return n},table:function(e,t){var i=ne(e,"table-layout"),n='<div class="table-container">\n  <table '.concat(i,"><tbody>\n  ").concat(e.rows.map((function(e){return"<tr>".concat(e.cols.filter((function(e){return!e.merged})).map((function(e){var i=e.options,n=ne(e,"table-cell"),o=isNaN(i.colspan)||1===i.colspan?"":'colspan="'.concat(i.colspan,'"'),l=isNaN(i.rowspan)||1===i.rowspan?"":'rowspan="'.concat(i.rowspan,'"'),s=[];i.cellWidth&&s.push("width: "+i.cellWidth+" !important"),i.cellHeight&&s.push("height: "+i.cellHeight+" !important");var a=s.length>0?'style="'.concat(s.join(";"),'"'):"";return"<td ".concat(n," ").concat(o," ").concat(l," ").concat(a,">").concat(e.widgetList.map((function(e){return"container"===e.category?le(e,t):ue(e,t)})).join(""),"\n                    </td>")})).join(""),"</tr>")})).join(""),"\n  </tbody></table>\n</div>");return n},tab:function(e,t){var i=ne(e),n=e.tabs&&e.tabs.length>0?'v-model="'.concat(e.options.name,'ActiveTab"'):"",o='<div class="tab-container">\n  <el-tabs '.concat(n,' type="').concat(e.displayType,'" ').concat(i,">\n    ").concat(e.tabs.map((function(e){var i=e.options,n=!0===i.disabled?"disabled":"";return'<el-tab-pane name="'.concat(i.name,'" label="').concat(i.label,'" ').concat(n,">\n        ").concat(e.widgetList.map((function(e){return"container"===e.category?le(e,t):ue(e,t)})).join(""),"</el-tab-pane>")})).join(""),"\n  </el-tabs>\n</div>");return o},"sub-form":function(e,t){}};function le(e,t){return oe[e.type]?oe[e.type](e,t):null}function se(e,t){var i=e.options;return{vModel:'v-model="'.concat(t.modelName,".").concat(i.name,'"'),readonly:i.readonly?'readonly="true"':"",disabled:i.disabled?':disabled="true"':"",size:i.size?'size="'.concat(i.size,'"'):"",type:i.type?'type="'.concat("number"===i.type?"text":i.type,'"'):"",showPassword:i.showPassword?':show-password="'.concat(i.showPassword,'"'):"",placeholder:i.placeholder?'placeholder="'.concat(i.placeholder,'"'):"",rows:Object(C["m"])(i.rows)&&!isNaN(i.rows)?'rows="'.concat(i.rows,'"'):"",clearable:i.clearable?"clearable":"",minlength:Object(C["m"])(i.minLength)&&!isNaN(i.minLength)?':minlength="'.concat(i.minLength,'"'):"",maxlength:Object(C["m"])(i.maxLength)&&!isNaN(i.maxLength)?':maxlength="'.concat(i.maxLength,'"'):"",showWordLimit:i.showWordLimit?':show-word-limit="true"':"",prefixIcon:i.prefixIcon?'prefix-icon="'.concat(i.prefixIcon,'"'):"",suffixIcon:i.suffixIcon?'suffix-icon="'.concat(i.suffixIcon,'"'):"",controlsPosition:"right"===i.controlsPosition?'controls-position="right"':"",min:Object(C["m"])(i.min)&&!isNaN(i.min)?':min="'.concat(i.min,'"'):"",max:Object(C["m"])(i.max)&&!isNaN(i.max)?':max="'.concat(i.max,'"'):"",precision:Object(C["m"])(i.precision)&&!isNaN(i.precision)?':precision="'.concat(i.precision,'"'):"",step:Object(C["m"])(i.step)&&!isNaN(i.step)?':step="'.concat(i.step,'"'):"",filterable:i.filterable?"filterable":"",allowCreate:i.allowCreate?"allow-create":"",defaultFirstOption:i.filterable&&i.allowCreate?"default-first-option":"",multiple:i.multiple?"multiple":"",multipleLimit:!isNaN(i.multipleLimit)&&i.multipleLimit>0?':multiple-limit="'.concat(i.multipleLimit,'"'):"",automaticDropdown:i.automaticDropdown?"automatic-dropdown":"",remote:i.remote?"remote":"",format:i.format?'format="'.concat(i.format,'"'):"",valueFormat:i.valueFormat?'value-format="'.concat(i.valueFormat,'"'):"",editable:i.editable?':editable="'.concat(i.editable,'"'):"",startPlaceholder:i.startPlaceholder?'start-placeholder="'.concat(i.startPlaceholder,'"'):"",endPlaceholder:i.endPlaceholder?'end-placeholder="'.concat(i.endPlaceholder,'"'):"",activeText:i.activeText?'active-text="'.concat(i.activeText,'"'):"",inactiveText:i.inactiveText?'inactive-text="'.concat(i.inactiveText,'"'):"",activeColor:i.activeColor?'active-color="'.concat(i.activeColor,'"'):"",inactiveColor:i.inactiveColor?'inactive-color="'.concat(i.inactiveColor,'"'):"",switchWidth:isNaN(i.switchWidth)||40===i.switchWidth?"":':width="'.concat(i.switchWidth,'"'),rateMax:isNaN(i.max)||5===i.max?"":':max="'.concat(i.max,'"'),lowThreshold:isNaN(i.lowThreshold)||2===i.lowThreshold?"":':low-threshold="'.concat(i.lowThreshold,'"'),highThreshold:isNaN(i.highThreshold)||4===i.highThreshold?"":':high-threshold="'.concat(i.highThreshold,'"'),allowHalf:i.allowHalf?"allow-half":"",showText:i.showText?"show-text":"",showScore:i.showScore?"show-score":"",sliderMin:isNaN(i.min)||0===i.min?"":':min="'.concat(i.min,'"'),sliderMax:isNaN(i.max)||100===i.max?"":':max="'.concat(i.max,'"'),sliderStep:isNaN(i.step)||1===i.step?"":':step="'.concat(i.step,'"'),sliderRange:i.range?"range":"",sliderVertical:i.vertical?"vertical":"",uploadAction:i.uploadURL?'action="'.concat(i.uploadURL,'"'):"",withCredentials:i.withCredentials?"with-credentials":"",multipleSelect:i.multipleSelect?"multiple":"",showFileList:i.showFileList?"show-file-list":"",limit:isNaN(i.limit)?"":':limit="'.concat(i.limit,'"'),uploadTipSlotChild:i.uploadTip?'<template #tip><div class="el-upload__tip">'.concat(i.uploadTip,"</div></template>"):"",pictureUploadIconChild:'<template #default><i class="el-icon-plus"></i></template>',fileUploadIconChild:'<template #default><i class="el-icon-plus"></i></template>',buttonType:i.type?'type="'.concat(i.type,'"'):"",buttonPlain:i.plain?"plain":"",buttonRound:i.round?"round":"",buttonCircle:i.circle?"circle":"",buttonIcon:i.icon?'icon="'.concat(i.icon,'"'):"",contentPosition:i.contentPosition&&"center"!==i.contentPosition?'content-position="'.concat(i.contentPosition,'"'):"",appendButtonChild:i.appendButton?'<template #append><el-button class="'.concat(i.buttonIcon,'" ').concat(i.appendButtonDisabled?"disabled":"","></el-button></template>"):""}}function ae(e,t){var i=e.options,n=i.buttonStyle?"el-radio-button":"el-radio",o=i.border?"border":"",l='style="{display: '.concat(i.displayStyle,'}"');return"<".concat(n,' v-for="(item, index) in ').concat(i.name,'Options" :key="index" :label="item.value"\n          :disabled="item.disabled" ').concat(o," ").concat(l,">{{item.label}}</").concat(n,">")}function re(e,t){var i=e.options,n=i.buttonStyle?"el-checkbox-button":"el-checkbox",o=i.border?"border":"",l='style="{display: '.concat(i.displayStyle,'}"');return"<".concat(n,' v-for="(item, index) in ').concat(i.name,'Options" :key="index" :label="item.value"\n          :disabled="item.disabled" ').concat(o," ").concat(l,">{{item.label}}</").concat(n,">")}function de(e,t){var i=e.options,n="el-option";return"<".concat(n,' v-for="(item, index) in ').concat(i.name,'Options" :key="index" :label="item.label"\n          :value="item.value" :disabled="item.disabled"></').concat(n,">")}var ce={input:function(e,t){var i=se(e,t),n=i.vModel,o=i.readonly,l=i.disabled,s=i.size,a=i.type,r=i.showPassword,d=i.placeholder,c=i.clearable,u=i.minlength,f=i.maxlength,p=i.showWordLimit,m=i.prefixIcon,g=i.suffixIcon,h=i.appendButtonChild;return"<el-input ".concat(n," ").concat(o," ").concat(l," ").concat(s," ").concat(a," ").concat(r," ").concat(d," ").concat(c,"\n            ").concat(u," ").concat(f," ").concat(p," ").concat(m," ").concat(g,">").concat(h,"</el-input>")},textarea:function(e,t){var i=se(e,t),n=i.vModel,o=i.readonly,l=i.disabled,s=i.size,a=i.type,r=i.showPassword,d=i.placeholder,c=i.rows,u=i.clearable,f=i.minlength,p=i.maxlength,m=i.showWordLimit;return'<el-input type="textarea" '.concat(n," ").concat(o," ").concat(l," ").concat(s," ").concat(a," ").concat(r," ").concat(d,"\n            ").concat(c," ").concat(u," ").concat(f," ").concat(p," ").concat(m,"></el-input>")},number:function(e,t){var i=se(e,t),n=i.vModel,o=i.disabled,l=i.size,s=i.type,a=i.showPassword,r=i.placeholder,d=i.controlsPosition,c=i.min,u=i.max,f=i.precision,p=i.step;return"<el-input-number ".concat(n,' class="full-width-input" ').concat(o," ").concat(l," ").concat(s," ").concat(a,"\n            ").concat(r," ").concat(d," ").concat(c," ").concat(u," ").concat(f," ").concat(p,"></el-input-number>")},radio:function(e,t){var i=se(e,t),n=i.vModel,o=i.disabled,l=i.size,s=ae(e,t);return"<el-radio-group ".concat(n," ").concat(o," ").concat(l,">").concat(s,"</el-radio-group>")},checkbox:function(e,t){var i=se(e,t),n=i.vModel,o=i.disabled,l=i.size,s=re(e,t);return"<el-checkbox-group ".concat(n," ").concat(o," ").concat(l,">").concat(s,"</el-checkbox-group>")},select:function(e,t){var i=se(e,t),n=i.vModel,o=i.disabled,l=i.size,s=i.clearable,a=i.filterable,r=i.allowCreate,d=i.defaultFirstOption,c=i.automaticDropdown,u=i.multiple,f=i.multipleLimit,p=i.remote,m=i.placeholder,g=de(e,t);return"<el-select ".concat(n,' class="full-width-input" ').concat(o," ").concat(l," ").concat(s," ").concat(a,"\n            ").concat(r," ").concat(d," ").concat(c," ").concat(u," ").concat(f," ").concat(m,"\n            ").concat(p,">").concat(g,"</el-select>")},time:function(e,t){var i=se(e,t),n=i.vModel,o=i.readonly,l=i.disabled,s=i.size,a=i.placeholder,r=i.clearable,d=i.format,c=i.editable;return"<el-time-picker ".concat(n,' class="full-width-input" ').concat(o," ").concat(l," ").concat(s," ").concat(d,'\n            value-format="HH:mm:ss" ').concat(a," ").concat(r," ").concat(c,"></el-time-picker>")},"time-range":function(e,t){var i=se(e,t),n=i.vModel,o=i.readonly,l=i.disabled,s=i.size,a=i.startPlaceholder,r=i.endPlaceholder,d=i.clearable,c=i.format,u=i.editable;return"<el-time-picker is-range ".concat(n,' class="full-width-input" ').concat(o," ").concat(l," ").concat(s," ").concat(c,'\n            value-format="HH:mm:ss" ').concat(a," ").concat(r," ").concat(d," ").concat(u,"></el-time-picker>")},date:function(e,t){var i=se(e,t),n=i.vModel,o=i.readonly,l=i.disabled,s=i.size,a=i.type,r=i.placeholder,d=i.clearable,c=i.format,u=i.valueFormat,f=i.editable;return"<el-date-picker ".concat(n," ").concat(a,' class="full-width-input" ').concat(o," ").concat(l," ").concat(s," ").concat(c,"\n              ").concat(u," ").concat(r," ").concat(d," ").concat(f,"></el-date-picker>")},"date-range":function(e,t){var i=se(e,t),n=i.vModel,o=i.readonly,l=i.disabled,s=i.size,a=i.type,r=i.startPlaceholder,d=i.endPlaceholder,c=i.clearable,u=i.format,f=i.valueFormat,p=i.editable;return"<el-date-picker is-range ".concat(n," ").concat(a,' class="full-width-input" ').concat(o," ").concat(l," ").concat(s," ").concat(u,"\n            ").concat(f," ").concat(r," ").concat(d," ").concat(c," ").concat(p,"></el-date-picker>")},switch:function(e,t){var i=se(e,t),n=i.vModel,o=i.disabled,l=i.activeText,s=i.inactiveText,a=i.activeColor,r=i.inactiveColor,d=i.switchWidth;return"<el-switch ".concat(n," ").concat(o," ").concat(l," ").concat(s," ").concat(a," ").concat(r,"\n            ").concat(d,"></el-switch>")},rate:function(e,t){var i=se(e,t),n=i.vModel,o=i.disabled,l=i.rateMax,s=i.lowThreshold,a=i.highThreshold,r=i.allowHalf,d=i.showText,c=i.showScore;return"<el-rate ".concat(n," ").concat(o," ").concat(l," ").concat(s," ").concat(a," ").concat(r,"\n            ").concat(d," ").concat(c,"></el-rate>")},color:function(e,t){var i=se(e,t),n=i.vModel,o=i.disabled,l=i.size;return"<el-color-picker ".concat(n," ").concat(o," ").concat(l,"></el-color-picker>")},slider:function(e,t){var i=se(e,t),n=i.vModel,o=i.disabled,l=i.sliderMin,s=i.sliderMax,a=i.sliderStep,r=i.sliderRange,d=i.sliderVertical;return"<el-slider ".concat(n," ").concat(o," ").concat(l," ").concat(s," ").concat(a," ").concat(r,"\n            ").concat(d,"></el-slider>")},"picture-upload":function(e,t){var i=se(e,t),n=(i.vModel,i.disabled),o=i.uploadAction,l=i.withCredentials,s=i.multipleSelect,a=i.showFileList,r=i.limit,d=i.uploadTipSlotChild,c=i.pictureUploadIconChild,u=e.options;return'<el-upload :file-list="'.concat(u.name,'FileList" :headers="').concat(u.name,'UploadHeaders" :data="').concat(u.name,'UploadData" \n            ').concat(n," ").concat(o,' list-type="picture-card" ').concat(l," ").concat(s," ").concat(a," \n            ").concat(r,">").concat(d," ").concat(c,"</el-upload>")},"file-upload":function(e,t){var i=se(e,t),n=(i.vModel,i.disabled),o=i.uploadAction,l=i.withCredentials,s=i.multipleSelect,a=i.showFileList,r=i.limit,d=i.uploadTipSlotChild,c=i.fileUploadIconChild,u=e.options;return'<el-upload :file-list="'.concat(u.name,'FileList" :headers="').concat(u.name,'UploadHeaders" :data="').concat(u.name,'UploadData" \n            ').concat(n," ").concat(o,' list-type="picture-card" ').concat(l," ").concat(s," ").concat(a," \n            ").concat(r,">").concat(d," ").concat(c,"</el-upload>")},"rich-editor":function(e,t){var i=se(e,t),n=i.vModel,o=i.disabled,l=i.placeholder;return"<vue-editor ".concat(n," ").concat(o," ").concat(l,"></vue-editor>")},cascader:function(e,t){var i=se(e,t),n=i.vModel,o=i.disabled,l=i.size,s=i.clearable,a=i.filterable,r=i.placeholder,d=e.options,c=':options="'.concat(d.name,'Options"');return"<el-cascader ".concat(n,' class="full-width-input" ').concat(c," ").concat(o," ").concat(l," ").concat(s,"\n            ").concat(a," ").concat(r,"></el-cascader>")},"static-text":function(e,t){return"<div>".concat(e.options.textContent,"</div>")},"html-text":function(e,t){return'<div v-html="'.concat(e.options.htmlContent,'"></div>')},button:function(e,t){var i=se(e,t),n=i.buttonType,o=i.buttonPlain,l=i.buttonRound,s=i.buttonCircle,a=i.buttonIcon,r=i.disabled;return"<el-button ".concat(n," ").concat(o," ").concat(l," ").concat(s," ").concat(a,"\n            ").concat(r,">").concat(e.options.label,"</el-button>")},divider:function(e,t){var i=se(e,t),n=i.contentPosition;return'<el-divider direction="horizontal" '.concat(n,"></el-divider>")}};function ue(e,t){var i=e.options,n=i.labelHidden?"":i.label,o=i.labelHidden?'label-width="0"':i.labelWidth?'label-width="'.concat(i.labelWidth,'px"'):"",l=i.labelTooltip?'title="'.concat(i.labelTooltip,'"'):"",s='prop="'.concat(i.name,'"'),a=[];i.required&&a.push("required"),i.customClass&&i.customClass.length>0&&a.push(i.customClass.join(" ")),i.labelAlign?"label-left-align"!==i.labelAlign&&a.push(i.labelAlign):e.formItemFlag&&"label-left-align"!==t.labelAlign&&a.push(t.labelAlign),e.formItemFlag||a.push("static-content-item");var r=a.length>0?'class="'.concat(a.join(" "),'"'):"",d='<template #label><span class="custom-label">'.concat("front"===i.labelIconPosition?i.labelTooltip?'<el-tooltip content="'.concat(i.labelTooltip,'" effect="light"><i class="').concat(i.labelIconClass,'"></i></el-tooltip>').concat(i.label):'<i class="'.concat(i.labelIconClass,'"></i>').concat(i.label):i.labelTooltip?"".concat(i.label,'<el-tooltip content="').concat(i.labelTooltip,'" effect="light"><i class="').concat(i.labelIconClass,'"></i></el-tooltip>'):"".concat(i.label,'<i class="').concat(i.labelIconClass,'"></i>'),"\n</span></template>");!i.labelIconClass&&(d="");var c=ce[e.type]?ce[e.type](e,t):null,u=!!e.formItemFlag,f=i.hidden?'v-show="false"':"";return u?'<el-form-item label="'.concat(n,'" ').concat(o," ").concat(l," ").concat(s," ").concat(r,">\n  ").concat(d,"\n  ").concat(c,"\n</el-form-item>"):"<div ".concat(r," ").concat(f,">").concat(c,"</div>")}function fe(e,t){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=i?"@submit.prevent":"@submit.native.prevent",o=[];t.forEach((function(t){"container"===t.category?o.push(le(t,e)):o.push(ue(t,e))}));var l='  <el-form :model="'.concat(e.modelName,'" ref="').concat(e.refName,'" :rules="').concat(e.rulesName,'"\n    label-position="').concat(e.labelPosition,'" label-width="').concat(e.labelWidth,'px" size="').concat(e.size||"medium",'"\n    ').concat(n,">\n  ").concat(o?o.join("\n"):"","\n</el-form>");return l}var pe=function(e){var t='  .el-input-number.full-width-input, .el-cascader.full-width-input {\n    width: 100% !important;\n  }\n  \n  .el-form-item--medium {\n    .el-radio {\n      line-height: 36px !important;\n    }\n  \n    .el-rate{\n      margin-top: 8px;\n    }\n  }\n\n  .el-form-item--small {\n    .el-radio {\n      line-height: 32px !important;\n    }\n  \n    .el-rate{\n      margin-top: 6px;\n    }\n  }\n\n  .el-form-item--mini {\n    .el-radio {\n      line-height: 28px !important;\n    }\n  \n    .el-rate{\n      margin-top: 4px;\n    }\n  }\n  \n  .clear-fix:before, .clear-fix:after {\n    display: table;\n    content: "";\n  }\n\n  .clear-fix:after {\n    clear: both;\n  }\n\n  .float-right {\n    float: right;\n  }\n\n'.concat(e.cssCode);return t},me=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i="  div.table-container {\n    table.table-layout {\n      width: 100%;\n      table-layout: fixed;\n      border-collapse: collapse;\n      \n      td.table-cell {\n        display: table-cell;\n        height: 36px;\n        border: 1px solid #e1e2e3;\n      }\n    }\n  }\n  \n  div.tab-container {\n  }\n  \n  .label-left-align ".concat(t?":deep(.el-form-item__label)":"::v-deep .el-form-item__label"," {\n    text-align: left;\n  }\n\n  .label-center-align ").concat(t?":deep(.el-form-item__label)":"::v-deep .el-form-item__label"," {\n    text-align: center;\n  }\n\n  .label-right-align ").concat(t?":deep(.el-form-item__label)":"::v-deep .el-form-item__label"," {\n    text-align: right;\n  }\n  \n  .custom-label {\n  }\n  \n  .static-content-item {\n    min-height: 20px;\n    display: flex;\n    align-items: center;\n\n    ").concat(t?":deep(.el-divider--horizontal)":"::v-deep .el-divider--horizontal"," {\n      margin: 0;\n    }\n  }");return i},ge=function(e,t){oe[e]=t},he=function(e,t){ce[e]=t},be=function(e,t,i){var n=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=i.html(fe(e,t,n),ee.html),l=i.js(n?ie(e,t):Object(X["f"])(e,t),ee.js),s=i.css(pe(e),ee.css),a=i.css(me(e,n),ee.css);return"\x3c!-- \nCodes Generated By VForm:\nhttps://www.vform666.com\n--\x3e\n\n<template>\n".concat(o,"\n</template>\n\n<script>\n").concat(l,'\n<\/script>\n\n<style lang="scss">\n').concat(s,'\n</style>\n\n<style lang="scss" scoped>\n').concat(a,"\n</style>")},ve=i("21a6"),we={name:"ToolbarPanel",mixins:[_["b"]],components:{VFormRender:q,CodeEditor:G["a"],Clipboard:Z.a,SvgIcon:W},props:{designer:Object,globalDsv:{type:Object,default:function(){return{}}}},inject:["getDesignerConfig"],data:function(){return{designerConfig:this.getDesignerConfig(),toolbarWidth:420,showPreviewDialogFlag:!1,showImportJsonDialogFlag:!1,showExportJsonDialogFlag:!1,showExportCodeDialogFlag:!1,showFormDataDialogFlag:!1,showExportSFCDialogFlag:!1,showNodeTreeDrawerFlag:!1,nodeTreeData:[],testFunc:"",importTemplate:"",jsonContent:"",jsonRawContent:"",formDataJson:"",formDataRawJson:"",vueCode:"",htmlCode:"",sfcCode:"",sfcCodeV3:"",activeCodeTab:"vue",activeSFCTab:"vue2",testFormData:{select62173:2},testOptionData:{select62173:[{label:"01",value:1},{label:"22",value:2},{label:"333",value:3}],select001:[{label:"辣椒",value:1},{label:"菠萝",value:2},{label:"丑橘子",value:3}]}}},computed:{formJson:function(){return{widgetList:Object(C["d"])(this.designer.widgetList),formConfig:Object(C["d"])(this.designer.formConfig)}},undoDisabled:function(){return!this.designer.undoEnabled()},redoDisabled:function(){return!this.designer.redoEnabled()},layoutType:function(){return this.designer.getLayoutType()},designerDsv:function(){return this.globalDsv}},watch:{"designer.widgetList":{deep:!0,handler:function(e){}}},mounted:function(){var e=this,t=this.designerConfig.toolbarMaxWidth||420,i=this.designerConfig.toolbarMinWidth||300,n=window.innerWidth-260-300-320-80;this.toolbarWidth=n>=t?t:n<=i?i:n,Object(C["a"])((function(){e.$nextTick((function(){var n=window.innerWidth-260-300-320-80;e.toolbarWidth=n>=t?t:n<=i?i:n}))}))},methods:{showToolButton:function(e){return void 0===this.designerConfig[e]||!!this.designerConfig[e]},buildTreeNodeOfWidget:function(e,t){var i=this,n={id:e.id,label:e.options.label||e.type};t.push(n),void 0!==e.category&&(n.children=[],"grid"===e.type?e.cols.map((function(t){var o={id:t.id,label:t.options.name||e.type,children:[]};n.children.push(o),t.widgetList.map((function(e){i.buildTreeNodeOfWidget(e,o.children)}))})):"table"===e.type?e.rows.map((function(e){var t={id:e.id,label:"table-row",selectable:!1,children:[]};n.children.push(t),e.cols.map((function(e){if(!e.merged){var n=t.children,o={id:e.id,label:"table-cell",children:[]};n.push(o),e.widgetList.map((function(e){i.buildTreeNodeOfWidget(e,o.children)}))}}))})):"tab"===e.type?e.tabs.map((function(t){var o={id:t.id,label:t.options.name||e.type,selectable:!1,children:[]};n.children.push(o),t.widgetList.map((function(e){i.buildTreeNodeOfWidget(e,o.children)}))})):("sub-form"===e.type||"container"===e.category)&&e.widgetList.map((function(e){i.buildTreeNodeOfWidget(e,n.children)})))},refreshNodeTree:function(){var e=this;this.nodeTreeData.length=0,this.designer.widgetList.forEach((function(t){e.buildTreeNodeOfWidget(t,e.nodeTreeData)}))},showNodeTreeDrawer:function(){var e=this;this.refreshNodeTree(),this.showNodeTreeDrawerFlag=!0,this.$nextTick((function(){e.designer.selectedId&&e.$refs.nodeTree.setCurrentKey(e.designer.selectedId)}))},undoHistory:function(){this.designer.undoHistoryStep()},redoHistory:function(){this.designer.redoHistoryStep()},changeLayoutType:function(e){this.designer.changeLayoutType(e)},clearFormWidget:function(){this.designer.clearDesigner(),this.designer.formWidget.clearWidgetRefList()},previewForm:function(){this.showPreviewDialogFlag=!0},saveAsFile:function(e,t){var i=this;this.$prompt(this.i18nt("designer.hint.fileNameForSave"),this.i18nt("designer.hint.saveFileTitle"),{inputValue:t,closeOnClickModal:!1,inputPlaceholder:this.i18nt("designer.hint.fileNameInputPlaceholder")}).then((function(n){var o=n.value;if(o||(o=t),1!=Object(C["i"])("vscode")){var l=new Blob([e],{type:"text/plain;charset=utf-8"});Object(ve["saveAs"])(l,o)}else i.vsSaveFile(o,e)})).catch((function(){}))},vsSaveFile:function(e,t){var i={cmd:"writeFile",data:{fileName:e,code:t}};window.parent.postMessage(i,"*")},importJson:function(){this.importTemplate=JSON.stringify(this.designer.getImportTemplate(),null,"  "),this.showImportJsonDialogFlag=!0},doJsonImport:function(){try{var e=JSON.parse(this.importTemplate);this.designer.loadFormJson(e),this.showImportJsonDialogFlag=!1,this.$message.success(this.i18nt("designer.hint.importJsonSuccess")),this.designer.emitHistoryChange(),this.designer.emitEvent("form-json-imported",[])}catch(t){this.$message.error(t+"")}},exportJson:function(){var e=Object(C["d"])(this.designer.widgetList),t=Object(C["d"])(this.designer.formConfig);this.jsonContent=JSON.stringify({widgetList:e,formConfig:t},null,"  "),this.jsonRawContent=JSON.stringify({widgetList:e,formConfig:t}),this.showExportJsonDialogFlag=!0},copyFormJson:function(e){Object(C["c"])(this.jsonRawContent,e,this.$message,this.i18nt("designer.hint.copyJsonSuccess"),this.i18nt("designer.hint.copyJsonFail"))},saveFormJson:function(){this.saveAsFile(this.jsonContent,"vform".concat(Object(C["e"])(),".json"))},exportCode:function(){this.vueCode=Q(this.formJson),this.htmlCode=Q(this.formJson,"html"),this.showExportCodeDialogFlag=!0},copyVueCode:function(e){Object(C["c"])(this.vueCode,e,this.$message,this.i18nt("designer.hint.copyVueCodeSuccess"),this.i18nt("designer.hint.copyVueCodeFail"))},copyHtmlCode:function(e){Object(C["c"])(this.htmlCode,e,this.$message,this.i18nt("designer.hint.copyHtmlCodeSuccess"),this.i18nt("designer.hint.copyHtmlCodeFail"))},saveVueCode:function(){this.saveAsFile(this.vueCode,"vform".concat(Object(C["e"])(),".vue"))},saveHtmlCode:function(){this.saveAsFile(this.htmlCode,"vform".concat(Object(C["e"])(),".html"))},generateSFC:function(){var e=this;te((function(t){e.sfcCode=be(e.designer.formConfig,e.designer.widgetList,t),e.sfcCodeV3=be(e.designer.formConfig,e.designer.widgetList,t,!0),e.showExportSFCDialogFlag=!0}))},copyV2SFC:function(e){Object(C["c"])(this.sfcCode,e,this.$message,this.i18nt("designer.hint.copySFCSuccess"),this.i18nt("designer.hint.copySFCFail"))},copyV3SFC:function(e){Object(C["c"])(this.sfcCodeV3,e,this.$message,this.i18nt("designer.hint.copySFCSuccess"),this.i18nt("designer.hint.copySFCFail"))},saveV2SFC:function(){this.saveAsFile(this.sfcCode,"vformV2-".concat(Object(C["e"])(),".vue"))},saveV3SFC:function(){this.saveAsFile(this.sfcCodeV3,"vformV3-".concat(Object(C["e"])(),".vue"))},getFormData:function(){var e=this;this.$refs["preForm"].getFormData().then((function(t){e.formDataJson=JSON.stringify(t,null,"  "),e.formDataRawJson=JSON.stringify(t),e.showFormDataDialogFlag=!0})).catch((function(t){e.$message.error(t)}))},copyFormDataJson:function(e){Object(C["c"])(this.formDataRawJson,e,this.$message,this.i18nt("designer.hint.copyJsonSuccess"),this.i18nt("designer.hint.copyJsonFail"))},saveFormData:function(){this.saveAsFile(this.htmlCode,"formData".concat(Object(C["e"])(),".json"))},resetForm:function(){this.$refs["preForm"].resetForm()},setFormDisabled:function(){this.$refs["preForm"].disableForm()},setFormEnabled:function(){this.$refs["preForm"].enableForm()},printFormJson:function(){var e={widgetList:Object(C["d"])(this.designer.widgetList),formConfig:Object(C["d"])(this.designer.formConfig)};console.log(e)},testValidate:function(){console.log("test===",this.$refs["preForm"].validateForm())},testSetFormData:function(){var e={checkbox45524:[1,2]};this.$refs["preForm"].setFormData(e)},testReloadOptionData:function(){this.testOptionData["select001"].push({label:"aaa",value:888}),this.$refs.preForm.reloadOptionData("select001")},handleFormChange:function(e,t,i,n){},testOnAppendButtonClick:function(e){console.log("test",e)},testOnButtonClick:function(e){console.log("test",e)},findWidgetById:function(e){var t=null;return Object(C["q"])(this.designer.widgetList,(function(i){i.id===e&&(t=i)})),t},onNodeTreeClick:function(e,t,i){if(void 0===e.selectable||e.selectable){var n=e.id,o=this.findWidgetById(n);o&&this.designer.setSelected(o)}else this.$message.info(this.i18nt("designer.hint.currentNodeCannotBeSelected"))}}},ye=we,xe=(i("ceec"),Object(L["a"])(ye,R,T,!1,null,"7fab259c",null)),Ce=xe.exports,_e=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-container",{staticClass:"panel-container"},[i("el-tabs",{staticStyle:{height:"100%",overflow:"hidden"},attrs:{"active-name":e.activeTab}},[i("el-tab-pane",{attrs:{label:e.i18nt("designer.hint.widgetSetting"),name:"1"}},[i("el-scrollbar",{staticClass:"setting-scrollbar",style:{height:e.scrollerHeight}},[e.designer.selectedWidget&&!e.designer.selectedWidget.category?[i("el-form",{staticClass:"setting-form",attrs:{model:e.optionModel,size:"mini","label-position":"left","label-width":"120px"},nativeOn:{submit:function(e){e.preventDefault()}}},[i("el-collapse",{staticClass:"setting-collapse",model:{value:e.widgetActiveCollapseNames,callback:function(t){e.widgetActiveCollapseNames=t},expression:"widgetActiveCollapseNames"}},[e.showCollapse(e.commonProps)?i("el-collapse-item",{attrs:{name:"1",title:e.i18nt("designer.setting.commonSetting")}},[e._l(e.commonProps,(function(t,n){return[e.hasPropEditor(n,t)?i(e.getPropEditor(n,t),{tag:"component",attrs:{designer:e.designer,"selected-widget":e.selectedWidget,"option-model":e.optionModel}}):e._e()]}))],2):e._e(),e.showCollapse(e.advProps)?i("el-collapse-item",{attrs:{name:"2",title:e.i18nt("designer.setting.advancedSetting")}},[e._l(e.advProps,(function(t,n){return[e.hasPropEditor(n,t)?i(e.getPropEditor(n,t),{tag:"component",attrs:{designer:e.designer,"selected-widget":e.selectedWidget,"option-model":e.optionModel}}):e._e()]}))],2):e._e(),e.showEventCollapse()&&e.showCollapse(e.eventProps)?i("el-collapse-item",{attrs:{name:"3",title:e.i18nt("designer.setting.eventSetting")}},[e._l(e.eventProps,(function(t,n){return[e.hasPropEditor(n,t)?i(e.getPropEditor(n,t),{tag:"component",attrs:{designer:e.designer,"selected-widget":e.selectedWidget,"option-model":e.optionModel}}):e._e()]}))],2):e._e()],1)],1)]:e._e(),e.designer.selectedWidget&&e.designer.selectedWidget.category?[i("el-form",{staticClass:"setting-form",attrs:{model:e.optionModel,size:"mini","label-position":"left","label-width":"120px"},nativeOn:{submit:function(e){e.preventDefault()}}},[i("el-collapse",{staticClass:"setting-collapse",model:{value:e.widgetActiveCollapseNames,callback:function(t){e.widgetActiveCollapseNames=t},expression:"widgetActiveCollapseNames"}},[e.showCollapse(e.commonProps)?i("el-collapse-item",{attrs:{name:"1",title:e.i18nt("designer.setting.commonSetting")}},[e._l(e.commonProps,(function(t,n){return[e.hasPropEditor(n,t)?i(e.getPropEditor(n,t),{tag:"component",attrs:{designer:e.designer,"selected-widget":e.selectedWidget,"option-model":e.optionModel}}):e._e()]}))],2):e._e(),e.showCollapse(e.advProps)?i("el-collapse-item",{attrs:{name:"2",title:e.i18nt("designer.setting.advancedSetting")}},[e._l(e.advProps,(function(t,n){return[e.hasPropEditor(n,t)?i(e.getPropEditor(n,t),{tag:"component",attrs:{designer:e.designer,"selected-widget":e.selectedWidget,"option-model":e.optionModel}}):e._e()]}))],2):e._e(),e.showEventCollapse()&&e.showCollapse(e.eventProps)?i("el-collapse-item",{attrs:{name:"3",title:e.i18nt("designer.setting.eventSetting")}},[e._l(e.eventProps,(function(t,n){return[e.hasPropEditor(n,t)?i(e.getPropEditor(n,t),{tag:"component",attrs:{designer:e.designer,"selected-widget":e.selectedWidget,"option-model":e.optionModel}}):e._e()]}))],2):e._e()],1)],1)]:e._e()],2)],1),e.designer?i("el-tab-pane",{attrs:{label:e.i18nt("designer.hint.formSetting"),name:"2"}},[i("el-scrollbar",{staticClass:"setting-scrollbar",style:{height:e.scrollerHeight}},[i("form-setting",{attrs:{designer:e.designer,"form-config":e.formConfig}})],1)],1):e._e()],1),e.showWidgetEventDialogFlag?i("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],staticClass:"small-padding-dialog",attrs:{title:e.i18nt("designer.setting.editWidgetEventHandler"),visible:e.showWidgetEventDialogFlag,"show-close":!0,"append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.showWidgetEventDialogFlag=t}}},[i("el-alert",{attrs:{type:"info",closable:!1,title:e.eventHeader}}),i("code-editor",{ref:"ecEditor",attrs:{mode:"javascript",readonly:!1},model:{value:e.eventHandlerCode,callback:function(t){e.eventHandlerCode=t},expression:"eventHandlerCode"}}),i("el-alert",{attrs:{type:"info",closable:!1,title:"}"}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.showWidgetEventDialogFlag=!1}}},[e._v(" "+e._s(e.i18nt("designer.hint.cancel")))]),i("el-button",{attrs:{type:"primary"},on:{click:e.saveEventHandler}},[e._v(" "+e._s(e.i18nt("designer.hint.confirm")))])],1)],1):e._e()],1)},Oe=[],Fe=(i("ac1f"),i("5319"),i("0654")),Me={};Fe.keys().map((function(e){var t=Fe(e).default;Me[t.name]=t}));var je=Me,Le=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("el-form",{staticClass:"setting-form",attrs:{model:e.formConfig,size:"mini","label-position":"left","label-width":"120px"},nativeOn:{submit:function(e){e.preventDefault()}}},[i("el-collapse",{staticClass:"setting-collapse",model:{value:e.formActiveCollapseNames,callback:function(t){e.formActiveCollapseNames=t},expression:"formActiveCollapseNames"}},[i("el-collapse-item",{attrs:{name:"1",title:e.i18nt("designer.setting.basicSetting")}},[i("el-form-item",{attrs:{label:e.i18nt("designer.setting.formSize")}},[i("el-select",{model:{value:e.formConfig.size,callback:function(t){e.$set(e.formConfig,"size",t)},expression:"formConfig.size"}},e._l(e.formSizes,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),i("el-form-item",{attrs:{label:e.i18nt("designer.setting.labelPosition")}},[i("el-radio-group",{staticClass:"radio-group-custom",model:{value:e.formConfig.labelPosition,callback:function(t){e.$set(e.formConfig,"labelPosition",t)},expression:"formConfig.labelPosition"}},[i("el-radio-button",{attrs:{label:"left"}},[e._v(e._s(e.i18nt("designer.setting.leftPosition")))]),i("el-radio-button",{attrs:{label:"top"}},[e._v(e._s(e.i18nt("designer.setting.topPosition")))])],1)],1),i("el-form-item",{attrs:{label:e.i18nt("designer.setting.labelAlign")}},[i("el-radio-group",{staticClass:"radio-group-custom",model:{value:e.formConfig.labelAlign,callback:function(t){e.$set(e.formConfig,"labelAlign",t)},expression:"formConfig.labelAlign"}},[i("el-radio-button",{attrs:{label:"label-left-align"}},[e._v(e._s(e.i18nt("designer.setting.leftAlign")))]),i("el-radio-button",{attrs:{label:"label-center-align"}},[e._v(e._s(e.i18nt("designer.setting.centerAlign")))]),i("el-radio-button",{attrs:{label:"label-right-align"}},[e._v(e._s(e.i18nt("designer.setting.rightAlign")))])],1)],1),i("el-form-item",{attrs:{label:e.i18nt("designer.setting.labelWidth")}},[i("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0},model:{value:e.formConfig.labelWidth,callback:function(t){e.$set(e.formConfig,"labelWidth",t)},expression:"formConfig.labelWidth"}})],1),i("el-form-item",{attrs:{label:e.i18nt("designer.setting.formCss")}},[i("el-button",{attrs:{type:"info",icon:"el-icon-edit",plain:"",round:""},on:{click:e.editFormCss}},[e._v(e._s(e.i18nt("designer.setting.addCss")))])],1),i("el-form-item",{attrs:{label:e.i18nt("designer.setting.customClass")}},[i("el-select",{attrs:{multiple:"",filterable:"","allow-create":"","default-first-option":""},model:{value:e.formConfig.customClass,callback:function(t){e.$set(e.formConfig,"customClass",t)},expression:"formConfig.customClass"}},e._l(e.cssClassList,(function(e,t){return i("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1),i("el-form-item",{attrs:{label:e.i18nt("designer.setting.globalFunctions")}},[i("el-button",{attrs:{type:"info",icon:"el-icon-edit",plain:"",round:""},on:{click:e.editGlobalFunctions}},[e._v(e._s(e.i18nt("designer.setting.addEventHandler")))])],1),i("el-form-item",{attrs:{"label-width":"0"}},[i("el-divider",{staticClass:"custom-divider"},[e._v(e._s(e.i18nt("designer.setting.formSFCSetting")))])],1),i("el-form-item",{attrs:{label:e.i18nt("designer.setting.formModelName")}},[i("el-input",{attrs:{type:"text"},model:{value:e.formConfig.modelName,callback:function(t){e.$set(e.formConfig,"modelName",t)},expression:"formConfig.modelName"}})],1),i("el-form-item",{attrs:{label:e.i18nt("designer.setting.formRefName")}},[i("el-input",{attrs:{type:"text"},model:{value:e.formConfig.refName,callback:function(t){e.$set(e.formConfig,"refName",t)},expression:"formConfig.refName"}})],1),i("el-form-item",{attrs:{label:e.i18nt("designer.setting.formRulesName")}},[i("el-input",{attrs:{type:"text"},model:{value:e.formConfig.rulesName,callback:function(t){e.$set(e.formConfig,"rulesName",t)},expression:"formConfig.rulesName"}})],1)],1),e.showEventCollapse()?i("el-collapse-item",{attrs:{name:"2",title:e.i18nt("designer.setting.eventSetting")}},[i("el-form-item",{attrs:{label:"onFormCreated","label-width":"150px"}},[i("el-button",{attrs:{type:"info",icon:"el-icon-edit",plain:"",round:""},on:{click:function(t){return e.editFormEventHandler("onFormCreated")}}},[e._v(" "+e._s(e.i18nt("designer.setting.addEventHandler")))])],1),i("el-form-item",{attrs:{label:"onFormMounted","label-width":"150px"}},[i("el-button",{attrs:{type:"info",icon:"el-icon-edit",plain:"",round:""},on:{click:function(t){return e.editFormEventHandler("onFormMounted")}}},[e._v(" "+e._s(e.i18nt("designer.setting.addEventHandler")))])],1),i("el-form-item",{attrs:{label:"onFormDataChange","label-width":"150px"}},[i("el-button",{attrs:{type:"info",icon:"el-icon-edit",plain:"",round:""},on:{click:function(t){return e.editFormEventHandler("onFormDataChange")}}},[e._v(" "+e._s(e.i18nt("designer.setting.addEventHandler")))])],1)],1):e._e()],1)],1),e.showFormEventDialogFlag?i("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],staticClass:"small-padding-dialog",attrs:{title:e.i18nt("designer.setting.editFormEventHandler"),visible:e.showFormEventDialogFlag,"show-close":!0,"append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.showFormEventDialogFlag=t}}},[i("el-alert",{attrs:{type:"info",closable:!1,title:"form."+e.eventParamsMap[e.curEventName]}}),i("code-editor",{ref:"ecEditor",attrs:{mode:"javascript",readonly:!1},model:{value:e.formEventHandlerCode,callback:function(t){e.formEventHandlerCode=t},expression:"formEventHandlerCode"}}),i("el-alert",{attrs:{type:"info",closable:!1,title:"}"}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.showFormEventDialogFlag=!1}}},[e._v(" "+e._s(e.i18nt("designer.hint.cancel")))]),i("el-button",{attrs:{type:"primary"},on:{click:e.saveFormEventHandler}},[e._v(" "+e._s(e.i18nt("designer.hint.confirm")))])],1)],1):e._e(),e.showEditFormCssDialogFlag?i("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],staticClass:"small-padding-dialog",attrs:{title:e.i18nt("designer.setting.formCss"),visible:e.showEditFormCssDialogFlag,"show-close":!0,"append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.showEditFormCssDialogFlag=t}}},[i("code-editor",{attrs:{mode:"css",readonly:!1},model:{value:e.formCssCode,callback:function(t){e.formCssCode=t},expression:"formCssCode"}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.showEditFormCssDialogFlag=!1}}},[e._v(" "+e._s(e.i18nt("designer.hint.cancel")))]),i("el-button",{attrs:{type:"primary"},on:{click:e.saveFormCss}},[e._v(" "+e._s(e.i18nt("designer.hint.confirm")))])],1)],1):e._e(),e.showEditFunctionsDialogFlag?i("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],staticClass:"small-padding-dialog",attrs:{title:e.i18nt("designer.setting.globalFunctions"),visible:e.showEditFunctionsDialogFlag,"show-close":!0,"append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.showEditFunctionsDialogFlag=t}}},[i("code-editor",{ref:"gfEditor",attrs:{mode:"javascript",readonly:!1},model:{value:e.functionsCode,callback:function(t){e.functionsCode=t},expression:"functionsCode"}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.showEditFunctionsDialogFlag=!1}}},[e._v(" "+e._s(e.i18nt("designer.hint.cancel")))]),i("el-button",{attrs:{type:"primary"},on:{click:e.saveGlobalFunctions}},[e._v(" "+e._s(e.i18nt("designer.hint.confirm")))])],1)],1):e._e()],1)},ke=[],We=(i("466d"),i("1276"),i("498a"),i("a630"),i("3ca3"),i("6062"),{name:"form-setting",mixins:[_["b"]],components:{CodeEditor:G["a"]},props:{designer:Object,formConfig:Object},inject:["getDesignerConfig"],data:function(){return{designerConfig:this.getDesignerConfig(),formActiveCollapseNames:["1","2"],formSizes:[{label:"default",value:""},{label:"large",value:"large"},{label:"medium",value:"medium"},{label:"small",value:"small"},{label:"mini",value:"mini"}],showEditFormCssDialogFlag:!1,formCssCode:"",cssClassList:[],showEditFunctionsDialogFlag:!1,functionsCode:"",showFormEventDialogFlag:!1,formEventHandlerCode:"",curEventName:"",eventParamsMap:{onFormCreated:"onFormCreated() {",onFormMounted:"onFormMounted() {",onFormDataChange:"onFormDataChange(fieldName, newValue, oldValue, formModel, subFormName, subFormRowIndex) {"}}},created:function(){var e=this;this.designer.handleEvent("form-json-imported",(function(){e.formCssCode=e.formConfig.cssCode,Object(C["j"])(e.formCssCode),e.extractCssClass(),e.designer.emitEvent("form-css-updated",Object(C["d"])(e.cssClassList))}))},mounted:function(){var e=this;setTimeout((function(){e.formCssCode=e.formConfig.cssCode,Object(C["j"])(e.formCssCode),e.extractCssClass(),e.designer.emitEvent("form-css-updated",Object(C["d"])(e.cssClassList))}),1200)},methods:{showEventCollapse:function(){return void 0===this.designerConfig["eventCollapse"]||!!this.designerConfig["eventCollapse"]},editFormCss:function(){this.formCssCode=this.designer.formConfig.cssCode,this.showEditFormCssDialogFlag=!0},extractCssClass:function(){var e=/\..*{/g,t=this.formCssCode.match(e),i=[];t&&t.length>0&&t.forEach((function(e){var t=e.split(",");t.length>0&&t.forEach((function(e){var t=e.trim();if(-1!==t.indexOf(".",1)){var n=t.substring(t.indexOf(".")+1,t.indexOf(".",1));n&&i.push(n.trim())}else if(-1!==t.indexOf(" ")){var o=t.substring(t.indexOf(".")+1,t.indexOf(" "));o&&i.push(o.trim())}else if(-1!==t.indexOf("{")){var l=t.substring(t.indexOf(".")+1,t.indexOf("{"));i.push(l.trim())}else{var s=t.substring(t.indexOf(".")+1);i.push(s.trim())}}))})),this.cssClassList.splice(0,this.cssClassList.length),this.cssClassList=Array.from(new Set(i))},saveFormCss:function(){this.extractCssClass(),this.designer.formConfig.cssCode=this.formCssCode,Object(C["j"])(this.formCssCode),this.showEditFormCssDialogFlag=!1,this.designer.emitEvent("form-css-updated",Object(C["d"])(this.cssClassList))},editGlobalFunctions:function(){this.functionsCode=this.designer.formConfig.functions,this.showEditFunctionsDialogFlag=!0},saveGlobalFunctions:function(){var e=this.$refs.gfEditor.getEditorAnnotations(),t=!1;e&&e.length>0&&(e.forEach((function(e){"error"===e.type&&(t=!0)})),t)?this.$message.error(this.i18nt("designer.setting.syntaxCheckWarning")):(this.designer.formConfig.functions=this.functionsCode,Object(C["k"])(this.functionsCode),this.showEditFunctionsDialogFlag=!1)},editFormEventHandler:function(e){this.curEventName=e,this.formEventHandlerCode=this.formConfig[e],this.showFormEventDialogFlag=!0},saveFormEventHandler:function(){var e=this.$refs.ecEditor.getEditorAnnotations(),t=!1;e&&e.length>0&&(e.forEach((function(e){"error"===e.type&&(t=!0)})),t)?this.$message.error(this.i18nt("designer.setting.syntaxCheckWarning")):(this.formConfig[this.curEventName]=this.formEventHandlerCode,this.showFormEventDialogFlag=!1)}}}),Se=We,Ee=(i("28ce"),Object(L["a"])(Se,Le,ke,!1,null,"5c7642f4",null)),De=Ee.exports,Ie={name:"name-editor",label:"label-editor",labelAlign:"labelAlign-editor",type:"type-editor",defaultValue:"defaultValue-editor",placeholder:"placeholder-editor",startPlaceholder:"startPlaceholder-editor",endPlaceholder:"endPlaceholder-editor",columnWidth:"columnWidth-editor",size:"size-editor",showStops:"showStops-editor",displayStyle:"displayStyle-editor",buttonStyle:"buttonStyle-editor",border:"border-editor",labelWidth:"labelWidth-editor",labelHidden:"labelHidden-editor",rows:"rows-editor",required:"required-editor",requiredHint:"requiredHint-editor",validation:"validation-editor",validationHint:"validationHint-editor",readonly:"readonly-editor",disabled:"disabled-editor",hidden:"hidden-editor",clearable:"clearable-editor",editable:"editable-editor",showPassword:"showPassword-editor",textContent:"textContent-editor",textAlign:"textAlign-editor",fontSize:"fontSize-editor",preWrap:"preWrap-editor",htmlContent:"htmlContent-editor",format:"format-editor",valueFormat:"valueFormat-editor",filterable:"filterable-editor",allowCreate:"allowCreate-editor",remote:"remote-editor",automaticDropdown:"automaticDropdown-editor",checkStrictly:"checkStrictly-editor",showAllLevels:"showAllLevels-editor",multiple:"multiple-editor",multipleLimit:"multipleLimit-editor",contentPosition:"contentPosition-editor",optionItems:"optionItems-editor",uploadURL:"uploadURL-editor",uploadTip:"uploadTip-editor",withCredentials:"withCredentials-editor",multipleSelect:"multipleSelect-editor",limit:"limit-editor",fileMaxSize:"fileMaxSize-editor",fileTypes:"fileTypes-editor",customClass:"customClass-editor",showBlankRow:"showBlankRow-editor",showRowNumber:"showRowNumber-editor",cellWidth:"cellWidth-editor",cellHeight:"cellHeight-editor",colHeight:"colHeight-editor",wordBreak:"wordBreak-editor",gutter:"gutter-editor",responsive:"responsive-editor",span:"span-editor",offset:"offset-editor",push:"push-editor",pull:"pull-editor"},Re={min:"min-editor",max:"max-editor",precision:"precision-editor",step:"step-editor",controlsPosition:"controlsPosition-editor",minLength:"minLength-editor",maxLength:"maxLength-editor",showWordLimit:"showWordLimit-editor",prefixIcon:"prefixIcon-editor",suffixIcon:"suffixIcon-editor",switchWidth:"switchWidth-editor",activeText:"activeText-editor",inactiveText:"inactiveText-editor",activeColor:"activeColor-editor",inactiveColor:"inactiveColor-editor",lowThreshold:"lowThreshold-editor",highThreshold:"highThreshold-editor",allowHalf:"allowHalf-editor",showText:"showText-editor",showScore:"showScore-editor",range:"range-editor",vertical:"vertical-editor",plain:"plain-editor",round:"round-editor",circle:"circle-editor",icon:"icon-editor",labelIconClass:"labelIconClass-editor",labelIconPosition:"labelIconPosition-editor",labelTooltip:"labelTooltip-editor",appendButton:"appendButton-editor",appendButtonDisabled:"appendButtonDisabled-editor",buttonIcon:"buttonIcon-editor"},Te={onCreated:"onCreated-editor",onMounted:"onMounted-editor",onClick:"onClick-editor",onInput:"onInput-editor",onChange:"onChange-editor",onFocus:"onFocus-editor",onBlur:"onBlur-editor",onRemoteQuery:"onRemoteQuery-editor",onBeforeUpload:"onBeforeUpload-editor",onUploadSuccess:"onUploadSuccess-editor",onUploadError:"onUploadError-editor",onFileRemove:"onFileRemove-editor",onValidate:"onValidate-editor",onAppendButtonClick:"onAppendButtonClick-editor",onSubFormRowAdd:"onSubFormRowAdd-editor",onSubFormRowInsert:"onSubFormRowInsert-editor",onSubFormRowDelete:"onSubFormRowDelete-editor",onSubFormRowChange:"onSubFormRowChange-editor"};function Pe(e,t){Ie[e]=t}function He(e,t){Te[e]=t}function Ne(e){return!!Ie[e]||!!Re[e]||!!Te[e]}function $e(e,t,i){n["default"].component(t,i),Pe(e,t)}function Ve(e,t,i){n["default"].component(t,i),He(e,t)}var Ae={COMMON_PROPERTIES:Ie,ADVANCED_PROPERTIES:Re,EVENT_PROPERTIES:Te},Be=Ae.COMMON_PROPERTIES,ze=Ae.ADVANCED_PROPERTIES,Ue=Ae.EVENT_PROPERTIES,Je={name:"SettingPanel",componentName:"SettingPanel",mixins:[_["b"]],components:Object(f["a"])({CodeEditor:G["a"],FormSetting:De},je),props:{designer:Object,selectedWidget:Object,formConfig:Object,globalDsv:{type:Object,default:function(){return{}}}},provide:function(){var e=this;return{isSubFormChildWidget:function(){return e.subFormChildWidgetFlag}}},inject:["getDesignerConfig"],data:function(){return{designerConfig:this.getDesignerConfig(),scrollerHeight:0,activeTab:"2",widgetActiveCollapseNames:["1","3"],formActiveCollapseNames:["1","2"],commonProps:Be,advProps:ze,eventProps:Ue,showWidgetEventDialogFlag:!1,eventHandlerCode:"",curEventName:"",eventHeader:"",subFormChildWidgetFlag:!1}},computed:{optionModel:{get:function(){return this.selectedWidget.options},set:function(e){this.selectedWidget.options=e}}},watch:{"designer.selectedWidget":{handler:function(e){e&&(this.activeTab="1")}},"selectedWidget.options":{deep:!0,handler:function(){this.designer.saveCurrentHistoryStep()}},formConfig:{deep:!0,handler:function(){this.designer.saveCurrentHistoryStep()}}},created:function(){var e=this;this.$on("editEventHandler",(function(e,t){this.editEventHandler(e,t)})),this.designer.handleEvent("form-css-updated",(function(t){e.designer.setCssClassList(t)})),this.designer.handleEvent("field-selected",(function(t){e.subFormChildWidgetFlag=!!t&&"sub-form"===t.type}))},mounted:function(){var e=this;this.designer.selectedWidget?this.activeTab="1":this.activeTab="2",this.scrollerHeight=window.innerHeight-56-48+"px",Object(C["a"])((function(){e.$nextTick((function(){e.scrollerHeight=window.innerHeight-56-48+"px"}))}))},methods:{showEventCollapse:function(){return void 0===this.designerConfig["eventCollapse"]||!!this.designerConfig["eventCollapse"]},hasPropEditor:function(e,t){if(!t)return!1;if(e.indexOf("-")<=-1){var i=this.selectedWidget.type+"-"+e;if(Ne(i))return!1}var n=e.replace(this.selectedWidget.type+"-","");return this.designer.hasConfig(this.selectedWidget,n)},getPropEditor:function(e,t){var i=e.replace(this.selectedWidget.type+"-",""),n="".concat(this.selectedWidget.type,"-").concat(i,"-editor");return this.$options.components[n]||this.$root.$options.components[n]?n:t},showCollapse:function(e){var t=!1;for(var i in e)if(e.hasOwnProperty(i)&&this.hasPropEditor(i,e[i])){t=!0;break}return t},editEventHandler:function(e,t){this.curEventName=e,this.eventHeader="".concat(this.optionModel.name,".").concat(e,"(").concat(t.join(", "),") {"),this.eventHandlerCode=this.selectedWidget.options[e]||"","onValidate"!==e||this.optionModel["onValidate"]||(this.eventHandlerCode="  /* sample code */\n  /*\n  if ((value > 100) || (value < 0)) {\n    callback(new Error('error message'))  //fail\n  } else {\n    callback();  //pass\n  }\n  */"),this.showWidgetEventDialogFlag=!0},saveEventHandler:function(){var e=this.$refs.ecEditor.getEditorAnnotations(),t=!1;e&&e.length>0&&(e.forEach((function(e){"error"===e.type&&(t=!0)})),t)?this.$message.error(this.i18nt("designer.setting.syntaxCheckWarning")):(this.selectedWidget.options[this.curEventName]=this.eventHandlerCode,this.showWidgetEventDialogFlag=!1)}}},qe=Je,Ge=(i("2c2c"),Object(L["a"])(qe,_e,Oe,!1,null,"9296a6f2",null)),Ke=Ge.exports,Ze=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"form-widget-container"},[i("el-form",{staticClass:"full-height-width widget-form",class:[e.customClass,e.layoutType+"-layout"],attrs:{"label-position":e.labelPosition,size:e.size,"validate-on-rule-change":!1}},[0===e.designer.widgetList.length?i("div",{staticClass:"no-widget-hint"},[e._v(e._s(e.i18nt("designer.noWidgetHint")))]):e._e(),i("draggable",e._b({attrs:{list:e.designer.widgetList,handle:".drag-handler",move:e.checkMove},on:{end:e.onDragEnd,add:e.onDragAdd,update:e.onDragUpdate}},"draggable",{group:"dragGroup",ghostClass:"ghost",animation:300},!1),[i("transition-group",{staticClass:"form-widget-list",attrs:{name:"fade",tag:"div"}},[e._l(e.designer.widgetList,(function(t,n){return["container"===t.category?[i(e.getWidgetName(t),{key:t.id,tag:"component",attrs:{widget:t,designer:e.designer,"parent-list":e.designer.widgetList,"index-of-parent-list":n,"parent-widget":null}})]:[i(e.getWidgetName(t),{key:t.id,tag:"component",attrs:{field:t,designer:e.designer,"parent-list":e.designer.widgetList,"index-of-parent-list":n,"parent-widget":null,"design-state":!0}})]]}))],2)],1)],1)],1)},Qe=[],Xe=i("8b30");Xe.keys().map((function(e){var t=Xe(e).default;n["default"].component(t.name,t)}));var Ye={name:"VFormWidget",componentName:"VFormWidget",mixins:[_["b"]],components:Object(f["a"])({Draggable:m.a},B["a"]),props:{designer:Object,formConfig:Object,optionData:{type:Object,default:function(){return{}}},globalDsv:{type:Object,default:function(){return{}}}},provide:function(){var e=this;return{refList:this.widgetRefList,formConfig:this.formConfig,getGlobalDsv:function(){return e.globalDsv},globalOptionData:this.optionData,getOptionData:function(){return e.optionData},globalModel:{formModel:this.formModel}}},inject:["getDesignerConfig"],data:function(){return{formModel:{},widgetRefList:{}}},computed:{labelPosition:function(){return this.designer.formConfig&&this.designer.formConfig.labelPosition?this.designer.formConfig.labelPosition:"left"},size:function(){return this.designer.formConfig&&this.designer.formConfig.size?this.designer.formConfig.size:"medium"},customClass:function(){return this.designer.formConfig.customClass||""},layoutType:function(){return this.designer.getLayoutType()}},watch:{"designer.widgetList":{deep:!0,handler:function(e){}},"designer.formConfig":{deep:!0,handler:function(e){}}},created:function(){this.designer.initDesigner(!!this.getDesignerConfig().resetFormJson),this.designer.loadPresetCssCode(this.getDesignerConfig().presetCssCode)},mounted:function(){this.disableFirefoxDefaultDrop(),this.designer.registerFormWidget(this)},methods:{getWidgetName:function(e){return e.type+"-widget"},disableFirefoxDefaultDrop:function(){var e=-1!==navigator.userAgent.toLowerCase().indexOf("firefox");e&&(document.body.ondrop=function(e){e.stopPropagation(),e.preventDefault()})},onDragEnd:function(e){},onDragAdd:function(e){var t=e.newIndex;this.designer.widgetList[t]&&this.designer.setSelected(this.designer.widgetList[t]),this.designer.emitHistoryChange(),this.designer.emitEvent("field-selected",null)},onDragUpdate:function(){this.designer.emitHistoryChange()},checkMove:function(e){return this.designer.checkWidgetMove(e)},getFormData:function(){return this.formModel},getWidgetRef:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=this.widgetRefList[e];return!i&&t&&this.$message.error(this.i18nt("render.hint.refNotFound")+e),i},getSelectedWidgetRef:function(){var e=this.designer.selectedWidgetName;return this.getWidgetRef(e)},clearWidgetRefList:function(){var e=this;Object.keys(this.widgetRefList).forEach((function(t){delete e.widgetRefList[t]}))},deleteWidgetRef:function(e){delete this.widgetRefList[e]}}},et=Ye,tt=(i("c833"),Object(L["a"])(et,Ze,Qe,!1,null,"34254691",null)),it=tt.exports;i("fb6a");function nt(e){var t=Object(C["d"])(Object(C["h"])());return{widgetList:[],formConfig:{cssCode:""},selectedId:null,selectedWidget:null,selectedWidgetName:null,vueInstance:e,formWidget:null,cssClassList:[],historyData:{index:-1,maxStep:20,steps:[]},initDesigner:function(e){this.widgetList=[],this.formConfig=Object(C["d"])(t),console.info("%cVariantForm %cVer".concat(Y["d"]," %chttps://www.yuque.com/visualdev/vform"),"color:#409EFF;font-size: 22px;font-weight:bolder","color:#999;font-size: 12px","color:#333"),e||this.initHistoryData()},clearDesigner:function(e){var i=0===this.widgetList.length;this.widgetList=[],this.selectedId=null,this.selectedWidgetName=null,this.selectedWidget={},Object(C["p"])(this.formConfig,t),e||(i?this.saveCurrentHistoryStep():this.emitHistoryChange())},loadPresetCssCode:function(e){""===this.formConfig.cssCode&&e&&(this.formConfig.cssCode=e)},getLayoutType:function(){return this.formConfig.layoutType||"PC"},changeLayoutType:function(e){this.formConfig.layoutType=e},getImportTemplate:function(){return{widgetList:[],formConfig:Object(C["d"])(t)}},loadFormJson:function(e){var t=!1;return e&&e.widgetList&&(this.formWidget.clearWidgetRefList(),this.widgetList=e.widgetList,t=!0),e&&e.formConfig&&(Object(C["p"])(this.formConfig,e.formConfig),t=!0),t&&this.emitEvent("form-json-imported",[]),t},setSelected:function(e){e?(this.selectedWidget=e,e.id&&(this.selectedId=e.id,this.selectedWidgetName=e.options.name)):this.clearSelected()},updateSelectedWidgetNameAndLabel:function(e,t,i){this.selectedWidgetName=t,i&&Object.keys(e.options).indexOf("label")>-1&&(e.options.label=i)},clearSelected:function(){this.selectedId=null,this.selectedWidgetName=null,this.selectedWidget={}},checkWidgetMove:function(e){if(e.draggedContext&&e.draggedContext.element){var t=e.draggedContext.element.category;e.draggedContext.element.type;if(e.to&&"sub-form-table"===e.to.className&&"container"===t)return!1}return!0},checkFieldMove:function(e){if(e.draggedContext&&e.draggedContext.element){e.draggedContext.element.category;var t=e.draggedContext.element.type+"";if(e.to&&"sub-form-table"===e.to.className&&"slot"===t)return!1}return!0},appendTableRow:function(e){var t=e.rows.length,i=Object(C["d"])(e.rows[e.rows.length-1]);i.id="table-row-"+Object(C["e"])(),i.merged=!1,i.cols.forEach((function(e){e.id="table-cell-"+Object(C["e"])(),e.options.name=e.id,e.merged=!1,e.options.colspan=1,e.options.rowspan=1,e.widgetList.length=0})),e.rows.splice(t,0,i),this.emitHistoryChange()},appendTableCol:function(e){var t=this,i=e.rows[0].cols.length;e.rows.forEach((function(e){var n=Object(C["d"])(t.getContainerByType("table-cell"));n.id="table-cell-"+Object(C["e"])(),n.options.name=n.id,n.merged=!1,n.options.colspan=1,n.options.rowspan=1,n.widgetList.length=0,e.cols.splice(i,0,n)})),this.emitHistoryChange()},insertTableRow:function(e,t,i,n,o){var l=o?t:t+1;if(!o){var s=l,a=!1;while(s<e.rows.length){if(!e.rows[s].cols[n].merged){l=s,a=!0;break}s++}a||(l=e.rows.length)}var r=Object(C["d"])(e.rows[i]);r.id="table-row-"+Object(C["e"])(),r.merged=!1,r.cols.forEach((function(e){e.id="table-cell-"+Object(C["e"])(),e.options.name=e.id,e.merged=!1,e.options.colspan=1,e.options.rowspan=1,e.widgetList.length=0})),e.rows.splice(l,0,r);var d=0;while(l<e.rows.length-1&&d<e.rows[0].cols.length){var c=e.rows[l+1].cols[d],u=c.merged;if(u){for(var f=e.rows,p={},m=null,g=l;g>=0;g--)if(!f[g].cols[d].merged&&f[g].cols[d].options.rowspan>1){m=g,p=f[g].cols[d];break}if(p.options){var h=p.options.rowspan+1;this.setPropsOfMergedRows(e.rows,m,d,p.options.colspan,h),d+=p.options.colspan}else d+=1}else d+=c.options.colspan||1}this.emitHistoryChange()},insertTableCol:function(e,t,i,n){var o=this,l=n?t:t+1;if(!n){var s=l,a=!1;while(s<e.rows[i].cols.length){if(!e.rows[i].cols[s].merged){l=s,a=!0;break}s++,a||(l=e.rows[i].cols.length)}}e.rows.forEach((function(e){var t=Object(C["d"])(o.getContainerByType("table-cell"));t.id="table-cell-"+Object(C["e"])(),t.options.name=t.id,t.merged=!1,t.options.colspan=1,t.options.rowspan=1,t.widgetList.length=0,e.cols.splice(l,0,t)}));var r=0;while(l<e.rows[0].cols.length-1&&r<e.rows.length){var d=e.rows[r].cols[l+1],c=d.merged;if(c){for(var u=e.rows[r].cols,f={},p=null,m=l;m>=0;m--)if(!u[m].merged&&u[m].options.colspan>1){p=m,f=u[m];break}if(f.options){var g=f.options.colspan+1;this.setPropsOfMergedCols(e.rows,r,p,g,f.options.rowspan),r+=f.options.rowspan}else r+=1}else r+=d.options.rowspan||1}this.emitHistoryChange()},setPropsOfMergedCols:function(e,t,i,n,o){for(var l=t;l<t+o;l++)for(var s=i;s<i+n;s++)l!==t||s!==i?(e[l].cols[s].merged=!0,e[l].cols[s].options.colspan=n,e[l].cols[s].widgetList=[]):e[l].cols[s].options.colspan=n},setPropsOfMergedRows:function(e,t,i,n,o){for(var l=t;l<t+o;l++)for(var s=i;s<i+n;s++)l!==t||s!==i?(e[l].cols[s].merged=!0,e[l].cols[s].options.rowspan=o,e[l].cols[s].widgetList=[]):e[l].cols[s].options.rowspan=o},setPropsOfSplitCol:function(e,t,i,n,o){for(var l=t;l<t+o;l++)for(var s=i;s<i+n;s++)e[l].cols[s].merged=!1,e[l].cols[s].options.rowspan=1,e[l].cols[s].options.colspan=1},setPropsOfSplitRow:function(e,t,i,n,o){for(var l=t;l<t+o;l++)for(var s=i;s<i+n;s++)e[l].cols[s].merged=!1,e[l].cols[s].options.rowspan=1,e[l].cols[s].options.colspan=1},mergeTableCol:function(e,t,i,n,o,l){var s=o?n:n+t[n].options.colspan,a=o?n-1:n;if(o){var r=a;while(r>=0){if(!e[i].cols[r].merged){a=r;break}r--}}t[s].widgetList&&t[s].widgetList.length>0&&(t[a].widgetList&&0!==t[a].widgetList.length||(t[a].widgetList=Object(C["d"])(t[s].widgetList)));var d=1*t[s].options.colspan+1*t[a].options.colspan;this.setPropsOfMergedCols(e,i,a,d,l.options.rowspan),this.emitHistoryChange()},mergeTableWholeRow:function(e,t,i,n){for(var o=e[i].cols[0].options.rowspan,l=!1,s=1;s<e[i].cols.length;s++)if(e[i].cols[s].options.rowspan!==o){l=!0;break}if(l)this.vueInstance.$message.info(this.vueInstance.i18nt("designer.hint.rowspanNotConsistentForMergeEntireRow"));else{var a=t.filter((function(e){return!e.merged&&!!e.widgetList&&e.widgetList.length>0}));a&&a.length>0&&a[0].id!==t[0].id&&(!t[0].widgetList||t[0].widgetList.length<=0)&&(t[0].widgetList=Object(C["d"])(a[0].widgetList)),this.setPropsOfMergedCols(e,i,0,t.length,t[n].options.rowspan),this.emitHistoryChange()}},mergeTableRow:function(e,t,i,n,o){var l=n?t:t+o.options.rowspan,s=n?t-1:t;if(n){var a=s;while(a>=0){if(!e[a].cols[i].merged){s=a;break}a--}}e[l].cols[i].widgetList&&e[l].cols[i].widgetList.length>0&&(e[s].cols[i].widgetList&&0!==e[s].cols[i].widgetList.length||(e[s].cols[i].widgetList=Object(C["d"])(e[l].cols[i].widgetList)));var r=1*e[l].cols[i].options.rowspan+1*e[s].cols[i].options.rowspan;this.setPropsOfMergedRows(e,s,i,o.options.colspan,r),this.emitHistoryChange()},mergeTableWholeCol:function(e,t,i,n){for(var o=e[0].cols[n].options.colspan,l=!1,s=1;s<e.length;s++)if(e[s].cols[n].options.colspan!==o){l=!0;break}if(l)this.vueInstance.$message.info(this.vueInstance.i18nt("designer.hint.colspanNotConsistentForMergeEntireColumn"));else{var a=[];e.forEach((function(e){var t=e.cols[n];!t.merged&&t.widgetList&&t.widgetList.length>0&&a.push(t)}));var r=e[0].cols[n];a&&a.length>0&&a[0].id!==r.id&&(!r.widgetList||r.widgetList.length<=0)&&(r.widgetList=Object(C["d"])(a[0].widgetList)),this.setPropsOfMergedRows(e,0,n,r.options.colspan,e.length),this.emitHistoryChange()}},undoMergeTableCol:function(e,t,i,n,o){this.setPropsOfSplitCol(e,t,i,n,o),this.emitHistoryChange()},undoMergeTableRow:function(e,t,i,n,o){this.setPropsOfSplitRow(e,t,i,n,o),this.emitHistoryChange()},deleteTableWholeCol:function(e,t){var i=!0;if(e.forEach((function(t){t.cols[0].options.colspan!==e[0].cols.length&&(i=!1)})),i)this.vueInstance.$message.info(this.vueInstance.i18nt("designer.hint.lastColCannotBeDeleted"));else{for(var n=e[0].cols[t].options.colspan,o=!1,l=1;l<e.length;l++)if(e[l].cols[t].options.colspan!==n){o=!0;break}o?this.vueInstance.$message.info(this.vueInstance.i18nt("designer.hint.colspanNotConsistentForDeleteEntireColumn")):(e.forEach((function(e){e.cols.splice(t,n)})),this.emitHistoryChange())}},deleteTableWholeRow:function(e,t){var i=!0;if(e[0].cols.forEach((function(t){t.options.rowspan!==e.length&&(i=!1)})),i)this.vueInstance.$message.info(this.vueInstance.i18nt("designer.hint.lastRowCannotBeDeleted"));else{for(var n=e[t].cols[0].options.rowspan,o=!1,l=1;l<e[t].cols.length;l++)if(e[t].cols[l].options.rowspan!==n){o=!0;break}o?this.vueInstance.$message.info(this.vueInstance.i18nt("designer.hint.rowspanNotConsistentForDeleteEntireRow")):(e.splice(t,n),this.emitHistoryChange())}},getContainerByType:function(e){var t=[].concat(Object(N["a"])(g),Object(N["a"])(h),Object(N["a"])(b),Object(N["a"])(v)),i=null;return t.forEach((function(t){t.category&&t.type&&t.type===e&&(i=t)})),i},getFieldWidgetByType:function(e){var t=[].concat(Object(N["a"])(g),Object(N["a"])(h),Object(N["a"])(b),Object(N["a"])(v)),i=null;return t.forEach((function(t){!t.category&&t.type&&t.type===e&&(i=t)})),i},hasConfig:function(e,t){var i=null;return i=e.category?this.getContainerByType(e.type):this.getFieldWidgetByType(e.type),!(!i||!i.options)&&Object.keys(i.options).indexOf(t)>-1},upgradeWidgetConfig:function(e){var t=null;t=e.category?this.getContainerByType(e.type):this.getFieldWidgetByType(e.type),t&&t.options&&Object.keys(t.options).forEach((function(i){e.hasOwnProperty(i)||(e.options[i]=Object(C["d"])(t.options[i]))}))},upgradeFormConfig:function(e){var t=this;Object.keys(this.formConfig).forEach((function(i){e.hasOwnProperty(i)||(e[i]=Object(C["d"])(t.formConfig[i]))}))},cloneGridCol:function(e,t){var i=Object(C["d"])(this.getContainerByType("grid-col"));i.options.span=e.options.span;var n=Object(C["e"])();i.id="grid-col-"+n,i.options.name="gridCol"+n,t.cols.push(i)},cloneContainer:function(e){var t=this;if("grid"===e.type){var i=Object(C["d"])(this.getContainerByType("grid"));return i.id=i.type+Object(C["e"])(),i.options.name=i.id,e.cols.forEach((function(e){var n=Object(C["d"])(t.getContainerByType("grid-col")),o=Object(C["e"])();n.id="grid-col-"+o,n.options.name="gridCol"+o,n.options.span=e.options.span,i.cols.push(n)})),i}if("table"===e.type){var n=Object(C["d"])(this.getContainerByType("table"));return n.id=n.type+Object(C["e"])(),n.options.name=n.id,e.rows.forEach((function(e){var t=Object(C["d"])(e);t.id="table-row-"+Object(C["e"])(),t.cols.forEach((function(e){e.id="table-cell-"+Object(C["e"])(),e.options.name=e.id,e.widgetList=[]})),n.rows.push(t)})),n}return null},moveUpWidget:function(e,t){if(e){if(0===t)return void this.vueInstance.$message(this.vueInstance.i18nt("designer.hint.moveUpFirstChildHint"));var i=e[t];e.splice(t,1),e.splice(t-1,0,i)}},moveDownWidget:function(e,t){if(e){if(t===e.length-1)return void this.vueInstance.$message(this.vueInstance.i18nt("designer.hint.moveDownLastChildHint"));var i=e[t];e.splice(t,1),e.splice(t+1,0,i)}},copyNewFieldWidget:function(e){var t=Object(C["d"])(e),i=Object(C["e"])();return t.id=t.type.replace(/-/g,"")+i,t.options.name=t.id,t.options.label=t.options.label||t.type.toLowerCase(),delete t.displayName,t},copyNewContainerWidget:function(e){var t=Object(C["d"])(e);if(t.id=t.type.replace(/-/g,"")+Object(C["e"])(),t.options.name=t.id,"grid"===t.type){var i=Object(C["d"])(this.getContainerByType("grid-col")),n=Object(C["e"])();i.id="grid-col-"+n,i.options.name="gridCol"+n,t.cols.push(i),i=Object(C["d"])(i),n=Object(C["e"])(),i.id="grid-col-"+n,i.options.name="gridCol"+n,t.cols.push(i)}else if("table"===t.type){var o={cols:[]};o.id="table-row-"+Object(C["e"])(),o.merged=!1;var l=Object(C["d"])(this.getContainerByType("table-cell"));l.id="table-cell-"+Object(C["e"])(),l.options.name=l.id,l.merged=!1,l.options.colspan=1,l.options.rowspan=1,o.cols.push(l),t.rows.push(o)}else if("tab"===t.type){var s=Object(C["d"])(this.getContainerByType("tab-pane"));s.id="tab-pane-"+Object(C["e"])(),s.options.name="tab1",s.options.label="tab 1",t.tabs.push(s)}return delete t.displayName,t},addContainerByDbClick:function(e){var t=this.copyNewContainerWidget(e);this.widgetList.push(t),this.setSelected(t)},addFieldByDbClick:function(e){var t=this.copyNewFieldWidget(e);if(this.selectedWidget&&"tab"===this.selectedWidget.type){var i=this.selectedWidget.tabs[0];this.selectedWidget.tabs.forEach((function(e){e.options.active&&(i=e)})),i&&i.widgetList.push(t)}else this.selectedWidget&&this.selectedWidget.widgetList?this.selectedWidget.widgetList.push(t):this.widgetList.push(t);this.setSelected(t),this.emitHistoryChange()},deleteColOfGrid:function(e,t){e&&e.cols&&e.cols.splice(t,1)},addNewColOfGrid:function(e){var t=e.cols,i=Object(C["d"])(this.getContainerByType("grid-col")),n=Object(C["e"])();if(i.id="grid-col-"+n,i.options.name="gridCol"+n,t&&t.length>0){var o=0;t.forEach((function(e){o+=e.options.span})),o>=24?(console.log("列栅格之和超出24"),e.cols.push(i)):(i.options.span=24-o>12?12:24-o,e.cols.push(i))}else e.cols=[i]},addTabPaneOfTabs:function(e){var t=e.tabs,i=Object(C["d"])(this.getContainerByType("tab-pane"));i.id="tab-pane-"+Object(C["e"])(),i.options.name=i.id,i.options.label="tab "+(t.length+1),t.push(i)},deleteTabPaneOfTabs:function(e,t){e.tabs.splice(t,1)},emitEvent:function(e,t){this.vueInstance.$emit(e,t)},handleEvent:function(e,t){this.vueInstance.$on(e,(function(e){return t(e)}))},setCssClassList:function(e){this.cssClassList=e},getCssClassList:function(){return this.cssClassList},registerFormWidget:function(e){this.formWidget=e},initHistoryData:function(){this.loadFormContentFromStorage(),this.historyData.index++,this.historyData.steps[this.historyData.index]={widgetList:Object(C["d"])(this.widgetList),formConfig:Object(C["d"])(this.formConfig)}},emitHistoryChange:function(){this.historyData.index===this.historyData.maxStep-1?this.historyData.steps.shift():this.historyData.index++,this.historyData.steps[this.historyData.index]={widgetList:Object(C["d"])(this.widgetList),formConfig:Object(C["d"])(this.formConfig)},this.saveFormContentToStorage(),this.historyData.index<this.historyData.steps.length-1&&(this.historyData.steps=this.historyData.steps.slice(0,this.historyData.index+1))},saveCurrentHistoryStep:function(){this.historyData.steps[this.historyData.index]=Object(C["d"])({widgetList:this.widgetList,formConfig:this.formConfig}),this.saveFormContentToStorage()},undoHistoryStep:function(){0!==this.historyData.index&&this.historyData.index--,this.widgetList=Object(C["d"])(this.historyData.steps[this.historyData.index].widgetList),this.formConfig=Object(C["d"])(this.historyData.steps[this.historyData.index].formConfig)},redoHistoryStep:function(){this.historyData.index!==this.historyData.steps.length-1&&this.historyData.index++,this.widgetList=Object(C["d"])(this.historyData.steps[this.historyData.index].widgetList),this.formConfig=Object(C["d"])(this.historyData.steps[this.historyData.index].formConfig)},undoEnabled:function(){return this.historyData.index>0&&this.historyData.steps.length>0},redoEnabled:function(){return this.historyData.index<this.historyData.steps.length-1},saveFormContentToStorage:function(){window.localStorage.setItem("widget__list__backup",JSON.stringify(this.widgetList)),window.localStorage.setItem("form__config__backup",JSON.stringify(this.formConfig))},loadFormContentFromStorage:function(){var e=window.localStorage.getItem("widget__list__backup");e&&(this.widgetList=JSON.parse(e));var t=window.localStorage.getItem("form__config__backup");t&&Object(C["p"])(this.formConfig,JSON.parse(t))}}}var ot={name:"VFormDesigner",componentName:"VFormDesigner",mixins:[_["b"]],components:{WidgetPanel:I,ToolbarPanel:Ce,SettingPanel:Ke,VFormWidget:it,SvgIcon:W},props:{fieldListApi:{type:Object,default:null},bannedWidgets:{type:Array,default:function(){return[]}},designerConfig:{type:Object,default:function(){return{languageMenu:!0,externalLink:!1,formTemplates:!0,eventCollapse:!0,widgetNameReadonly:!1,clearDesignerButton:!0,previewFormButton:!0,importJsonButton:!0,exportJsonButton:!0,exportCodeButton:!0,generateSFCButton:!0,toolbarMaxWidth:420,toolbarMinWidth:300,presetCssCode:"",resetFormJson:!1}}},globalDsv:{type:Object,default:function(){return{}}}},data:function(){return{vFormVersion:Y["d"],curLangName:"",vsCodeFlag:!1,caseName:"",docUrl:"https://www.vform666.com/document.html",gitUrl:"https://github.com/vform666/variant-form",chatUrl:"https://www.vform666.com/pages/chat-group/",subScribeUrl:"https://www.vform666.com/pages/pro/",scrollerHeight:0,designer:nt(this),fieldList:[]}},provide:function(){var e=this;return{serverFieldList:this.fieldList,getDesignerConfig:function(){return e.designerConfig},getBannedWidgets:function(){return e.bannedWidgets}}},created:function(){this.vsCodeFlag=1==Object(C["i"])("vscode"),this.caseName=Object(C["i"])("case")},mounted:function(){var e=this;this.initLocale(),this.scrollerHeight=window.innerHeight-56-36+"px",Object(C["a"])((function(){e.$nextTick((function(){e.scrollerHeight=window.innerHeight-56-36+"px"}))})),this.loadCase(),this.loadFieldListFromServer()},methods:{showLink:function(e){return void 0===this.designerConfig[e]||!!this.designerConfig[e]},openHome:function(){if(this.vsCodeFlag){var e={cmd:"openUrl",data:{url:"https://www.vform666.com/"}};window.parent.postMessage(e,"*")}},openUrl:function(e,t){if(this.vsCodeFlag){var i={cmd:"openUrl",data:{url:t}};window.parent.postMessage(i,"*")}else{var n=e.currentTarget;n.href=t}},loadCase:function(){var e=this;this.caseName&&l.a.get(Y["c"]+this.caseName+".txt").then((function(t){t.data.code?e.$message.error(e.i18nt("designer.hint.sampleLoadedFail")):(e.setFormJson(t.data),e.$message.success(e.i18nt("designer.hint.sampleLoadedSuccess")))})).catch((function(t){e.$message.error(e.i18nt("designer.hint.sampleLoadedFail")+":"+t)}))},initLocale:function(){var e=localStorage.getItem("v_form_locale");e=this.vsCodeFlag?e||"en-US":e||"zh-CN",this.curLangName=this.i18nt("application."+e),this.changeLanguage(e)},loadFieldListFromServer:function(){var e=this;if(this.fieldListApi){var t=this.fieldListApi.headers||{};l.a.get(this.fieldListApi.URL,{headers:t}).then((function(t){var i=e.fieldListApi.labelKey||"label",n=e.fieldListApi.nameKey||"name";e.fieldList.splice(0,e.fieldList.length),t.data.forEach((function(t){e.fieldList.push({label:t[i],name:t[n]})}))})).catch((function(t){e.$message.error(t)}))}},handleLanguageChanged:function(e){this.changeLanguage(e),this.curLangName=this.i18nt("application."+e)},changeLanguage:function(e){Object(_["a"])(e)},setFormJson:function(e){var t=!1;e&&("string"===typeof e?t=this.designer.loadFormJson(JSON.parse(e)):e.constructor===Object&&(t=this.designer.loadFormJson(e)),t&&this.designer.emitHistoryChange())},getFormJson:function(){return{widgetList:Object(C["d"])(this.designer.widgetList),formConfig:Object(C["d"])(this.designer.formConfig)}},clearDesigner:function(){this.$refs.toolbarRef.clearFormWidget()},refreshDesigner:function(){var e=this.getFormJson();this.designer.clearDesigner(!0),this.designer.loadFormJson(e)},previewForm:function(){this.$refs.toolbarRef.previewForm()},importJson:function(){this.$refs.toolbarRef.importJson()},exportJson:function(){this.$refs.toolbarRef.exportJson()},exportCode:function(){this.$refs.toolbarRef.exportCode()},generateSFC:function(){this.$refs.toolbarRef.generateSFC()},getFieldWidgets:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return e?Object(C["g"])(e):Object(C["g"])(this.designer.widgetList)},getContainerWidgets:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return e?Object(C["f"])(e):Object(C["f"])(this.designer.widgetList)},upgradeFormJson:function(e){var t=this;if(e.widgetList&&e.formConfig)return Object(C["q"])(e.widgetList,(function(e){t.designer.upgradeWidgetConfig(e)})),this.designer.upgradeFormConfig(e.formConfig),e;this.$message.error("Invalid form json!")},getWidgetRef:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return this.$refs["formRef"].getWidgetRef(e,t)},getSelectedWidgetRef:function(){return this.$refs["formRef"].getSelectedWidgetRef()}}},lt=ot,st=(i("0086"),Object(L["a"])(lt,r,d,!1,null,"7a9423a6",null)),at=st.exports,rt={name:"App",components:{VFormDesigner:at},data:function(){return{jsonDefData:{},formId:"",designerConfig:{externalLink:!1,resetFormJson:!1,toolbarMaxWidth:490},globalDsv:{testApiHost:"http://www.test.com/api",testPort:8080}}},created:function(){this.formId=Object(C["i"])("formId"),console.log("formId:"+this.formId)},mounted:function(){console.log("formId:"+this.formId);var e=this;l.a.post("/service-common/sys-form-def/get-by-id",{id:this.formId}).then((function(t){var i=t.data;if(i.success)try{var n=i.data.designerData,o=JSON.stringify(n,null,"  "),l=JSON.parse(o),s=JSON.parse(l);e.jsonDefData=s,e.$refs.vfDesignerRef.designer.loadFormJson(s),e.$refs.vfDesignerRef.designer.emitHistoryChange(),e.$refs.vfDesignerRef.designer.emitEvent("form-json-imported",[])}catch(a){console.log("err"),console.log(a),e.$message.error(a+"")}else this.$message.error(i.message)})).catch((function(e){console.log(e)}))},methods:{saveJson:function(){var e=this.$refs.vfDesignerRef.getFormJson(),t=JSON.stringify(e),i=this;l.a.post("/service-common/sys-form-def/save-form-by-id",{id:this.formId,designerData:t}).then((function(e){var t=e.data;t.success?i.$message.success(t.message):i.$message.error(t.message)})).catch((function(e){console.log(e)}))}}},dt=rt,ct=(i("5c0b"),Object(L["a"])(dt,s,a,!1,null,null,null)),ut=ct.exports,ft=i("5c96"),pt=i.n(ft);i("caad"),i("2532");n["default"].directive("dialogDrag",{bind:function(e,t,i,n){var o=e.querySelector(".el-dialog__header"),l=e.querySelector(".el-dialog");o.style.cursor="move";var s=l.currentStyle||window.getComputedStyle(l,null);o.onmousedown=function(e){var t,i,n=e.clientX-o.offsetLeft,a=e.clientY-o.offsetTop;s.left.includes("%")?(t=+document.body.clientWidth*(+s.left.replace(/%/g,"")/100),i=+document.body.clientHeight*(+s.top.replace(/%/g,"")/100)):(t=+s.left.replace(/px/g,""),i=+s.top.replace(/px/g,"")),document.onmousemove=function(e){var o=e.clientX-n,s=e.clientY-a;l.style.left="".concat(o+t,"px"),l.style.top="".concat(s+i,"px")},document.onmouseup=function(e){document.onmousemove=null,document.onmouseup=null}}}}),n["default"].directive("dialogDragWidth",{bind:function(e,t,i,n){var o=t.value.$el.querySelector(".el-dialog");e.onmousedown=function(t){var i=t.clientX-e.offsetLeft;document.onmousemove=function(e){e.preventDefault();var t=e.clientX-i;o.style.width="".concat(t,"px")},document.onmouseup=function(e){document.onmousemove=null,document.onmouseup=null}}}});i("985d"),i("0fae"),i("b20f"),i("0f59"),i("a9e3");var mt=function(e,t){return{props:{optionModel:Object},render:function(i){var n=this;return i("el-form-item",{attrs:{label:Object(_["c"])(t)}},[i("el-input",{attrs:{type:"text"},model:{value:n.optionModel[e],callback:function(t){n.$set(n.optionModel,e,t)}}})])}}},gt=function(e,t){return{props:{optionModel:Object},render:function(i){var n=this;return i("el-form-item",{attrs:{label:Object(_["c"])(t)}},[i("el-switch",{model:{value:n.optionModel[e],callback:function(t){n.$set(n.optionModel,e,t)}}})])}}},ht=function(e,t,i){return{props:{optionModel:Object},render:function(n){var o=this;return n("el-form-item",{attrs:{label:Object(_["c"])(t)}},[n("el-radio-group",{model:{value:o.optionModel[e],callback:function(t){o.$set(o.optionModel,e,t)}}},[i.optionItems.map((function(e){return n("el-radio-button",{attrs:{label:e.value}},[e.label])}))])])}}},bt=function(e,t,i){return{props:{optionModel:Object},render:function(n){var o=this;return n("el-form-item",{attrs:{label:Object(_["c"])(t)}},[n("el-select",{model:{value:o.optionModel[e],callback:function(t){o.$set(o.optionModel,e,t)}}},[i.optionItems.map((function(e){return n("el-option",{attrs:{label:e.label,value:e.value}})}))])])}}},vt=function(e,t){return{props:{optionModel:Object},mixins:[$["a"]],methods:{editEventHandler:function(){this.dispatch("SettingPanel","editEventHandler",[e,Object(N["a"])(t)])}},render:function(t){return t("el-form-item",{attrs:{label:e,"label-width":"150px"}},[t("el-button",{attrs:{type:"info",icon:"el-icon-edit",plain:!0,round:!0},on:{click:this.editEventHandler}},[Object(_["c"])("designer.setting.addEventHandler")])])}}},wt={type:"card",category:"container",icon:"card",widgetList:[],options:{name:"",label:"card",hidden:!1,folded:!1,showFold:!0,cardWidth:"100%",shadow:"never",customClass:""}},yt={type:"alert",icon:"alert",formItemFlag:!1,options:{name:"",title:"Good things are coming...",type:"info",description:"",closable:!0,closeText:"",center:!0,showIcon:!1,effect:"light",hidden:!1,onClose:"",customClass:""}},xt=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("container-wrapper",{attrs:{designer:e.designer,widget:e.widget,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList}},[i("el-card",{key:e.widget.id,staticClass:"card-container",class:[e.selected?"selected":"",e.widget.options.folded?"folded":"",e.customClass],style:{width:e.widget.options.cardWidth+"!important"||!1},attrs:{shadow:e.widget.options.shadow},nativeOn:{click:function(t){return t.stopPropagation(),e.selectWidget(e.widget)}}},[i("div",{staticClass:"clear-fix",attrs:{slot:"header"},slot:"header"},[i("span",[e._v(e._s(e.widget.options.label))]),e.widget.options.showFold?i("i",{staticClass:"float-right",class:[e.widget.options.folded?"el-icon-arrow-up":"el-icon-arrow-down"],on:{click:e.toggleCard}}):e._e()]),i("draggable",e._b({attrs:{list:e.widget.widgetList,handle:".drag-handler",move:e.checkContainerMove},on:{add:function(t){return e.onContainerDragAdd(t,e.widget.widgetList)},update:e.onContainerDragUpdate}},"draggable",{group:"dragGroup",ghostClass:"ghost",animation:200},!1),[i("transition-group",{staticClass:"form-widget-list",attrs:{name:"fade",tag:"div"}},[e._l(e.widget.widgetList,(function(t,n){return["container"===t.category?[i(t.type+"-widget",{key:t.id,tag:"component",attrs:{widget:t,designer:e.designer,"parent-list":e.widget.widgetList,"index-of-parent-list":n,"parent-widget":e.widget}})]:[i(t.type+"-widget",{key:t.id,tag:"component",attrs:{field:t,designer:e.designer,"parent-list":e.widget.widgetList,"index-of-parent-list":n,"parent-widget":e.widget,"design-state":!0}})]]}))],2)],1)],1)],1)},Ct=[],_t=i("61ca"),Ot=i("cd36"),Ft=i("26a6"),Mt={name:"card-widget",componentName:"ContainerWidget",mixins:[_["b"],_t["a"],Ft["a"]],inject:["refList"],components:Object(f["a"])({Draggable:m.a,ContainerWrapper:Ot["default"]},B["a"]),props:{widget:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object},computed:{selected:function(){return this.widget.id===this.designer.selectedId},customClass:function(){return this.widget.options.customClass||""}},created:function(){this.initRefList()},methods:{checkContainerMove:function(e){return!0},toggleCard:function(){this.widget.options.folded=!this.widget.options.folded},setFolded:function(e){this.widget.options.folded=!!e}}},jt=Mt,Lt=(i("09c3"),Object(L["a"])(jt,xt,Ct,!1,null,"228afde5",null)),kt=Lt.exports,Wt=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("container-item-wrapper",{attrs:{widget:e.widget}},[i("el-card",{directives:[{name:"show",rawName:"v-show",value:!e.widget.options.hidden,expression:"!widget.options.hidden"}],key:e.widget.id,ref:e.widget.id,staticClass:"card-container",class:[e.widget.options.folded?"folded":"",e.customClass],style:{width:e.widget.options.cardWidth+"!important"||!1},attrs:{shadow:e.widget.options.shadow}},[i("div",{staticClass:"clear-fix",attrs:{slot:"header"},slot:"header"},[i("span",[e._v(e._s(e.widget.options.label))]),e.widget.options.showFold?i("i",{staticClass:"float-right",class:[e.widget.options.folded?"el-icon-arrow-up":"el-icon-arrow-down"],on:{click:e.toggleCard}}):e._e()]),e.widget.widgetList&&e.widget.widgetList.length>0?[e._l(e.widget.widgetList,(function(t,n){return["container"===t.category?[i(e.getComponentByContainer(t),{key:n,tag:"component",attrs:{widget:t,"parent-list":e.widget.widgetList,"index-of-parent-list":n,"parent-widget":e.widget},scopedSlots:e._u([e._l(Object.keys(e.$scopedSlots),(function(t){return{key:t,fn:function(i){return[e._t(t,null,null,i)]}}}))],null,!0)})]:[i(t.type+"-widget",{key:n,tag:"component",attrs:{field:t,designer:null,"parent-list":e.widget.widgetList,"index-of-parent-list":n,"parent-widget":e.widget},scopedSlots:e._u([e._l(Object.keys(e.$scopedSlots),(function(t){return{key:t,fn:function(i){return[e._t(t,null,null,i)]}}}))],null,!0)})]]}))]:e._e()],2)],1)},St=[],Et=i("d67f"),Dt=i("90c2"),It=i("34f0"),Rt={name:"card-item",componentName:"ContainerItem",mixins:[$["a"],_["b"],Et["a"],It["a"]],components:Object(f["a"])({ContainerItemWrapper:Dt["default"]},B["a"]),props:{widget:Object},inject:["refList","sfRefList","globalModel"],computed:{customClass:function(){return this.widget.options.customClass||""}},created:function(){this.initRefList()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{toggleCard:function(){this.widget.options.folded=!this.widget.options.folded}}},Tt=Rt,Pt=(i("2e46"),Object(L["a"])(Tt,Wt,St,!1,null,"2a1af67f",null)),Ht=Pt.exports,Nt=(i("a4d3"),i("e01a"),function(e,t){var i=e.options,n=ne(e),o=i.cardWidth?'style="{width: '.concat(i.cardWidth,' !important}"'):"",l='shadow="'.concat(i.shadow,'"'),s=i.hidden?'v-show="false"':"",a='<div class="card-container">\n  <el-card '.concat(n," ").concat(o," ").concat(l," ").concat(s,'>\n    <div slot="header" class="clear-fix">\n      <span>').concat(i.label,"</span>\n      ").concat(i.showFold?'<i class="float-right el-icon-arrow-down"></i>':"","\n    </div>\n    ").concat(e.widgetList.map((function(e){return"container"===e.category?le(e,t):ue(e,t)})).join(""),"\n  </el-card>\n</div>");return a}),$t=function(e,t){var i=e.options,n='title="'.concat(i.title,'"'),o="type=".concat(i.type),l=i.description?'description="'.concat(i.description,'"'):"",s=':closable="'.concat(i.closable,'"'),a=i.closeText?'close-text="'.concat(i.closeText,'"'):"",r=':center="'.concat(i.center,'"'),d=':show-icon="'.concat(i.showIcon,'"'),c='effect="'.concat(i.effect,'"'),u="<el-alert ".concat(n," ").concat(o," ").concat(l," ").concat(s," ").concat(a," ").concat(r," \n  ").concat(d," ").concat(c,">\n</el-alert>");return u},Vt=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("static-content-wrapper",{attrs:{designer:e.designer,field:e.field,"design-state":e.designState,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList,"sub-form-row-index":e.subFormRowIndex,"sub-form-col-index":e.subFormColIndex,"sub-form-row-id":e.subFormRowId}},[i("el-alert",{ref:"fieldEditor",attrs:{title:e.field.options.title,type:e.field.options.type,description:e.field.options.description,closable:e.field.options.closable,center:e.field.options.center,"close-text":e.field.options.closeText,"show-icon":e.field.options.showIcon,effect:e.field.options.effect},on:{close:e.handleCloseCustomEvent}})],1)},At=[],Bt=i("828b"),zt=i("2d11"),Ut={name:"alert-widget",componentName:"FieldWidget",mixins:[$["a"],zt["a"],_["b"]],props:{field:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object,designState:{type:Boolean,default:!1},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""}},components:{StaticContentWrapper:Bt["default"]},created:function(){this.registerToRefList(),this.initEventHandler()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{handleCloseCustomEvent:function(){if(this.field.options.onClose){var e=new Function(this.field.options.onClose);e.call(this)}}}},Jt=Ut,qt=Object(L["a"])(Jt,Vt,At,!1,null,"b1f52dec",null),Gt=qt.exports,Kt=function(){w(wt),n["default"].component(kt.name,kt),n["default"].component(Ht.name,Ht),$e("card-folded","card-folded-editor",gt("folded","extension.setting.cardFolded")),$e("card-showFold","card-showFold-editor",gt("showFold","extension.setting.cardShowFold")),$e("card-cardWidth","card-cardWidth-editor",mt("cardWidth","extension.setting.cardWidth"));var e=[{label:"never",value:"never"},{label:"hover",value:"hover"},{label:"always",value:"always"}];$e("card-shadow","card-shadow-editor",bt("shadow","extension.setting.cardShadow",{optionItems:e})),ge("card",Nt),y(yt),n["default"].component(Gt.name,Gt),$e("alert-title","alert-title-editor",mt("title","extension.setting.alertTitle"));var t=[{label:"success",value:"success"},{label:"warning",value:"warning"},{label:"info",value:"info"},{label:"error",value:"error"}];n["default"].component("alert-type-editor",bt("type","extension.setting.alertType",{optionItems:t})),$e("alert-description","alert-description-editor",mt("description","extension.setting.description")),$e("alert-closable","alert-closable-editor",gt("closable","extension.setting.closable")),$e("alert-closeText","alert-closeText-editor",mt("closeText","extension.setting.closeText")),$e("alert-center","alert-center-editor",gt("center","extension.setting.center")),$e("alert-showIcon","alert-showIcon-editor",gt("showIcon","extension.setting.showIcon"));var i=[{label:"light",value:"light"},{label:"dark",value:"dark"}];$e("alert-effect","alert-effect-editor",ht("effect","extension.setting.effect",{optionItems:i})),Ve("alert-onClose","alert-onClose-editor",vt("onClose",[])),he("alert",$t)};Kt(),n["default"].use(pt.a,{size:"small"}),"undefined"!==typeof window&&(window.axios=l.a),n["default"].config.productionTip=!1,new n["default"]({el:"#app",render:function(e){return e(ut)}})},5835:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("container-wrapper",{attrs:{designer:e.designer,widget:e.widget,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList}},[i("div",{key:e.widget.id,staticClass:"tab-container",class:{selected:e.selected},on:{click:function(t){return t.stopPropagation(),e.selectWidget(e.widget)}}},[i("el-tabs",{attrs:{type:e.widget.displayType},on:{"tab-click":e.onTabClick},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},e._l(e.widget.tabs,(function(t,n){return i("el-tab-pane",{key:n,attrs:{label:t.options.label,name:t.options.name},nativeOn:{click:function(t){return t.stopPropagation(),e.selectWidget(e.widget)}}},[i("draggable",e._b({attrs:{list:t.widgetList,handle:".drag-handler",move:e.checkContainerMove},on:{add:function(i){return e.onContainerDragAdd(i,t.widgetList)},update:e.onContainerDragUpdate}},"draggable",{group:"dragGroup",ghostClass:"ghost",animation:200},!1),[i("transition-group",{staticClass:"form-widget-list",attrs:{name:"fade",tag:"div"}},[e._l(t.widgetList,(function(n,o){return["container"===n.category?[i(n.type+"-widget",{key:n.id,tag:"component",attrs:{widget:n,designer:e.designer,"parent-list":t.widgetList,"index-of-parent-list":o,"parent-widget":e.widget}})]:[i(n.type+"-widget",{key:n.id,tag:"component",attrs:{field:n,designer:e.designer,"parent-list":t.widgetList,"index-of-parent-list":o,"parent-widget":e.widget,"design-state":!0}})]]}))],2)],1)],1)})),1)],1)])},o=[],l=i("5530"),s=(i("a9e3"),i("d3b7"),i("159b"),i("b0c0"),i("b76a")),a=i.n(s),r=i("79fa"),d=i("61ca"),c=i("cd36"),u=i("c029"),f=i("26a6"),p={name:"tab-widget",componentName:"ContainerWidget",mixins:[r["b"],d["a"],f["a"]],inject:["refList"],components:Object(l["a"])({ContainerWrapper:c["default"],Draggable:a.a},u["a"]),props:{widget:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object},data:function(){return{activeTab:"tab1"}},computed:{selected:function(){return this.widget.id===this.designer.selectedId},customClass:function(){return this.widget.options.customClass||""}},watch:{},created:function(){this.initRefList()},mounted:function(){},methods:{onTabClick:function(e){var t=e.paneName;this.widget.tabs.forEach((function(e){e.options.active=e.options.name===t}))}}},m=p,g=(i("a817"),i("2877")),h=Object(g["a"])(m,n,o,!1,null,"5f891f14",null);t["default"]=h.exports},5905:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.round")}},[i("el-switch",{model:{value:e.optionModel.round,callback:function(t){e.$set(e.optionModel,"round",t)},expression:"optionModel.round"}})],1)},o=[],l=i("79fa"),s={name:"round-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"f1c29618",null);t["default"]=d.exports},"5a42":function(e,t,i){"use strict";i("4415")},"5bc5":function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-color-field",use:"icon-color-field-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-color-field"><defs><style type="text/css"></style></defs><path d="M619.52 490.666667h-0.853333l-85.333334-85.333334h0.853334z" p-id="52732" /><path d="M619.52 507.733333h-0.853333a17.066667 17.066667 0 1 1 0-34.133333c9.437867 0 17.476267 7.645867 17.476266 17.066667s-7.185067 17.066667-16.622933 17.066666z m-85.333333-85.333333c-9.437867 0-17.493333-7.645867-17.493334-17.066667s7.202133-17.066667 16.64-17.066666h0.853334a17.066667 17.066667 0 1 1 0 34.133333zM192 866.133333a34.133333 34.133333 0 0 1-24.132267-58.2656l42.666667-42.666666a34.133333 34.133333 0 1 1 48.264533 48.264533l-42.666666 42.666667a33.962667 33.962667 0 0 1-24.132267 10.001066z" p-id="52733" /><path d="M619.52 490.666667l-0.426667-0.426667L534.186667 405.333333l212.48-213.333333h85.333333v85.333333z" p-id="52734" /><path d="M662.186667 567.466667a33.9968 33.9968 0 0 1-24.132267-10.001067l-170.666667-170.666667a34.133333 34.133333 0 0 1 48.2816-48.2816l170.666667 170.666667A34.133333 34.133333 0 0 1 662.186667 567.466667z" p-id="52735" /><path d="M320 806.4h-85.333333a17.066667 17.066667 0 0 1-17.066667-17.066667v-85.333333c0-4.539733 1.792-8.874667 5.000533-12.066133l299.093334-299.093334a16.520533 16.520533 0 0 1 3.566933-2.730666l209.3056-210.141867c3.208533-3.208533 7.560533-5.0176 12.100267-5.0176h85.333333a17.066667 17.066667 0 0 1 17.066667 17.066667v85.333333a17.066667 17.066667 0 0 1-4.9664 12.049067l-212.48 213.333333a17.954133 17.954133 0 0 1-3.618134 2.781867l-295.936 295.918933a17.134933 17.134933 0 0 1-12.066133 4.9664z m-68.266667-34.133333h61.201067l294.0928-294.0928a16.520533 16.520533 0 0 1 3.566933-2.730667L814.933333 270.2848V209.066667h-61.184L546.286933 417.3824a17.954133 17.954133 0 0 1-3.618133 2.781867L251.733333 711.0656v61.201067z" p-id="52736" /></symbol>'});s.a.add(a);t["default"]=a},"5c0b":function(e,t,i){"use strict";i("9c0c")},"5ce3":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{"label-width":"0"}},[i("el-divider",{staticClass:"custom-divider-margin-top"},[e._v(e._s(e.i18nt("designer.setting.optionsSetting")))]),i("option-items-setting",{attrs:{designer:e.designer,"selected-widget":e.selectedWidget}})],1)},o=[],l=i("79fa"),s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"option-items-pane"},["radio"===e.selectedWidget.type||"select"===e.selectedWidget.type&&!e.selectedWidget.options.multiple?i("el-radio-group",{on:{change:e.emitDefaultValueChange},model:{value:e.optionModel.defaultValue,callback:function(t){e.$set(e.optionModel,"defaultValue",t)},expression:"optionModel.defaultValue"}},[i("draggable",e._b({attrs:{tag:"ul",list:e.optionModel.optionItems}},"draggable",{group:"optionsGroup",ghostClass:"ghost",handle:".drag-option"},!1),e._l(e.optionModel.optionItems,(function(t,n){return i("li",{key:n},[i("el-radio",{attrs:{label:t.value}},[i("el-input",{staticStyle:{width:"100px"},attrs:{size:"mini"},model:{value:t.value,callback:function(i){e.$set(t,"value",i)},expression:"option.value"}}),i("el-input",{staticStyle:{width:"100px"},attrs:{size:"mini"},model:{value:t.label,callback:function(i){e.$set(t,"label",i)},expression:"option.label"}}),i("i",{staticClass:"iconfont icon-drag drag-option"}),i("el-button",{staticClass:"col-delete-button",attrs:{circle:"",plain:"",size:"mini",type:"danger",icon:"el-icon-minus"},on:{click:function(i){return e.deleteOption(t,n)}}})],1)],1)})),0)],1):"checkbox"===e.selectedWidget.type||"select"===e.selectedWidget.type&&e.selectedWidget.options.multiple?i("el-checkbox-group",{on:{change:e.emitDefaultValueChange},model:{value:e.optionModel.defaultValue,callback:function(t){e.$set(e.optionModel,"defaultValue",t)},expression:"optionModel.defaultValue"}},[i("draggable",e._b({attrs:{tag:"ul",list:e.optionModel.optionItems}},"draggable",{group:"optionsGroup",ghostClass:"ghost",handle:".drag-option"},!1),e._l(e.optionModel.optionItems,(function(t,n){return i("li",{key:n},[i("el-checkbox",{attrs:{label:t.value}},[i("el-input",{staticStyle:{width:"100px"},attrs:{size:"mini"},model:{value:t.value,callback:function(i){e.$set(t,"value",i)},expression:"option.value"}}),i("el-input",{staticStyle:{width:"100px"},attrs:{size:"mini"},model:{value:t.label,callback:function(i){e.$set(t,"label",i)},expression:"option.label"}}),i("i",{staticClass:"iconfont icon-drag drag-option"}),i("el-button",{staticClass:"col-delete-button",attrs:{circle:"",plain:"",size:"mini",type:"danger",icon:"el-icon-minus"},on:{click:function(i){return e.deleteOption(t,n)}}})],1)],1)})),0)],1):"cascader"===e.selectedWidget.type?i("el-cascader",{staticStyle:{width:"100%"},attrs:{options:e.optionModel.optionItems,placeholder:e.i18nt("render.hint.selectPlaceholder")},on:{change:e.emitDefaultValueChange},model:{value:e.optionModel.defaultValue,callback:function(t){e.$set(e.optionModel,"defaultValue",t)},expression:"optionModel.defaultValue"}}):e._e(),"cascader"===e.selectedWidget.type?i("div",[i("el-button",{attrs:{type:"text"},on:{click:e.importCascaderOptions}},[e._v(e._s(e.i18nt("designer.setting.importOptions")))]),i("el-button",{attrs:{type:"text"},on:{click:e.resetDefault}},[e._v(e._s(e.i18nt("designer.setting.resetDefault")))])],1):e._e(),"radio"===e.selectedWidget.type||"checkbox"===e.selectedWidget.type||"select"===e.selectedWidget.type?i("div",[i("el-button",{attrs:{type:"text"},on:{click:e.addOption}},[e._v(e._s(e.i18nt("designer.setting.addOption")))]),i("el-button",{attrs:{type:"text"},on:{click:e.importOptions}},[e._v(e._s(e.i18nt("designer.setting.importOptions")))]),i("el-button",{attrs:{type:"text"},on:{click:e.resetDefault}},[e._v(e._s(e.i18nt("designer.setting.resetDefault")))])],1):e._e(),e.showImportDialogFlag?i("el-dialog",{staticClass:"small-padding-dialog",attrs:{title:e.i18nt("designer.setting.importOptions"),visible:e.showImportDialogFlag,"show-close":!0,"append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.showImportDialogFlag=t}}},[i("el-form-item",[i("el-input",{attrs:{type:"textarea",rows:"10"},model:{value:e.optionLines,callback:function(t){e.optionLines=t},expression:"optionLines"}})],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{size:"large",type:"primary"},on:{click:e.saveOptions}},[e._v(e._s(e.i18nt("designer.hint.confirm")))]),i("el-button",{attrs:{size:"large",type:""},on:{click:function(t){e.showImportDialogFlag=!1}}},[e._v(e._s(e.i18nt("designer.hint.cancel")))])],1)],1):e._e(),e.showImportCascaderDialogFlag?i("el-dialog",{staticClass:"small-padding-dialog",attrs:{title:e.i18nt("designer.setting.importOptions"),visible:e.showImportCascaderDialogFlag,"show-close":!0,"append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.showImportCascaderDialogFlag=t}}},[i("code-editor",{attrs:{mode:"json",readonly:!1},model:{value:e.cascaderOptions,callback:function(t){e.cascaderOptions=t},expression:"cascaderOptions"}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{size:"large",type:"primary"},on:{click:e.saveCascaderOptions}},[e._v(e._s(e.i18nt("designer.hint.confirm")))]),i("el-button",{attrs:{size:"large",type:""},on:{click:function(t){e.showImportCascaderDialogFlag=!1}}},[e._v(e._s(e.i18nt("designer.hint.cancel")))])],1)],1):e._e()],1)},a=[],r=(i("b0c0"),i("a434"),i("d3b7"),i("159b"),i("ac1f"),i("1276"),i("498a"),i("e9c4"),i("b76a")),d=i.n(r),c=i("9470"),u={name:"OptionItemsSetting",mixins:[l["b"]],components:{Draggable:d.a,CodeEditor:c["a"]},props:{designer:Object,selectedWidget:Object},data:function(){return{showImportDialogFlag:!1,optionLines:"",cascaderOptions:"",showImportCascaderDialogFlag:!1,separator:","}},computed:{optionModel:function(){return this.selectedWidget.options}},watch:{"selectedWidget.options":{deep:!0,handler:function(e){}}},methods:{emitDefaultValueChange:function(){if(this.designer&&this.designer.formWidget){var e=this.designer.formWidget.getWidgetRef(this.selectedWidget.options.name);e&&e.refreshDefaultValue&&e.refreshDefaultValue()}},deleteOption:function(e,t){this.optionModel.optionItems.splice(t,1)},addOption:function(){var e=this.optionModel.optionItems.length+1;this.optionModel.optionItems.push({value:e,label:"new option"})},importOptions:function(){var e=this;this.optionLines="",this.optionModel.optionItems.length>0&&this.optionModel.optionItems.forEach((function(t){t.value===t.label?e.optionLines+=t.value+"\n":e.optionLines+=t.value+e.separator+t.label+"\n"})),this.showImportDialogFlag=!0},saveOptions:function(){var e=this,t=this.optionLines.split("\n");t.length>0?(this.optionModel.optionItems=[],t.forEach((function(t){t&&t.trim()&&(-1!==t.indexOf(e.separator)?e.optionModel.optionItems.push({value:t.split(e.separator)[0],label:t.split(e.separator)[1]}):e.optionModel.optionItems.push({value:t,label:t}))}))):this.optionModel.optionItems=[],this.showImportDialogFlag=!1},resetDefault:function(){"checkbox"===this.selectedWidget.type||"select"===this.selectedWidget.type&&this.selectedWidget.options.multiple?this.optionModel.defaultValue=[]:this.optionModel.defaultValue="",this.emitDefaultValueChange()},importCascaderOptions:function(){this.cascaderOptions=JSON.stringify(this.optionModel.optionItems,null,"  "),this.showImportCascaderDialogFlag=!0},saveCascaderOptions:function(){try{var e=JSON.parse(this.cascaderOptions);this.optionModel.optionItems=e,this.showImportCascaderDialogFlag=!1}catch(t){this.$message.error(this.i18nt("designer.hint.invalidOptionsData")+t.message)}}}},f=u,p=(i("6277"),i("2877")),m=Object(p["a"])(f,s,a,!1,null,"ba79f704",null),g=m.exports,h={name:"optionItems-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},components:{OptionItemsSetting:g}},b=h,v=Object(p["a"])(b,n,o,!1,null,"a79f1ebe",null);t["default"]=v.exports},"5d50":function(e,t,i){"use strict";i("aaa9")},"5f2d":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticStyle:{display:"none"}})},o=[],l={name:"select-defaultValue-editor",props:{designer:Object,selectedWidget:Object,optionModel:Object}},s=l,a=i("2877"),r=Object(a["a"])(s,n,o,!1,null,"16342388",null);t["default"]=r.exports},"5fe5":function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-cascader-field",use:"icon-cascader-field-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-cascader-field"><defs><style type="text/css"></style></defs><path d="M661.377376 411.069935V475.664516H314.175312v395.654882h557.144086V475.664516h-48.447312V411.069935h48.447312c35.674839 0 64.594581 28.919742 64.59458 64.594581v395.654882c0 35.674839-28.919742 64.594581-64.59458 64.59458h-557.144086c-35.674839 0-64.600086-28.919742-64.600086-64.59458V475.664516c0-35.674839 28.925247-64.594581 64.600086-64.594581h347.202064z m48.447312-322.983913c35.674839 0 64.600086 28.919742 64.600086 64.59458v403.731269c0 35.674839-28.925247 64.594581-64.600086 64.594581H362.622624v-64.594581h347.202064V152.680602h-557.144086v403.731269h48.447312V621.006452h-48.447312c-35.674839 0-64.594581-28.919742-64.59458-64.594581V152.680602C88.086022 117.005763 117.005763 88.086022 152.680602 88.086022h557.144086z" p-id="66339" /></symbol>'});s.a.add(a);t["default"]=a},6038:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.required")}},[i("el-switch",{model:{value:e.optionModel.required,callback:function(t){e.$set(e.optionModel,"required",t)},expression:"optionModel.required"}})],1)},o=[],l=i("79fa"),s={name:"required-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"7464dd76",null);t["default"]=d.exports},6043:function(e,t,i){},"612b":function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-undo",use:"icon-undo-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-undo"><defs><style type="text/css"></style></defs><path d="M609.206 396.656h-415.702l201.87-199.152c17.787-17.787 17.787-43.185 0-60.973s-41.555-18.641-59.42-0.778l-273.097 266.804c-17.787 17.787-17.787 41.399 0 59.186l273.173 267.115c17.787 17.787 41.555 17.787 59.42-0.078 17.787-17.787 17.787-41.166 0-58.953l-201.948-195.501h415.702c166.219 0 311.155 129.712 311.155 290.029v41.555c0 23.769 15.069 41.555 38.836 41.555s38.836-17.787 38.836-41.555v-41.555c0-207.852-175.073-367.7-388.828-367.7z" p-id="3383" /></symbol>'});s.a.add(a);t["default"]=a},"61ca":function(e,t,i){"use strict";i("a434");t["a"]={inject:["getGlobalDsv"],methods:{appendTableRow:function(e){this.designer.appendTableRow(e)},appendTableCol:function(e){this.designer.appendTableCol(e)},onContainerDragAdd:function(e,t){var i=e.newIndex;t[i]&&this.designer.setSelected(t[i]),this.designer.emitHistoryChange(),this.designer.emitEvent("field-selected",this.widget)},onContainerDragUpdate:function(){this.designer.emitHistoryChange()},checkContainerMove:function(e){return this.designer.checkWidgetMove(e)},selectWidget:function(e){this.designer.setSelected(e)},selectParentWidget:function(){this.parentWidget?this.designer.setSelected(this.parentWidget):this.designer.clearSelected()},moveUpWidget:function(){this.designer.moveUpWidget(this.parentList,this.indexOfParentList),this.designer.emitHistoryChange()},moveDownWidget:function(){this.designer.moveDownWidget(this.parentList,this.indexOfParentList),this.designer.emitHistoryChange()},cloneContainer:function(e){if(this.parentList){var t=this.designer.cloneContainer(e);this.parentList.splice(this.indexOfParentList+1,0,t),this.designer.setSelected(t),this.designer.emitHistoryChange()}},removeWidget:function(){var e=this;if(this.parentList){var t=this.designer.selectedWidgetName,i=null;1===this.parentList.length?this.parentWidget&&(i=this.parentWidget):i=this.parentList.length===1+this.indexOfParentList?this.parentList[this.indexOfParentList-1]:this.parentList[this.indexOfParentList+1],this.$nextTick((function(){e.parentList.splice(e.indexOfParentList,1),e.designer.setSelected(i),e.designer.formWidget.deleteWidgetRef(t),e.designer.emitHistoryChange()}))}},setWidgetOption:function(e,t){this.widget.options.hasOwnProperty(e)&&(this.widget.options[e]=t)}}}},"61e5":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.displayType")}},[i("el-select",{model:{value:e.optionModel.type,callback:function(t){e.$set(e.optionModel,"type",t)},expression:"optionModel.type"}},[i("el-option",{attrs:{label:"default",value:""}}),i("el-option",{attrs:{label:"primary",value:"primary"}}),i("el-option",{attrs:{label:"success",value:"success"}}),i("el-option",{attrs:{label:"warning",value:"warning"}}),i("el-option",{attrs:{label:"danger",value:"danger"}}),i("el-option",{attrs:{label:"info",value:"info"}}),i("el-option",{attrs:{label:"text",value:"text"}})],1)],1)},o=[],l=i("79fa"),s=i("b2bf"),a={name:"button-type-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"20d8ef71",null);t["default"]=c.exports},"623d":function(e,t,i){"use strict";i("9d21")},6277:function(e,t,i){"use strict";i("c60b")},6425:function(e,t,i){"use strict";i("4daf")},"653d":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.defaultValue")}},[i("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0,max:e.optionModel.max},on:{change:e.emitDefaultValueChange},model:{value:e.optionModel.defaultValue,callback:function(t){e.$set(e.optionModel,"defaultValue",t)},expression:"optionModel.defaultValue"}})],1)},o=[],l=i("79fa"),s=i("b2bf"),a={name:"rate-defaultValue-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"42328733",null);t["default"]=c.exports},6592:function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-data-table",use:"icon-data-table-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-data-table"><defs><style type="text/css"></style></defs><path d="M915.692 39.385H108.308c-15.754 0-29.539 13.784-29.539 29.538v98.462c0 15.753 13.785 29.538 29.539 29.538h807.384c15.754 0 29.539-13.785 29.539-29.538V68.923c0-15.754-13.785-29.538-29.539-29.538zM285.538 275.692h-177.23c-15.754 0-29.539 13.785-29.539 29.539v59.077c0 15.754 13.785 29.538 29.539 29.538h177.23c15.754 0 29.539-13.784 29.539-29.538V305.23c0-15.754-13.785-29.539-29.539-29.539z m315.077 0h-177.23c-15.754 0-29.539 13.785-29.539 29.539v59.077c0 15.754 13.785 29.538 29.539 29.538h177.23c15.754 0 29.539-13.784 29.539-29.538V305.23c0-15.754-13.785-29.539-29.539-29.539z m315.077 0h-177.23c-15.754 0-29.539 13.785-29.539 29.539v59.077c0 15.754 13.785 29.538 29.539 29.538h177.23c15.754 0 29.539-13.784 29.539-29.538V305.23c0-15.754-13.785-29.539-29.539-29.539zM285.538 472.615h-177.23c-15.754 0-29.539 13.785-29.539 29.539v59.077c0 15.754 13.785 29.538 29.539 29.538h177.23c15.754 0 29.539-13.784 29.539-29.538v-59.077c0-15.754-13.785-29.539-29.539-29.539z m315.077 0h-177.23c-15.754 0-29.539 13.785-29.539 29.539v59.077c0 15.754 13.785 29.538 29.539 29.538h177.23c15.754 0 29.539-13.784 29.539-29.538v-59.077c0-15.754-13.785-29.539-29.539-29.539z m315.077 0h-177.23c-15.754 0-29.539 13.785-29.539 29.539v59.077c0 15.754 13.785 29.538 29.539 29.538h177.23c15.754 0 29.539-13.784 29.539-29.538v-59.077c0-15.754-13.785-29.539-29.539-29.539zM285.538 669.538h-177.23c-15.754 0-29.539 13.785-29.539 29.539v59.077c0 15.754 13.785 29.538 29.539 29.538h177.23c15.754 0 29.539-13.784 29.539-29.538v-59.077c0-15.754-13.785-29.539-29.539-29.539z m315.077 0h-177.23c-15.754 0-29.539 13.785-29.539 29.539v59.077c0 15.754 13.785 29.538 29.539 29.538h177.23c15.754 0 29.539-13.784 29.539-29.538v-59.077c0-15.754-13.785-29.539-29.539-29.539z m315.077 0h-177.23c-15.754 0-29.539 13.785-29.539 29.539v59.077c0 15.754 13.785 29.538 29.539 29.538h177.23c15.754 0 29.539-13.784 29.539-29.538v-59.077c0-15.754-13.785-29.539-29.539-29.539zM285.538 866.462h-177.23c-15.754 0-29.539 13.784-29.539 29.538v59.077c0 15.754 13.785 29.538 29.539 29.538h177.23c15.754 0 29.539-13.784 29.539-29.538V896c0-15.754-13.785-29.538-29.539-29.538z m315.077 0h-177.23c-15.754 0-29.539 13.784-29.539 29.538v59.077c0 15.754 13.785 29.538 29.539 29.538h177.23c15.754 0 29.539-13.784 29.539-29.538V896c0-15.754-13.785-29.538-29.539-29.538z m315.077 0h-177.23c-15.754 0-29.539 13.784-29.539 29.538v59.077c0 15.754 13.785 29.538 29.539 29.538h177.23c15.754 0 29.539-13.784 29.539-29.538V896c0-15.754-13.785-29.538-29.539-29.538z" p-id="11935" /></symbol>'});s.a.add(a);t["default"]=a},"65d0":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",[i("span",{attrs:{slot:"label"},slot:"label"},[e._v(e._s(e.i18nt("designer.setting.validation"))+" "),i("el-tooltip",{attrs:{effect:"light",content:e.i18nt("designer.setting.validationHelp")}},[i("i",{staticClass:"el-icon-info"})])],1),i("el-select",{attrs:{filterable:"",clearable:"","allow-create":"","default-first-option":""},model:{value:e.optionModel.validation,callback:function(t){e.$set(e.optionModel,"validation",t)},expression:"optionModel.validation"}},e._l(e.fieldValidators,(function(e,t){return i("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1)},o=[],l=i("79fa"),s={name:"validation-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},data:function(){return{fieldValidators:[{value:"number",label:this.i18nt("designer.hint.numberValidator")},{value:"letter",label:this.i18nt("designer.hint.letterValidator")},{value:"letterAndNumber",label:this.i18nt("designer.hint.letterAndNumberValidator")},{value:"mobilePhone",label:this.i18nt("designer.hint.mobilePhoneValidator")},{value:"email",label:this.i18nt("designer.hint.emailValidator")},{value:"url",label:this.i18nt("designer.hint.urlValidator")},{value:"noChinese",label:this.i18nt("designer.hint.noChineseValidator")},{value:"chinese",label:this.i18nt("designer.hint.chineseValidator")}]}}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"9621bce6",null);t["default"]=d.exports},"663b":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.switchWidth")}},[i("el-input-number",{staticStyle:{width:"100%"},model:{value:e.optionModel.switchWidth,callback:function(t){e.$set(e.optionModel,"switchWidth",t)},expression:"optionModel.switchWidth"}})],1)},o=[],l=i("79fa"),s={name:"switchWidth-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"556424f5",null);t["default"]=d.exports},6656:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.valueFormat")}},[i("el-select",{attrs:{filterable:"","allow-create":""},model:{value:e.optionModel.valueFormat,callback:function(t){e.$set(e.optionModel,"valueFormat",t)},expression:"optionModel.valueFormat"}},[i("el-option",{attrs:{label:"yyyy-MM-dd",value:"yyyy-MM-dd"}}),i("el-option",{attrs:{label:"yyyy-MM-dd HH:mm:ss",value:"yyyy-MM-dd HH:mm:ss"}})],1)],1)},o=[],l=i("79fa"),s={name:"date-valueFormat-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"3f656d0a",null);t["default"]=d.exports},6732:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("el-form-item",{attrs:{label:e.i18nt("designer.setting.gridColHeight")}},[i("el-input",{staticClass:"hide-spin-button",attrs:{type:"number",min:"0"},nativeOn:{input:function(t){return e.inputNumberHandler.apply(null,arguments)}},model:{value:e.optionModel.colHeight,callback:function(t){e.$set(e.optionModel,"colHeight",t)},expression:"optionModel.colHeight"}})],1)],1)},o=[],l=i("79fa"),s=i("b2bf"),a={name:"colHeight-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"9bc52e60",null);t["default"]=c.exports},"67ab":function(e,t,i){},6873:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.suffixIcon")}},[i("el-input",{attrs:{type:"text"},model:{value:e.optionModel.suffixIcon,callback:function(t){e.$set(e.optionModel,"suffixIcon",t)},expression:"optionModel.suffixIcon"}})],1)},o=[],l=i("79fa"),s={name:"suffixIcon-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"bef2ffc8",null);t["default"]=d.exports},6947:function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-date-range-field",use:"icon-date-range-field-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-date-range-field"><defs><style type="text/css"></style></defs><path d="M887.467 192.853H786.773v-73.386c0-10.24-6.826-17.067-17.066-17.067s-17.067 6.827-17.067 17.067v73.386H303.787v-73.386c0-10.24-6.827-17.067-17.067-17.067s-17.067 6.827-17.067 17.067v73.386H168.96c-46.08 0-85.333 37.547-85.333 85.334v558.08c0 46.08 37.546 85.333 85.333 85.333h718.507c46.08 0 85.333-37.547 85.333-85.333v-558.08c0-47.787-37.547-85.334-85.333-85.334zM168.96 226.987h100.693v66.56c0 10.24 6.827 17.066 17.067 17.066s17.067-6.826 17.067-17.066v-66.56h450.56v66.56c0 10.24 6.826 17.066 17.066 17.066s17.067-6.826 17.067-17.066v-66.56h98.987c27.306 0 51.2 22.186 51.2 51.2v88.746H117.76v-88.746c0-29.014 22.187-51.2 51.2-51.2z m718.507 660.48H168.96c-27.307 0-51.2-22.187-51.2-51.2v-435.2h820.907v435.2c0 27.306-22.187 51.2-51.2 51.2z" p-id="46574" /><path d="M858.453 493.227H327.68c-10.24 0-17.067 6.826-17.067 17.066V624.64H194.56c-10.24 0-17.067 6.827-17.067 17.067v133.12c0 10.24 6.827 17.066 17.067 17.066H460.8c10.24 0 17.067-6.826 17.067-17.066V660.48h380.586c10.24 0 17.067-6.827 17.067-17.067v-133.12c0-10.24-6.827-17.066-17.067-17.066zM445.44 527.36v97.28h-98.987v-97.28h98.987z m-230.4 131.413h98.987v98.987H215.04v-98.987z m131.413 97.28v-97.28h98.987v97.28h-98.987z m133.12-228.693h97.28v98.987h-97.28V527.36z m131.414 0h98.986v98.987h-98.986V527.36z m230.4 97.28H742.4v-98.987h98.987v98.987z" p-id="46575" /></symbol>'});s.a.add(a);t["default"]=a},"6a79":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("td",{staticClass:"table-cell",class:[e.customClass],style:{width:e.widget.options.cellWidth+" !important"||!1,height:e.widget.options.cellHeight+" !important"||!1,"word-break":e.widget.options.wordBreak?"break-all":"normal"},attrs:{colspan:e.widget.options.colspan||1,rowspan:e.widget.options.rowspan||1}},[e._l(e.widget.widgetList,(function(t,n){return["container"===t.category?[i(e.getComponentByContainer(t),{key:n,tag:"component",attrs:{widget:t,"parent-list":e.widget.widgetList,"index-of-parent-list":n,"parent-widget":e.widget},scopedSlots:e._u([e._l(Object.keys(e.$scopedSlots),(function(t){return{key:t,fn:function(i){return[e._t(t,null,null,i)]}}}))],null,!0)})]:[i(t.type+"-widget",{key:n,tag:"component",attrs:{field:t,"parent-list":e.widget.widgetList,"index-of-parent-list":n,"parent-widget":e.widget},scopedSlots:e._u([e._l(Object.keys(e.$scopedSlots),(function(t){return{key:t,fn:function(i){return[e._t(t,null,null,i)]}}}))],null,!0)})]]}))],2)},o=[],l=i("5530"),s=(i("a9e3"),i("c6e3")),a=i("79fa"),r=i("d67f"),d=i("c029"),c={name:"TableCellItem",componentName:"ContainerItem",mixins:[s["a"],a["b"],r["a"]],components:Object(l["a"])({},d["a"]),props:{widget:Object,rowIndex:Number,colIndex:Number},inject:["refList","globalModel"],computed:{customClass:function(){return this.widget.options.customClass||""}},created:function(){},methods:{}},u=c,f=(i("77e3"),i("2877")),p=Object(f["a"])(u,n,o,!1,null,"a6efbede",null);t["default"]=p.exports},"6bcd":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.requiredHint")}},[i("el-input",{attrs:{type:"text"},model:{value:e.optionModel.requiredHint,callback:function(t){e.$set(e.optionModel,"requiredHint",t)},expression:"optionModel.requiredHint"}})],1)},o=[],l=i("79fa"),s=i("b2bf"),a={name:"requiredHint-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"184a7e8e",null);t["default"]=c.exports},"6c26":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:"onChange","label-width":"150px"}},[i("el-button",{attrs:{type:"info",icon:"el-icon-edit",plain:"",round:""},on:{click:function(t){return e.editEventHandler("onChange",e.eventParams)}}},[e._v(" "+e._s(e.i18nt("designer.setting.addEventHandler")))])],1)},o=[],l=i("79fa"),s=i("7d6c"),a={name:"onChange-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},data:function(){return{eventParams:["value","oldValue","subFormData","rowId"]}}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"4d21c55c",null);t["default"]=c.exports},"6c62":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.defaultValue")}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:e.optionModel.type,format:e.optionModel.format,"value-format":e.optionModel.valueFormat},on:{change:e.emitDefaultValueChange},model:{value:e.optionModel.defaultValue,callback:function(t){e.$set(e.optionModel,"defaultValue",t)},expression:"optionModel.defaultValue"}})],1)},o=[],l=i("79fa"),s=i("b2bf"),a={name:"date-defaultValue-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"040cc7a6",null);t["default"]=c.exports},"6caf":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.filterable")}},[i("el-switch",{model:{value:e.optionModel.filterable,callback:function(t){e.$set(e.optionModel,"filterable",t)},expression:"optionModel.filterable"}})],1)},o=[],l=i("79fa"),s={name:"filterable-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"5c0c4b3f",null);t["default"]=d.exports},"6ce9":function(e,t,i){},"6d04":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("form-item-wrapper",{attrs:{designer:e.designer,field:e.field,rules:e.rules,"design-state":e.designState,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList,"sub-form-row-index":e.subFormRowIndex,"sub-form-col-index":e.subFormColIndex,"sub-form-row-id":e.subFormRowId}},[i("el-checkbox-group",{ref:"fieldEditor",attrs:{disabled:e.field.options.disabled,size:e.field.options.size},on:{change:e.handleChangeEvent},model:{value:e.fieldModel,callback:function(t){e.fieldModel=t},expression:"fieldModel"}},[e.field.options.buttonStyle?e._l(e.field.options.optionItems,(function(t,n){return i("el-checkbox-button",{key:n,style:{display:e.field.options.displayStyle},attrs:{label:t.value,disabled:t.disabled,border:e.field.options.border}},[e._v(e._s(t.label))])})):e._l(e.field.options.optionItems,(function(t,n){return i("el-checkbox",{key:n,style:{display:e.field.options.displayStyle},attrs:{label:t.value,disabled:t.disabled,border:e.field.options.border}},[e._v(e._s(t.label))])}))],2)],1)},o=[],l=(i("a9e3"),i("9eeb")),s=i("c6e3"),a=i("79fa"),r=i("2d11"),d={name:"checkbox-widget",componentName:"FieldWidget",mixins:[s["a"],r["a"],a["b"]],props:{field:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object,designState:{type:Boolean,default:!1},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""}},components:{FormItemWrapper:l["default"]},inject:["refList","formConfig","globalOptionData","globalModel"],data:function(){return{oldFieldValue:null,fieldModel:null,rules:[]}},computed:{},beforeCreate:function(){},created:function(){this.initOptionItems(),this.initFieldModel(),this.registerToRefList(),this.initEventHandler(),this.buildFieldRules(),this.handleOnCreated()},mounted:function(){this.handleOnMounted()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{}},c=d,u=(i("cde5"),i("2877")),f=Object(u["a"])(c,n,o,!1,null,"608e81d3",null);t["default"]=f.exports},"6d29":function(e,t,i){"use strict";i("ceaf")},"6e3b":function(e,t,i){},"6e5c":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("static-content-wrapper",{attrs:{designer:e.designer,field:e.field,"design-state":e.designState,"display-style":e.field.options.displayStyle,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList,"sub-form-row-index":e.subFormRowIndex,"sub-form-col-index":e.subFormColIndex,"sub-form-row-id":e.subFormRowId}},[i("el-button",{ref:"fieldEditor",attrs:{type:e.field.options.type,size:e.field.options.size,plain:e.field.options.plain,round:e.field.options.round,circle:e.field.options.circle,icon:e.field.options.icon,disabled:e.field.options.disabled},nativeOn:{click:function(t){return e.handleButtonWidgetClick.apply(null,arguments)}}},[e._v(" "+e._s(e.field.options.label))])],1)},o=[],l=(i("a9e3"),i("828b")),s=i("c6e3"),a=i("79fa"),r=i("2d11"),d={name:"button-widget",componentName:"FieldWidget",mixins:[s["a"],r["a"],a["b"]],props:{field:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object,designState:{type:Boolean,default:!1},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""}},components:{StaticContentWrapper:l["default"]},computed:{},beforeCreate:function(){},created:function(){this.registerToRefList(),this.initEventHandler(),this.handleOnCreated()},mounted:function(){this.handleOnMounted()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{}},c=d,u=(i("b620"),i("2877")),f=Object(u["a"])(c,n,o,!1,null,"1293f105",null);t["default"]=f.exports},"6f49":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.circle")}},[i("el-switch",{model:{value:e.optionModel.circle,callback:function(t){e.$set(e.optionModel,"circle",t)},expression:"optionModel.circle"}})],1)},o=[],l=i("79fa"),s={name:"circle-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"46cbc20e",null);t["default"]=d.exports},7007:function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-slider-field",use:"icon-slider-field-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-slider-field"><defs><style type="text/css"></style></defs><path d="M951.453 476.844H523.672a131.836 131.836 0 0 0-254.18 0H72.547v70.312h196.945a131.836 131.836 0 0 0 254.18 0h427.781z" p-id="53484" /></symbol>'});s.a.add(a);t["default"]=a},"70db":function(e,t,i){},"714d":function(e,t,i){},"71bc":function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_require__.d(__webpack_exports__,"b",(function(){return buildDefaultValueListFn})),__webpack_require__.d(__webpack_exports__,"d",(function(){return buildRulesListFn})),__webpack_require__.d(__webpack_exports__,"c",(function(){return buildFieldOptionsFn})),__webpack_require__.d(__webpack_exports__,"e",(function(){return buildUploadDataFn})),__webpack_require__.d(__webpack_exports__,"a",(function(){return buildActiveTabs})),__webpack_require__.d(__webpack_exports__,"f",(function(){return genVue2JS}));var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("99af"),core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0___default=__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__),core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("b0c0"),core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_1___default=__webpack_require__.n(core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_1__),core_js_modules_es_json_stringify_js__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("e9c4"),core_js_modules_es_json_stringify_js__WEBPACK_IMPORTED_MODULE_2___default=__webpack_require__.n(core_js_modules_es_json_stringify_js__WEBPACK_IMPORTED_MODULE_2__),core_js_modules_es_array_join_js__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__("a15b"),core_js_modules_es_array_join_js__WEBPACK_IMPORTED_MODULE_3___default=__webpack_require__.n(core_js_modules_es_array_join_js__WEBPACK_IMPORTED_MODULE_3__),_utils_util__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__("ca00"),_utils_i18n__WEBPACK_IMPORTED_MODULE_5__=__webpack_require__("79fa"),_utils_validators__WEBPACK_IMPORTED_MODULE_6__=__webpack_require__("a00a");function buildDefaultValueListFn(e,t,i){return function(e){var t=e.options,n=t.defaultValue;Object(_utils_util__WEBPACK_IMPORTED_MODULE_4__["m"])(n)?i.push("".concat(t.name,": ").concat(JSON.stringify(n),",")):i.push("".concat(t.name,": null,"))}}function buildRulesListFn(formConfig,widgetList,resultList){return function(fieldWidget){var fop=fieldWidget.options,fieldRules=[];if(fop.required&&fieldRules.push("{\n        required: true,\n        message: '".concat(Object(_utils_i18n__WEBPACK_IMPORTED_MODULE_5__["c"])("render.hint.fieldRequired"),"',\n      }")),fop.validation){var vldName=fop.validation;_utils_validators__WEBPACK_IMPORTED_MODULE_6__["a"][vldName]?fieldRules.push("{\n          pattern: ".concat(eval(Object(_utils_validators__WEBPACK_IMPORTED_MODULE_6__["b"])(vldName)),",\n          trigger: ['blur', 'change'],\n          message: '").concat(fop.validationHint,"'\n        }")):fieldRules.push("{\n          pattern: '".concat(eval(vldName),"',\n          trigger: ['blur', 'change'],\n          message: '").concat(fop.validationHint,"'\n        }"))}fieldRules.length>0&&resultList.push("".concat(fop.name,": [").concat(fieldRules.join(","),"],"))}}function buildFieldOptionsFn(e,t,i){return function(e){var t=e.options,n=e.type;"radio"!==n&&"checkbox"!==n&&"select"!==n&&"cascader"!==n||i.push("".concat(t.name,"Options: ").concat(JSON.stringify(t.optionItems),","))}}function buildUploadDataFn(e,t,i){return function(e){var t=e.options,n=e.type;"picture-upload"!==n&&"file-upload"!==n||(i.push("".concat(t.name,"FileList: [],")),i.push("".concat(t.name,"UploadHeaders: {},")),i.push("".concat(t.name,"UploadData: {},")))}}function buildActiveTabs(e,t){var i=[],n=function(e){var t=e.options,n=e.type;"tab"===n&&e.tabs.length>0&&i.push("'".concat(t.name,"ActiveTab': '").concat(e.tabs[0].options.name,"',"))};return Object(_utils_util__WEBPACK_IMPORTED_MODULE_4__["r"])(t,n),i}var genVue2JS=function(e,t){var i=[],n=[],o=[],l=[];Object(_utils_util__WEBPACK_IMPORTED_MODULE_4__["s"])(t,(function(s){buildDefaultValueListFn(e,t,i)(s),buildRulesListFn(e,t,n)(s),buildFieldOptionsFn(e,t,o)(s),buildUploadDataFn(e,t,l)(s)}));var s=buildActiveTabs(e,t),a="  export default {\n    components: {},\n    props: {},\n    data() {\n      return {\n        ".concat(e.modelName,": {\n          ").concat(i.join("\n"),"\n        },\n        \n        ").concat(e.rulesName,": {\n          ").concat(n.join("\n"),"\n        },\n        \n        ").concat(s.join("\n"),"\n        \n        ").concat(o.join("\n"),"\n        \n        ").concat(l.join("\n"),"\n      }\n    },\n    computed: {},\n    watch: {},\n    created() {\n    },\n    mounted() {\n    },\n    methods: {\n      submitForm() {\n        this.$refs['vForm'].validate(valid => {\n          if (!valid) return\n          \n          //TODO: 提交表单\n        })\n      },\n      \n      resetForm() {\n        this.$refs['vForm'].resetFields()\n      }\n    }\n  }");return a}},"732a":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("el-form-item",{attrs:{"label-width":"0"}},[i("el-divider",{staticClass:"custom-divider"},[e._v(e._s(e.i18nt("designer.setting.customLabelIcon")))])],1),i("el-form-item",{attrs:{label:e.i18nt("designer.setting.labelIconClass")}},[i("el-input",{attrs:{type:"text"},model:{value:e.optionModel.labelIconClass,callback:function(t){e.$set(e.optionModel,"labelIconClass",t)},expression:"optionModel.labelIconClass"}})],1)],1)},o=[],l=i("79fa"),s={name:"labelIconClass-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"64edb29a",null);t["default"]=d.exports},7331:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:"onUploadSuccess","label-width":"150px"}},[i("el-button",{attrs:{type:"info",icon:"el-icon-edit",plain:"",round:""},on:{click:function(t){return e.editEventHandler("onUploadSuccess",e.eventParams)}}},[e._v(" "+e._s(e.i18nt("designer.setting.addEventHandler")))])],1)},o=[],l=i("79fa"),s=i("7d6c"),a={name:"onUploadSuccess-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},data:function(){return{eventParams:["result","file","fileList"]}}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"40a94791",null);t["default"]=c.exports},"74c3":function(e,t,i){"use strict";i("17f0")},"768f":function(e,t,i){i("99af"),i("fb6a"),console.log=function(e){return function(){0}}(console.log)},"77e3":function(e,t,i){"use strict";i("0b6a")},7873:function(e,t,i){},7899:function(e,t,i){var n={"./button-widget.vue":"6e5c","./cascader-widget.vue":"c077","./checkbox-widget.vue":"6d04","./color-widget.vue":"3ad3","./date-range-widget.vue":"ba08","./date-widget.vue":"da4e","./divider-widget.vue":"de19","./file-upload-widget.vue":"4a70","./form-item-wrapper.vue":"9eeb","./html-text-widget.vue":"090f","./input-widget.vue":"d67b","./number-widget.vue":"cf32","./picture-upload-widget.vue":"7ede","./radio-widget.vue":"b87d","./rate-widget.vue":"2faa","./rich-editor-widget.vue":"f4c1","./select-widget.vue":"8a3e","./slider-widget.vue":"826c","./slot-widget.vue":"b8e9","./static-content-wrapper.vue":"828b","./static-text-widget.vue":"851c","./switch-widget.vue":"ecaa","./textarea-widget.vue":"84b5","./time-range-widget.vue":"cab0","./time-widget.vue":"20c0"};function o(e){var t=l(e);return i(t)}function l(e){if(!i.o(n,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}o.keys=function(){return Object.keys(n)},o.resolve=l,e.exports=o,o.id="7899"},"791d":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.minLength")}},[i("el-input",{staticClass:"hide-spin-button",staticStyle:{width:"100%"},attrs:{type:"number",min:"0"},nativeOn:{input:function(t){return e.inputNumberHandler.apply(null,arguments)}},model:{value:e.minLength,callback:function(t){e.minLength=t},expression:"minLength"}})],1)},o=[],l=(i("a9e3"),i("79fa")),s=i("b2bf"),a={name:"minLength-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},computed:{minLength:{get:function(){return this.optionModel["minLength"]},set:function(e){!e||isNaN(e)?this.optionModel.minLength=null:this.optionModel.minLength=Number(e)}}}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"78aca82c",null);t["default"]=c.exports},7962:function(e,t,i){},"79ad":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.defaultValue")}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:e.optionModel.type,format:e.optionModel.format,"value-format":e.optionModel.valueFormat},on:{change:e.emitDefaultValueChange},model:{value:e.optionModel.defaultValue,callback:function(t){e.$set(e.optionModel,"defaultValue",t)},expression:"optionModel.defaultValue"}})],1)},o=[],l=i("79fa"),s=i("b2bf"),a={name:"date-range-defaultValue-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"9f2a9a06",null);t["default"]=c.exports},"79fa":function(e,t,i){"use strict";i.d(t,"a",(function(){return W})),i.d(t,"c",(function(){return S}));var n=i("5530"),o=(i("ac1f"),i("5319"),i("a026")),l=i("53ca");i("1276"),i("d3b7"),i("159b"),i("b64b");function s(e){return void 0!==e&&null!==e}function a(e,t){var i=t.split("."),n=e;return i.forEach((function(e){n=s(n)&&s(n[e])?n[e]:null})),n}function r(e){var t=Object(l["a"])(e);return null!==e&&("object"===t||"function"===t)}var d=Object.prototype.hasOwnProperty;function c(e,t,i){var n=t[i];s(n)&&(d.call(e,i)&&r(n)?e[i]=u(Object(e[i]),t[i]):e[i]=n)}function u(e,t){return Object.keys(t).forEach((function(i){c(e,t,i)})),e}var f=function(e,t){var i=e.prototype;i.$si18n=i.$si18n||{},u(i.$si18n,t);var n=new e({data:t});Object.defineProperty(e.prototype.$si18n,"lang",{get:function(){return n.lang}}),i.$st=function(e){var t=n.messages[n.lang];if(!i.$si18n.messages)return function(){return e};for(var o=a(t,e),l=arguments.length,s=new Array(l>1?l-1:0),r=1;r<l;r++)s[r-1]=arguments[r];return"function"===typeof o?o.apply(void 0,s):null!==o?o:e},i.$st2=function(e,t){var i=n.messages[n.lang],o=a(i,e);return null!==o?o:a(i,t)},i.$si18n.add=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};u(i.$si18n.messages,e)},i.$si18n.setLang=function(e){n.lang=e},e.mixin({beforeCreate:function(){this.$options.i18n&&this.$si18n.add(this.$options.i18n)}})},p={install:f},m=i("b2d6"),g=i.n(m),h=i("f0d9"),b=i.n(h),v=i("4897"),w=i.n(v),y={application:{"zh-CN":"简体中文","en-US":"English",productTitle:"Online Form Designer",github:"GitHub",document:"Docs",qqGroup:"WeChat Group",deployment:"Deployment",subscription:"Subscription"},designer:{componentLib:"Components",formLib:"Templates",containerTitle:"Container",dragHandlerHint:"drag container or field to layout center",dragAction:"drag",basicFieldTitle:"Basic Field",advancedFieldTitle:"Advanced Field",customFieldTitle:"Customized Field",noWidgetHint:"Please select a widget from the left list, drag and drop to this container.",widgetLabel:{grid:"Grid",table:"Table",tab:"Tab",section:"Section","sub-form":"SubForm","grid-col":"GridCol","table-cell":"TableCell","tab-pane":"TabPane","data-table":"DataTable",input:"Input",textarea:"Textarea",number:"InputNumber",radio:"Radio",checkbox:"Checkbox",select:"Select",time:"Time","time-range":"Time range",date:"Date","date-range":"Date range",switch:"Switch",rate:"Rate",color:"ColorPicker",slider:"Slider","static-text":"Text","html-text":"HTML",button:"Button",divider:"Divider","picture-upload":"Picture","file-upload":"File","rich-editor":"Rich Editor",cascader:"Cascader",slot:"Slot",custom:"Custom Component"},hint:{selectParentWidget:"Select parent of this widget",moveUpWidget:"Move up this widget",moveDownWidget:"Move down this widget",cloneWidget:"Clone this widget",insertRow:"Insert new row",insertColumn:"Insert new column",remove:"Remove this widget",cellSetting:"Cell setting",dragHandler:"Drag handler",copyField:"Copy field widget",onlyFieldWidgetAcceptable:"Only field widget can be dragged into sub-form",moveUpFirstChildHint:"First child can not be move up",moveDownLastChildHint:"Last child can not be move down",closePreview:"Close",copyJson:"Copy",saveFormJson:"Save As File",copyVueCode:"Copy Vue Code",copyHtmlCode:"Copy HTML Code",copyJsonSuccess:"Copy succeed",importJsonSuccess:"Import succeed",copyJsonFail:"Copy failed",copyVueCodeSuccess:"Copy succeed",copyVueCodeFail:"Copy failed",copyHtmlCodeSuccess:"Copy succeed",copyHtmlCodeFail:"Copy failed",saveVueCode:"Save Vue File",saveHtmlCode:"Save Html File",getFormData:"Get Data",resetForm:"Reset",disableForm:"Disable",enableForm:"Enable",exportFormData:"Form Data",copyFormData:"Copy",saveFormData:"Save As File",copyVue2SFC:"Copy Vue2",copyVue3SFC:"Copy Vue3",copySFCFail:"Copy failed",copySFCSuccess:"Copy succeed",saveVue2SFC:"Save As Vue2",saveVue3SFC:"Save As Vue3",fileNameForSave:"File name:",saveFileTitle:"Save as File",fileNameInputPlaceholder:"Enter the file name",sampleLoadedSuccess:"Example loaded successfully",sampleLoadedFail:"Sample load failed",loadFormTemplate:"Load This",loadFormTemplateHint:"Are you sure to load this template?",loadFormTemplateSuccess:"Load form template success!",loadFormTemplateFailed:"Load form template failed.",currentNodeCannotBeSelected:"The current node cannot be selected.",widgetSetting:"Widget Config",formSetting:"Form Config",prompt:"Prompt",confirm:"OK",cancel:"Cancel",import:"Import",importJsonHint:"The code to be imported should have the following JSON format.",invalidOptionsData:"Invalid data of options:",lastPaneCannotBeDeleted:"The last pane cannot be deleted.",duplicateName:"Duplicate name: ",nameRequired:"Name required.",numberValidator:"Number",letterValidator:"Letter",letterAndNumberValidator:"LetterAndNumber",mobilePhoneValidator:"MobilePhone",emailValidator:"Email",urlValidator:"URL",noChineseValidator:"Non-Chinese",chineseValidator:"Chinese",rowspanNotConsistentForMergeEntireRow:"Cells in this row don't have the same rowspan, operation failed.",colspanNotConsistentForMergeEntireColumn:"Cells in this column don't have the same colspan, operation failed.",rowspanNotConsistentForDeleteEntireRow:"Cells in this row don't have the same rowspan, operation failed.",colspanNotConsistentForDeleteEntireColumn:"Cells in this column don't have the same colspan, operation failed.",lastColCannotBeDeleted:"The last col cannot be deleted.",lastRowCannotBeDeleted:"The last row cannot be deleted."},toolbar:{undoHint:"Undo",redoHint:"Redo",pcLayout:"PC",padLayout:"Pad",mobileLayout:"H5",nodeTreeHint:"Tree View Of Component Hierarchy",nodeTreeTitle:"Tree View Of Component Hierarchy",clear:"Clear",preview:"Preview",importJson:"Import",exportJson:"Export",exportCode:"Codes",generateCode:"Generate Code",generateSFC:"Generate SFC"},setting:{basicSetting:"Basic Setting",attributeSetting:"Attribute Setting",commonSetting:"Common Setting",advancedSetting:"Advanced Setting",eventSetting:"Event Setting",uniqueName:"Unique Name",editNameHelp:"Press enter to confirm the modification",label:"Label",displayType:"Type",defaultValue:"Default Value",placeholder:"Placeholder",startPlaceholder:"Start Placeholder",endPlaceholder:"End Placeholder",widgetColumnWidth:"Width",widgetSize:"Size",fontSize:"Font Size",textAlign:"Text Align",showStops:"Show Stops",displayStyle:"Display Style",inlineLayout:"inline",blockLayout:"block",buttonStyle:"Show As Button",border:"Show Border",labelWidth:"Width Of Label",rows:"Rows",labelHidden:"Hide Label",required:"Required",requiredHint:"Failure Hint",validation:"Validation",validationHelp:"Regular expressions supported",validationHint:"Validation Hint",readonly:"Readonly",disabled:"Disabled",hidden:"Hidden",textContent:"Text",preWrap:"Line Wrap",htmlContent:"HTML",clearable:"Clearable",editable:"Editable",format:"Format",valueFormat:"Value Format",showPassword:"Show Reveal",filterable:"Filterable",allowCreate:"Allow Create",remote:"Remote Query",automaticDropdown:"Automatic Dropdown",multiple:"Multiple",multipleLimit:"Multiple Limit",checkStrictly:"Any Level Selectable",showAllLevels:"Show All Levels",contentPosition:"Content Position",plain:"Plain",round:"Round",circle:"Circle",icon:"Icon",optionsSetting:"Options Setting",addOption:"Add Option",importOptions:"Import Options",resetDefault:"Reset Default",uploadSetting:"Upload Setting",uploadURL:"Upload URL",uploadTip:"Tip Content",withCredentials:"Send Cookie",multipleSelect:"File Multi-select",showFileList:"Show File List",limit:"Max Upload Number",fileMaxSize:"Max Size(MB)",fileTypes:"Upload File Types",fileTypesHelp:"Allows to add more file types",headers:"Request Headers",cellWidth:"Width",cellHeight:"Height",wordBreak:"Line Wrap",gridColHeight:"Height Of Col(px)",gutter:"Gutter(px)",columnSetting:"Cols Setting",colsOfGrid:"Cols Of Grid:",colSpanTitle:"Spans Of Col",colOffsetTitle:"Offset Of Col",colPushTitle:"Push Of Col",colPullTitle:"Pull Of Col",addColumn:"Add Column",responsive:"Responsive",tabPaneSetting:"Tab Panes",addTabPane:"Add Tab Pane",paneActive:"Active",customLabelIcon:"Custom Label",labelIconClass:"Label Icon Class",labelIconPosition:"Label Icon Position",labelTooltip:"Label Tooltip",minValue:"Min Value",maxValue:"Max Value",precision:"Precision",step:"Step",controlsPosition:"Controls Position",minLength:"Min Length",maxLength:"Max Length",showWordLimit:"Show Word Limit",prefixIcon:"Prefix Icon",suffixIcon:"Suffix Icon",inputButton:"Input Button Setting",appendButton:"Append Button",appendButtonDisabled:"Button Disabled",appendButtonIcon:"Append Button Icon",buttonIcon:"Button Icon",switchWidth:"Width of Switch(px)",activeText:"Active Text",inactiveText:"Inactive Text",activeColor:"Active Color",inactiveColor:"Inactive Color",maxStars:"Stars Max Number",lowThreshold:"Low Threshold",highThreshold:"High Threshold",allowHalf:"Allow Half",showText:"Show Text",showScore:"Show Score",range:"Range",vertical:"Vertical",showBlankRow:"Show Blank Row",showRowNumber:"Show Row Number",insertColumnToLeft:"insert column to left",insertColumnToRight:"insert column to right",insertRowAbove:"insert row above",insertRowBelow:"insert row below",mergeLeftColumn:"merge left cell",mergeRightColumn:"merge right cell",mergeEntireRow:"merge entire row",mergeRowAbove:"merge cell above",mergeRowBelow:"merge cell below",mergeEntireColumn:"merge entire column",undoMergeCol:"undo merge column",undoMergeRow:"undo merge row",deleteEntireCol:"delete entire column",deleteEntireRow:"delete entire row",widgetName:"Unique Name",formSize:"Size",labelPosition:"Position Of Label",topPosition:"Top",leftPosition:"Left",labelAlign:"Label Align",leftAlign:"Left",centerAlign:"Center",rightAlign:"Right",formCss:"Form CSS",addCss:"Edit",customClass:"Custom Class",globalFunctions:"Global Functions",addEventHandler:"Edit",editWidgetEventHandler:"Edit Widget Event Handler",editFormEventHandler:"Edit Form Event Handler",formSFCSetting:"SFC Setting",formModelName:"Model Name",formRefName:"Ref Name",formRulesName:"Rules Name",syntaxCheckWarning:"Syntax error in the javascript codes, please check again!",tableWidth:"Width(px/%)",tableHeight:"Height(px/%)",showCheckBox:"Show CheckBox",showIndex:"Show Row Number",showPagination:"Show Pagination",smallPagination:"Small Pagination",tableColEdit:"Edit Cols",tableDataEdit:"Edit Data",stripe:"Stripe",showSummary:"Show Summary",rowSpacing:"Row Spacing(px)",editAction:"Edit...",columnName:"Name",columnLabel:"Label",columnWidth:"Width(px/%)",visibleColumn:"Visible",sortableColumn:"Sortable",fixedColumn:"Fixed",alignTypeOfColumn:"Align",formatOfColumn:"Format",actionColumn:"Action",addTableColumn:"Add New Column",deleteTableColumn:"Delete This Column",OnlyOneColumnCannotBeDeleted:"The last column cannot be deleted."}}},x={application:{"zh-CN":"简体中文","en-US":"English",productTitle:"表单设计器",github:"GitHub",document:"文档",qqGroup:"技术WX群",deployment:"私有部署",subscription:"订阅Pro"},designer:{componentLib:"组件库",formLib:"表单模板",containerTitle:"容器",dragHandlerHint:"鼠标拖拽容器组件或字段组件并放置于表单中",dragAction:"拖动",basicFieldTitle:"基础字段",advancedFieldTitle:"高级字段",customFieldTitle:"自定义扩展字段",noWidgetHint:"请从左侧列表中选择一个组件, 然后用鼠标拖动组件放置于此处.",widgetLabel:{grid:"栅格",table:"表格",tab:"标签页",section:"区块","sub-form":"子表单","grid-col":"栅格列","table-cell":"单元格","tab-pane":"选项卡页","data-table":"数据表格",input:"单行输入",textarea:"多行输入",number:"计数器",radio:"单选项",checkbox:"多选项",select:"下拉选项",time:"时间","time-range":"时间范围",date:"日期","date-range":"日期范围",switch:"开关",rate:"评分",color:"颜色选择器",slider:"滑块","static-text":"静态文字","html-text":"HTML",button:"按钮",divider:"分隔线","picture-upload":"图片","file-upload":"文件","rich-editor":"富文本",cascader:"级联选择",slot:"插槽",custom:"Custom Component"},hint:{selectParentWidget:"选中父组件",moveUpWidget:"上移组件",moveDownWidget:"下移组件",cloneWidget:"复制组件",insertRow:"插入新行",insertColumn:"插入新列",remove:"移除组件",cellSetting:"单元格操作",dragHandler:"拖拽手柄",copyField:"复制字段组件",onlyFieldWidgetAcceptable:"子表单只能接收字段组件",moveUpFirstChildHint:"已经移动到最上面",moveDownLastChildHint:"已经移动到最下面",closePreview:"关闭",copyJson:"复制JSON",saveFormJson:"保存为文件",copyVueCode:"复制Vue代码",copyHtmlCode:"复制HTML代码",copyJsonSuccess:"复制JSON成功",importJsonSuccess:"导入JSON成功",copyJsonFail:"复制JSON失败",copyVueCodeSuccess:"复制Vue代码成功",copyVueCodeFail:"复制Vue代码失败",copyHtmlCodeSuccess:"复制HTML代码成功",copyHtmlCodeFail:"复制HTML代码失败",saveVueCode:"保存Vue文件",saveHtmlCode:"保存Html文件",getFormData:"获取数据",resetForm:"重置表单",disableForm:"禁用编辑",enableForm:"恢复编辑",exportFormData:"表单数据",copyFormData:"复制JSON",saveFormData:"保存为文件",copyVue2SFC:"复制Vue2代码",copyVue3SFC:"复制Vue3代码",copySFCFail:"复制SFC代码失败",copySFCSuccess:"复制SFC代码成功",saveVue2SFC:"保存为Vue2组件",saveVue3SFC:"保存为Vue3组件",fileNameForSave:"文件名：",saveFileTitle:"保存为文件",fileNameInputPlaceholder:"请输入文件名",sampleLoadedSuccess:"表单示例加载成功",sampleLoadedFail:"表单示例加载失败",loadFormTemplate:"加载此模板",loadFormTemplateHint:"是否加载这个模板？加载后会覆盖设计器当前表单，你可以使用“撤销”功能恢复。",loadFormTemplateSuccess:"表单模板加载成功",loadFormTemplateFailed:"表单模板加载失败",currentNodeCannotBeSelected:"当前组件节点不可选择",widgetSetting:"组件设置",formSetting:"表单设置",prompt:"提示",confirm:"确定",cancel:"取消",import:"导入",importJsonHint:"导入的JSON内容须符合下述格式，以保证顺利导入.",invalidOptionsData:"无效的选项数据:",lastPaneCannotBeDeleted:"仅剩一个选项卡页不可删除.",duplicateName:"组件名称已存在: ",nameRequired:"组件名称不可为空",numberValidator:"数字",letterValidator:"字母",letterAndNumberValidator:"数字字母",mobilePhoneValidator:"手机号码",emailValidator:"邮箱",urlValidator:"网址",noChineseValidator:"非中文字符",chineseValidator:"仅中文字符",rowspanNotConsistentForMergeEntireRow:"存在行高不一致的单元格, 无法合并整行.",colspanNotConsistentForMergeEntireColumn:"存在列宽不一致的单元格, 无法合并整列.",rowspanNotConsistentForDeleteEntireRow:"存在行高不一致的单元格, 不可删除整行.",colspanNotConsistentForDeleteEntireColumn:"存在列宽不一致的单元格, 不可删除整列.",lastColCannotBeDeleted:"最后一列不可删除.",lastRowCannotBeDeleted:"最后一行不可删除."},toolbar:{undoHint:"撤销",redoHint:"重做",pcLayout:"PC",padLayout:"Pad",mobileLayout:"H5",nodeTreeHint:"组件层次结构树",nodeTreeTitle:"组件层次结构树",clear:"清空",preview:"预览",importJson:"导入JSON",exportJson:"导出JSON",exportCode:"导出代码",generateCode:"生成代码",generateSFC:"生成SFC"},setting:{basicSetting:"基本属性",attributeSetting:"属性设置",commonSetting:"常见属性",advancedSetting:"高级属性",eventSetting:"事件属性",uniqueName:"唯一名称",editNameHelp:"修改名称后需按回车确认",label:"标签",displayType:"显示类型",defaultValue:"默认值",placeholder:"占位内容",startPlaceholder:"起始占位内容",endPlaceholder:"截止占位内容",widgetColumnWidth:"组件列宽",widgetSize:"组件大小",fontSize:"字体大小",textAlign:"文字对齐",showStops:"显示间断点",displayStyle:"显示样式",inlineLayout:"行内",blockLayout:"块",buttonStyle:"显示为按钮",border:"带有边框",labelWidth:"标签宽度",rows:"行数",labelHidden:"隐藏字段标签",required:"必填字段",validation:"字段校验",requiredHint:"必填校验提示",validationHelp:"支持输入正则表达式",validationHint:"校验失败提示",readonly:"只读",disabled:"禁用",hidden:"隐藏",textContent:"静态文字",preWrap:"自动换行",htmlContent:"HTML",clearable:"可清除",editable:"可输入",format:"显示格式",valueFormat:"绑定值格式",showPassword:"可显示密码",filterable:"可搜索选项",allowCreate:"允许创建选项",remote:"可远程搜索",automaticDropdown:"自动弹出选项",multiple:"选项可多选",multipleLimit:"多选数量限制",checkStrictly:"任意级节点可选",showAllLevels:"显示完整路径",contentPosition:"文字位置",plain:"朴素按钮",round:"圆角按钮",circle:"圆形按钮",icon:"图标",optionsSetting:"选项设置",addOption:"增加选项",importOptions:"导入选项",resetDefault:"重设选中项",uploadSetting:"上传参数设置",uploadURL:"上传地址",uploadTip:"上传提示内容",withCredentials:"发送cookie凭证",multipleSelect:"文件可多选",showFileList:"显示文件列表",limit:"最大上传数量",fileMaxSize:"文件大小限制(MB)",fileTypes:"上传文件类型",fileTypesHelp:"支持添加其他文件类型",headers:"上传请求头",cellWidth:"宽度",cellHeight:"高度",wordBreak:"文字自动换行",gridColHeight:"栅格列统一高度(px)",gutter:"栅格间隔(px)",columnSetting:"栅格属性设置",colsOfGrid:"当前栅格列:",colSpanTitle:"栅格宽度",colOffsetTitle:"左侧间隔格数",colPushTitle:"右移栅格数",colPullTitle:"左移栅格数",addColumn:"增加栅格",responsive:"响应式布局",tabPaneSetting:"选项卡设置",addTabPane:"增加选项卡页",paneActive:"激活",customLabelIcon:"定制字段标签",labelIconClass:"标签Icon样式",labelIconPosition:"标签Icon位置",labelTooltip:"标签文字提示",minValue:"最小值",maxValue:"最大值",precision:"精度",step:"增减步长",controlsPosition:"控制按钮位置",minLength:"最小长度",maxLength:"最大长度",showWordLimit:"显示字数统计",prefixIcon:"头部Icon",suffixIcon:"尾部Icon",inputButton:"输入框按钮设置",appendButton:"添加后置按钮",appendButtonDisabled:"后置按钮禁用",appendButtonIcon:"后置按钮Icon",buttonIcon:"按钮Icon",switchWidth:"开关宽度（像素）",activeText:"开启时文字描述",inactiveText:"关闭时文字描述",activeColor:"开启时背景色",inactiveColor:"关闭时背景色",maxStars:"最大评分值",lowThreshold:"低分界限值",highThreshold:"高分界限值",allowHalf:"允许半选",showText:"显示辅助文字",showScore:"显示当前分数",range:"是否为范围选择",vertical:"是否竖向显示",showBlankRow:"默认显示新行",showRowNumber:"显示行号",insertColumnToLeft:"插入左侧列",insertColumnToRight:"插入右侧列",insertRowAbove:"插入上方行",insertRowBelow:"插入下方行",mergeLeftColumn:"合并左侧单元格",mergeRightColumn:"合并右侧单元格",mergeEntireRow:"合并整行",mergeRowAbove:"合并上方单元格",mergeRowBelow:"合并下方单元格",mergeEntireColumn:"合并整列",undoMergeCol:"撤销列合并",undoMergeRow:"撤销行合并",deleteEntireCol:"删除整列",deleteEntireRow:"删除整行",widgetName:"组件唯一名称",formSize:"全局组件大小",labelPosition:"字段标签位置",topPosition:"顶部",leftPosition:"左边",labelAlign:"标签对齐",leftAlign:"居左",centerAlign:"居中",rightAlign:"居右",formCss:"表单全局CSS",addCss:"编写CSS",customClass:"自定义CSS样式",globalFunctions:"表单全局函数",addEventHandler:"编写代码",editWidgetEventHandler:"组件事件处理",editFormEventHandler:"表单事件处理",formSFCSetting:"生成SFC设置",formModelName:"数据对象名称",formRefName:"引用名称",formRulesName:"验证规则名称",syntaxCheckWarning:"JS代码存在语法错误，请仔细检查！",tableWidth:"宽度(px/%)",tableHeight:"高度(px/%)",showCheckBox:"是否显示复选框列",showIndex:"是否显示行号",showPagination:"是否显示分页",smallPagination:"小型分页",tableColEdit:"表格列编辑",tableDataEdit:"表格数据编辑",showSummary:"是否合计",stripe:"是否斑马线",rowSpacing:"行距（px）",editAction:"编辑...",columnName:"字段名称",columnLabel:"显示名称",columnWidth:"列宽(px/%)",visibleColumn:"是否显示",sortableColumn:"是否排序",fixedColumn:"是否固定",alignTypeOfColumn:"对齐方式",formatOfColumn:"格式化",actionColumn:"操作",addTableColumn:"增加列",deleteTableColumn:"删除列",OnlyOneColumnCannotBeDeleted:"表格只有一列时不可删除."}}},C={render:{hint:{prompt:"Prompt",confirm:"OK",cancel:"Cancel",selectPlaceholder:"Pick some item",timePlaceholder:"Select time",startTimePlaceholder:"Start time",endTimePlaceholder:"End time",datePlaceholder:"Select date",startDatePlaceholder:"Start date",endDatePlaceholder:"End date",blankCellContent:"--",uploadError:"Upload error: ",uploadExceed:"The maximum number(${uploadLimit}) of file uploads has been exceeded.",unsupportedFileType:"Unsupported format: ",fileSizeExceed:"File size out of limit: ",refNotFound:"Ref not found: ",fieldRequired:"Input value should be not null.",invalidNumber:"Invalid number format",selectFile:" File...",downloadFile:"Download",removeFile:"Remove",validationFailed:"Form validation failed",subFormAction:"Action",subFormAddAction:"Add",subFormAddActionHint:"add new row",insertSubFormRow:"insert new row",deleteSubFormRow:"delete this row",nonSubFormType:"The type of widget don't match sub-form"}}},_={render:{hint:{prompt:"提示",confirm:"确定",cancel:"取消",selectPlaceholder:"请选择",timePlaceholder:"选择时间",startTimePlaceholder:"起始时间",endTimePlaceholder:"截止时间",datePlaceholder:"选择日期",startDatePlaceholder:"起始日期",endDatePlaceholder:"截止日期",blankCellContent:"--",uploadError:"上传错误: ",uploadExceed:"最大上传数量(${uploadLimit})已超出.",unsupportedFileType:"不支持格式: ",fileSizeExceed:"文件大小已超出: ",refNotFound:"组件未找到: ",fieldRequired:"字段值不可为空",invalidNumber:"数据格式错误",selectFile:" 选择文件",downloadFile:"下载",removeFile:"移除",validationFailed:"表单数据校验失败",subFormAction:"操作",subFormAddAction:"新增",subFormAddActionHint:"新增行",insertSubFormRow:"插入行",deleteSubFormRow:"删除行",nonSubFormType:"组件类型不是子表单"}}},O={extension:{widgetLabel:{card:"Card",alert:"Alert"},setting:{cardFolded:"Folded",cardShowFold:"Show Fold",cardWidth:"Width Of Card",cardShadow:"Shadow",alertTitle:"Title",alertType:"Type",description:"Description",closable:"Closable",closeText:"Text On Close Btn",center:"Center",showIcon:"Show Icon",effect:"Effect"}}},F={extension:{widgetLabel:{card:"卡片",alert:"提示"},setting:{cardFolded:"是否收起",cardShowFold:"显示折叠按钮",cardWidth:"卡片宽度",cardShadow:"显示阴影",alertTitle:"标题",alertType:"类型",description:"辅助性文字",closable:"是否可关闭",closeText:"关闭按钮文字",center:"文字居中",showIcon:"显示图标",effect:"显示效果"}}},M={"en-US":Object(n["a"])(Object(n["a"])(Object(n["a"])(Object(n["a"])({something:{}},g.a),y),C),O),"zh-CN":Object(n["a"])(Object(n["a"])(Object(n["a"])(Object(n["a"])({something:{}},b.a),x),_),F)},j=/(%|)\{([0-9a-zA-Z_]+)\}/g;function L(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var k=function(e,t){return e.replace(j,(function(i,n,o,l){var s;return"{"===e[l-1]&&"}"===e[l+i.length]?o:(s=L(t,o)?t[o]:null,null===s||void 0===s?"":s)}))};o["default"].use(p,{lang:localStorage.getItem("v_form_locale")||"zh-CN",messages:M}),w.a.i18n((function(e,t){var i=o["default"].prototype.$st(e);return k(i,t)}));var W=function(e){o["default"].prototype.$si18n.setLang(e),localStorage.setItem("v_form_locale",e)},S=function(e){return o["default"].prototype.$st(e)};t["b"]={methods:{i18nt:function(e){return this.$st(e)},i18n2t:function(e,t){return this.$st2(e,t)}}}},"7b2c":function(e,t,i){"use strict";i("423c")},"7c77":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.displayStyle")}},[i("el-radio-group",{model:{value:e.optionModel.displayStyle,callback:function(t){e.$set(e.optionModel,"displayStyle",t)},expression:"optionModel.displayStyle"}},[i("el-radio",{attrs:{label:"inline"}},[e._v(e._s(e.i18nt("designer.setting.inlineLayout")))]),i("el-radio",{attrs:{label:"block"}},[e._v(e._s(e.i18nt("designer.setting.blockLayout")))])],1)],1)},o=[],l=i("79fa"),s={name:"displayStyle-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"e0557052",null);t["default"]=d.exports},"7ceb":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.appendButtonIcon")}},[i("el-input",{attrs:{type:"text"},model:{value:e.optionModel.buttonIcon,callback:function(t){e.$set(e.optionModel,"buttonIcon",t)},expression:"optionModel.buttonIcon"}})],1)},o=[],l=i("79fa"),s={name:"buttonIcon-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"6661828a",null);t["default"]=d.exports},"7d6c":function(e,t,i){"use strict";var n=i("2909"),o=i("c6e3");t["a"]={mixins:[o["a"]],created:function(){},methods:{editEventHandler:function(e,t){this.dispatch("SettingPanel","editEventHandler",[e,Object(n["a"])(t)])}}}},"7de6":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.format")}},[i("el-select",{attrs:{filterable:"","allow-create":""},model:{value:e.optionModel.format,callback:function(t){e.$set(e.optionModel,"format",t)},expression:"optionModel.format"}},[i("el-option",{attrs:{label:"HH:mm:ss",value:"HH:mm:ss"}}),i("el-option",{attrs:{label:"HH时mm分ss秒",value:"HH时mm分ss秒"}}),i("el-option",{attrs:{label:"hh:mm:ss",value:"hh:mm:ss"}})],1)],1)},o=[],l=i("79fa"),s={name:"time-format-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"48147b42",null);t["default"]=d.exports},"7ede":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("form-item-wrapper",{attrs:{designer:e.designer,field:e.field,rules:e.rules,"design-state":e.designState,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList,"sub-form-row-index":e.subFormRowIndex,"sub-form-col-index":e.subFormColIndex,"sub-form-row-id":e.subFormRowId}},[i("el-upload",{ref:"fieldEditor",class:{hideUploadDiv:e.uploadBtnHidden},attrs:{disabled:e.field.options.disabled,action:e.field.options.uploadURL,headers:e.uploadHeaders,data:e.uploadData,"with-credentials":e.field.options.withCredentials,multiple:e.field.options.multipleSelect,"file-list":e.fileList,"show-file-list":e.field.options.showFileList,"list-type":"picture-card",limit:e.field.options.limit,"on-exceed":e.handlePictureExceed,"on-preview":e.handlePicturePreview,"before-upload":e.beforePictureUpload,"on-success":e.handlePictureUpload,"on-error":e.handleUploadError,"on-remove":e.handlePictureRemove}},[e.field.options.uploadTip?i("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v(e._s(e.field.options.uploadTip))]):e._e(),i("i",{staticClass:"el-icon-plus avatar-uploader-icon"})]),e.showPreviewDialogFlag?i("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"",visible:e.showPreviewDialogFlag,"append-to-body":"",width:"60%","show-close":!0,"custom-class":"drag-dialog small-padding-dialog","close-on-click-modal":!0,"close-on-press-escape":!0,"destroy-on-close":!0},on:{"update:visible":function(t){e.showPreviewDialogFlag=t}}},[i("img",{attrs:{src:e.previewUrl,width:"100%",alt:""}})]):e._e()],1)},o=[],l=(i("a9e3"),i("ac1f"),i("5319"),i("d3b7"),i("b0c0"),i("d81d"),i("a434"),i("9eeb")),s=i("c6e3"),a=i("79fa"),r=i("ca00"),d=i("2d11"),c={name:"picture-upload-widget",componentName:"FieldWidget",mixins:[s["a"],d["a"],a["b"]],props:{field:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object,designState:{type:Boolean,default:!1},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""}},components:{FormItemWrapper:l["default"]},inject:["refList","formConfig","globalOptionData","globalModel"],data:function(){return{oldFieldValue:null,fieldModel:[],rules:[],uploadHeaders:{},uploadData:{key:""},fileList:[],uploadBtnHidden:!1,previewUrl:"",showPreviewDialogFlag:!1}},computed:{},beforeCreate:function(){},created:function(){this.initFieldModel(),this.registerToRefList(),this.initEventHandler(),this.buildFieldRules(),this.handleOnCreated()},mounted:function(){this.handleOnMounted()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{handlePictureExceed:function(){var e=this.field.options.limit;this.$message.warning(this.i18nt("render.hint.uploadExceed").replace("${uploadLimit}",e))},handlePicturePreview:function(e){this.previewUrl=e.url,this.showPreviewDialogFlag=!0},beforePictureUpload:function(e){var t=!1;if(this.field.options&&this.field.options.fileTypes){var i=this.field.options.fileTypes;i.length>0&&(t=i.some((function(t){return e.type==="image/"+t})))}if(!t)return this.$message.error(this.i18nt("render.hint.unsupportedFileType")+e.type),!1;var n=!1,o=5;return this.field.options&&this.field.options.fileMaxSize&&(o=this.field.options.fileMaxSize),n=e.size/1024/1024<=o,n?(this.uploadData.key=e.name,this.handleOnBeforeUpload(e)):(this.$message.error(this.i18nt("render.hint.fileSizeExceed")+o+"MB"),!1)},handleOnBeforeUpload:function(e){if(this.field.options.onBeforeUpload){var t=new Function("file",this.field.options.onBeforeUpload),i=t.call(this,e);return"boolean"!==typeof i||i}return!0},updateFieldModelAndEmitDataChangeForUpload:function(e,t,i){var n=Object(r["d"])(this.fieldModel);t&&t.name&&t.url?this.fieldModel.push({name:t.name,url:t.url}):i&&i.name&&i.url?this.fieldModel.push({name:i.name,url:i.url}):this.fieldModel=Object(r["d"])(e),this.syncUpdateFormModel(this.fieldModel),this.emitFieldDataChange(this.fieldModel,n)},handlePictureUpload:function(e,t,i){if("success"===t.status){var n=null;if(this.field.options.onUploadSuccess){var o=new Function("result","file","fileList",this.field.options.onUploadSuccess);n=o.call(this,e,t,i)}this.updateFieldModelAndEmitDataChangeForUpload(i,n,e),this.fileList=Object(r["d"])(i),this.uploadBtnHidden=i.length>=this.field.options.limit}},updateFieldModelAndEmitDataChangeForRemove:function(e,t){var i=Object(r["d"])(this.fieldModel),n=-1;this.fileList.map((function(t,i){t.name===e.name&&(t.url===e.url||t.uid&&t.uid===e.uid)&&(n=i)})),n>-1&&this.fieldModel.splice(n,1),this.syncUpdateFormModel(this.fieldModel),this.emitFieldDataChange(this.fieldModel,i)},handlePictureRemove:function(e,t){if(this.updateFieldModelAndEmitDataChangeForRemove(e,t),this.fileList=Object(r["d"])(t),this.uploadBtnHidden=t.length>=this.field.options.limit,this.field.options.onFileRemove){var i=new Function("file","fileList",this.field.options.onFileRemove);i.call(this,e,t)}},handleUploadError:function(e,t,i){if(this.field.options.onUploadError){var n=new Function("error","file","fileList",this.field.options.onUploadError);n.call(this,e,t,i)}else this.$message({message:this.i18nt("render.hint.uploadError")+e,duration:3e3,type:"error"})}}},u=c,f=(i("1697"),i("2877")),p=Object(f["a"])(u,n,o,!1,null,"5eb6eaec",null);t["default"]=p.exports},8029:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:"onInput","label-width":"150px"}},[i("el-button",{attrs:{type:"info",icon:"el-icon-edit",plain:"",round:""},on:{click:function(t){return e.editEventHandler("onInput",e.eventParams)}}},[e._v(" "+e._s(e.i18nt("designer.setting.addEventHandler")))])],1)},o=[],l=i("79fa"),s=i("7d6c"),a={name:"onInput-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},data:function(){return{eventParams:["value"]}}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"6178f9e2",null);t["default"]=c.exports},8163:function(e,t,i){},"826c":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("form-item-wrapper",{attrs:{designer:e.designer,field:e.field,rules:e.rules,"design-state":e.designState,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList,"sub-form-row-index":e.subFormRowIndex,"sub-form-col-index":e.subFormColIndex,"sub-form-row-id":e.subFormRowId}},[i("el-slider",{ref:"fieldEditor",attrs:{disabled:e.field.options.disabled,"show-stops":e.field.options.showStops,min:e.field.options.min,max:e.field.options.max,step:e.field.options.step,range:e.field.options.range,vertical:e.field.options.vertical},on:{change:e.handleChangeEvent},model:{value:e.fieldModel,callback:function(t){e.fieldModel=t},expression:"fieldModel"}})],1)},o=[],l=(i("a9e3"),i("9eeb")),s=i("c6e3"),a=i("79fa"),r=i("2d11"),d={name:"slider-widget",componentName:"FieldWidget",mixins:[s["a"],r["a"],a["b"]],props:{field:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object,designState:{type:Boolean,default:!1},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""}},components:{FormItemWrapper:l["default"]},inject:["refList","formConfig","globalOptionData","globalModel"],data:function(){return{oldFieldValue:null,fieldModel:null,rules:[]}},computed:{},beforeCreate:function(){},created:function(){this.initFieldModel(),this.registerToRefList(),this.initEventHandler(),this.buildFieldRules(),this.handleOnCreated()},mounted:function(){this.handleOnMounted()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{}},c=d,u=(i("9560"),i("2877")),f=Object(u["a"])(c,n,o,!1,null,"ddcdb608",null);t["default"]=f.exports},"828b":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"field-wrapper",class:{"design-time-bottom-margin":!!this.designer},style:{display:e.displayStyle}},[e.field.options.hidden&&!0!==e.designState?e._e():i("div",{staticClass:"static-content-item",class:[e.selected?"selected":"",e.customClass],style:{display:e.displayStyle},on:{click:function(t){return t.stopPropagation(),e.selectField(e.field)}}},[e._t("default")],2),this.designer?[e.designer.selectedId===e.field.id?i("div",{staticClass:"field-action"},[i("i",{staticClass:"el-icon-back",attrs:{title:e.i18nt("designer.hint.selectParentWidget")},on:{click:function(t){return t.stopPropagation(),e.selectParentWidget(e.field)}}}),e.parentList&&e.parentList.length>1?i("i",{staticClass:"el-icon-top",attrs:{title:e.i18nt("designer.hint.moveUpWidget")},on:{click:function(t){return t.stopPropagation(),e.moveUpWidget(e.field)}}}):e._e(),e.parentList&&e.parentList.length>1?i("i",{staticClass:"el-icon-bottom",attrs:{title:e.i18nt("designer.hint.moveDownWidget")},on:{click:function(t){return t.stopPropagation(),e.moveDownWidget(e.field)}}}):e._e(),i("i",{staticClass:"el-icon-delete",attrs:{title:e.i18nt("designer.hint.remove")},on:{click:function(t){return t.stopPropagation(),e.removeFieldWidget.apply(null,arguments)}}})]):e._e(),e.designer.selectedId===e.field.id?i("div",{staticClass:"drag-handler background-opacity"},[i("i",{staticClass:"el-icon-rank",attrs:{title:e.i18nt("designer.hint.dragHandler")}}),i("i",[e._v(e._s(e.i18n2t("designer.widgetLabel."+e.field.type,"extension.widgetLabel."+e.field.type)))]),!0===e.field.options.hidden?i("i",{staticClass:"iconfont icon-hide"}):e._e()]):e._e()]:e._e()],2)},o=[],l=(i("a9e3"),i("a15b"),i("a434"),i("79fa")),s={name:"static-content-wrapper",mixins:[l["b"]],props:{field:Object,designer:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designState:{type:Boolean,default:!1},displayStyle:{type:String,default:"block"},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""}},computed:{selected:function(){return!!this.designer&&this.field.id===this.designer.selectedId},customClass:function(){return this.field.options.customClass?this.field.options.customClass.join(" "):""}},methods:{selectField:function(e){this.designer&&(this.designer.setSelected(e),this.designer.emitEvent("field-selected",this.parentWidget))},selectParentWidget:function(){this.parentWidget?this.designer.setSelected(this.parentWidget):this.designer.clearSelected()},moveUpWidget:function(){this.designer.moveUpWidget(this.parentList,this.indexOfParentList),this.designer.emitHistoryChange()},moveDownWidget:function(){this.designer.moveDownWidget(this.parentList,this.indexOfParentList),this.designer.emitHistoryChange()},removeFieldWidget:function(){var e=this;if(this.parentList){var t=this.designer.selectedWidgetName,i=null;1===this.parentList.length?this.parentWidget&&(i=this.parentWidget):i=this.parentList.length===1+this.indexOfParentList?this.parentList[this.indexOfParentList-1]:this.parentList[this.indexOfParentList+1],this.$nextTick((function(){e.parentList.splice(e.indexOfParentList,1),e.designer.setSelected(i),e.designer.formWidget.deleteWidgetRef(t),e.designer.emitHistoryChange()}))}}}},a=s,r=(i("8e97"),i("2877")),d=Object(r["a"])(a,n,o,!1,null,"733e177f",null);t["default"]=d.exports},8299:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.plain")}},[i("el-switch",{model:{value:e.optionModel.plain,callback:function(t){e.$set(e.optionModel,"plain",t)},expression:"optionModel.plain"}})],1)},o=[],l=i("79fa"),s={name:"plain-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"3e3f86f2",null);t["default"]=d.exports},"839e":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:"onBeforeUpload","label-width":"150px"}},[i("el-button",{attrs:{type:"info",icon:"el-icon-edit",plain:"",round:""},on:{click:function(t){return e.editEventHandler("onBeforeUpload",e.eventParams)}}},[e._v(" "+e._s(e.i18nt("designer.setting.addEventHandler")))])],1)},o=[],l=i("79fa"),s=i("7d6c"),a={name:"onBeforeUpload-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},data:function(){return{eventParams:["file"]}}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"6985c67c",null);t["default"]=c.exports},"848c":function(e,t,i){},"84b5":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("form-item-wrapper",{attrs:{designer:e.designer,field:e.field,rules:e.rules,"design-state":e.designState,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList,"sub-form-row-index":e.subFormRowIndex,"sub-form-col-index":e.subFormColIndex,"sub-form-row-id":e.subFormRowId}},[i("el-input",{ref:"fieldEditor",attrs:{type:"textarea",disabled:e.field.options.disabled,readonly:e.field.options.readonly,size:e.field.options.size,placeholder:e.field.options.placeholder,rows:e.field.options.rows,minlength:e.field.options.minLength,maxlength:e.field.options.maxLength,"show-word-limit":e.field.options.showWordLimit},on:{focus:e.handleFocusCustomEvent,blur:e.handleBlurCustomEvent,input:e.handleInputCustomEvent,change:e.handleChangeEvent},model:{value:e.fieldModel,callback:function(t){e.fieldModel=t},expression:"fieldModel"}})],1)},o=[],l=(i("a9e3"),i("9eeb")),s=i("c6e3"),a=i("79fa"),r=i("2d11"),d={name:"textarea-widget",componentName:"FieldWidget",mixins:[s["a"],r["a"],a["b"]],props:{field:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object,designState:{type:Boolean,default:!1},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""}},components:{FormItemWrapper:l["default"]},inject:["refList","formConfig","globalOptionData","globalModel"],data:function(){return{oldFieldValue:null,fieldModel:null,rules:[]}},computed:{},beforeCreate:function(){},created:function(){this.initFieldModel(),this.registerToRefList(),this.initEventHandler(),this.buildFieldRules(),this.handleOnCreated()},mounted:function(){this.handleOnMounted()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{}},c=d,u=(i("2ec9"),i("2877")),f=Object(u["a"])(c,n,o,!1,null,"90e01c78",null);t["default"]=f.exports},"84ed":function(e,t,i){},"851c":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("static-content-wrapper",{attrs:{designer:e.designer,field:e.field,"design-state":e.designState,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList,"sub-form-row-index":e.subFormRowIndex,"sub-form-col-index":e.subFormColIndex,"sub-form-row-id":e.subFormRowId}},[i("div",{ref:"fieldEditor",style:e.field.options.fontSize?"font-size: "+e.field.options.fontSize+";":""},[i("pre",{style:{"white-space":e.field.options.preWrap?"pre-wrap":"pre","text-align":e.field.options.textAlign?e.field.options.textAlign:"left"}},[e._v(e._s(e.field.options.textContent))])])])},o=[],l=(i("a9e3"),i("828b")),s=i("c6e3"),a=i("79fa"),r=i("2d11"),d={name:"static-text-widget",componentName:"FieldWidget",mixins:[s["a"],r["a"],a["b"]],props:{field:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object,designState:{type:Boolean,default:!1},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""}},components:{StaticContentWrapper:l["default"]},computed:{},beforeCreate:function(){},created:function(){this.registerToRefList(),this.initEventHandler(),this.handleOnCreated()},mounted:function(){this.handleOnMounted()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{}},c=d,u=(i("e894"),i("2877")),f=Object(u["a"])(c,n,o,!1,null,"52f85f88",null);t["default"]=f.exports},8740:function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-vue-sfc",use:"icon-vue-sfc-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-vue-sfc"><defs><style type="text/css"></style></defs><path d="M454.137642 11.17578L54.066443 174.091899c-72.088591 29.490787-72.088591 120.523113 0 150.0139l400.275996 162.916119c36.45389 14.950191 78.846896 14.950191 115.505583 0l400.071199-162.813721c72.190989-29.593186 72.088591-120.830308-0.307196-150.116298L569.745624 11.278178A155.338625 155.338625 0 0 0 454.137642 11.073381z m469.190231 237.871869L532.370147 408.584616l-7.88469 2.457565a55.090429 55.090429 0 0 1-32.562744-2.457565L100.350595 249.047649 491.615517 89.817879a55.090429 55.090429 0 0 1 40.447434 0l391.162524 159.22977z" fill="#1890FF" p-id="1211" /><path d="M498.681018 729.911317c-20.274916 0-40.652231-3.788747-59.391168-11.673436L53.759247 561.260878C20.479713 547.846666 0 519.891858 0 488.557896c0-31.43636 20.582112-59.391169 53.861646-72.702982l36.556288-15.052589c21.4013-8.806277 47.103341-1.023986 57.240799 17.407756 10.239857 18.226945 1.023986 40.140238-20.479714 48.946515l-36.453889 14.950191c-3.071957 1.228783-4.710334 3.58395-4.710334 6.451109 0 3.071957 1.535978 5.119928 4.710334 6.45111l385.325805 156.874604c14.335799 5.939117 30.924367 5.939117 45.362565 0l385.325806-156.874604c3.071957-1.228783 4.915131-3.58395 4.915131-6.45111 0-3.071957-1.535978-5.222327-4.607936-6.451109L862.195929 463.879842c-21.4013-8.806277-30.412374-30.71957-20.377314-48.946515 10.342255-18.329343 35.839498-26.214033 57.240798-17.407756l44.850572 18.431742c33.177136 13.516611 53.759247 41.266622 53.759248 72.702982 0 31.333961-20.479713 59.391169-53.759248 72.702982L558.481781 718.237881a161.584938 161.584938 0 0 1-59.800763 11.673436z" fill="#5DE1C8" p-id="1212" /><path d="M498.681018 966.247209c-20.274916 0-40.652231-3.891146-59.391168-11.673437L53.759247 797.59677C20.479713 784.080159 0 756.227749 0 724.996186c0-31.43636 20.582112-59.493567 53.861646-72.805381l36.556288-14.95019c21.4013-8.806277 47.103341-1.023986 57.240799 17.407756 10.239857 18.226945 1.023986 40.140238-20.479714 48.946515l-36.453889 14.95019c-3.071957 1.126384-4.710334 3.58395-4.710334 6.45111 0 3.071957 1.535978 5.119928 4.710334 6.348711l385.325805 156.977002c14.335799 5.939117 30.924367 5.939117 45.362565 0L906.739306 731.1401c3.071957-1.126384 4.915131-3.58395 4.915131-6.348711 0-3.071957-1.535978-5.324725-4.607936-6.45111l-44.850572-18.329343c-21.4013-8.806277-30.412374-30.71957-20.377314-48.946515 10.342255-18.431742 35.839498-26.214033 57.240798-17.407756l44.850572 18.329343c33.177136 13.516611 53.759247 41.369021 53.759248 72.702982 0 31.43636-20.479713 59.493567-53.759248 72.805381L558.481781 954.573772a161.584938 161.584938 0 0 1-59.800763 11.673437z" fill="#FF7272" p-id="1213" /></symbol>'});s.a.add(a);t["default"]=a},8745:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.fontSize")}},[i("el-input",{model:{value:e.optionModel.fontSize,callback:function(t){e.$set(e.optionModel,"fontSize",t)},expression:"optionModel.fontSize"}})],1)},o=[],l=i("79fa"),s={name:"fontSize-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"27111335",null);t["default"]=d.exports},"87ca":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.disabled")}},[i("el-switch",{model:{value:e.optionModel.disabled,callback:function(t){e.$set(e.optionModel,"disabled",t)},expression:"optionModel.disabled"}})],1)},o=[],l=i("79fa"),s={name:"disabled-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"481194aa",null);t["default"]=d.exports},8921:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("container-item-wrapper",{attrs:{widget:e.widget}},[i("div",{directives:[{name:"show",rawName:"v-show",value:!e.widget.options.hidden,expression:"!widget.options.hidden"}],key:e.widget.id,staticClass:"table-container"},[i("table",{ref:e.widget.id,staticClass:"table-layout",class:[e.customClass]},[i("tbody",e._l(e.widget.rows,(function(t,n){return i("tr",{key:t.id},[e._l(t.cols,(function(t,o){return[t.merged?e._e():i("table-cell-item",{key:o,attrs:{widget:t,"parent-list":e.widget.cols,"row-index":n,"col-index":o,"parent-widget":e.widget},scopedSlots:e._u([e._l(Object.keys(e.$scopedSlots),(function(t){return{key:t,fn:function(i){return[e._t(t,null,null,i)]}}}))],null,!0)})]}))],2)})),0)])])])},o=[],l=i("c6e3"),s=i("79fa"),a=i("d67f"),r=i("90c2"),d=i("6a79"),c=i("34f0"),u={name:"table-item",componentName:"ContainerItem",mixins:[l["a"],s["b"],a["a"],c["a"]],components:{ContainerItemWrapper:r["default"],TableCellItem:d["default"]},props:{widget:Object},inject:["refList","sfRefList","globalModel"],created:function(){this.initRefList()},mounted:function(){},beforeDestroy:function(){this.unregisterFromRefList()},methods:{}},f=u,p=(i("5d50"),i("2877")),m=Object(p["a"])(f,n,o,!1,null,"5a8d7072",null);t["default"]=m.exports},"89ef":function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-html-text",use:"icon-html-text-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-html-text"><defs><style type="text/css"></style></defs><path d="M137.6 512l204.8-204.8c12.8-12.8 12.8-32 0-44.8-12.8-12.8-32-12.8-44.8 0L70.4 489.6c-6.4 6.4-9.6 12.8-9.6 22.4 0 9.6 3.2 16 9.6 22.4l227.2 227.2c12.8 12.8 32 12.8 44.8 0 12.8-12.8 12.8-32 0-44.8L137.6 512z m464-339.2c-16-3.2-35.2 6.4-38.4 22.4L396.8 812.8c-3.2 16 6.4 35.2 22.4 38.4 16 3.2 35.2-6.4 38.4-22.4L624 211.2c6.4-16-3.2-35.2-22.4-38.4z m352 316.8L726.4 262.4c-12.8-12.8-32-12.8-44.8 0-12.8 12.8-12.8 32 0 44.8L886.4 512 681.6 716.8c-12.8 12.8-12.8 32 0 44.8 12.8 12.8 32 12.8 44.8 0l227.2-227.2c6.4-6.4 9.6-16 9.6-22.4 0-9.6-3.2-16-9.6-22.4z" p-id="56264" /></symbol>'});s.a.add(a);t["default"]=a},"8a3e":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("form-item-wrapper",{attrs:{designer:e.designer,field:e.field,rules:e.rules,"design-state":e.designState,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList,"sub-form-row-index":e.subFormRowIndex,"sub-form-col-index":e.subFormColIndex,"sub-form-row-id":e.subFormRowId}},[i("el-select",{ref:"fieldEditor",staticClass:"full-width-input",attrs:{disabled:e.field.options.disabled,size:e.field.options.size,clearable:e.field.options.clearable,filterable:e.field.options.filterable,"allow-create":e.field.options.allowCreate,"default-first-option":e.allowDefaultFirstOption,"automatic-dropdown":e.field.options.automaticDropdown,multiple:e.field.options.multiple,"multiple-limit":e.field.options.multipleLimit,placeholder:e.field.options.placeholder||e.i18nt("render.hint.selectPlaceholder"),remote:e.field.options.remote,"remote-method":e.remoteMethod},on:{focus:e.handleFocusCustomEvent,blur:e.handleBlurCustomEvent,change:e.handleChangeEvent},model:{value:e.fieldModel,callback:function(t){e.fieldModel=t},expression:"fieldModel"}},e._l(e.field.options.optionItems,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value,disabled:e.disabled}})})),1)],1)},o=[],l=(i("a9e3"),i("9eeb")),s=i("c6e3"),a=i("79fa"),r=i("2d11"),d={name:"select-widget",componentName:"FieldWidget",mixins:[s["a"],r["a"],a["b"]],props:{field:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object,designState:{type:Boolean,default:!1},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""}},components:{FormItemWrapper:l["default"]},inject:["refList","formConfig","globalOptionData","globalModel"],data:function(){return{oldFieldValue:null,fieldModel:null,rules:[]}},computed:{allowDefaultFirstOption:function(){return!!this.field.options.filterable&&!!this.field.options.allowCreate},remoteMethod:function(){return this.field.options.remote&&this.field.options.onRemoteQuery?this.remoteQuery:void 0}},beforeCreate:function(){},created:function(){this.initOptionItems(),this.initFieldModel(),this.registerToRefList(),this.initEventHandler(),this.buildFieldRules(),this.handleOnCreated()},mounted:function(){this.handleOnMounted()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{}},c=d,u=(i("25fa"),i("2877")),f=Object(u["a"])(c,n,o,!1,null,"12ea4af7",null);t["default"]=f.exports},"8aa9":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.noLabelSetting||"button"===e.selectedWidget.type?e._e():i("el-form-item",{attrs:{label:e.i18nt("designer.setting.labelAlign")}},[i("el-radio-group",{staticClass:"radio-group-custom",model:{value:e.optionModel.labelAlign,callback:function(t){e.$set(e.optionModel,"labelAlign",t)},expression:"optionModel.labelAlign"}},[i("el-radio-button",{attrs:{label:"label-left-align"}},[e._v(" "+e._s(e.i18nt("designer.setting.leftAlign")))]),i("el-radio-button",{attrs:{label:"label-center-align"}},[e._v(" "+e._s(e.i18nt("designer.setting.centerAlign")))]),i("el-radio-button",{attrs:{label:"label-right-align"}},[e._v(" "+e._s(e.i18nt("designer.setting.rightAlign")))])],1)],1)},o=[],l=i("79fa"),s={name:"labelAlign-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},computed:{noLabelSetting:function(){return"static-text"===this.selectedWidget.type||"html-text"===this.selectedWidget.type}}},a=s,r=(i("6d29"),i("2877")),d=Object(r["a"])(a,n,o,!1,null,"3312ca7f",null);t["default"]=d.exports},"8b30":function(e,t,i){var n={"./container-wrapper.vue":"cd36","./grid-col-widget.vue":"f7729","./grid-widget.vue":"e11b","./tab-widget.vue":"5835","./table-cell-widget.vue":"1516","./table-widget.vue":"f6e6"};function o(e){var t=l(e);return i(t)}function l(e){if(!i.o(n,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}o.keys=function(){return Object.keys(n)},o.resolve=l,e.exports=o,o.id="8b30"},"8b39":function(e,t,i){},"8e97":function(e,t,i){"use strict";i("fbe0")},"8f10":function(e,t,i){"use strict";i("8163")},"8f6d":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.showWordLimit")}},[i("el-switch",{model:{value:e.optionModel.showWordLimit,callback:function(t){e.$set(e.optionModel,"showWordLimit",t)},expression:"optionModel.showWordLimit"}})],1)},o=[],l=i("79fa"),s={name:"showWordLimit-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"b6bf0f48",null);t["default"]=d.exports},"8fb7":function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-tab",use:"icon-tab-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-tab"><defs><style type="text/css"></style></defs><path d="M908.8 1005.44H115.2a101.76 101.76 0 0 1-101.12-101.76V110.72A101.76 101.76 0 0 1 115.2 8.96h296.96a32.64 32.64 0 0 1 32 32V262.4a32 32 0 0 1-32 32 32 32 0 0 1-32-32v-192H115.2a37.76 37.76 0 0 0-37.12 37.76v795.52a37.76 37.76 0 0 0 37.12 37.76h793.6a37.76 37.76 0 0 0 37.12-37.76V267.52a32 32 0 0 1 32-32 32 32 0 0 1 32 32v636.16a101.76 101.76 0 0 1-101.12 101.76z" p-id="9210" /><path d="M977.92 299.52a32.64 32.64 0 0 1-32-32V180.48a37.12 37.12 0 0 0-37.12-37.76H421.12a32 32 0 0 1-32-32 32 32 0 0 1 32-32h487.68a101.76 101.76 0 0 1 101.12 101.76v87.04a32 32 0 0 1-32 32z" p-id="9211" /><path d="M977.92 299.52H64a32 32 0 0 1-32-32 32 32 0 0 1 32-32h913.92a32 32 0 0 1 32 32 32 32 0 0 1-32 32z" p-id="9212" /><path d="M699.52 299.52a32 32 0 0 1-32-32V110.72a32 32 0 0 1 64 0v156.8a32 32 0 0 1-32 32z" p-id="9213" /></symbol>'});s.a.add(a);t["default"]=a},9e3:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.labelIconPosition")}},[i("el-select",{model:{value:e.optionModel.labelIconPosition,callback:function(t){e.$set(e.optionModel,"labelIconPosition",t)},expression:"optionModel.labelIconPosition"}},e._l(e.labelIconPosition,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)},o=[],l=i("79fa"),s={name:"labelIconPosition-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},data:function(){return{labelIconPosition:[{label:"front",value:"front"},{label:"rear",value:"rear"}]}}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"7d90012c",null);t["default"]=d.exports},"90c2":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"container-wrapper",class:[e.customClass]},[e._t("default")],2)},o=[],l=(i("a15b"),{name:"container-item-wrapper",props:{widget:{type:Object,required:!0}},computed:{customClass:function(){return this.widget.options.customClass?this.widget.options.customClass.join(" "):""}}}),s=l,a=i("2877"),r=Object(a["a"])(s,n,o,!1,null,"4277aed9",null);t["default"]=r.exports},9164:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return"input"===e.selectedWidget.type?i("el-form-item",{attrs:{label:e.i18nt("designer.setting.displayType")}},[i("el-select",{model:{value:e.optionModel.type,callback:function(t){e.$set(e.optionModel,"type",t)},expression:"optionModel.type"}},[i("el-option",{attrs:{label:"text",value:"text"}}),i("el-option",{attrs:{label:"password",value:"password"}})],1)],1):e._e()},o=[],l=i("79fa"),s={name:"type-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},computed:{}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"5adb4552",null);t["default"]=d.exports},"91df":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.highThreshold")}},[i("el-input-number",{staticClass:"hide-spin-button",staticStyle:{width:"100%"},attrs:{min:e.optionModel.lowThreshold,max:e.optionModel.max},model:{value:e.optionModel.highThreshold,callback:function(t){e.$set(e.optionModel,"highThreshold",t)},expression:"optionModel.highThreshold"}})],1)},o=[],l=i("79fa"),s={name:"highThreshold-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"389ab4fb",null);t["default"]=d.exports},"937f":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.wordBreak")}},[i("el-switch",{model:{value:e.optionModel.wordBreak,callback:function(t){e.$set(e.optionModel,"wordBreak",t)},expression:"optionModel.wordBreak"}})],1)},o=[],l=i("79fa"),s={name:"table-cell-wordBreak-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"1e1ced73",null);t["default"]=d.exports},9381:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticStyle:{display:"none"}})},o=[],l={name:"cascader-defaultValue-editor",props:{designer:Object,selectedWidget:Object,optionModel:Object}},s=l,a=i("2877"),r=Object(a["a"])(s,n,o,!1,null,"accf4f04",null);t["default"]=r.exports},"93b5":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:"onClick","label-width":"150px"}},[i("el-button",{attrs:{type:"info",icon:"el-icon-edit",plain:"",round:""},on:{click:function(t){return e.editEventHandler("onClick",e.eventParams)}}},[e._v(" "+e._s(e.i18nt("designer.setting.addEventHandler")))])],1)},o=[],l=i("79fa"),s=i("7d6c"),a={name:"onClick-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},data:function(){return{eventParams:[]}}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"2bc90074",null);t["default"]=c.exports},9470:function(e,t,i){"use strict";var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"ace-container"},[i("div",{ref:"ace",staticClass:"ace-editor"})])},o=[],l=i("6d4f"),s=i.n(l),a=(i("1bb7"),i("f14c"),i("45db"),i("803d"),i("03c0"),i("01ea")),r={name:"CodeEditor",props:{value:{type:String,required:!0},readonly:{type:Boolean,default:!1},mode:{type:String,default:"javascript"},userWorker:{type:Boolean,default:!0}},mounted:function(){var e=this;s.a.config.set("basePath",a["a"]),this.addAutoCompletion(s.a),this.aceEditor=s.a.edit(this.$refs.ace,{maxLines:20,minLines:5,fontSize:12,theme:this.themePath,mode:this.modePath,tabSize:2,readOnly:this.readonly,highlightActiveLine:!0,value:this.codeValue}),this.aceEditor.setOptions({enableBasicAutocompletion:!0,enableSnippets:!0,enableLiveAutocompletion:!0}),"json"===this.mode?this.setJsonMode():"css"===this.mode&&this.setCssMode(),this.userWorker||this.aceEditor.getSession().setUseWorker(!1),this.aceEditor.getSession().on("change",(function(t){e.$emit("input",e.aceEditor.getValue())}))},data:function(){return{aceEditor:null,themePath:"ace/theme/sqlserver",modePath:"ace/mode/javascript",codeValue:this.value}},watch:{},methods:{addAutoCompletion:function(e){var t=[{meta:"VForm API",caption:"getWidgetRef",value:"getWidgetRef()",score:1},{meta:"VForm API",caption:"getFormRef",value:"getFormRef()",score:1}],i=e.require("ace/ext/language_tools");i.addCompleter({getCompletions:function(e,i,n,o,l){return 0===o.length?l(null,[]):l(null,t)}})},setJsonMode:function(){this.aceEditor.getSession().setMode("ace/mode/json")},setCssMode:function(){this.aceEditor.getSession().setMode("ace/mode/css")},getEditorAnnotations:function(){return this.aceEditor.getSession().getAnnotations()}}},d=r,c=(i("74c3"),i("2877")),u=Object(c["a"])(d,n,o,!1,null,"d13460f4",null);t["a"]=u.exports},9481:function(e,t,i){},"955b":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:"onRemoteQuery","label-width":"150px"}},[i("el-button",{attrs:{type:"info",icon:"el-icon-edit",plain:"",round:""},on:{click:function(t){return e.editEventHandler("onRemoteQuery",e.eventParams)}}},[e._v(" "+e._s(e.i18nt("designer.setting.addEventHandler")))])],1)},o=[],l=i("79fa"),s=i("7d6c"),a={name:"onRemoteQuery-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},data:function(){return{eventParams:["keyword"]}}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"e6e9ae72",null);t["default"]=c.exports},9560:function(e,t,i){"use strict";i("9481")},"985d":function(e,t,i){i("d81d"),i("d3b7"),i("ddb0");var n=function(e){return e.keys().map(e)},o=i("51ff");n(o)},"98f5":function(e,t,i){},"99c0":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.validationHint")}},[i("el-input",{attrs:{type:"text"},model:{value:e.optionModel.validationHint,callback:function(t){e.$set(e.optionModel,"validationHint",t)},expression:"optionModel.validationHint"}})],1)},o=[],l=i("79fa"),s={name:"validationHint-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"2dfa4c7e",null);t["default"]=d.exports},"9a48":function(e,t,i){},"9b00":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.fileMaxSize")}},[i("el-input-number",{staticClass:"hide-spin-button",staticStyle:{width:"100%"},attrs:{min:1},model:{value:e.optionModel.fileMaxSize,callback:function(t){e.$set(e.optionModel,"fileMaxSize",t)},expression:"optionModel.fileMaxSize"}})],1)},o=[],l=i("79fa"),s={name:"fileMaxSize-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"6aaf7d9c",null);t["default"]=d.exports},"9b6e":function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-number-field",use:"icon-number-field-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-number-field"><defs><style type="text/css"></style></defs><path d="M960 1024H64a64 64 0 0 1-64-64V64a64 64 0 0 1 64-64h896a64 64 0 0 1 64 64v896a64 64 0 0 1-64 64z m0-896a64 64 0 0 0-64-64H128a64 64 0 0 0-64 64v768a64 64 0 0 0 64 64h768a64 64 0 0 0 64-64V128z m-128 640h-128a64 64 0 0 1 0-128h64V576h-64a64 64 0 0 1 0-128h64V384h-64a64 64 0 0 1 0-128h128a64 64 0 0 1 64 64v384a64 64 0 0 1-64 64z m-320-128a64 64 0 0 1 0 128H384a64 64 0 0 1-64-64V512a64 64 0 0 1 64-64h64V384H384a64 64 0 0 1 0-128h128a64 64 0 0 1 64 64v192a64 64 0 0 1-64 64H448v64h64z m-320 128a64 64 0 0 1-64-64V320a64 64 0 0 1 128 0v384a64 64 0 0 1-64 64z" p-id="24162" /></symbol>'});s.a.add(a);t["default"]=a},"9bbf":function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-drag",use:"icon-drag-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-drag"><defs><style type="text/css"></style></defs><path d="M574.958 267.016h-63.454 204.649L511.213 63.655 307.85 267.016h141.191V456.68H258.688v125.917H449.04V772.95h125.917V582.596h188.875V456.679H574.958V267.016z m-63.704 693.33l189.62-187.396H323.126l188.129 187.395zM71.292 518.891l187.395 189.62v-377.75L71.292 518.892z m692.54-188.13v377.75L952.708 518.89 763.833 330.762z" p-id="6350" /></symbol>'});s.a.add(a);t["default"]=a},"9c0c":function(e,t,i){},"9c31":function(e,t,i){},"9d09":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:"onAppendButtonClick","label-width":"150px"}},[i("el-button",{attrs:{type:"info",icon:"el-icon-edit",plain:"",round:""},on:{click:function(t){return e.editEventHandler("onAppendButtonClick",e.eventParams)}}},[e._v(" "+e._s(e.i18nt("designer.setting.addEventHandler")))])],1)},o=[],l=i("79fa"),s=i("7d6c"),a={name:"onAppendButtonClick-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},data:function(){return{eventParams:[]}}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"678bf974",null);t["default"]=c.exports},"9d21":function(e,t,i){},"9dcd":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.range")}},[i("el-switch",{model:{value:e.optionModel.range,callback:function(t){e.$set(e.optionModel,"range",t)},expression:"optionModel.range"}})],1)},o=[],l=i("79fa"),s={name:"range-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"0cabcba9",null);t["default"]=d.exports},"9ebd":function(e,t,i){"use strict";i("eb5e")},"9eeb":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"field-wrapper",class:{"design-time-bottom-margin":!!this.designer}},[!e.field.formItemFlag||e.field.options.hidden&&!0!==e.designState?e._e():i("el-form-item",{class:[e.selected?"selected":"",e.labelAlign,e.customClass,e.field.options.required?"required":""],attrs:{label:e.label,"label-width":e.labelWidth+"px",title:e.field.options.labelTooltip,rules:e.rules,prop:e.getPropName()},nativeOn:{click:function(t){return t.stopPropagation(),e.selectField(e.field)}}},[e.field.options.labelIconClass?i("span",{staticClass:"custom-label",attrs:{slot:"label"},slot:"label"},["front"===e.field.options.labelIconPosition?[e.field.options.labelTooltip?[i("el-tooltip",{attrs:{content:e.field.options.labelTooltip,effect:"light"}},[i("i",{class:e.field.options.labelIconClass})]),e._v(e._s(e.label))]:[i("i",{class:e.field.options.labelIconClass}),e._v(e._s(e.label))]]:"rear"===e.field.options.labelIconPosition?[e.field.options.labelTooltip?[e._v(" "+e._s(e.label)),i("el-tooltip",{attrs:{content:e.field.options.labelTooltip,effect:"light"}},[i("i",{class:e.field.options.labelIconClass})])]:[e._v(" "+e._s(e.label)),i("i",{class:e.field.options.labelIconClass})]]:e._e()],2):e._e(),e._t("default")],2),this.designer?[e.designer.selectedId===e.field.id?i("div",{staticClass:"field-action"},[i("i",{staticClass:"el-icon-back",attrs:{title:e.i18nt("designer.hint.selectParentWidget")},on:{click:function(t){return t.stopPropagation(),e.selectParentWidget(e.field)}}}),e.parentList&&e.parentList.length>1?i("i",{staticClass:"el-icon-top",attrs:{title:e.i18nt("designer.hint.moveUpWidget")},on:{click:function(t){return t.stopPropagation(),e.moveUpWidget(e.field)}}}):e._e(),e.parentList&&e.parentList.length>1?i("i",{staticClass:"el-icon-bottom",attrs:{title:e.i18nt("designer.hint.moveDownWidget")},on:{click:function(t){return t.stopPropagation(),e.moveDownWidget(e.field)}}}):e._e(),i("i",{staticClass:"el-icon-delete",attrs:{title:e.i18nt("designer.hint.remove")},on:{click:function(t){return t.stopPropagation(),e.removeFieldWidget.apply(null,arguments)}}})]):e._e(),e.designer.selectedId===e.field.id?i("div",{staticClass:"drag-handler background-opacity"},[i("i",{staticClass:"el-icon-rank",attrs:{title:e.i18nt("designer.hint.dragHandler")}}),i("i",[e._v(e._s(e.i18n2t("designer.widgetLabel."+e.field.type,"extension.widgetLabel."+e.field.type)))]),!0===e.field.options.hidden?i("i",{staticClass:"iconfont icon-hide"}):e._e()]):e._e()]:e._e()],2)},o=[],l=(i("a9e3"),i("a15b"),i("b0c0"),i("a434"),i("79fa")),s={name:"form-item-wrapper",mixins:[l["b"]],props:{field:Object,designer:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designState:{type:Boolean,default:!1},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""},rules:Array},inject:["formConfig"],computed:{selected:function(){return!!this.designer&&this.field.id===this.designer.selectedId},label:function(){return this.field.options.labelHidden?"":this.field.options.label},labelWidth:function(){return this.field.options.labelHidden?this.designState?5:0:this.field.options.labelWidth?this.field.options.labelWidth:this.designer?this.designer.formConfig.labelWidth:this.formConfig.labelWidth},labelAlign:function(){return this.field.options.labelAlign?this.field.options.labelAlign:this.designer?this.designer.formConfig.labelAlign||"label-left-align":this.formConfig.labelAlign||"label-left-align"},customClass:function(){return this.field.options.customClass?this.field.options.customClass.join(" "):""},subFormName:function(){return this.parentWidget?this.parentWidget.options.name:""},subFormItemFlag:function(){return!!this.parentWidget&&"sub-form"===this.parentWidget.type}},created:function(){},methods:{selectField:function(e){this.designer&&(this.designer.setSelected(e),this.designer.emitEvent("field-selected",this.parentWidget))},selectParentWidget:function(){this.parentWidget?this.designer.setSelected(this.parentWidget):this.designer.clearSelected()},moveUpWidget:function(){this.designer.moveUpWidget(this.parentList,this.indexOfParentList),this.designer.emitHistoryChange()},moveDownWidget:function(){this.designer.moveDownWidget(this.parentList,this.indexOfParentList),this.designer.emitHistoryChange()},removeFieldWidget:function(){var e=this;if(this.parentList){var t=this.designer.selectedWidgetName,i=null;1===this.parentList.length?this.parentWidget&&(i=this.parentWidget):i=this.parentList.length===1+this.indexOfParentList?this.parentList[this.indexOfParentList-1]:this.parentList[this.indexOfParentList+1],this.$nextTick((function(){e.parentList.splice(e.indexOfParentList,1),e.designer.setSelected(i),e.designer.formWidget.deleteWidgetRef(t),e.designer.emitHistoryChange()}))}},getPropName:function(){return this.subFormItemFlag&&!this.designState?this.subFormName+"."+this.subFormRowIndex+"."+this.field.options.name:this.field.options.name}}},a=s,r=(i("b478"),i("2877")),d=Object(r["a"])(a,n,o,!1,null,"6f598f02",null);t["default"]=d.exports},a00a:function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_require__.d(__webpack_exports__,"b",(function(){return getRegExp}));var core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("ac1f"),core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_0___default=__webpack_require__.n(core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_0__),core_js_modules_es_regexp_test_js__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("00b4"),core_js_modules_es_regexp_test_js__WEBPACK_IMPORTED_MODULE_1___default=__webpack_require__.n(core_js_modules_es_regexp_test_js__WEBPACK_IMPORTED_MODULE_1__),_util__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("ca00"),getRegExp=function(e){var t={number:"/^[-]?\\d+(\\.\\d+)?$/",letter:"/^[A-Za-z]+$/",letterAndNumber:"/^[A-Za-z0-9]+$/",mobilePhone:"/^[1][3-9][0-9]{9}$/",letterStartNumberIncluded:"/^[A-Za-z]+[A-Za-z\\d]*$/",noChinese:"/^[^一-龥]+$/",chinese:"/^[一-龥]+$/",email:"/^([-_A-Za-z0-9.]+)@([_A-Za-z0-9]+\\.)+[A-Za-z0-9]{2,3}$/",url:"/^([hH][tT]{2}[pP]:\\/\\/|[hH][tT]{2}[pP][sS]:\\/\\/)(([A-Za-z0-9-~]+)\\.)+([A-Za-z0-9-~\\/])+$/"};return t[e]},validateFn=function validateFn(validatorName,rule,value,callback,defaultErrorMsg){if(Object(_util__WEBPACK_IMPORTED_MODULE_2__["n"])(value)||value.length<=0)callback();else{var reg=eval(getRegExp(validatorName));if(reg.test(value))callback();else{var errTxt=rule.errorMsg||defaultErrorMsg;callback(new Error(errTxt))}}},FormValidators={number:function(e,t,i){validateFn("number",e,t,i,"["+e.label+"]包含非数字字符")},letter:function(e,t,i){validateFn("letter",e,t,i,"["+e.label+"]包含非字母字符")},letterAndNumber:function(e,t,i){validateFn("letterAndNumber",e,t,i,"["+e.label+"]只能输入字母或数字")},mobilePhone:function(e,t,i){validateFn("mobilePhone",e,t,i,"["+e.label+"]手机号码格式有误")},noBlankStart:function(e,t,i){},noBlankEnd:function(e,t,i){},letterStartNumberIncluded:function(e,t,i){validateFn("letterStartNumberIncluded",e,t,i,"["+e.label+"]必须以字母开头，可包含数字")},noChinese:function(e,t,i){validateFn("noChinese",e,t,i,"["+e.label+"]不可输入中文字符")},chinese:function(e,t,i){validateFn("chinese",e,t,i,"["+e.label+"]只能输入中文字符")},email:function(e,t,i){validateFn("email",e,t,i,"["+e.label+"]邮箱格式有误")},url:function(e,t,i){validateFn("url",e,t,i,"["+e.label+"]URL格式有误")},regExp:function regExp(rule,value,callback){if(Object(_util__WEBPACK_IMPORTED_MODULE_2__["n"])(value)||value.length<=0)callback();else{var pattern=eval(rule.regExp);if(pattern.test(value))callback();else{var errTxt=rule.errorMsg||"["+rule.label+"]invalid value";callback(new Error(errTxt))}}}};__webpack_exports__["a"]=FormValidators},a0f1:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.readonly")}},[i("el-switch",{model:{value:e.optionModel.readonly,callback:function(t){e.$set(e.optionModel,"readonly",t)},expression:"optionModel.readonly"}})],1)},o=[],l=i("79fa"),s={name:"readonly-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"59d799df",null);t["default"]=d.exports},a2c6:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.uploadTip")}},[i("el-input",{attrs:{type:"text"},model:{value:e.optionModel.uploadTip,callback:function(t){e.$set(e.optionModel,"uploadTip",t)},expression:"optionModel.uploadTip"}})],1)},o=[],l=i("79fa"),s={name:"uploadTip-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"32c9310d",null);t["default"]=d.exports},a4ae:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return"password"===e.optionModel.type?i("el-form-item",{attrs:{label:e.i18nt("designer.setting.showPassword")}},[i("el-switch",{model:{value:e.optionModel.showPassword,callback:function(t){e.$set(e.optionModel,"showPassword",t)},expression:"optionModel.showPassword"}})],1):e._e()},o=[],l=i("79fa"),s={name:"showPassword-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"70d3a53e",null);t["default"]=d.exports},a817:function(e,t,i){"use strict";i("84ed")},a867:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.editable")}},[i("el-switch",{model:{value:e.optionModel.editable,callback:function(t){e.$set(e.optionModel,"editable",t)},expression:"optionModel.editable"}})],1)},o=[],l=i("79fa"),s={name:"editable-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"650031f7",null);t["default"]=d.exports},a8a0:function(e,t,i){},a906:function(e,t,i){"use strict";i("bdcf")},a93f:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-col",e._b({directives:[{name:"show",rawName:"v-show",value:!e.widget.options.hidden,expression:"!widget.options.hidden"}],key:e.widget.id,staticClass:"grid-cell",class:[e.customClass],style:e.colHeightStyle},"el-col",e.layoutProps,!1),[e.widget.widgetList&&e.widget.widgetList.length>0?[e._l(e.widget.widgetList,(function(t,n){return["container"===t.category?[i(e.getComponentByContainer(t),{key:n,tag:"component",attrs:{widget:t,"parent-list":e.widget.widgetList,"index-of-parent-list":n,"parent-widget":e.widget},scopedSlots:e._u([e._l(Object.keys(e.$scopedSlots),(function(t){return{key:t,fn:function(i){return[e._t(t,null,null,i)]}}}))],null,!0)})]:[i(t.type+"-widget",{key:n,tag:"component",attrs:{field:t,designer:null,"parent-list":e.widget.widgetList,"index-of-parent-list":n,"parent-widget":e.widget},scopedSlots:e._u([e._l(Object.keys(e.$scopedSlots),(function(t){return{key:t,fn:function(i){return[e._t(t,null,null,i)]}}}))],null,!0)})]]}))]:[i("el-col",[i("div",{staticClass:"blank-cell"},[i("span",{staticClass:"invisible-content"},[e._v(e._s(e.i18nt("render.hint.blankCellContent")))])])])]],2)},o=[],l=i("5530"),s=(i("a9e3"),i("c6e3")),a=i("79fa"),r=i("d67f"),d=i("c029"),c={name:"GridColItem",componentName:"ContainerItem",mixins:[s["a"],a["b"],r["a"]],components:Object(l["a"])({},d["a"]),props:{widget:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,colHeight:{type:String,default:null}},inject:["refList","globalModel","formConfig","previewState"],data:function(){return{layoutProps:{span:this.widget.options.span,md:this.widget.options.md||12,sm:this.widget.options.sm||12,xs:this.widget.options.xs||12,offset:this.widget.options.offset||0,push:this.widget.options.push||0,pull:this.widget.options.pull||0}}},computed:{customClass:function(){return this.widget.options.customClass||""},colHeightStyle:function(){return this.colHeight?{height:this.colHeight+"px"}:{}}},created:function(){this.initLayoutProps(),this.initRefList()},methods:{initLayoutProps:function(){if(this.widget.options.responsive)if(this.previewState){this.layoutProps.md=void 0,this.layoutProps.sm=void 0,this.layoutProps.xs=void 0;var e=this.formConfig.layoutType;this.layoutProps.span="H5"===e?this.widget.options.xs||12:"Pad"===e?this.widget.options.sm||12:this.widget.options.md||12}else this.layoutProps.span=void 0;else this.layoutProps.md=void 0,this.layoutProps.sm=void 0,this.layoutProps.xs=void 0}}},u=c,f=(i("5a42"),i("2877")),p=Object(f["a"])(u,n,o,!1,null,"2991df90",null);t["default"]=p.exports},a979:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.format")}},[i("el-select",{attrs:{filterable:"","allow-create":""},model:{value:e.optionModel.format,callback:function(t){e.$set(e.optionModel,"format",t)},expression:"optionModel.format"}},[i("el-option",{attrs:{label:"HH:mm:ss",value:"HH:mm:ss"}}),i("el-option",{attrs:{label:"HH时mm分ss秒",value:"HH时mm分ss秒"}}),i("el-option",{attrs:{label:"hh:mm:ss",value:"hh:mm:ss"}})],1)],1)},o=[],l=i("79fa"),s={name:"time-range-format-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"288e4b9c",null);t["default"]=d.exports},a982:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.minValue")}},[i("el-input-number",{staticClass:"hide-spin-button",staticStyle:{width:"100%"},model:{value:e.minValue,callback:function(t){e.minValue=t},expression:"minValue"}})],1)},o=[],l=(i("a9e3"),i("79fa")),s={name:"min-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},computed:{minValue:{get:function(){return this.optionModel["min"]},set:function(e){void 0===e||null===e||isNaN(e)?this.optionModel.min=null:this.optionModel.min=Number(e)}}}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"9f972014",null);t["default"]=d.exports},aa43:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.multipleSelect")}},[i("el-switch",{model:{value:e.optionModel.multipleSelect,callback:function(t){e.$set(e.optionModel,"multipleSelect",t)},expression:"optionModel.multipleSelect"}})],1)},o=[],l=i("79fa"),s={name:"multipleSelect-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"a402edb8",null);t["default"]=d.exports},aaa9:function(e,t,i){},ab136:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("el-form-item",{attrs:{"label-width":"0"}},[i("el-divider",{staticClass:"custom-divider"},[e._v(e._s(e.i18nt("designer.setting.inputButton")))])],1),i("el-form-item",{attrs:{label:e.i18nt("designer.setting.appendButton")}},[i("el-switch",{model:{value:e.optionModel.appendButton,callback:function(t){e.$set(e.optionModel,"appendButton",t)},expression:"optionModel.appendButton"}})],1)],1)},o=[],l=i("79fa"),s={name:"appendButton-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"3afb70e8",null);t["default"]=d.exports},acb0:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.preWrap")}},[i("el-switch",{model:{value:e.optionModel.preWrap,callback:function(t){e.$set(e.optionModel,"preWrap",t)},expression:"optionModel.preWrap"}})],1)},o=[],l=i("79fa"),s={name:"static-text-preWrap-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"ccadc688",null);t["default"]=d.exports},ad7e:function(e,t,i){},af9a:function(e,t,i){},b205:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticStyle:{display:"none"}})},o=[],l={name:"radio-defaultValue-editor",props:{designer:Object,selectedWidget:Object,optionModel:Object}},s=l,a=i("2877"),r=Object(a["a"])(s,n,o,!1,null,"596e0157",null);t["default"]=r.exports},b20f:function(e,t,i){},b296:function(e,t,i){},b2b6:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.textContent")}},[i("el-input",{attrs:{type:"textarea",rows:3},model:{value:e.optionModel.textContent,callback:function(t){e.$set(e.optionModel,"textContent",t)},expression:"optionModel.textContent"}})],1)},o=[],l=i("79fa"),s={name:"textContent-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"36e95fa9",null);t["default"]=d.exports},b2bf:function(e,t,i){"use strict";i("b0c0"),i("ac1f"),i("5319");t["a"]={methods:{hasConfig:function(e){return!(!this.designer||!this.designer.selectedWidget)&&this.designer.hasConfig(this.selectedWidget,e)},emitDefaultValueChange:function(){if(this.designer&&this.designer.formWidget){var e=this.designer.formWidget.getWidgetRef(this.designer.selectedWidget.options.name);e&&e.refreshDefaultValue&&e.refreshDefaultValue()}},inputNumberHandler:function(e){var t=e.target;t.value=t.value.replace(/[^0-9]/gi,"")},onRemoteChange:function(e){e&&(this.optionModel.filterable=!0,this.optionModel.allowCreate=!1)},onMultipleSelected:function(e){if(e){var t=this.designer.formWidget.getWidgetRef(this.optionModel.name);t&&t.clearSelectedOptions&&t.clearSelectedOptions(),this.optionModel.defaultValue=[]}else this.optionModel.defaultValue&&this.optionModel.defaultValue.length>0?this.optionModel.defaultValue=this.optionModel.defaultValue[0]:this.optionModel.defaultValue=""}}}},b302:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.defaultValue")}},[i("el-time-picker",{staticStyle:{width:"100%"},attrs:{"is-range":"",format:e.optionModel.format,"value-format":"HH:mm:ss"},on:{change:e.emitDefaultValueChange},model:{value:e.optionModel.defaultValue,callback:function(t){e.$set(e.optionModel,"defaultValue",t)},expression:"optionModel.defaultValue"}})],1)},o=[],l=i("79fa"),s=i("b2bf"),a={name:"time-range-defaultValue-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"12ed1d6b",null);t["default"]=c.exports},b30d:function(e,t,i){"use strict";i("b296")},b46f:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:"onMounted","label-width":"150px"}},[i("el-button",{attrs:{type:"info",icon:"el-icon-edit",plain:"",round:""},on:{click:function(t){return e.editEventHandler("onMounted",e.eventParams)}}},[e._v(" "+e._s(e.i18nt("designer.setting.addEventHandler")))])],1)},o=[],l=i("79fa"),s=i("7d6c"),a={name:"onMounted-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},data:function(){return{eventParams:[]}}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"479f4bf4",null);t["default"]=c.exports},b478:function(e,t,i){"use strict";i("70db")},b574:function(e,t,i){},b620:function(e,t,i){"use strict";i("6e3b")},b674:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.allowHalf")}},[i("el-switch",{model:{value:e.optionModel.allowHalf,callback:function(t){e.$set(e.optionModel,"allowHalf",t)},expression:"optionModel.allowHalf"}})],1)},o=[],l=i("79fa"),s={name:"allowHalf-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"75c91e5d",null);t["default"]=d.exports},b6b9:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.showBlankRow")}},[i("el-switch",{model:{value:e.optionModel.showBlankRow,callback:function(t){e.$set(e.optionModel,"showBlankRow",t)},expression:"optionModel.showBlankRow"}})],1)},o=[],l=i("79fa"),s={name:"showBlankRow-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"aacb0e56",null);t["default"]=d.exports},b87d:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("form-item-wrapper",{attrs:{designer:e.designer,field:e.field,rules:e.rules,"design-state":e.designState,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList,"sub-form-row-index":e.subFormRowIndex,"sub-form-col-index":e.subFormColIndex,"sub-form-row-id":e.subFormRowId}},[i("el-radio-group",{ref:"fieldEditor",attrs:{disabled:e.field.options.disabled,size:e.field.options.size},on:{change:e.handleChangeEvent},model:{value:e.fieldModel,callback:function(t){e.fieldModel=t},expression:"fieldModel"}},[e.field.options.buttonStyle?e._l(e.field.options.optionItems,(function(t,n){return i("el-radio-button",{key:n,style:{display:e.field.options.displayStyle},attrs:{label:t.value,disabled:t.disabled,border:e.field.options.border}},[e._v(e._s(t.label))])})):e._l(e.field.options.optionItems,(function(t,n){return i("el-radio",{key:n,style:{display:e.field.options.displayStyle},attrs:{label:t.value,disabled:t.disabled,border:e.field.options.border}},[e._v(e._s(t.label))])}))],2)],1)},o=[],l=(i("a9e3"),i("9eeb")),s=i("c6e3"),a=i("79fa"),r=i("2d11"),d={name:"radio-widget",componentName:"FieldWidget",mixins:[s["a"],r["a"],a["b"]],props:{field:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object,designState:{type:Boolean,default:!1},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""}},components:{FormItemWrapper:l["default"]},inject:["refList","formConfig","globalOptionData","globalModel"],data:function(){return{oldFieldValue:null,fieldModel:null,rules:[]}},computed:{},beforeCreate:function(){},created:function(){this.initOptionItems(),this.initFieldModel(),this.registerToRefList(),this.initEventHandler(),this.buildFieldRules(),this.handleOnCreated()},mounted:function(){this.handleOnMounted()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{}},c=d,u=(i("13f0"),i("2877")),f=Object(u["a"])(c,n,o,!1,null,"ef35f5b6",null);t["default"]=f.exports},b8d7:function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-select-field",use:"icon-select-field-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-select-field"><defs><style type="text/css"></style></defs><path d="M374.784 649.514667a32 32 0 0 1 3.072 41.685333l-3.114667 3.584L225.28 843.946667a32 32 0 0 1-37.845333 5.504l-3.968-2.56-85.333334-64a32 32 0 0 1 34.432-53.76l3.968 2.56 63.146667 47.36 129.834667-129.621334a32 32 0 0 1 45.269333 0.042667zM906.154667 725.333333a32 32 0 0 1 4.309333 63.701334l-4.309333 0.298666h-448a32 32 0 0 1-4.352-63.744l4.352-0.256h448z m0.512-256a32 32 0 0 1 4.352 63.701334l-4.352 0.298666h-448a32 32 0 0 1-4.352-63.701333L458.666667 469.333333h448zM374.826667 137.557333a32 32 0 0 1 2.986666 41.685334l-3.114666 3.584L224.853333 332.032a32 32 0 0 1-37.888 5.418667l-3.925333-2.56-84.906667-64a32 32 0 0 1 34.517334-53.674667l3.968 2.56 62.72 47.274667 130.261333-129.578667a32 32 0 0 1 45.226667 0.085333z m531.328 75.818667a32 32 0 0 1 4.309333 63.701333l-4.309333 0.298667H459.349333a32 32 0 0 1-4.352-63.744l4.352-0.256h446.805334z" p-id="40341" /></symbol>'});s.a.add(a);t["default"]=a},b8e9:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("static-content-wrapper",{attrs:{designer:e.designer,field:e.field,"design-state":e.designState,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList,"sub-form-row-index":e.subFormRowIndex,"sub-form-col-index":e.subFormColIndex,"sub-form-row-id":e.subFormRowId}},[i("div",{class:[e.designState?"slot-wrapper-design":"slot-wrapper-render"]},[e._t(e.field.options.name,null,{formModel:e.formModel}),e.designState?i("div",{staticClass:"slot-title"},[e._v(e._s(e.field.options.label))]):e._e()],2)])},o=[],l=(i("a9e3"),i("828b")),s=i("c6e3"),a=i("79fa"),r=i("2d11"),d={name:"slot-widget",componentName:"FieldWidget",mixins:[s["a"],r["a"],a["b"]],props:{field:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object,designState:{type:Boolean,default:!1},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""}},components:{StaticContentWrapper:l["default"]},computed:{},beforeCreate:function(){},created:function(){this.registerToRefList(),this.initEventHandler(),this.handleOnCreated()},mounted:function(){this.handleOnMounted()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{}},c=d,u=(i("f1ab"),i("2877")),f=Object(u["a"])(c,n,o,!1,null,"856e2df6",null);t["default"]=f.exports},b90a:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:"onCreated","label-width":"150px"}},[i("el-button",{attrs:{type:"info",icon:"el-icon-edit",plain:"",round:""},on:{click:function(t){return e.editEventHandler("onCreated",e.eventParams)}}},[e._v(" "+e._s(e.i18nt("designer.setting.addEventHandler")))])],1)},o=[],l=i("79fa"),s=i("7d6c"),a={name:"onCreated-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},data:function(){return{eventParams:[]}}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"20e467a8",null);t["default"]=c.exports},ba08:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("form-item-wrapper",{attrs:{designer:e.designer,field:e.field,rules:e.rules,"design-state":e.designState,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList,"sub-form-row-index":e.subFormRowIndex,"sub-form-col-index":e.subFormColIndex,"sub-form-row-id":e.subFormRowId}},[i("el-date-picker",{ref:"fieldEditor",staticClass:"full-width-input",attrs:{type:e.field.options.type,disabled:e.field.options.disabled,readonly:e.field.options.readonly,size:e.field.options.size,clearable:e.field.options.clearable,editable:e.field.options.editable,format:e.field.options.format,"value-format":e.field.options.valueFormat,"start-placeholder":e.field.options.startPlaceholder||e.i18nt("render.hint.startDatePlaceholder"),"end-placeholder":e.field.options.endPlaceholder||e.i18nt("render.hint.endDatePlaceholder")},on:{focus:e.handleFocusCustomEvent,blur:e.handleBlurCustomEvent,change:e.handleChangeEvent},model:{value:e.fieldModel,callback:function(t){e.fieldModel=t},expression:"fieldModel"}})],1)},o=[],l=(i("a9e3"),i("9eeb")),s=i("c6e3"),a=i("79fa"),r=i("2d11"),d={name:"date-range-widget",componentName:"FieldWidget",mixins:[s["a"],r["a"],a["b"]],props:{field:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object,designState:{type:Boolean,default:!1},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""}},components:{FormItemWrapper:l["default"]},inject:["refList","formConfig","globalOptionData","globalModel"],data:function(){return{oldFieldValue:null,fieldModel:null,rules:[]}},computed:{},beforeCreate:function(){},created:function(){this.initFieldModel(),this.registerToRefList(),this.initEventHandler(),this.buildFieldRules(),this.handleOnCreated()},mounted:function(){this.handleOnMounted()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{}},c=d,u=(i("447a"),i("2877")),f=Object(u["a"])(c,n,o,!1,null,"76c3fdc8",null);t["default"]=f.exports},bae2:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.defaultValue")}},[i("el-color-picker",{on:{change:e.emitDefaultValueChange},model:{value:e.optionModel.defaultValue,callback:function(t){e.$set(e.optionModel,"defaultValue",t)},expression:"optionModel.defaultValue"}})],1)},o=[],l=i("79fa"),s=i("b2bf"),a={name:"color-defaultValue-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"667e90e8",null);t["default"]=c.exports},baf1:function(e,t,i){},bb60:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.multiple")}},[i("el-switch",{model:{value:e.optionModel.multiple,callback:function(t){e.$set(e.optionModel,"multiple",t)},expression:"optionModel.multiple"}})],1)},o=[],l=i("79fa"),s=i("b2bf"),a={name:"cascader-multiple-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"115602ef",null);t["default"]=c.exports},bc16:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.buttonIcon")}},[i("el-input",{attrs:{type:"text"},model:{value:e.optionModel.icon,callback:function(t){e.$set(e.optionModel,"icon",t)},expression:"optionModel.icon"}})],1)},o=[],l=i("79fa"),s={name:"icon-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"4b90cd5a",null);t["default"]=d.exports},bdcf:function(e,t,i){},c029:function(e,t,i){"use strict";i("d81d"),i("d3b7"),i("ddb0"),i("b0c0");var n=i("7899"),o={};n.keys().map((function(e){var t=n(e).default;o[t.name]=t})),t["a"]=o},c061:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.noLabelSetting?e._e():i("el-form-item",{attrs:{label:e.i18nt("designer.setting.label")}},[i("el-input",{attrs:{type:"text"},model:{value:e.optionModel.label,callback:function(t){e.$set(e.optionModel,"label",t)},expression:"optionModel.label"}})],1)},o=[],l=i("79fa"),s={name:"label-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},computed:{noLabelSetting:function(){return"static-text"===this.selectedWidget.type||"html-text"===this.selectedWidget.type}}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"5d9d81e5",null);t["default"]=d.exports},c077:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("form-item-wrapper",{attrs:{designer:e.designer,field:e.field,rules:e.rules,"design-state":e.designState,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList,"sub-form-row-index":e.subFormRowIndex,"sub-form-col-index":e.subFormColIndex,"sub-form-row-id":e.subFormRowId}},[i("el-cascader",{ref:"fieldEditor",staticClass:"full-width-input",attrs:{options:e.field.options.optionItems,disabled:e.field.options.disabled,size:e.field.options.size,clearable:e.field.options.clearable,filterable:e.field.options.filterable,"show-all-levels":e.showFullPath,props:{checkStrictly:e.field.options.checkStrictly,multiple:e.field.options.multiple,expandTrigger:"hover"},placeholder:e.field.options.placeholder||e.i18nt("render.hint.selectPlaceholder")},on:{"visible-change":e.hideDropDownOnClick,"expand-change":e.hideDropDownOnClick,focus:e.handleFocusCustomEvent,blur:e.handleBlurCustomEvent,change:e.handleChangeEvent},model:{value:e.fieldModel,callback:function(t){e.fieldModel=t},expression:"fieldModel"}})],1)},o=[],l=(i("a9e3"),i("d3b7"),i("159b"),i("9eeb")),s=i("c6e3"),a=i("79fa"),r=i("2d11"),d={name:"cascader-widget",componentName:"FieldWidget",mixins:[s["a"],r["a"],a["b"]],props:{field:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object,designState:{type:Boolean,default:!1},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""}},components:{FormItemWrapper:l["default"]},inject:["refList","formConfig","globalOptionData","globalModel"],data:function(){return{oldFieldValue:null,fieldModel:null,rules:[]}},computed:{showFullPath:function(){return void 0===this.field.options.showAllLevels||!!this.field.options.showAllLevels}},beforeCreate:function(){},created:function(){this.initOptionItems(),this.initFieldModel(),this.registerToRefList(),this.initEventHandler(),this.buildFieldRules(),this.handleOnCreated()},mounted:function(){this.handleOnMounted()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{hideDropDownOnClick:function(){var e=this;setTimeout((function(){document.querySelectorAll(".el-cascader-panel .el-radio").forEach((function(t){t.onclick=function(){e.$refs.fieldEditor.dropDownVisible=!1}}))}),100)}}},c=d,u=(i("dbe8"),i("2877")),f=Object(u["a"])(c,n,o,!1,null,"9fb36e9a",null);t["default"]=f.exports},c391:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:"onFileRemove","label-width":"150px"}},[i("el-button",{attrs:{type:"info",icon:"el-icon-edit",plain:"",round:""},on:{click:function(t){return e.editEventHandler("onFileRemove",e.eventParams)}}},[e._v(" "+e._s(e.i18nt("designer.setting.addEventHandler")))])],1)},o=[],l=i("79fa"),s=i("7d6c"),a={name:"onFileRemove-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},data:function(){return{eventParams:["file","fileList"]}}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"21c42c04",null);t["default"]=c.exports},c3f1:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.responsive")}},[i("el-switch",{model:{value:e.optionModel.responsive,callback:function(t){e.$set(e.optionModel,"responsive",t)},expression:"optionModel.responsive"}})],1)},o=[],l=i("79fa"),s={name:"grid-col-responsive-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"1d360e4e",null);t["default"]=d.exports},c3f8:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:"onSubFormRowInsert","label-width":"150px"}},[i("el-button",{attrs:{type:"info",icon:"el-icon-edit",plain:"",round:""},on:{click:function(t){return e.editEventHandler("onSubFormRowInsert",e.eventParams)}}},[e._v(" "+e._s(e.i18nt("designer.setting.addEventHandler")))])],1)},o=[],l=i("79fa"),s=i("7d6c"),a={name:"onSubFormRowInsert-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},data:function(){return{eventParams:["subFormData","newRowId"]}}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"ae0fbd12",null);t["default"]=c.exports},c60b:function(e,t,i){},c610:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.hasConfig("optionItems")?e._e():i("el-form-item",{attrs:{label:e.i18nt("designer.setting.defaultValue")}},[i("el-input",{attrs:{type:"text"},on:{change:e.emitDefaultValueChange},model:{value:e.optionModel.defaultValue,callback:function(t){e.$set(e.optionModel,"defaultValue",t)},expression:"optionModel.defaultValue"}})],1)},o=[],l=i("79fa"),s=i("b2bf"),a={name:"defaultValue-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"14ff026e",null);t["default"]=c.exports},c635:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return"static-text"===e.selectedWidget.type?i("el-form-item",{attrs:{label:e.i18nt("designer.setting.textAlign")}},[i("el-radio-group",{staticClass:"radio-group-custom",model:{value:e.optionModel.textAlign,callback:function(t){e.$set(e.optionModel,"textAlign",t)},expression:"optionModel.textAlign"}},[i("el-radio-button",{attrs:{label:"left"}},[e._v(" "+e._s(e.i18nt("designer.setting.leftAlign")))]),i("el-radio-button",{attrs:{label:"center"}},[e._v(" "+e._s(e.i18nt("designer.setting.centerAlign")))]),i("el-radio-button",{attrs:{label:"right"}},[e._v(" "+e._s(e.i18nt("designer.setting.rightAlign")))])],1)],1):e._e()},o=[],l=i("79fa"),s={name:"textAlign-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=(i("e895"),i("2877")),d=Object(r["a"])(a,n,o,!1,null,"2a8305e8",null);t["default"]=d.exports},c67a:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:"onSubFormRowAdd","label-width":"150px"}},[i("el-button",{attrs:{type:"info",icon:"el-icon-edit",plain:"",round:""},on:{click:function(t){return e.editEventHandler("onSubFormRowAdd",e.eventParams)}}},[e._v(" "+e._s(e.i18nt("designer.setting.addEventHandler")))])],1)},o=[],l=i("79fa"),s=i("7d6c"),a={name:"onSubFormRowAdd-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},data:function(){return{eventParams:["subFormData","newRowId"]}}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"4e7e840d",null);t["default"]=c.exports},c6c1:function(e,t,i){},c6d2:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.startPlaceholder")}},[i("el-input",{attrs:{type:"text"},model:{value:e.optionModel.startPlaceholder,callback:function(t){e.$set(e.optionModel,"startPlaceholder",t)},expression:"optionModel.startPlaceholder"}})],1)},o=[],l=i("79fa"),s={name:"startPlaceholder-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"10c0a37e",null);t["default"]=d.exports},c6e3:function(e,t,i){"use strict";i("d3b7"),i("159b"),i("99af");function n(e,t,i){this.$children.forEach((function(o){var l=o.$options.componentName;l===e?(o.$emit.apply(o,[t].concat(i)),n.apply(o,[e,t].concat([i]))):n.apply(o,[e,t].concat([i]))}))}t["a"]={methods:{dispatch:function(e,t,i){var n=this.$parent||this.$root,o=n.$options.componentName;while(n&&(!o||o!==e))n=n.$parent,n&&(o=n.$options.componentName);n&&n.$emit.apply(n,[t].concat(i))},broadcast:function(e,t,i){n.call(this,e,t,i)}}}},c833:function(e,t,i){"use strict";i("98f5")},c885:function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-custom-component",use:"icon-custom-component-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-custom-component"><defs><style type="text/css"></style></defs><path d="M811.008 335.872c-2.048-7.168-11.264-9.216-17.408-4.096L690.176 435.2c-8.192 8.192-20.48 8.192-28.672 0l-72.704-72.704c-8.192-8.192-8.192-20.48 0-28.672l104.448-104.448c5.12-5.12 3.072-14.336-4.096-17.408-17.408-4.096-35.84-7.168-54.272-7.168-108.544 0-195.584 94.208-183.296 204.8 2.048 17.408 6.144 32.768 12.288 48.128L225.28 697.344c-27.648 27.648-27.648 73.728 0 101.376 14.336 14.336 32.768 21.504 51.2 21.504s36.864-7.168 51.2-21.504l238.592-238.592c15.36 6.144 31.744 10.24 48.128 12.288 111.616 12.288 204.8-74.752 204.8-183.296 0-18.432-3.072-36.864-8.192-53.248z" p-id="68660" /></symbol>'});s.a.add(a);t["default"]=a},c9d4:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("container-item-wrapper",{attrs:{widget:e.widget}},[i("div",{directives:[{name:"show",rawName:"v-show",value:!e.widget.options.hidden,expression:"!widget.options.hidden"}],key:e.widget.id,staticClass:"tab-container"},[i("el-tabs",{ref:e.widget.id,class:[e.customClass],attrs:{type:e.widget.displayType},model:{value:e.activeTabName,callback:function(t){e.activeTabName=t},expression:"activeTabName"}},e._l(e.visibleTabs,(function(t,n){return i("el-tab-pane",{key:n,attrs:{label:t.options.label,disabled:t.options.disabled,name:t.options.name}},[e._l(t.widgetList,(function(n,o){return["container"===n.category?[i(e.getComponentByContainer(n),{key:o,tag:"component",attrs:{widget:n,"parent-list":t.widgetList,"index-of-parent-list":o,"parent-widget":e.widget},scopedSlots:e._u([e._l(Object.keys(e.$scopedSlots),(function(t){return{key:t,fn:function(i){return[e._t(t,null,null,i)]}}}))],null,!0)})]:[i(n.type+"-widget",{key:o,tag:"component",attrs:{field:n,"parent-list":t.widgetList,"index-of-parent-list":o,"parent-widget":e.widget},scopedSlots:e._u([e._l(Object.keys(e.$scopedSlots),(function(t){return{key:t,fn:function(i){return[e._t(t,null,null,i)]}}}))],null,!0)})]]}))],2)})),1)],1)])},o=[],l=i("5530"),s=(i("4de4"),i("d3b7"),i("b0c0"),i("c6e3")),a=i("79fa"),r=i("d67f"),d=i("90c2"),c=i("34f0"),u=i("c029"),f={name:"tab-item",componentName:"ContainerItem",mixins:[s["a"],a["b"],r["a"],c["a"]],components:Object(l["a"])({ContainerItemWrapper:d["default"]},u["a"]),props:{widget:Object},inject:["refList","sfRefList","globalModel"],data:function(){return{activeTabName:""}},computed:{visibleTabs:function(){return this.widget.tabs.filter((function(e){return!e.options.hidden}))}},created:function(){this.initRefList()},mounted:function(){this.initActiveTab()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{initActiveTab:function(){if("tab"===this.widget.type&&this.widget.tabs.length>0){var e=this.widget.tabs.filter((function(e){return!0===e.options.active}));e.length>0?this.activeTabName=e[0].options.name:this.activeTabName=this.widget.tabs[0].options.name}}}},p=f,m=i("2877"),g=Object(m["a"])(p,n,o,!1,null,"66f2f987",null);t["default"]=g.exports},ca00:function(e,t,i){"use strict";i.d(t,"n",(function(){return l})),i.d(t,"m",(function(){return s})),i.d(t,"l",(function(){return a})),i.d(t,"e",(function(){return r})),i.d(t,"d",(function(){return d})),i.d(t,"p",(function(){return c})),i.d(t,"a",(function(){return u})),i.d(t,"j",(function(){return f})),i.d(t,"k",(function(){return p})),i.d(t,"o",(function(){return m})),i.d(t,"s",(function(){return g})),i.d(t,"r",(function(){return h})),i.d(t,"q",(function(){return b})),i.d(t,"t",(function(){return w})),i.d(t,"g",(function(){return y})),i.d(t,"f",(function(){return x})),i.d(t,"c",(function(){return C})),i.d(t,"i",(function(){return _})),i.d(t,"h",(function(){return O})),i.d(t,"b",(function(){return F}));i("ac1f"),i("00b4"),i("e9c4"),i("d3b7"),i("159b"),i("b64b"),i("b0c0"),i("841c"),i("1276");var n=i("b311"),o=i.n(n);function l(e){return null===e||void 0===e}function s(e){return null!==e&&void 0!==e}function a(e){return void 0===e||!e&&0!==e&&"0"!==e||!/[^\s]/.test(e)}var r=function(){return Math.floor(1e5*Math.random()+2e4*Math.random()+5e3*Math.random())},d=function(e){if(void 0!==e)return JSON.parse(JSON.stringify(e))},c=function(e,t){Object.keys(t).forEach((function(i){e[i]=t[i]}))},u=function(e){var t=window.onresize;"function"!=typeof window.onresize?window.onresize=e:window.onresize=function(){t(),e()}},f=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",i=document.getElementsByTagName("head")[0],n=document.getElementById("vform-custom-css");n&&i.removeChild(n),t&&(n=document.getElementById("vform-custom-css-"+t),n&&i.removeChild(n));var o=document.createElement("style");o.type="text/css",o.rel="stylesheet",o.id=t?"vform-custom-css-"+t:"vform-custom-css";try{o.appendChild(document.createTextNode(e))}catch(l){o.styleSheet.cssText=e}i.appendChild(o)},p=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",i=document.getElementsByTagName("body")[0],n=document.getElementById("v_form_global_functions");n&&i.removeChild(n),t&&(n=document.getElementById("v_form_global_functions-"+t),n&&i.removeChild(n));var o=document.createElement("script");o.id=t?"v_form_global_functions-"+t:"v_form_global_functions",o.type="text/javascript",o.innerHTML=e,i.appendChild(o)},m=function(e,t){var i=encodeURIComponent(e),n=document.getElementById(i);if(!n){var o=document.createElement("script");o.src=e,o.id=i,document.body.appendChild(o),o.onload=o.onreadystatechange=function(e,i){!i&&o.readyState&&"loaded"!==o.readyState&&"complete"!==o.readyState||(o=o.onload=o.onreadystatechange=null,i||t())}}};function g(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;e&&e.forEach((function(e){e.formItemFlag?t(e,i):"grid"===e.type?e.cols.forEach((function(i){g(i.widgetList,t,e)})):"table"===e.type?e.rows.forEach((function(i){i.cols.forEach((function(i){g(i.widgetList,t,e)}))})):"tab"===e.type?e.tabs.forEach((function(i){g(i.widgetList,t,e)})):("sub-form"===e.type||"container"===e.category)&&g(e.widgetList,t,e)}))}function h(e,t){e&&e.forEach((function(e){"container"===e.category&&t(e),"grid"===e.type?e.cols.forEach((function(e){h(e.widgetList,t)})):"table"===e.type?e.rows.forEach((function(e){e.cols.forEach((function(e){h(e.widgetList,t)}))})):"tab"===e.type?e.tabs.forEach((function(e){h(e.widgetList,t)})):("sub-form"===e.type||"container"===e.category)&&h(e.widgetList,t)}))}function b(e,t){e&&e.forEach((function(e){t(e),"grid"===e.type?e.cols.forEach((function(e){t(e),b(e.widgetList,t)})):"table"===e.type?e.rows.forEach((function(e){e.cols.forEach((function(e){t(e),b(e.widgetList,t)}))})):"tab"===e.type?e.tabs.forEach((function(e){b(e.widgetList,t)})):("sub-form"===e.type||"container"===e.category)&&b(e.widgetList,t)}))}function v(e,t){e.category?w(e,t):e.formItemFlag&&t(e)}function w(e,t){"grid"===e.type?e.cols.forEach((function(e){e.widgetList.forEach((function(e){v(e,t)}))})):"table"===e.type?e.rows.forEach((function(e){e.cols.forEach((function(e){e.widgetList.forEach((function(e){v(e,t)}))}))})):"tab"===e.type?e.tabs.forEach((function(e){e.widgetList.forEach((function(e){v(e,t)}))})):("sub-form"===e.type||"container"===e.category)&&e.widgetList.forEach((function(e){v(e,t)}))}function y(e){if(!e)return[];var t=[],i=function(e){t.push({type:e.type,name:e.options.name,field:e})};return g(e,i),t}function x(e){if(!e)return[];var t=[],i=function(e){t.push({type:e.type,name:e.options.name,container:e})};return h(e,i),t}function C(e,t,i,n,l){var s=new o.a(t.target,{text:function(){return e}});s.on("success",(function(){i.success(n),s.destroy()})),s.on("error",(function(){i.error(l),s.destroy()})),s.onClick(t)}function _(e){for(var t=window.location.search.substring(1),i=t.split("&"),n=0;n<i.length;n++){var o=i[n].split("=");if(o[0]==e)return o[1]}}function O(){return{modelName:"formData",refName:"vForm",rulesName:"rules",labelWidth:80,labelPosition:"left",size:"",labelAlign:"label-left-align",cssCode:"",customClass:[],functions:"",layoutType:"PC",onFormCreated:"",onFormMounted:"",onFormDataChange:""}}function F(){return{widgetList:[],formConfig:d(O())}}},cab0:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("form-item-wrapper",{attrs:{designer:e.designer,field:e.field,rules:e.rules,"design-state":e.designState,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList,"sub-form-row-index":e.subFormRowIndex,"sub-form-col-index":e.subFormColIndex,"sub-form-row-id":e.subFormRowId}},[i("el-time-picker",{ref:"fieldEditor",staticClass:"full-width-input",attrs:{"is-range":"",disabled:e.field.options.disabled,readonly:e.field.options.readonly,size:e.field.options.size,clearable:e.field.options.clearable,editable:e.field.options.editable,format:e.field.options.format,"value-format":"HH:mm:ss","start-placeholder":e.field.options.startPlaceholder||e.i18nt("render.hint.startTimePlaceholder"),"end-placeholder":e.field.options.endPlaceholder||e.i18nt("render.hint.endTimePlaceholder")},on:{focus:e.handleFocusCustomEvent,blur:e.handleBlurCustomEvent,change:e.handleChangeEvent},model:{value:e.fieldModel,callback:function(t){e.fieldModel=t},expression:"fieldModel"}})],1)},o=[],l=(i("a9e3"),i("9eeb")),s=i("c6e3"),a=i("79fa"),r=i("2d11"),d={name:"time-range-widget",componentName:"FieldWidget",mixins:[s["a"],r["a"],a["b"]],props:{field:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object,designState:{type:Boolean,default:!1},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""}},components:{FormItemWrapper:l["default"]},inject:["refList","formConfig","globalOptionData","globalModel"],data:function(){return{oldFieldValue:null,fieldModel:null,rules:[]}},computed:{},beforeCreate:function(){},created:function(){this.initFieldModel(),this.registerToRefList(),this.initEventHandler(),this.buildFieldRules(),this.handleOnCreated()},mounted:function(){this.handleOnMounted()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{}},c=d,u=(i("4120"),i("2877")),f=Object(u["a"])(c,n,o,!1,null,"68ea79e5",null);t["default"]=f.exports},cbd6:function(e,t,i){"use strict";i("8b39")},cbdd:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.vertical")}},[i("el-switch",{model:{value:e.optionModel.vertical,callback:function(t){e.$set(e.optionModel,"vertical",t)},expression:"optionModel.vertical"}})],1)},o=[],l=i("79fa"),s={name:"vertical-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"8e7b19b4",null);t["default"]=d.exports},cc5f:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.displayType")}},[i("el-select",{model:{value:e.optionModel.type,callback:function(t){e.$set(e.optionModel,"type",t)},expression:"optionModel.type"}},[i("el-option",{attrs:{label:"datetime",value:"datetime"}}),i("el-option",{attrs:{label:"date",value:"date"}}),i("el-option",{attrs:{label:"dates",value:"dates"}}),i("el-option",{attrs:{label:"year",value:"year"}}),i("el-option",{attrs:{label:"month",value:"month"}}),i("el-option",{attrs:{label:"week",value:"week"}})],1)],1)},o=[],l=i("79fa"),s={name:"date-type-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"1eb20f34",null);t["default"]=d.exports},cc78:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.customClass")}},[i("el-select",{attrs:{multiple:"",filterable:"","allow-create":"","default-first-option":""},model:{value:e.optionModel.customClass,callback:function(t){e.$set(e.optionModel,"customClass",t)},expression:"optionModel.customClass"}},e._l(e.cssClassList,(function(e,t){return i("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1)},o=[],l=i("79fa"),s=i("ca00"),a={name:"customClass-editor",componentName:"PropertyEditor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},data:function(){return{cssClassList:[]}},created:function(){var e=this;this.cssClassList=Object(s["d"])(this.designer.getCssClassList()),this.designer.handleEvent("form-css-updated",(function(t){e.cssClassList=t}))}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"18a2c67d",null);t["default"]=c.exports},cd36:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"container-wrapper",class:[e.customClass]},[e._t("default"),e.designer.selectedId!==e.widget.id||e.widget.internal?e._e():i("div",{staticClass:"container-action"},[i("i",{staticClass:"el-icon-back",attrs:{title:e.i18nt("designer.hint.selectParentWidget")},on:{click:function(t){return t.stopPropagation(),e.selectParentWidget(e.widget)}}}),e.parentList&&e.parentList.length>1?i("i",{staticClass:"el-icon-top",attrs:{title:e.i18nt("designer.hint.moveUpWidget")},on:{click:function(t){return t.stopPropagation(),e.moveUpWidget()}}}):e._e(),e.parentList&&e.parentList.length>1?i("i",{staticClass:"el-icon-bottom",attrs:{title:e.i18nt("designer.hint.moveDownWidget")},on:{click:function(t){return t.stopPropagation(),e.moveDownWidget()}}}):e._e(),"table"===e.widget.type?i("i",{staticClass:"iconfont icon-insertrow",attrs:{title:e.i18nt("designer.hint.insertRow")},on:{click:function(t){return t.stopPropagation(),e.appendTableRow(e.widget)}}}):e._e(),"table"===e.widget.type?i("i",{staticClass:"iconfont icon-insertcolumn",attrs:{title:e.i18nt("designer.hint.insertColumn")},on:{click:function(t){return t.stopPropagation(),e.appendTableCol(e.widget)}}}):e._e(),"grid"===e.widget.type||"table"===e.widget.type?i("i",{staticClass:"el-icon-copy-document",attrs:{title:e.i18nt("designer.hint.cloneWidget")},on:{click:function(t){return t.stopPropagation(),e.cloneContainer(e.widget)}}}):e._e(),i("i",{staticClass:"el-icon-delete",attrs:{title:e.i18nt("designer.hint.remove")},on:{click:function(t){return t.stopPropagation(),e.removeWidget.apply(null,arguments)}}})]),e.designer.selectedId!==e.widget.id||e.widget.internal?e._e():i("div",{staticClass:"drag-handler"},[i("i",{staticClass:"el-icon-rank",attrs:{title:e.i18nt("designer.hint.dragHandler")}}),i("i",[e._v(e._s(e.i18n2t("designer.widgetLabel."+e.widget.type,"extension.widgetLabel."+e.widget.type)))]),!0===e.widget.options.hidden?i("i",{staticClass:"iconfont icon-hide"}):e._e()])],2)},o=[],l=(i("a9e3"),i("a15b"),i("79fa")),s=i("61ca"),a={name:"container-wrapper",mixins:[l["b"],s["a"]],props:{widget:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object},computed:{customClass:function(){return this.widget.options.customClass?this.widget.options.customClass.join(" "):""}}},r=a,d=(i("d49a"),i("2877")),c=Object(d["a"])(r,n,o,!1,null,"b98cf8dc",null);t["default"]=c.exports},cde5:function(e,t,i){"use strict";i("a8a0")},ce37:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.colPullTitle")}},[i("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0,max:24},model:{value:e.optionModel.pull,callback:function(t){e.$set(e.optionModel,"pull",e._n(t))},expression:"optionModel.pull"}})],1)},o=[],l=i("79fa"),s={name:"grid-col-pull-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"5a74fe7e",null);t["default"]=d.exports},ceaf:function(e,t,i){},ceec:function(e,t,i){"use strict";i("ee21")},cf32:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("form-item-wrapper",{attrs:{designer:e.designer,field:e.field,rules:e.rules,"design-state":e.designState,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList,"sub-form-row-index":e.subFormRowIndex,"sub-form-col-index":e.subFormColIndex,"sub-form-row-id":e.subFormRowId}},[i("el-input-number",{ref:"fieldEditor",staticClass:"full-width-input",attrs:{disabled:e.field.options.disabled,size:e.field.options.size,"controls-position":e.field.options.controlsPosition,placeholder:e.field.options.placeholder,min:e.field.options.min,max:e.field.options.max,precision:e.field.options.precision,step:e.field.options.step},on:{focus:e.handleFocusCustomEvent,blur:e.handleBlurCustomEvent,change:e.handleChangeEvent},model:{value:e.fieldModel,callback:function(t){e.fieldModel=t},expression:"fieldModel"}})],1)},o=[],l=(i("a9e3"),i("9eeb")),s=i("c6e3"),a=i("79fa"),r=i("2d11"),d={name:"number-widget",componentName:"FieldWidget",mixins:[s["a"],r["a"],a["b"]],props:{field:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object,designState:{type:Boolean,default:!1},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""}},components:{FormItemWrapper:l["default"]},inject:["refList","formConfig","globalOptionData","globalModel"],data:function(){return{oldFieldValue:null,fieldModel:null,rules:[]}},computed:{},beforeCreate:function(){},created:function(){this.initFieldModel(),this.registerToRefList(),this.initEventHandler(),this.buildFieldRules(),this.handleOnCreated()},mounted:function(){this.handleOnMounted()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{}},c=d,u=(i("34c5"),i("2877")),f=Object(u["a"])(c,n,o,!1,null,"a039267e",null);t["default"]=f.exports},d119:function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-file-upload-field",use:"icon-file-upload-field-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-file-upload-field"><defs><style type="text/css"></style></defs><path d="M676.48 72.96l209.92 211.2 9.6 23.04v620.8l-32 32h-704l-32-32v-832l32-32h494.08l22.4 8.96zM640 320h192l-192-192v192zM192 128v768h640V384H608L576 352V128H192z m512 320H320v64h384V448zM320 576h384v64H320V576z m384 128H320v64h384v-64z" p-id="65361" /></symbol>'});s.a.add(a);t["default"]=a},d1ba:function(e,t,i){"use strict";i("c6c1")},d2d9:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("el-form-item",{attrs:{label:e.i18nt("designer.setting.customClass")}},[i("el-select",{attrs:{multiple:"",filterable:"","allow-create":"","default-first-option":""},model:{value:e.optionModel.customClass,callback:function(t){e.$set(e.optionModel,"customClass",t)},expression:"optionModel.customClass"}},e._l(e.cssClassList,(function(e,t){return i("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1),i("el-form-item",{attrs:{label:e.i18nt("designer.setting.tabPaneSetting")}}),i("el-form-item",{staticClass:"panes-setting",attrs:{"label-width":"0"}},[i("draggable",e._b({attrs:{tag:"ul",list:e.selectedWidget.tabs}},"draggable",{group:"panesGroup",ghostClass:"ghost",handle:".drag-option"},!1),[e._l(e.selectedWidget.tabs,(function(t,n){return i("li",{key:n,staticClass:"col-item"},[i("el-checkbox",{staticStyle:{"margin-right":"8px"},attrs:{disabled:""},on:{change:function(i){return e.onTabPaneActiveChange(i,t)}},model:{value:t.options.active,callback:function(i){e.$set(t.options,"active",i)},expression:"tpItem.options.active"}},[e._v(e._s(e.i18nt("designer.setting.paneActive")))]),i("el-input",{staticStyle:{width:"155px"},attrs:{type:"text"},model:{value:t.options.label,callback:function(i){e.$set(t.options,"label",i)},expression:"tpItem.options.label"}}),i("i",{staticClass:"iconfont icon-drag drag-option"}),i("el-button",{staticClass:"col-delete-button",attrs:{circle:"",plain:"",size:"mini",type:"danger",icon:"el-icon-minus"},on:{click:function(t){return e.deleteTabPane(e.selectedWidget,n)}}})],1)})),i("div",[i("el-button",{attrs:{type:"text"},on:{click:function(t){return e.addTabPane(e.selectedWidget)}}},[e._v(e._s(e.i18nt("designer.setting.addTabPane")))])],1)],2)],1)],1)},o=[],l=i("79fa"),s=i("b76a"),a=i.n(s),r=i("ca00"),d={name:"tab-customClass-editor",componentName:"PropertyEditor",mixins:[l["b"]],components:{Draggable:a.a},props:{designer:Object,selectedWidget:Object,optionModel:Object},data:function(){return{cssClassList:[]}},created:function(){var e=this;this.cssClassList=Object(r["d"])(this.designer.getCssClassList()),this.designer.handleEvent("form-css-updated",(function(t){e.cssClassList=t}))},methods:{onTabPaneActiveChange:function(e,t){},addTabPane:function(e){this.designer.addTabPaneOfTabs(e),this.designer.emitHistoryChange()},deleteTabPane:function(e,t){1!==e.tabs.length?(this.designer.deleteTabPaneOfTabs(e,t),this.designer.emitHistoryChange()):this.$message.info(this.i18nt("designer.hint.lastPaneCannotBeDeleted"))}}},c=d,u=(i("fe05"),i("2877")),f=Object(u["a"])(c,n,o,!1,null,"6ddd24c1",null);t["default"]=f.exports},d2e4:function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-textarea-field",use:"icon-textarea-field-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-textarea-field"><defs><style type="text/css"></style></defs><path d="M896.4 173.1H128.9c-35.2 0-49 13.8-49 49v575.6c0 35.2 13.8 49 49 49h767.5c35.2 0 49-13.8 49-49V222.1c0-35.2-13.8-49-49-49z m0 592.6c0 16-12.8 32-32 32H160.9c-19.2 0-32-12.8-32-32V254.1c0-16 12.8-32 32-32h703.5c19.2 0 32 12.8 32 32v511.6z" p-id="12940" /><path d="M710.2 766.7h141.5c8.1 0 14.7-6.6 14.7-14.7V610.4c0-1.3-1.6-2-2.6-1.1L709.1 764.1c-1 1-0.3 2.6 1.1 2.6zM207.2 594.3h-13.5c-10 0-18.2-8.2-18.2-18.2V291.8c0-10.2 8.4-18.6 18.6-18.6h12.8c10.2 0 18.6 8.4 18.6 18.6v284.3c-0.1 10-8.3 18.2-18.3 18.2z" p-id="12941" /></symbol>'});s.a.add(a);t["default"]=a},d312:function(e,t,i){"use strict";i("7873")},d3eb:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.showStops")}},[i("el-switch",{model:{value:e.optionModel.showStops,callback:function(t){e.$set(e.optionModel,"showStops",t)},expression:"optionModel.showStops"}})],1)},o=[],l=i("79fa"),s={name:"showStops-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"24139f8e",null);t["default"]=d.exports},d3fb:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAWdJREFUeNrsmjFuwjAUhp8jxJQBujQH4ABhYmaDU7Sdu/QIcIKKE0APwM7GEQgLU/dmo0N3178SI0Qx2KpQ8sJ7kpUojsn/2S/JrxeImhRa64lpe13fgLbJsWZVCu+Yzdq0lMlcZ0qpPnai8sAbI/GI1K6EXYG92XSYZfy3WYWuBdAc71kDoFquzo+MaLHx+6H1S9iYNCF6Hxf7w/n180c9otcBUdz+2xdxmOnVJ9Fyd74v4pIu2RdzAFcIgAAIgABUG62qBTz3/c57jGsK8JRKClVgC3I/E+cygPIUEgABEAABEIDGADjrQvlP0XzfkqFjQgMlld7DiXiEC4DF7F8qbMGvbPPbXBjWGMUqBIphoWO8zBzE+1bmQgMpZ8X4XuN4jDyFBEAABEAA7g8giRkDwAPhE9PNzNx/jFmWh5u4c2aO7WdWm0Izhvfv7JBC5SrAVrH9qwGVB6ZYmjqnDTRa8Y2IXwEGAI1dAiVnUcUMAAAAAElFTkSuQmCC"},d459:function(e,t,i){"use strict";i("112c")},d49a:function(e,t,i){"use strict";i("6ce9")},d53b:function(e,t,i){"use strict";i("f6f1")},d67b:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("form-item-wrapper",{attrs:{designer:e.designer,field:e.field,rules:e.rules,"design-state":e.designState,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList,"sub-form-row-index":e.subFormRowIndex,"sub-form-col-index":e.subFormColIndex,"sub-form-row-id":e.subFormRowId}},[i("el-input",{ref:"fieldEditor",staticClass:"hide-spin-button",attrs:{disabled:e.field.options.disabled,readonly:e.field.options.readonly,size:e.field.options.size,type:e.inputType,"show-password":e.field.options.showPassword,placeholder:e.field.options.placeholder,clearable:e.field.options.clearable,minlength:e.field.options.minLength,maxlength:e.field.options.maxLength,"show-word-limit":e.field.options.showWordLimit,"prefix-icon":e.field.options.prefixIcon,"suffix-icon":e.field.options.suffixIcon},on:{focus:e.handleFocusCustomEvent,blur:e.handleBlurCustomEvent,input:e.handleInputCustomEvent,change:e.handleChangeEvent},model:{value:e.fieldModel,callback:function(t){e.fieldModel=t},expression:"fieldModel"}},[e.field.options.appendButton?i("el-button",{class:e.field.options.buttonIcon,attrs:{slot:"append",disabled:e.field.options.disabled||e.field.options.appendButtonDisabled},nativeOn:{click:function(t){return e.emitAppendButtonClick.apply(null,arguments)}},slot:"append"}):e._e()],1)],1)},o=[],l=(i("a9e3"),i("9eeb")),s=i("c6e3"),a=i("79fa"),r=i("2d11"),d={name:"input-widget",componentName:"FieldWidget",mixins:[s["a"],r["a"],a["b"]],props:{field:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object,designState:{type:Boolean,default:!1},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""}},components:{FormItemWrapper:l["default"]},inject:["refList","formConfig","globalOptionData","globalModel"],data:function(){return{oldFieldValue:null,fieldModel:null,rules:[]}},computed:{inputType:function(){return"number"===this.field.options.type?"text":this.field.options.type}},beforeCreate:function(){},created:function(){this.initFieldModel(),this.registerToRefList(),this.initEventHandler(),this.buildFieldRules(),this.handleOnCreated()},mounted:function(){this.handleOnMounted()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{}},c=d,u=(i("cbd6"),i("2877")),f=Object(u["a"])(c,n,o,!1,null,"97099720",null);t["default"]=f.exports},d67f:function(e,t,i){"use strict";i("b0c0");t["a"]={methods:{initRefList:function(){null!==this.refList&&this.widget.options.name&&(this.refList[this.widget.options.name]=this)},getWidgetRef:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=this.refList[e];return!i&&t&&this.$message.error(this.i18nt("render.hint.refNotFound")+e),i},getFormRef:function(){return this.refList["v_form_ref"]},getComponentByContainer:function(e){return"grid"===e.type?"vf-grid-item":e.type+"-item"}}}},d6e6:function(e,t,i){},d741:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.withCredentials")}},[i("el-switch",{model:{value:e.optionModel.withCredentials,callback:function(t){e.$set(e.optionModel,"withCredentials",t)},expression:"optionModel.withCredentials"}})],1)},o=[],l=i("79fa"),s={name:"withCredentials-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"3d15893a",null);t["default"]=d.exports},d773:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:"onSubFormRowDelete","label-width":"150px"}},[i("el-button",{attrs:{type:"info",icon:"el-icon-edit",plain:"",round:""},on:{click:function(t){return e.editEventHandler("onSubFormRowDelete",e.eventParams)}}},[e._v(" "+e._s(e.i18nt("designer.setting.addEventHandler")))])],1)},o=[],l=i("79fa"),s=i("7d6c"),a={name:"onSubFormRowDelete-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},data:function(){return{eventParams:["subFormData","deletedDataRow"]}}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"1a13f95e",null);t["default"]=c.exports},d8ec:function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-grid",use:"icon-grid-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-grid"><defs><style type="text/css"></style></defs><path d="M819.3536 921.6h102.4v-102.4h-102.4v102.4z m102.4-204.8h-102.4a102.4 102.4 0 0 0-102.4 102.4v102.4a102.4 102.4 0 0 0 102.4 102.4h102.4a102.4 102.4 0 0 0 102.4-102.4v-102.4a102.4 102.4 0 0 0-102.4-102.4z m-460.8 204.8h102.4v-102.4h-102.4v102.4z m102.4-204.8h-102.4a102.4 102.4 0 0 0-102.4 102.4v102.4a102.4 102.4 0 0 0 102.4 102.4h102.4a102.4 102.4 0 0 0 102.4-102.4v-102.4a102.4 102.4 0 0 0-102.4-102.4z m-460.8 204.8h102.4v-102.4h-102.4v102.4z m102.4-204.8h-102.4a102.4 102.4 0 0 0-102.4 102.4v102.4a102.4 102.4 0 0 0 102.4 102.4h102.4a102.4 102.4 0 0 0 102.4-102.4v-102.4a102.4 102.4 0 0 0-102.4-102.4z m614.4-153.6h102.4V460.8h-102.4v102.4z m102.4-204.8h-102.4a102.4 102.4 0 0 0-102.4 102.4v102.4a102.4 102.4 0 0 0 102.4 102.4h102.4a102.4 102.4 0 0 0 102.4-102.4V460.8a102.4 102.4 0 0 0-102.4-102.4z m-460.8 204.8h102.4V460.8h-102.4v102.4z m102.4-204.8h-102.4a102.4 102.4 0 0 0-102.4 102.4v102.4a102.4 102.4 0 0 0 102.4 102.4h102.4a102.4 102.4 0 0 0 102.4-102.4V460.8a102.4 102.4 0 0 0-102.4-102.4z m-460.8 204.8h102.4V460.8h-102.4v102.4z m102.4-204.8h-102.4a102.4 102.4 0 0 0-102.4 102.4v102.4a102.4 102.4 0 0 0 102.4 102.4h102.4a102.4 102.4 0 0 0 102.4-102.4V460.8a102.4 102.4 0 0 0-102.4-102.4z m614.4-153.6h102.4V102.4h-102.4v102.4z m102.4-204.8h-102.4a102.4 102.4 0 0 0-102.4 102.4v102.4a102.4 102.4 0 0 0 102.4 102.4h102.4a102.4 102.4 0 0 0 102.4-102.4V102.4a102.4 102.4 0 0 0-102.4-102.4z m-460.8 204.8h102.4V102.4h-102.4v102.4z m102.4-204.8h-102.4a102.4 102.4 0 0 0-102.4 102.4v102.4a102.4 102.4 0 0 0 102.4 102.4h102.4a102.4 102.4 0 0 0 102.4-102.4V102.4a102.4 102.4 0 0 0-102.4-102.4z m-460.8 204.8h102.4V102.4h-102.4v102.4z m102.4-204.8h-102.4a102.4 102.4 0 0 0-102.4 102.4v102.4a102.4 102.4 0 0 0 102.4 102.4h102.4a102.4 102.4 0 0 0 102.4-102.4V102.4a102.4 102.4 0 0 0-102.4-102.4z" p-id="8473" /></symbol>'});s.a.add(a);t["default"]=a},da4e:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("form-item-wrapper",{attrs:{designer:e.designer,field:e.field,rules:e.rules,"design-state":e.designState,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList,"sub-form-row-index":e.subFormRowIndex,"sub-form-col-index":e.subFormColIndex,"sub-form-row-id":e.subFormRowId}},[i("el-date-picker",{ref:"fieldEditor",staticClass:"full-width-input",attrs:{type:e.field.options.type,readonly:e.field.options.readonly,disabled:e.field.options.disabled,size:e.field.options.size,clearable:e.field.options.clearable,editable:e.field.options.editable,format:e.field.options.format,"value-format":e.field.options.valueFormat,placeholder:e.field.options.placeholder||e.i18nt("render.hint.datePlaceholder")},on:{focus:e.handleFocusCustomEvent,blur:e.handleBlurCustomEvent,change:e.handleChangeEvent},model:{value:e.fieldModel,callback:function(t){e.fieldModel=t},expression:"fieldModel"}})],1)},o=[],l=(i("a9e3"),i("9eeb")),s=i("c6e3"),a=i("79fa"),r=i("2d11"),d={name:"date-widget",componentName:"FieldWidget",mixins:[s["a"],r["a"],a["b"]],props:{field:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object,designState:{type:Boolean,default:!1},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""}},components:{FormItemWrapper:l["default"]},inject:["refList","formConfig","globalOptionData","globalModel"],data:function(){return{oldFieldValue:null,fieldModel:null,rules:[]}},computed:{},beforeCreate:function(){},created:function(){this.initFieldModel(),this.registerToRefList(),this.initEventHandler(),this.buildFieldRules(),this.handleOnCreated()},mounted:function(){this.handleOnMounted()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{}},c=d,u=(i("38b9"),i("2877")),f=Object(u["a"])(c,n,o,!1,null,"ea728dba",null);t["default"]=f.exports},db13:function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-sub-form",use:"icon-sub-form-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-sub-form"><defs><style type="text/css"></style></defs><path d="M512 106.666667H112a5.333333 5.333333 0 0 0-5.333333 5.333333v800a5.333333 5.333333 0 0 0 5.333333 5.333333h800a5.333333 5.333333 0 0 0 5.333333-5.333333V112a5.333333 5.333333 0 0 0-5.333333-5.333333z m0 74.666666h325.333333a5.333333 5.333333 0 0 1 5.333334 5.333334v160a5.333333 5.333333 0 0 1-5.333334 5.333333H186.666667a5.333333 5.333333 0 0 1-5.333334-5.333333V186.666667a5.333333 5.333333 0 0 1 5.333334-5.333334z m85.333333 250.666667v405.333333a5.333333 5.333333 0 0 1-5.333333 5.333334H432a5.333333 5.333333 0 0 1-5.333333-5.333334V432a5.333333 5.333333 0 0 1 5.333333-5.333333h160a5.333333 5.333333 0 0 1 5.333333 5.333333z m-410.666666-5.333333h160a5.333333 5.333333 0 0 1 5.333333 5.333333v405.333333a5.333333 5.333333 0 0 1-5.333333 5.333334H186.666667a5.333333 5.333333 0 0 1-5.333334-5.333334V432a5.333333 5.333333 0 0 1 5.333334-5.333333z m485.333333 410.666666V432a5.333333 5.333333 0 0 1 5.333333-5.333333h160a5.333333 5.333333 0 0 1 5.333334 5.333333v405.333333a5.333333 5.333333 0 0 1-5.333334 5.333334h-160a5.333333 5.333333 0 0 1-5.333333-5.333334z" p-id="97355" /></symbol>'});s.a.add(a);t["default"]=a},dbe8:function(e,t,i){"use strict";i("9c31")},dca80:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.appendButtonDisabled")}},[i("el-switch",{model:{value:e.optionModel.appendButtonDisabled,callback:function(t){e.$set(e.optionModel,"appendButtonDisabled",t)},expression:"optionModel.appendButtonDisabled"}})],1)},o=[],l=i("79fa"),s={name:"appendButtonDisabled-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"5ce987da",null);t["default"]=d.exports},de19:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("static-content-wrapper",{attrs:{designer:e.designer,field:e.field,"design-state":e.designState,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList,"sub-form-row-index":e.subFormRowIndex,"sub-form-col-index":e.subFormColIndex,"sub-form-row-id":e.subFormRowId}},[i("el-divider",{ref:"fieldEditor",attrs:{direction:"horizontal","content-position":e.field.options.contentPosition}},[e._v(" "+e._s(e.field.options.label))])],1)},o=[],l=(i("a9e3"),i("828b")),s=i("c6e3"),a=i("79fa"),r=i("2d11"),d={name:"divider-widget",componentName:"FieldWidget",mixins:[s["a"],r["a"],a["b"]],props:{field:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object,designState:{type:Boolean,default:!1},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""}},components:{StaticContentWrapper:l["default"]},computed:{},beforeCreate:function(){},created:function(){this.registerToRefList(),this.initEventHandler(),this.handleOnCreated()},mounted:function(){this.handleOnMounted()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{}},c=d,u=(i("d1ba"),i("2877")),f=Object(u["a"])(c,n,o,!1,null,"0faf59b2",null);t["default"]=f.exports},df12:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("el-form-item",{attrs:{label:e.i18nt("designer.setting.htmlContent")}}),i("el-form-item",{attrs:{"label-width":"0"}},[i("el-input",{staticClass:"html-content-editor",attrs:{type:"textarea",rows:5},model:{value:e.optionModel.htmlContent,callback:function(t){e.$set(e.optionModel,"htmlContent",t)},expression:"optionModel.htmlContent"}})],1)],1)},o=[],l=i("79fa"),s={name:"htmlContent-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=(i("effc"),i("2877")),d=Object(r["a"])(a,n,o,!1,null,"a3fcc8ba",null);t["default"]=d.exports},df90:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.endPlaceholder")}},[i("el-input",{attrs:{type:"text"},model:{value:e.optionModel.endPlaceholder,callback:function(t){e.$set(e.optionModel,"endPlaceholder",t)},expression:"optionModel.endPlaceholder"}})],1)},o=[],l=i("79fa"),s={name:"endPlaceholder-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"373a69c1",null);t["default"]=d.exports},e072:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:"onValidate","label-width":"150px"}},[i("el-button",{attrs:{type:"info",icon:"el-icon-edit",plain:"",round:""},on:{click:function(t){return e.editEventHandler("onValidate",e.eventParams)}}},[e._v(" "+e._s(e.i18nt("designer.setting.addEventHandler")))])],1)},o=[],l=i("79fa"),s=i("7d6c"),a={name:"onValidate-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},data:function(){return{eventParams:["rule","value","callback"]}}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"4509c6b4",null);t["default"]=c.exports},e090:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.maxLength")}},[i("el-input",{staticClass:"hide-spin-button",staticStyle:{width:"100%"},attrs:{type:"number",min:"1"},nativeOn:{input:function(t){return e.inputNumberHandler.apply(null,arguments)}},model:{value:e.maxLength,callback:function(t){e.maxLength=t},expression:"maxLength"}})],1)},o=[],l=(i("a9e3"),i("79fa")),s=i("b2bf"),a={name:"maxLength-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},computed:{maxLength:{get:function(){return this.optionModel["maxLength"]},set:function(e){!e||isNaN(e)?this.optionModel.maxLength=null:this.optionModel.maxLength=Number(e)}}}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"361e66e1",null);t["default"]=c.exports},e0b7:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.defaultValue")}},[i("el-switch",{attrs:{"active-text":"true","inactive-text":"false"},on:{change:e.emitDefaultValueChange},model:{value:e.optionModel.defaultValue,callback:function(t){e.$set(e.optionModel,"defaultValue",t)},expression:"optionModel.defaultValue"}})],1)},o=[],l=i("79fa"),s=i("b2bf"),a={name:"switch-defaultValue-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"98bc90ba",null);t["default"]=c.exports},e11b:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("container-wrapper",{attrs:{designer:e.designer,widget:e.widget,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList}},[i("el-row",{key:e.widget.id,staticClass:"grid-container",class:[e.selected?"selected":"",e.customClass],attrs:{gutter:e.widget.options.gutter},nativeOn:{click:function(t){return t.stopPropagation(),e.selectWidget(e.widget)}}},[e._l(e.widget.cols,(function(t,n){return[i("grid-col-widget",{key:t.id,attrs:{widget:t,designer:e.designer,"parent-list":e.widget.cols,"index-of-parent-list":n,"parent-widget":e.widget,"col-height":e.widget.options.colHeight}})]}))],2)],1)},o=[],l=(i("a9e3"),i("79fa")),s=i("f7729"),a=i("61ca"),r=i("cd36"),d=i("26a6"),c={name:"grid-widget",componentName:"ContainerWidget",mixins:[l["b"],a["a"],d["a"]],inject:["refList"],components:{ContainerWrapper:r["default"],GridColWidget:s["default"]},props:{widget:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object},computed:{selected:function(){return this.widget.id===this.designer.selectedId},customClass:function(){return this.widget.options.customClass||""}},watch:{},created:function(){this.initRefList()},mounted:function(){},methods:{}},u=c,f=(i("3c10"),i("2877")),p=Object(f["a"])(u,n,o,!1,null,"ec57838c",null);t["default"]=p.exports},e447:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("el-form-item",{attrs:{"label-width":"0"}},[i("el-divider",{staticClass:"custom-divider"},[e._v(e._s(e.i18nt("designer.setting.columnSetting")))])],1),i("el-form-item",{attrs:{label:e.i18nt("designer.setting.gutter")}},[i("el-input-number",{staticStyle:{width:"100%"},model:{value:e.optionModel.gutter,callback:function(t){e.$set(e.optionModel,"gutter",t)},expression:"optionModel.gutter"}})],1),i("el-form-item",{attrs:{label:e.i18nt("designer.setting.colsOfGrid")}}),i("el-form-item",{attrs:{"label-width":"0"}},[e._l(e.selectedWidget.cols,(function(t,n){return i("li",{key:n,staticClass:"col-item"},[i("span",{staticClass:"col-span-title"},[e._v(e._s(e.i18nt("designer.setting.colSpanTitle"))+e._s(n+1))]),i("el-input-number",{staticClass:"cell-span-input",attrs:{min:1,max:24},on:{change:function(i,o){return e.spanChanged(e.selectedWidget,t,n,i,o)}},model:{value:t.options.span,callback:function(i){e.$set(t.options,"span",e._n(i))},expression:"colItem.options.span"}}),i("el-button",{staticClass:"col-delete-button",attrs:{circle:"",plain:"",size:"mini",type:"danger",icon:"el-icon-minus"},on:{click:function(t){return e.deleteCol(e.selectedWidget,n)}}})],1)})),i("div",[i("el-button",{attrs:{type:"text"},on:{click:function(t){return e.addNewCol(e.selectedWidget)}}},[e._v(e._s(e.i18nt("designer.setting.addColumn")))])],1)],2)],1)},o=[],l=(i("d3b7"),i("159b"),i("79fa")),s={name:"gutter-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},methods:{spanChanged:function(e){var t=0;e.cols.forEach((function(e){t+=e.options.span})),t>24&&console.log("列栅格之和超出24"),this.designer.saveCurrentHistoryStep()},deleteCol:function(e,t){this.designer.deleteColOfGrid(e,t),this.designer.emitHistoryChange()},addNewCol:function(e){this.designer.addNewColOfGrid(e),this.designer.emitHistoryChange()}}},a=s,r=(i("2445"),i("2877")),d=Object(r["a"])(a,n,o,!1,null,"ebfa1f06",null);t["default"]=d.exports},e486:function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-node-tree",use:"icon-node-tree-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-node-tree"><defs><style type="text/css"></style></defs><path d="M332.48 500.864a25.6 25.6 0 1 0 0-51.2H192.384v-184.96a115.2 115.2 0 0 0 89.6-112.128c0-63.488-51.712-115.2-115.2-115.2s-115.2 51.712-115.2 115.2a115.2 115.2 0 0 0 89.6 112.128v696.192a25.6 25.6 0 1 0 51.2 0v-141.12c2.304 0.192 4.48 0.512 6.912 0.512h133.184a25.6 25.6 0 1 0 0-51.2H199.296c-3.456 0-5.504-0.448-6.08-0.256a29.184 29.184 0 0 1-0.896-8.576V500.8h140.16zM102.784 152.64c0-35.264 28.736-64 64-64s64 28.736 64 64-28.736 64-64 64-64-28.736-64-64zM921.216 360.064h-486.4c-28.224 0-51.2 22.976-51.2 51.2v128c0 28.224 22.976 51.2 51.2 51.2h486.4c28.224 0 51.2-22.976 51.2-51.2v-128c0-28.224-22.976-51.2-51.2-51.2z m-486.336 179.2v-128h486.4v128h-486.4zM921.216 679.616h-486.4c-28.224 0-51.2 22.976-51.2 51.2v128c0 28.224 22.976 51.2 51.2 51.2h486.4c28.224 0 51.2-22.976 51.2-51.2v-128c0-28.224-22.976-51.2-51.2-51.2z m-486.336 179.2v-128h486.4v128h-486.4z" p-id="4876" /></symbol>'});s.a.add(a);t["default"]=a},e632:function(e,t,i){},e801:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.labelWidth")}},[i("el-input",{staticClass:"hide-spin-button",attrs:{type:"number",min:"0"},nativeOn:{input:function(t){return e.inputNumberHandler.apply(null,arguments)}},model:{value:e.optionModel.labelWidth,callback:function(t){e.$set(e.optionModel,"labelWidth",t)},expression:"optionModel.labelWidth"}})],1)},o=[],l=i("79fa"),s=i("b2bf"),a={name:"labelWidth-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"39b735cc",null);t["default"]=c.exports},e86f:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.multiple")}},[i("el-switch",{on:{change:e.onMultipleSelected},model:{value:e.optionModel.multiple,callback:function(t){e.$set(e.optionModel,"multiple",t)},expression:"optionModel.multiple"}})],1)},o=[],l=i("79fa"),s=i("b2bf"),a={name:"multiple-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"30a47068",null);t["default"]=c.exports},e894:function(e,t,i){"use strict";i("4ef4")},e895:function(e,t,i){"use strict";i("3cd5")},e8d7:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.showAllLevels")}},[i("el-switch",{model:{value:e.optionModel.showAllLevels,callback:function(t){e.$set(e.optionModel,"showAllLevels",t)},expression:"optionModel.showAllLevels"}})],1)},o=[],l=i("79fa"),s={name:"showAllLevels-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"1f0743ee",null);t["default"]=d.exports},e905:function(e,t,i){},e934:function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-section",use:"icon-section-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-section"><defs><style type="text/css"></style></defs><path d="M141.074286 906.496h741.851428c89.581714 0 134.582857-44.562286 134.582857-132.845714V250.331429c0-88.283429-45.001143-132.845714-134.582857-132.845715H141.074286C51.931429 117.504 6.491429 161.645714 6.491429 250.331429V773.668571c0 88.704 45.44 132.845714 134.582857 132.845715z m1.28-68.992c-42.861714 0-66.852571-22.710857-66.852572-67.291429V253.805714c0-44.580571 23.990857-67.291429 66.852572-67.291428h739.291428c42.422857 0 66.852571 22.710857 66.852572 67.291428V770.194286c0 44.580571-24.429714 67.291429-66.852572 67.291428z" p-id="11329" /></symbol>'});s.a.add(a);t["default"]=a},e96c:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.activeText")}},[i("el-input",{model:{value:e.optionModel.activeText,callback:function(t){e.$set(e.optionModel,"activeText",t)},expression:"optionModel.activeText"}})],1)},o=[],l=i("79fa"),s={name:"activeText-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"2684f3ad",null);t["default"]=d.exports},eb5e:function(e,t,i){},ecaa:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("form-item-wrapper",{attrs:{designer:e.designer,field:e.field,rules:e.rules,"design-state":e.designState,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList,"sub-form-row-index":e.subFormRowIndex,"sub-form-col-index":e.subFormColIndex,"sub-form-row-id":e.subFormRowId}},[i("el-switch",{ref:"fieldEditor",attrs:{disabled:e.field.options.disabled,"active-text":e.field.options.activeText,"inactive-text":e.field.options.inactiveText,"active-color":e.field.options.activeColor,"inactive-color":e.field.options.inactiveColor,width:e.field.options.switchWidth},on:{change:e.handleChangeEvent},model:{value:e.fieldModel,callback:function(t){e.fieldModel=t},expression:"fieldModel"}})],1)},o=[],l=(i("a9e3"),i("9eeb")),s=i("c6e3"),a=i("79fa"),r=i("2d11"),d={name:"switch-widget",componentName:"FieldWidget",mixins:[s["a"],r["a"],a["b"]],props:{field:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object,designState:{type:Boolean,default:!1},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""}},components:{FormItemWrapper:l["default"]},inject:["refList","formConfig","globalOptionData","globalModel"],data:function(){return{oldFieldValue:null,fieldModel:null,rules:[]}},computed:{},beforeCreate:function(){},created:function(){this.initFieldModel(),this.registerToRefList(),this.initEventHandler(),this.buildFieldRules(),this.handleOnCreated()},mounted:function(){this.handleOnMounted()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{}},c=d,u=(i("7b2c"),i("2877")),f=Object(u["a"])(c,n,o,!1,null,"88bb0ad8",null);t["default"]=f.exports},ecf2:function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-slot-field",use:"icon-slot-field-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-slot-field"><defs><style type="text/css"></style></defs><path d="M475.937391 244.869565m18.031305 0l36.285217 0q18.031304 0 18.031304 18.031305l0 217.266087q0 18.031304-18.031304 18.031304l-36.285217 0q-18.031304 0-18.031305-18.031304l0-217.266087q0-18.031304 18.031305-18.031305Z" p-id="2852" /><path d="M305.41913 525.801739m18.031305 0l36.285217 0q18.031304 0 18.031305 18.031304l0 217.266087q0 18.031304-18.031305 18.031305l-36.285217 0q-18.031304 0-18.031305-18.031305l0-217.266087q0-18.031304 18.031305-18.031304Z" p-id="2853" /><path d="M646.233043 525.801739m18.031305 0l36.285217 0q18.031304 0 18.031305 18.031304l0 217.266087q0 18.031304-18.031305 18.031305l-36.285217 0q-18.031304 0-18.031305-18.031305l0-217.266087q0-18.031304 18.031305-18.031304Z" p-id="2854" /><path d="M827.436522 122.212174H196.563478a74.573913 74.573913 0 0 0-74.351304 74.351304v630.873044a74.573913 74.573913 0 0 0 74.351304 74.351304h630.873044a74.573913 74.573913 0 0 0 74.351304-74.351304V196.563478a74.573913 74.573913 0 0 0-74.351304-74.351304z m52.090435 705.224348a52.090435 52.090435 0 0 1-52.090435 52.090435H196.563478a52.090435 52.090435 0 0 1-52.090435-52.090435V196.563478a52.090435 52.090435 0 0 1 52.090435-52.090435h630.873044a52.090435 52.090435 0 0 1 52.090435 52.090435z" p-id="2855" /></symbol>'});s.a.add(a);t["default"]=a},ee21:function(e,t,i){},efdc:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("container-item-wrapper",{attrs:{widget:e.widget}},[i("div",{directives:[{name:"show",rawName:"v-show",value:!e.widget.options.hidden,expression:"!widget.options.hidden"}],key:e.widget.id,staticClass:"sub-form-container"},[i("el-row",{staticClass:"header-row"},[i("div",{staticClass:"action-header-column"},[i("span",{staticClass:"action-label"},[e._v(e._s(e.i18nt("render.hint.subFormAction")))]),i("el-button",{staticClass:"action-button",attrs:{disabled:e.actionDisabled,round:"",type:"primary",size:"mini",title:e.i18nt("render.hint.subFormAddActionHint")},on:{click:e.addSubFormRow}},[e._v(" "+e._s(e.i18nt("render.hint.subFormAddAction"))),i("i",{staticClass:"el-icon-plus el-icon-right"})])],1),e._l(e.widget.widgetList,(function(t){return[i("div",{key:t.id+"thc",staticClass:"field-header-column",class:[e.getLabelAlign(e.widget,t),t.options.required?"is-required":""],style:{width:t.options.columnWidth}},[t.options.labelIconClass?i("span",{staticClass:"custom-label"},["front"===t.options.labelIconPosition?[t.options.labelTooltip?[i("el-tooltip",{attrs:{content:t.options.labelTooltip,effect:"light"}},[i("i",{class:t.options.labelIconClass})]),e._v(e._s(t.options.label))]:[i("i",{class:t.options.labelIconClass}),e._v(e._s(t.options.label))]]:"rear"===t.options.labelIconPosition?[t.options.labelTooltip?[e._v(" "+e._s(t.options.label)),i("el-tooltip",{attrs:{content:t.options.labelTooltip,effect:"light"}},[i("i",{class:t.options.labelIconClass})])]:[e._v(" "+e._s(t.options.label)),i("i",{class:t.options.labelIconClass})]]:e._e()],2):[i("span",{attrs:{title:t.options.labelTooltip}},[e._v(e._s(t.options.label))])]],2)]}))],2),e._l(e.rowIdData,(function(t,n){return i("el-row",{key:t,staticClass:"sub-form-row"},[i("div",{staticClass:"sub-form-action-column hide-label"},[i("div",{staticClass:"action-button-column"},[i("el-button",{attrs:{disabled:e.actionDisabled,circle:"",type:"",icon:"el-icon-circle-plus-outline",title:e.i18nt("render.hint.insertSubFormRow")},on:{click:function(t){return e.insertSubFormRow(n)}}}),i("el-button",{attrs:{disabled:e.actionDisabled,circle:"",type:"",icon:"el-icon-delete",title:e.i18nt("render.hint.deleteSubFormRow")},on:{click:function(t){return e.deleteSubFormRow(n)}}}),e.widget.options.showRowNumber?i("span",{staticClass:"row-number-span"},[e._v("#"+e._s(n+1))]):e._e()],1)]),e._l(e.widget.widgetList,(function(o,l){return[i("div",{key:o.id+"tc"+t,staticClass:"sub-form-table-column hide-label",style:{width:o.options.columnWidth}},[i(o.type+"-widget",{key:e.fieldSchemaData[n][l].id,tag:"component",attrs:{field:e.fieldSchemaData[n][l],"parent-list":e.widget.widgetList,"index-of-parent-list":l,"parent-widget":e.widget,"sub-form-row-id":t,"sub-form-row-index":n,"sub-form-col-index":l}})],1)]}))],2)}))],2)])},o=[],l=i("5530"),s=(i("b0c0"),i("a434"),i("d3b7"),i("159b"),i("c6e3")),a=i("79fa"),r=i("ca00"),d=i("d67f"),c=i("90c2"),u=i("34f0"),f=i("c029"),p={name:"sub-form-item",componentName:"ContainerItem",mixins:[s["a"],a["b"],d["a"],u["a"]],components:Object(l["a"])({ContainerItemWrapper:c["default"]},f["a"]),props:{widget:Object},inject:["refList","sfRefList","globalModel"],data:function(){return{rowIdData:[],fieldSchemaData:[],actionDisabled:!1}},created:function(){this.initRefList(),this.registerSubFormToRefList(),this.initRowIdData(!0),this.initFieldSchemaData(),this.initEventHandler()},mounted:function(){this.handleSubFormFirstRowAdd()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{getLabelAlign:function(e,t){return t.options.labelAlign||e.options.labelAlign},registerSubFormToRefList:function(){"sub-form"===this.widget.type&&(this.sfRefList[this.widget.options.name]=this)},initRowIdData:function(e){var t=this;if("sub-form"===this.widget.type){this.rowIdData.splice(0,this.rowIdData.length);var i=this.formModel[this.widget.options.name];i&&i.length>0&&(i.forEach((function(){t.rowIdData.push("id"+Object(r["e"])())})),e&&setTimeout((function(){t.handleSubFormRowChange(i)}),800))}},addToRowIdData:function(){this.rowIdData.push("id"+Object(r["e"])())},insertToRowIdData:function(e){this.rowIdData.splice(e,0,"id"+Object(r["e"])())},deleteFromRowIdData:function(e){this.rowIdData.splice(e,1)},getRowIdData:function(){return this.rowIdData},getWidgetRefOfSubForm:function(e,t){var i=e+"@row"+this.rowIdData[t];return this.getWidgetRef(i)},initFieldSchemaData:function(){var e=this;if("sub-form"===this.widget.type){var t=this.rowIdData.length;if(this.fieldSchemaData.splice(0,this.fieldSchemaData.length),t>0)for(var i=function(t){var i=[];e.widget.widgetList.forEach((function(t){i.push(e.cloneFieldSchema(t))})),e.fieldSchemaData.push(i)},n=0;n<t;n++)i(n)}},addToFieldSchemaData:function(e){var t=this,i=[];this.widget.widgetList.forEach((function(e){i.push(t.cloneFieldSchema(e))})),void 0===e?this.fieldSchemaData.push(i):this.fieldSchemaData.splice(e,0,i)},deleteFromFieldSchemaData:function(e){this.fieldSchemaData.splice(e,1)},cloneFieldSchema:function(e){var t=Object(r["d"])(e);return t.id=e.type+Object(r["e"])(),t},initEventHandler:function(){var e=this;"sub-form"===this.widget.type&&this.$on("setFormData",(function(t){e.initRowIdData(!1),e.initFieldSchemaData();var i=t[e.widget.options.name]||[];setTimeout((function(){e.handleSubFormRowChange(i)}),800)}))},handleSubFormFirstRowAdd:function(){if("sub-form"===this.widget.type&&this.widget.options.showBlankRow&&1===this.rowIdData.length){var e=this.formModel[this.widget.options.name]||[];this.handleSubFormRowAdd(e,this.rowIdData[0]),this.handleSubFormRowChange(e)}},addSubFormRow:function(){var e={};this.widget.widgetList.forEach((function(t){t.formItemFlag&&(e[t.options.name]=t.options.defaultValue)}));var t=this.formModel[this.widget.options.name]||[];t.push(e),this.addToRowIdData(),this.addToFieldSchemaData(),this.handleSubFormRowAdd(t,this.rowIdData[t.length-1]),this.handleSubFormRowChange(t)},insertSubFormRow:function(e){var t={};this.widget.widgetList.forEach((function(e){e.formItemFlag&&(t[e.options.name]=e.options.defaultValue)}));var i=this.formModel[this.widget.options.name]||[];i.splice(e,0,t),this.insertToRowIdData(e),this.addToFieldSchemaData(e),this.handleSubFormRowInsert(i,this.rowIdData[e]),this.handleSubFormRowChange(i)},deleteSubFormRow:function(e){var t=this;this.$confirm(this.i18nt("render.hint.deleteSubFormRow")+"?",this.i18nt("render.hint.prompt"),{confirmButtonText:this.i18nt("render.hint.confirm"),cancelButtonText:this.i18nt("render.hint.cancel")}).then((function(){var i=t.formModel[t.widget.options.name]||[],n=Object(r["d"])(i[e]);i.splice(e,1),t.deleteFromRowIdData(e),t.deleteFromFieldSchemaData(e),t.handleSubFormRowDelete(i,n),t.handleSubFormRowChange(i)})).catch((function(){}))},handleSubFormRowChange:function(e){if(this.widget.options.onSubFormRowChange){var t=new Function("subFormData",this.widget.options.onSubFormRowChange);t.call(this,e)}},handleSubFormRowAdd:function(e,t){if(this.widget.options.onSubFormRowAdd){var i=new Function("subFormData","newRowId",this.widget.options.onSubFormRowAdd);i.call(this,e,t)}},handleSubFormRowInsert:function(e,t){if(this.widget.options.onSubFormRowInsert){var i=new Function("subFormData","newRowId",this.widget.options.onSubFormRowInsert);i.call(this,e,t)}},handleSubFormRowDelete:function(e,t){if(this.widget.options.onSubFormRowDelete){var i=new Function("subFormData","deletedDataRow",this.widget.options.onSubFormRowDelete);i.call(this,e,t)}}}},m=p,g=(i("d459"),i("2877")),h=Object(g["a"])(m,n,o,!1,null,"7dbaba43",null);t["default"]=h.exports},effc:function(e,t,i){"use strict";i("3559")},f0c7:function(e,t,i){},f1ab:function(e,t,i){"use strict";i("4eff")},f250:function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-rate-field",use:"icon-rate-field-usage",viewBox:"0 0 1069 1024",content:'<symbol class="icon" viewBox="0 0 1069 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-rate-field"><defs><style type="text/css"></style></defs><path d="M633.73 378.02l9.498 18.688 20.78 2.798 206.616 27.332a11.465 11.465 0 0 1 6.61 19.473L729.966 593.665l-14.893 14.893 3.8 20.683 37.847 204.89a11.465 11.465 0 0 1-16.481 12.296l-185.55-94.58-18.687-9.493-18.487 9.992-183.24 99.35a11.465 11.465 0 0 1-16.784-11.867l32.543-205.796 3.297-20.786-15.192-14.492-151.033-143.484a11.465 11.465 0 0 1 6.1-19.64L399 402.998l20.786-3.296 9.092-18.98 89.713-188.078a11.465 11.465 0 0 1 20.569-0.263l94.568 185.635zM496.647 85.52L374.89 340.501l-279.126 44.26a34.395 34.395 0 0 0-18.303 58.908l204.873 194.663-44.169 279.115a34.395 34.395 0 0 0 50.366 35.616l248.4-134.679L788.776 946.66a34.395 34.395 0 0 0 49.437-36.894l-51.306-277.854 199.731-199.909a34.395 34.395 0 0 0-19.828-58.408l-280.118-37.032L558.33 84.713a34.395 34.395 0 0 0-61.682 0.802z" p-id="49627" /></symbol>'});s.a.add(a);t["default"]=a},f2db:function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-alert",use:"icon-alert-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-alert"><defs><style type="text/css"></style></defs><path d="M512 85.162667a319.573333 319.573333 0 0 1 319.829333 309.333333l0.170667 10.666667v174.805333l58.88 134.656a53.290667 53.290667 0 0 1-48.853333 74.709333L640 789.418667a128 128 0 0 1-255.786667 7.509333L384 789.290667l-201.6 0.042666a53.333333 53.333333 0 0 1-48.938667-74.581333L192 580.010667V405.162667c0-177.28 143.018667-320 320-320zM576 789.333333l-128 0.128a64 64 0 0 0 127.701333 6.144l0.256-6.272zM512 149.162667c-141.653333 0-256 114.090667-256 256v188.16L198.656 725.333333h627.072L768 593.365333V405.717333l-0.170667-9.6A255.488 255.488 0 0 0 512 149.162667z m384 202.837333h85.333333a32 32 0 0 1 4.352 63.701333L981.333333 416h-85.333333a32 32 0 0 1-4.352-63.701333L896 352z m-853.333333 0h85.333333a32 32 0 0 1 4.352 63.701333L128 416H42.666667a32 32 0 0 1-4.352-63.701333L42.666667 352z m921.6-243.2a32 32 0 0 1-2.816 41.685333l-3.584 3.114667-85.333334 64a32 32 0 0 1-41.984-48.085333l3.584-3.114667 85.333334-64a32 32 0 0 1 44.8 6.4zM104.533333 102.4l85.333334 64a32 32 0 1 1-38.4 51.2l-85.333334-64a32 32 0 1 1 38.4-51.2z" p-id="6432" /></symbol>'});s.a.add(a);t["default"]=a},f319:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.labelTooltip")}},[i("el-input",{attrs:{type:"text"},model:{value:e.optionModel.labelTooltip,callback:function(t){e.$set(e.optionModel,"labelTooltip",t)},expression:"optionModel.labelTooltip"}})],1)},o=[],l=i("79fa"),s={name:"labelTooltip-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"4bb85541",null);t["default"]=d.exports},f355:function(e,t,i){},f388:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("el-form-item",{directives:[{name:"show",rawName:"v-show",value:!!e.isSubFormChildWidget(),expression:"!!isSubFormChildWidget()"}],attrs:{label:e.i18nt("designer.setting.widgetColumnWidth")}},[i("el-input",{attrs:{type:"text"},model:{value:e.optionModel.columnWidth,callback:function(t){e.$set(e.optionModel,"columnWidth",t)},expression:"optionModel.columnWidth"}})],1)],1)},o=[],l=i("79fa"),s={name:"columnWidth-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},inject:["isSubFormChildWidget"]},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"5f410a01",null);t["default"]=d.exports},f4a6:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:"onFocus","label-width":"150px"}},[i("el-button",{attrs:{type:"info",icon:"el-icon-edit",plain:"",round:""},on:{click:function(t){return e.editEventHandler("onFocus",e.eventParams)}}},[e._v(" "+e._s(e.i18nt("designer.setting.addEventHandler")))])],1)},o=[],l=i("79fa"),s=i("7d6c"),a={name:"onFocus-editor",mixins:[l["b"],s["a"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},data:function(){return{eventParams:["event"]}}},r=a,d=i("2877"),c=Object(d["a"])(r,n,o,!1,null,"ccd01010",null);t["default"]=c.exports},f4c1:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("form-item-wrapper",{attrs:{designer:e.designer,field:e.field,rules:e.rules,"design-state":e.designState,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList,"sub-form-row-index":e.subFormRowIndex,"sub-form-col-index":e.subFormColIndex,"sub-form-row-id":e.subFormRowId}},[i("vue-editor",{ref:"fieldEditor",attrs:{"editor-toolbar":e.customToolbar,disabled:e.field.options.disabled,placeholder:e.field.options.placeholder},on:{"text-change":e.handleRichEditorChangeEvent,focus:e.handleRichEditorFocusEvent,blur:e.handleRichEditorBlurEvent},model:{value:e.fieldModel,callback:function(t){e.fieldModel=t},expression:"fieldModel"}})],1)},o=[],l=(i("a9e3"),i("9eeb")),s=i("5873"),a=i("c6e3"),r=i("79fa"),d=i("ca00"),c=i("2d11"),u={name:"rich-editor-widget",componentName:"FieldWidget",mixins:[a["a"],c["a"],r["b"]],props:{field:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object,designState:{type:Boolean,default:!1},subFormRowIndex:{type:Number,default:-1},subFormColIndex:{type:Number,default:-1},subFormRowId:{type:String,default:""}},components:{FormItemWrapper:l["default"],VueEditor:s["a"]},inject:["refList","formConfig","globalOptionData","globalModel"],data:function(){return{oldFieldValue:null,fieldModel:null,rules:[],customToolbar:[],valueChangedFlag:!1}},computed:{},beforeCreate:function(){},created:function(){this.initFieldModel(),this.registerToRefList(),this.initEventHandler(),this.buildFieldRules(),this.handleOnCreated()},mounted:function(){this.handleOnMounted()},beforeDestroy:function(){this.unregisterFromRefList()},methods:{handleRichEditorChangeEvent:function(){this.valueChangedFlag=!0,this.syncUpdateFormModel(this.fieldModel)},handleRichEditorFocusEvent:function(){this.oldFieldValue=Object(d["d"])(this.fieldModel)},handleRichEditorBlurEvent:function(){this.valueChangedFlag&&(this.emitFieldDataChange(this.fieldModel,this.oldFieldValue),this.valueChangedFlag=!1)}}},f=u,p=(i("f8e7"),i("2877")),m=Object(p["a"])(f,n,o,!1,null,"29731672",null);t["default"]=m.exports},f582:function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-checkbox-field",use:"icon-checkbox-field-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-checkbox-field"><defs><style type="text/css"></style></defs><path d="M897.940444 896.76927c6.258541-6.27696 10.256598-14.833847 10.256598-24.530696L908.197042 147.672294c0-9.118682-3.998057-18.235316-10.256598-24.533766l0 0c-6.27696-6.257517-14.815427-9.695826-24.511253-9.695826l0 0-723.784474 0 0 0c-9.68764 0-18.235316 3.437286-24.503067 9.695826l0 0c-6.26775 6.297426-9.686616 15.414061-9.686616 24.533766L115.455033 872.238574c0 9.69685 3.419889 18.253736 9.686616 24.530696 6.26775 6.277984 14.815427 10.276041 24.503067 10.276041l0 0 723.784474 0 0 0C883.126041 907.045311 891.663484 903.047254 897.940444 896.76927L897.940444 896.76927zM149.644717 61.521169l723.784474 0 0 0c23.933085 0 45.586245 9.69685 60.97984 25.110911 15.396665 15.97381 25.073048 37.665855 25.073048 61.039191L959.482079 872.238574c0 23.969924-9.676383 45.64355-25.073048 61.056588l0 0c-15.393595 15.395642-37.046754 25.092491-60.97984 25.092491l0 0-723.784474 0 0 0c-23.364127 0-45.016263-9.69685-60.971653-25.092491l0 0c-15.395642-15.414061-25.082258-37.086663-25.082258-61.056588L63.590805 147.672294c0-23.37436 9.686616-45.065382 25.082258-61.039191l0 0C104.628454 71.218018 126.28059 61.521169 149.644717 61.521169L149.644717 61.521169z" p-id="25736" /><path d="M417.41939 698.269357c-6.025227 0-12.047384-2.301416-16.667611-6.89913L259.500731 550.119179c-9.172917-9.148357-9.172917-24.093744 0-33.290197 9.169847-9.147334 24.115234-9.147334 33.288151 0l124.583436 124.606972 312.89429-312.916802c9.194406-9.171893 24.139793-9.171893 33.288151 0 9.196453 9.171893 9.196453 24.116257 0 33.289174L433.991834 691.370227c-4.618181 4.644787-10.642384 6.89913-16.665565 6.89913L417.41939 698.269357z" p-id="25737" /></symbol>'});s.a.add(a);t["default"]=a},f584:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",[i("span",{attrs:{slot:"label"},slot:"label"},[e._v(e._s(e.i18nt("designer.setting.fileTypes"))+" "),i("el-tooltip",{attrs:{effect:"light",content:e.i18nt("designer.setting.fileTypesHelp")}},[i("i",{staticClass:"el-icon-info"})])],1),i("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"","allow-create":"",filterable:"","default-first-option":""},model:{value:e.optionModel.fileTypes,callback:function(t){e.$set(e.optionModel,"fileTypes",t)},expression:"optionModel.fileTypes"}},e._l(e.uploadFileTypes,(function(e,t){return i("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1)},o=[],l=i("79fa"),s={name:"file-upload-fileTypes-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},data:function(){return{uploadFileTypes:[{value:"doc",label:"doc"},{value:"xls",label:"xls"},{value:"docx",label:"docx"},{value:"xlsx",label:"xlsx"}]}}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"68a1d928",null);t["default"]=d.exports},f6e6:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("container-wrapper",{attrs:{designer:e.designer,widget:e.widget,"parent-widget":e.parentWidget,"parent-list":e.parentList,"index-of-parent-list":e.indexOfParentList}},[i("div",{key:e.widget.id,staticClass:"table-container",class:[e.selected?"selected":"",e.customClass],on:{click:function(t){return t.stopPropagation(),e.selectWidget(e.widget)}}},[i("table",{staticClass:"table-layout"},[i("tbody",e._l(e.widget.rows,(function(t,n){return i("tr",{key:t.id},[e._l(t.cols,(function(o,l){return[o.merged?e._e():i("table-cell-widget",{key:o.id,attrs:{widget:o,designer:e.designer,"parent-list":e.widget.cols,"row-index":n,"row-length":e.widget.rows.length,"col-index":l,"col-length":t.cols.length,"col-array":t.cols,"row-array":e.widget.rows,"parent-widget":e.widget}})]}))],2)})),0)])])])},o=[],l=(i("a9e3"),i("79fa")),s=i("61ca"),a=i("cd36"),r=i("1516"),d=i("26a6"),c={name:"table-widget",componentName:"ContainerWidget",mixins:[l["b"],s["a"],d["a"]],inject:["refList"],components:{ContainerWrapper:a["default"],TableCellWidget:r["default"]},props:{widget:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object},computed:{selected:function(){return this.widget.id===this.designer.selectedId},customClass:function(){return this.widget.options.customClass||""}},watch:{},created:function(){this.initRefList()},mounted:function(){},methods:{}},u=c,f=(i("5479"),i("2877")),p=Object(f["a"])(u,n,o,!1,null,"37d3c0b7",null);t["default"]=p.exports},f6f1:function(e,t,i){},f746:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("container-item-wrapper",{attrs:{widget:e.widget}},[i("el-row",{directives:[{name:"show",rawName:"v-show",value:!e.widget.options.hidden,expression:"!widget.options.hidden"}],key:e.widget.id,ref:e.widget.id,staticClass:"grid-container",class:[e.customClass],attrs:{gutter:e.widget.options.gutter}},[e._l(e.widget.cols,(function(t,n){return[i("grid-col-item",{key:n,attrs:{widget:t,"parent-list":e.widget.cols,"index-of-parent-list":n,"parent-widget":e.widget,"col-height":e.widget.options.colHeight},scopedSlots:e._u([e._l(Object.keys(e.$scopedSlots),(function(t){return{key:t,fn:function(i){return[e._t(t,null,null,i)]}}}))],null,!0)})]}))],2)],1)},o=[],l=i("c6e3"),s=i("79fa"),a=i("d67f"),r=i("90c2"),d=i("a93f"),c=i("34f0"),u={name:"vf-grid-item",componentName:"ContainerItem",mixins:[l["a"],s["b"],a["a"],c["a"]],components:{ContainerItemWrapper:r["default"],GridColItem:d["default"]},props:{widget:Object},inject:["refList","sfRefList","globalModel"],created:function(){this.initRefList()},mounted:function(){},beforeDestroy:function(){this.unregisterFromRefList()},methods:{}},f=u,p=i("2877"),m=Object(p["a"])(f,n,o,!1,null,"7382a44f",null);t["default"]=m.exports},f7729:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return"grid-col"===e.widget.type?i("el-col",e._b({key:e.widget.id,staticClass:"grid-cell",class:[e.selected?"selected":"",e.customClass],style:e.colHeightStyle,nativeOn:{click:function(t){return t.stopPropagation(),e.selectWidget(e.widget)}}},"el-col",e.layoutProps,!1),[i("draggable",e._b({attrs:{list:e.widget.widgetList,handle:".drag-handler",move:e.checkContainerMove},on:{end:function(t){return e.onGridDragEnd(t,e.widget.widgetList)},add:function(t){return e.onGridDragAdd(t,e.widget.widgetList)},update:e.onGridDragUpdate}},"draggable",{group:"dragGroup",ghostClass:"ghost",animation:200},!1),[i("transition-group",{staticClass:"form-widget-list",attrs:{name:"fade",tag:"div"}},[e._l(e.widget.widgetList,(function(t,n){return["container"===t.category?[i(t.type+"-widget",{key:t.id,tag:"component",attrs:{widget:t,designer:e.designer,"parent-list":e.widget.widgetList,"index-of-parent-list":n,"parent-widget":e.widget}})]:[i(t.type+"-widget",{key:t.id,tag:"component",attrs:{field:t,designer:e.designer,"parent-list":e.widget.widgetList,"index-of-parent-list":n,"parent-widget":e.widget,"design-state":!0}})]]}))],2)],1),e.designer.selectedId===e.widget.id&&"grid-col"===e.widget.type?i("div",{staticClass:"grid-col-action"},[i("i",{staticClass:"el-icon-back",attrs:{title:e.i18nt("designer.hint.selectParentWidget")},on:{click:function(t){return t.stopPropagation(),e.selectParentWidget(e.widget)}}}),e.parentList&&e.parentList.length>1?i("i",{staticClass:"el-icon-top",attrs:{title:e.i18nt("designer.hint.moveUpWidget")},on:{click:function(t){return t.stopPropagation(),e.moveUpWidget()}}}):e._e(),e.parentList&&e.parentList.length>1?i("i",{staticClass:"el-icon-bottom",attrs:{title:e.i18nt("designer.hint.moveDownWidget")},on:{click:function(t){return t.stopPropagation(),e.moveDownWidget()}}}):e._e(),i("i",{staticClass:"el-icon-copy-document",attrs:{title:e.i18nt("designer.hint.cloneWidget")},on:{click:function(t){return t.stopPropagation(),e.cloneGridCol(e.widget)}}}),i("i",{staticClass:"el-icon-delete",attrs:{title:e.i18nt("designer.hint.remove")},on:{click:function(t){return t.stopPropagation(),e.removeWidget.apply(null,arguments)}}})]):e._e(),e.designer.selectedId===e.widget.id&&"grid-col"===e.widget.type?i("div",{staticClass:"grid-col-handler"},[i("i",[e._v(e._s(e.i18nt("designer.widgetLabel."+e.widget.type)))])]):e._e()],1):e._e()},o=[],l=i("5530"),s=(i("a9e3"),i("a434"),i("b76a")),a=i.n(s),r=i("79fa"),d=i("c029"),c=i("26a6"),u={name:"GridColWidget",componentName:"GridColWidget",mixins:[r["b"],c["a"]],inject:["refList"],components:Object(l["a"])({Draggable:a.a},d["a"]),props:{widget:Object,parentWidget:Object,parentList:Array,indexOfParentList:Number,designer:Object,colHeight:{type:String,default:null}},data:function(){return{layoutProps:{span:this.widget.options.span||12,offset:this.widget.options.offset||0,push:this.widget.options.push||0,pull:this.widget.options.pull||0}}},computed:{selected:function(){return this.widget.id===this.designer.selectedId},customClass:function(){return this.widget.options.customClass||""},colHeightStyle:function(){return this.colHeight?{height:this.colHeight+"px"}:{}}},watch:{"designer.formConfig.layoutType":{handler:function(e){this.widget.options.responsive?this.layoutProps.span="H5"===e?this.widget.options.xs||12:"Pad"===e?this.widget.options.sm||12:this.widget.options.md||12:this.layoutProps.span=this.widget.options.span||12}},"widget.options.responsive":{handler:function(e){var t=this.designer.formConfig.layoutType;this.layoutProps.span=e?"H5"===t?this.widget.options.xs||12:"Pad"===t?this.widget.options.sm||12:this.widget.options.md||12:this.widget.options.span||12}},"widget.options.span":{handler:function(e){this.layoutProps.span=e}},"widget.options.md":{handler:function(e){this.layoutProps.span=e}},"widget.options.sm":{handler:function(e){this.layoutProps.span=e}},"widget.options.xs":{handler:function(e){this.layoutProps.span=e}},"widget.options.offset":{handler:function(e){this.layoutProps.offset=e}},"widget.options.push":{handler:function(e){this.layoutProps.push=e}},"widget.options.pull":{handler:function(e){this.layoutProps.pull=e}}},created:function(){this.initRefList(),this.initLayoutProps()},methods:{initLayoutProps:function(){if(this.widget.options.responsive){var e=this.designer.formConfig.layoutType;this.layoutProps.span="H5"===e?this.widget.options.xs||12:"Pad"===e?this.widget.options.sm||12:this.widget.options.md||12}else this.layoutProps.span=this.widget.options.span},onGridDragEnd:function(e,t){},onGridDragAdd:function(e,t){var i=e.newIndex;t[i]&&this.designer.setSelected(t[i]),this.designer.emitHistoryChange(),this.designer.emitEvent("field-selected",this.widget)},onGridDragUpdate:function(){this.designer.emitHistoryChange()},selectWidget:function(e){console.log("id: "+e.id),this.designer.setSelected(e)},checkContainerMove:function(e){return this.designer.checkWidgetMove(e)},selectParentWidget:function(){this.parentWidget?this.designer.setSelected(this.parentWidget):this.designer.clearSelected()},moveUpWidget:function(){this.designer.moveUpWidget(this.parentList,this.indexOfParentList)},moveDownWidget:function(){this.designer.moveDownWidget(this.parentList,this.indexOfParentList)},cloneGridCol:function(e){this.designer.cloneGridCol(e,this.parentWidget)},removeWidget:function(){var e=this;if(this.parentList){var t=null;1===this.parentList.length?this.parentWidget&&(t=this.parentWidget):t=this.parentList.length===1+this.indexOfParentList?this.parentList[this.indexOfParentList-1]:this.parentList[this.indexOfParentList+1],this.$nextTick((function(){e.parentList.splice(e.indexOfParentList,1),e.designer.setSelected(t),e.designer.emitHistoryChange()}))}}}},f=u,p=(i("1415"),i("2877")),m=Object(p["a"])(f,n,o,!1,null,"3151e59a",null);t["default"]=m.exports},f8e7:function(e,t,i){"use strict";i("e632")},fa85:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.colPushTitle")}},[i("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0,max:24},model:{value:e.optionModel.push,callback:function(t){e.$set(e.optionModel,"push",e._n(t))},expression:"optionModel.push"}})],1)},o=[],l=i("79fa"),s={name:"grid-col-push-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"570c4841",null);t["default"]=d.exports},fb56:function(e,t,i){"use strict";i("ff5e")},fbe0:function(e,t,i){},fc8b:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.lowThreshold")}},[i("el-input-number",{staticClass:"hide-spin-button",staticStyle:{width:"100%"},attrs:{min:1,max:e.optionModel.highThreshold},model:{value:e.optionModel.lowThreshold,callback:function(t){e.$set(e.optionModel,"lowThreshold",t)},expression:"optionModel.lowThreshold"}})],1)},o=[],l=i("79fa"),s={name:"lowThreshold-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"d66e35d0",null);t["default"]=d.exports},fcbf:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.cellHeight")}},[i("el-input",{attrs:{type:"text"},model:{value:e.optionModel.cellHeight,callback:function(t){e.$set(e.optionModel,"cellHeight",t)},expression:"optionModel.cellHeight"}})],1)},o=[],l=i("79fa"),s={name:"cellHeight-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"52af6c29",null);t["default"]=d.exports},fd28:function(e,t,i){"use strict";i.r(t);var n=i("e017"),o=i.n(n),l=i("21a1"),s=i.n(l),a=new o.a({id:"icon-document",use:"icon-document-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-document"><defs><style type="text/css"></style></defs><path d="M979.478261 706.381913a44.521739 44.521739 0 0 1-11.842783 57.967304l-4.36313 2.849392-393.794783 227.862261-5.38713 4.763826a84.012522 84.012522 0 0 1-43.942957 17.808695l-8.102956 0.400696c-19.055304 0-37.309217-6.544696-52.045913-18.253913l-5.431652-4.719304-393.750261-227.862261a44.521739 44.521739 0 0 1-18.610087-56.186435l2.359652-4.630261a44.521739 44.521739 0 0 1 60.816696-16.250435l405.325913 234.540522 1.335652 1.513739 1.335652-1.513739 405.281391-234.540522a44.521739 44.521739 0 0 1 60.861218 16.250435z m0-222.608696a44.521739 44.521739 0 0 1-11.842783 57.967305l-4.36313 2.849391-393.794783 227.862261-5.38713 4.763826a84.012522 84.012522 0 0 1-43.942957 17.808696l-8.102956 0.400695c-19.055304 0-37.309217-6.544696-52.045913-18.253913l-5.431652-4.719304-393.750261-227.862261a44.521739 44.521739 0 0 1-18.610087-56.186435l2.359652-4.630261a44.521739 44.521739 0 0 1 60.816696-16.250434l405.325913 234.540521 1.335652 1.513739 1.335652-1.513739 405.281391-234.540521a44.521739 44.521739 0 0 1 60.861218 16.250434zM512 0c18.788174 0 36.864 6.099478 51.645217 17.185391l4.58574 3.739826 403.500521 199.68 5.609739 6.144c32.50087 35.439304 32.50087 89.889391 0 125.328696l-5.609739 6.144-403.500521 199.590957-4.541218 3.784347a86.238609 86.238609 0 0 1-43.675826 16.829218L512 578.782609c-18.788174 0-36.864-6.099478-51.645217-17.185392l-4.630261-3.784347L52.268522 358.221913l-5.609739-6.144a92.738783 92.738783 0 0 1 0-125.328696l5.609739-6.144L455.724522 20.925217l4.585739-3.739826c12.688696-9.48313 27.826087-15.315478 43.675826-16.829217z" fill="#008df0" p-id="5455" /></symbol>'});s.a.add(a);t["default"]=a},fe05:function(e,t,i){"use strict";i("2dd9")},fe5d:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.allowCreate")}},[i("el-switch",{model:{value:e.optionModel.allowCreate,callback:function(t){e.$set(e.optionModel,"allowCreate",t)},expression:"optionModel.allowCreate"}})],1)},o=[],l=i("79fa"),s={name:"allowCreate-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"7ea31b71",null);t["default"]=d.exports},feea:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.displayType")}},[i("el-select",{model:{value:e.optionModel.type,callback:function(t){e.$set(e.optionModel,"type",t)},expression:"optionModel.type"}},[i("el-option",{attrs:{label:"daterange",value:"daterange"}}),i("el-option",{attrs:{label:"datetimerange",value:"datetimerange"}}),i("el-option",{attrs:{label:"monthrange",value:"monthrange"}})],1)],1)},o=[],l=i("79fa"),s={name:"date-range-type-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"574545a5",null);t["default"]=d.exports},ff09:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.maxValue")}},[i("el-input-number",{staticClass:"hide-spin-button",staticStyle:{width:"100%"},model:{value:e.maxValue,callback:function(t){e.maxValue=t},expression:"maxValue"}})],1)},o=[],l=(i("a9e3"),i("79fa")),s={name:"max-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},computed:{maxValue:{get:function(){return this.optionModel["max"]},set:function(e){!e||isNaN(e)?this.optionModel.max=null:this.optionModel.max=Number(e)}}}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"edcd3882",null);t["default"]=d.exports},ff5e:function(e,t,i){},ff66:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:e.i18nt("designer.setting.precision")}},[i("el-input-number",{staticClass:"hide-spin-button",staticStyle:{width:"100%"},attrs:{min:0},model:{value:e.optionModel.precision,callback:function(t){e.$set(e.optionModel,"precision",t)},expression:"optionModel.precision"}})],1)},o=[],l=i("79fa"),s={name:"precision-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"6d29eaf2",null);t["default"]=d.exports},ffcf:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[e.optionModel.responsive?e._e():i("el-form-item",{attrs:{label:e.i18nt("designer.setting.colSpanTitle")}},[i("el-input-number",{staticStyle:{width:"100%"},attrs:{min:1,max:24},model:{value:e.optionModel.span,callback:function(t){e.$set(e.optionModel,"span",e._n(t))},expression:"optionModel.span"}})],1),e.optionModel.responsive&&"PC"===e.formConfig.layoutType?i("el-form-item",{attrs:{label:e.i18nt("designer.setting.colSpanTitle")+"(PC)"}},[i("el-input-number",{staticStyle:{width:"100%"},attrs:{min:1,max:24},model:{value:e.optionModel.md,callback:function(t){e.$set(e.optionModel,"md",e._n(t))},expression:"optionModel.md"}})],1):e._e(),e.optionModel.responsive&&"Pad"===e.formConfig.layoutType?i("el-form-item",{attrs:{label:e.i18nt("designer.setting.colSpanTitle")+"(Pad)"}},[i("el-input-number",{staticStyle:{width:"100%"},attrs:{min:1,max:24},model:{value:e.optionModel.sm,callback:function(t){e.$set(e.optionModel,"sm",e._n(t))},expression:"optionModel.sm"}})],1):e._e(),e.optionModel.responsive&&"H5"===e.formConfig.layoutType?i("el-form-item",{attrs:{label:e.i18nt("designer.setting.colSpanTitle")+"(H5)"}},[i("el-input-number",{staticStyle:{width:"100%"},attrs:{min:1,max:24},model:{value:e.optionModel.xs,callback:function(t){e.$set(e.optionModel,"xs",e._n(t))},expression:"optionModel.xs"}})],1):e._e()],1)},o=[],l=i("79fa"),s={name:"grid-col-span-editor",mixins:[l["b"]],props:{designer:Object,selectedWidget:Object,optionModel:Object},computed:{formConfig:function(){return this.designer.formConfig}}},a=s,r=i("2877"),d=Object(r["a"])(a,n,o,!1,null,"163e0823",null);t["default"]=d.exports}});