var X=(g,_,x)=>new Promise((E,f)=>{var h=u=>{try{p(x.next(u))}catch(v){f(v)}},n=u=>{try{p(x.throw(u))}catch(v){f(v)}},p=u=>u.done?E(u.value):Promise.resolve(u.value).then(h,n);p((x=x.apply(g,_)).next())});import{d as ae,Z as Q,l as S,a0 as G,a8 as ie,cT as B,L as oe,r as o,o as b,c as L,u as s,E as R,w as e,e as t,f as l,q as c,F as J,a7 as Z,A as de,B as _e,j as ne,a9 as fe,cN as pe,aE as me,cL as ve,aD as ge,aG as he,aX as ye,Q as K,s as W,t as z,R as Y,bd as we,a5 as be}from"./index-bb2cbf17.js";import{c as ee}from"./chartEditStore-55fbe93c.js";import{i as $}from"./icon-f36697ff.js";import{T as M,D as A}from"./index-819682d7.js";import{u as le}from"./useTargetData.hook-a16b3b4d.js";import{M as xe}from"./EditorWorker-54a88558.js";import"./editorWorker-43a98755.js";import{g as Ce}from"./plugin-3ef0fcec.js";import{c as Se}from"./http-36f53bd1.js";import{F as te}from"./fileTypeEnum-21359a08.js";const P=g=>(de("data-v-394ad52c"),g=g(),_e(),g),Te=P(()=>c("p",null,[c("span",{class:"func-keyword"},"function"),l("  filter(data, res)  {")],-1)),ke={class:"go-ml-4"},Fe=P(()=>c("p",null,"}",-1)),Ee=P(()=>c("span",{class:"func-keyword"},"function",-1)),Re={class:"editor-data-show"},qe={class:"editor-data-show"},je={class:"editor-data-show"},Ie={class:"go-flex-items-center"},Ne=ae({__name:"index",setup(g){const{DocumentTextIcon:_}=$.ionicons5,{FilterIcon:x,FilterEditIcon:E}=$.carbon,{targetData:f,chartEditStore:h}=le();Q(f.value.request),Q(h.getRequestGlobalConfig);const n=S(!1),p=S(f.value.filter||"return data"),u=S(!1),v=S(""),q=()=>X(this,null,function*(){try{const m=yield Se(Z(f.value.request),Z(h.getRequestGlobalConfig));if(m){v.value=m;return}window.$message.warning("没有拿到返回值，请检查接口！")}catch(m){console.error(m),window.$message.warning("数据异常，请检查参数！")}}),O=G(()=>{try{const m=new Function("data","res",p.value),y=ie(v.value),a=m(y==null?void 0:y.data,y);return u.value=!1,B(a)}catch(m){return u.value=!0,`过滤函数错误，日志：${m}`}}),I=()=>{n.value=!0},V=()=>{Ce({message:"是否删除过滤器",onPositiveCallback:()=>{f.value.filter=void 0}})},N=()=>{n.value=!1},D=()=>{if(u.value){window.$message.error("过滤函数错误，无法进行保存");return}f.value.filter=p.value,N()};return oe(()=>n.value,m=>{m&&(q(),p.value=f.value.filter||"return data")}),(m,y)=>{const a=o("n-code"),r=o("n-icon"),d=o("n-button"),i=o("n-space"),C=o("n-card"),k=o("n-text"),F=o("n-tag"),j=o("n-divider"),U=o("n-scrollbar"),H=o("n-modal");return b(),L(J,null,[s(f).filter?(b(),R(C,{key:0},{footer:e(()=>[t(i,{justify:"end"},{default:e(()=>[t(d,{type:"primary",tertiary:"",size:"small",onClick:I},{icon:e(()=>[t(r,null,{default:e(()=>[t(s(E))]),_:1})]),default:e(()=>[l(" 编辑 ")]),_:1}),t(d,{tertiary:"",size:"small",onClick:V},{default:e(()=>[l(" 删除 ")]),_:1})]),_:1})]),default:e(()=>[Te,c("div",ke,[t(a,{code:s(f).filter,language:"typescript"},null,8,["code"])]),Fe]),_:1})):(b(),R(d,{key:1,class:"go-ml-3",onClick:I},{icon:e(()=>[t(r,null,{default:e(()=>[t(s(x))]),_:1})]),default:e(()=>[l(" 新增过滤器 ")]),_:1})),t(H,{class:"go-chart-data-monaco-editor",show:n.value,"onUpdate:show":y[1]||(y[1]=T=>n.value=T),"mask-closable":!1,closeOnEsc:!1},{default:e(()=>[t(C,{bordered:!1,role:"dialog",size:"small","aria-modal":"true",style:{width:"1200px",height:"700px"}},{header:e(()=>[t(i,null,{default:e(()=>[t(k,null,{default:e(()=>[l("过滤器函数编辑器")]),_:1})]),_:1})]),"header-extra":e(()=>[]),action:e(()=>[t(i,{justify:"space-between"},{default:e(()=>[c("div",Ie,[t(F,{bordered:!1,type:"primary"},{icon:e(()=>[t(r,{component:s(_)},null,8,["component"])]),default:e(()=>[l(" 规则 ")]),_:1}),t(k,{class:"go-ml-2",depth:"2"},{default:e(()=>[l("过滤器默认处理接口返回值的「data」字段")]),_:1})]),t(i,null,{default:e(()=>[t(d,{size:"medium",onClick:N},{default:e(()=>[l("取消")]),_:1}),t(d,{size:"medium",type:"primary",onClick:D},{default:e(()=>[l("保存")]),_:1})]),_:1})]),_:1})]),default:e(()=>[t(i,{size:"small",vertical:""},{default:e(()=>[t(i,{justify:"space-between"},{default:e(()=>[c("div",null,[t(i,{vertical:""},{default:e(()=>[t(F,{type:"info"},{default:e(()=>[Ee,l("  filter(data, res)  { ")]),_:1}),t(s(xe),{modelValue:p.value,"onUpdate:modelValue":y[0]||(y[0]=T=>p.value=T),width:"660px",height:"500px",language:"javascript"},null,8,["modelValue"]),t(F,{type:"info"},{default:e(()=>[l("}")]),_:1})]),_:1})]),t(j,{vertical:"",style:{height:"580px"}}),t(U,{style:{"max-height":"580px"}},{default:e(()=>[t(i,{size:15,vertical:""},{default:e(()=>[c("div",Re,[t(i,null,{default:e(()=>{var T;return[t(k,{depth:"3"},{default:e(()=>[l("默认过滤数据(data)：")]),_:1}),t(a,{code:s(B)((T=v.value)==null?void 0:T.data)||"暂无",language:"json","word-wrap":!0},null,8,["code"])]}),_:1})]),c("div",qe,[t(i,null,{default:e(()=>[t(k,{depth:"3"},{default:e(()=>[l("接口返回数据(res)：")]),_:1}),t(a,{code:s(B)(v.value)||"暂无",language:"json","word-wrap":!0},null,8,["code"])]),_:1})]),c("div",je,[t(i,null,{default:e(()=>[t(k,{depth:"3"},{default:e(()=>[l("过滤器结果：")]),_:1}),t(a,{code:O.value||"暂无",language:"json","word-wrap":!0},null,8,["code"])]),_:1})])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["show"])],64)}}});const De=ne(Ne,[["__scopeId","data-v-394ad52c"]]),Ue=g=>{const _=S();return{uploadFileListRef:_,beforeUpload:({file:h})=>{_.value=[];const n=h.file.type;return n!==te.JSON&&n!==te.TXT?(window.$message.warning("仅支持上传 【JSON】 格式文件，请重新上传！"),!1):!0},customRequest:h=>{const{file:n}=h;fe(()=>{n.file?pe(n.file).then(p=>{g.value.option.dataset=me(p)}):window.$message.error("导入数据失败，请稍后重试或联系管理员！")})},download:()=>{try{window.$message.success("下载中，请耐心等待..."),ve(ge(g.value.option.dataset),void 0,"json")}catch(h){window.$message.error("下载失败，数据错误！")}}}},ze=ae({__name:"index",props:{show:{type:Boolean,required:!1},ajax:{type:Boolean,required:!0}},setup(g){const{targetData:_}=le(),x=["字段","映射","状态"],{HelpOutlineIcon:E}=$.ionicons5,{DocumentAddIcon:f,DocumentDownloadIcon:h}=$.carbon,n=S(),p=S(),u=S(),v=S(!1),{uploadFileListRef:q,customRequest:O,beforeUpload:I,download:V}=Ue(_),N=G(()=>_.value.request.requestDataType!==he.STATIC),D=G(()=>_.value.chartConfig.chartFrame===ee.ECHARTS),m=a=>{let r=A.SUCCESS;for(let d=0;d<n.value.length;d++)if(n.value[d][a]===void 0)return r=A.FAILURE,r;return A.SUCCESS},y=()=>{try{return p.value.map((a,r)=>r===0?{field:"通用标识",mapping:a,result:A.NULL}:{field:`数据项-${r}`,mapping:a,result:m(a)})}catch(a){return[]}};return oe(()=>{var a,r;return(r=(a=_.value)==null?void 0:a.option)==null?void 0:r.dataset},a=>{var r,d;a&&((d=(r=_==null?void 0:_.value)==null?void 0:r.chartConfig)==null?void 0:d.chartFrame)===ee.ECHARTS?(n.value=a,D.value&&(p.value=a.dimensions,u.value=y())):a!=null?(u.value=null,n.value=a):(v.value=!0,n.value="此组件无数据源"),ye(a)&&(u.value=null)},{immediate:!0}),(a,r)=>{const d=o("n-badge"),i=o("n-text"),C=o("n-space"),k=o("n-table"),F=o("n-timeline-item"),j=o("n-icon"),U=o("n-button"),H=o("n-upload"),T=o("n-tooltip"),se=o("n-code"),re=o("n-card"),ce=o("n-timeline");return b(),R(ce,{class:"go-chart-configurations-timeline"},{default:e(()=>[K(t(F,{type:"info",title:s(M).MAPPING},{default:e(()=>[t(k,{striped:""},{default:e(()=>[c("thead",null,[c("tr",null,[(b(),L(J,null,W(x,w=>c("th",{key:w},z(w),1)),64))])]),c("tbody",null,[(b(!0),L(J,null,W(u.value,(w,ue)=>(b(),L("tr",{key:ue},[c("td",null,z(w.field),1),c("td",null,z(w.mapping),1),c("td",null,[w.result===0?(b(),R(C,{key:0},{default:e(()=>[t(d,{dot:"",type:"success"}),t(i,null,{default:e(()=>[l("无")]),_:1})]),_:1})):(b(),R(C,{key:1},{default:e(()=>[t(d,{dot:"",type:w.result===1?"success":"error"},null,8,["type"]),t(i,null,{default:e(()=>[l("匹配"+z(w.result===1?"成功":"失败"),1)]),_:2},1024)]),_:2},1024))])]))),128))])]),_:1})]),_:1},8,["title"]),[[Y,D.value&&u.value]]),K(t(F,{color:"#97846c",title:s(M).FILTER},{default:e(()=>[t(C,{size:18,vertical:""},{default:e(()=>[t(i,{depth:"3"},{default:e(()=>[l("过滤器默认处理接口返回值的「data」字段")]),_:1}),t(s(De))]),_:1})]),_:1},8,["title"]),[[Y,N.value]]),t(F,{type:"success",title:s(M).CONTENT},{default:e(()=>[t(C,{vertical:""},{default:e(()=>[t(C,{class:"source-btn-box"},{default:e(()=>[t(H,{"file-list":s(q),"onUpdate:fileList":r[0]||(r[0]=w=>we(q)?q.value=w:null),"show-file-list":!1,customRequest:s(O),onBeforeUpload:s(I)},{default:e(()=>[t(C,null,{default:e(()=>[g.ajax?be("",!0):(b(),R(U,{key:0,class:"sourceBtn-item",disabled:v.value},{icon:e(()=>[t(j,null,{default:e(()=>[t(s(f))]),_:1})]),default:e(()=>[l(" 导入（json / txt） ")]),_:1},8,["disabled"]))]),_:1})]),_:1},8,["file-list","customRequest","onBeforeUpload"]),c("div",null,[t(U,{class:"sourceBtn-item",disabled:v.value,onClick:s(V)},{icon:e(()=>[t(j,null,{default:e(()=>[t(s(h))]),_:1})]),default:e(()=>[l(" 下载 ")]),_:1},8,["disabled","onClick"]),t(T,{trigger:"hover"},{trigger:e(()=>[t(j,{class:"go-ml-1",size:"21",depth:3},{default:e(()=>[t(s(E))]),_:1})]),default:e(()=>[t(i,{depth:"3"},{default:e(()=>[l("点击【下载】查看完整数据")]),_:1})]),_:1})])]),_:1}),t(re,{size:"small"},{default:e(()=>[t(se,{code:s(B)(n.value),language:"json"},null,8,["code"])]),_:1})]),_:1})]),_:1},8,["title"])]),_:1})}}});const Xe=ne(ze,[["__scopeId","data-v-f6d90859"]]);export{Xe as C};
