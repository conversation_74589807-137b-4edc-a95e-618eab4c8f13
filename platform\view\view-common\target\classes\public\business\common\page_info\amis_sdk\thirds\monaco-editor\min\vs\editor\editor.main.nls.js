/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.30.1(829382514cb1065f5ebb90f436e1c6103e153953)
 * Released under the MIT license
 * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
 *-----------------------------------------------------------*/define("vs/editor/editor.main.nls",{"vs/base/browser/ui/actionbar/actionViewItems":["{0} ({1})"],"vs/base/browser/ui/findinput/findInput":["input"],"vs/base/browser/ui/findinput/findInputCheckboxes":["Match Case","Match Whole Word","Use Regular Expression"],"vs/base/browser/ui/findinput/replaceInput":["input","Preserve Case"],"vs/base/browser/ui/iconLabel/iconLabelHover":["Loading..."],"vs/base/browser/ui/inputbox/inputBox":["Error: {0}","Warning: {0}","Info: {0}","for history"],"vs/base/browser/ui/keybindingLabel/keybindingLabel":["Unbound"],"vs/base/browser/ui/menu/menu":["{0} ({1})"],"vs/base/browser/ui/tree/abstractTree":["Clear","Disable Filter on Type","Enable Filter on Type","No elements found","Matched {0} out of {1} elements"],"vs/base/common/actions":["(empty)"],"vs/base/common/errorMessage":["{0}: {1}","A system error occurred ({0})","An unknown error occurred. Please consult the log for more details.","An unknown error occurred. Please consult the log for more details.","{0} ({1} errors in total)","An unknown error occurred. Please consult the log for more details."],"vs/base/common/keybindingLabels":["Ctrl","Shift","Alt","Windows","Ctrl","Shift","Alt","Super","Control","Shift","Alt","Command","Control","Shift","Alt","Windows","Control","Shift","Alt","Super"],"vs/base/parts/quickinput/browser/quickInput":["Back","Press 'Enter' to confirm your input or 'Escape' to cancel","{0}/{1}","Type to narrow down results.","{0} Results","{0} Selected","OK","Custom","Back ({0})","Back"],"vs/base/parts/quickinput/browser/quickInputList":["Quick Input"],"vs/editor/browser/controller/coreCommands":["Stick to the end even when going to longer lines","Stick to the end even when going to longer lines","Removed secondary cursors"],"vs/editor/browser/controller/textAreaHandler":["editor","The editor is not accessible at this time. Press {0} for options."],"vs/editor/browser/core/keybindingCancellation":["Whether the editor runs a cancellable operation, e.g. like 'Peek References'"],"vs/editor/browser/editorExtensions":["&&Undo","Undo","&&Redo","Redo","&&Select All","Select All"],"vs/editor/browser/widget/codeEditorWidget":["The number of cursors has been limited to {0}."],"vs/editor/browser/widget/diffEditorWidget":["Line decoration for inserts in the diff editor.","Line decoration for removals in the diff editor.","Cannot compare files because one file is too large."],"vs/editor/browser/widget/diffReview":["Icon for 'Insert' in diff review.","Icon for 'Remove' in diff review.","Icon for 'Close' in diff review.","Close","no lines changed","1 line changed","{0} lines changed","Difference {0} of {1}: original line {2}, {3}, modified line {4}, {5}","blank","{0} unchanged line {1}","{0} original line {1} modified line {2}","+ {0} modified line {1}","- {0} original line {1}","Go to Next Difference","Go to Previous Difference"],"vs/editor/browser/widget/inlineDiffMargin":["Copy deleted lines","Copy deleted line","Copy changed lines","Copy changed line","Copy deleted line ({0})","Copy changed line ({0})","Revert this change","Copy deleted line ({0})","Copy changed line ({0})"],"vs/editor/common/config/commonEditorConfig":["Editor","The number of spaces a tab is equal to. This setting is overridden based on the file contents when `#editor.detectIndentation#` is on.","Insert spaces when pressing `Tab`. This setting is overridden based on the file contents when `#editor.detectIndentation#` is on.","Controls whether `#editor.tabSize#` and `#editor.insertSpaces#` will be automatically detected when a file is opened based on the file contents.","Remove trailing auto inserted whitespace.","Special handling for large files to disable certain memory intensive features.","Controls whether completions should be computed based on words in the document.","Only suggest words from the active document.","Suggest words from all open documents of the same language.","Suggest words from all open documents.","Controls from which documents word based completions are computed.","Semantic highlighting enabled for all color themes.","Semantic highlighting disabled for all color themes.","Semantic highlighting is configured by the current color theme's `semanticHighlighting` setting.","Controls whether the semanticHighlighting is shown for the languages that support it.","Keep peek editors open even when double clicking their content or when hitting `Escape`.","Lines above this length will not be tokenized for performance reasons","Defines the bracket symbols that increase or decrease the indentation.","The opening bracket character or string sequence.","The closing bracket character or string sequence.","Defines the bracket pairs that are colorized by their nesting level if bracket pair colorization is enabled.","The opening bracket character or string sequence.","The closing bracket character or string sequence.","Timeout in milliseconds after which diff computation is cancelled. Use 0 for no timeout.","Maximum file size in MB for which to compute diffs. Use 0 for no limit.","Controls whether the diff editor shows the diff side by side or inline.","When enabled, the diff editor ignores changes in leading or trailing whitespace.","Controls whether the diff editor shows +/- indicators for added/removed changes.","Controls whether the editor shows CodeLens.","Lines will never wrap.","Lines will wrap at the viewport width.","Lines will wrap according to the `#editor.wordWrap#` setting."],"vs/editor/common/config/editorOptions":["The editor will use platform APIs to detect when a Screen Reader is attached.","The editor will be permanently optimized for usage with a Screen Reader. Word wrapping will be disabled.","The editor will never be optimized for usage with a Screen Reader.","Controls whether the editor should run in a mode where it is optimized for screen readers. Setting to on will disable word wrapping.","Controls whether a space character is inserted when commenting.","Controls if empty lines should be ignored with toggle, add or remove actions for line comments.","Controls whether copying without a selection copies the current line.","Controls whether the cursor should jump to find matches while typing.","Never seed search string from the editor selection.","Always seed search string from the editor selection, including word at cursor position.","Only seed search string from the editor selection.","Controls whether the search string in the Find Widget is seeded from the editor selection.","Never turn on Find in Selection automatically (default).","Always turn on Find in Selection automatically.","Turn on Find in Selection automatically when multiple lines of content are selected.","Controls the condition for turning on Find in Selection automatically.","Controls whether the Find Widget should read or modify the shared find clipboard on macOS.","Controls whether the Find Widget should add extra lines on top of the editor. When true, you can scroll beyond the first line when the Find Widget is visible.","Controls whether the search automatically restarts from the beginning (or the end) when no further matches can be found.","Enables/Disables font ligatures ('calt' and 'liga' font features). Change this to a string for fine-grained control of the 'font-feature-settings' CSS property.","Explicit 'font-feature-settings' CSS property. A boolean can be passed instead if one only needs to turn on/off ligatures.","Configures font ligatures or font features. Can be either a boolean to enable/disable ligatures or a string for the value of the CSS 'font-feature-settings' property.","Controls the font size in pixels.",'Only "normal" and "bold" keywords or numbers between 1 and 1000 are allowed.','Controls the font weight. Accepts "normal" and "bold" keywords or numbers between 1 and 1000.',"Show peek view of the results (default)","Go to the primary result and show a peek view","Go to the primary result and enable peek-less navigation to others","This setting is deprecated, please use separate settings like 'editor.editor.gotoLocation.multipleDefinitions' or 'editor.editor.gotoLocation.multipleImplementations' instead.","Controls the behavior the 'Go to Definition'-command when multiple target locations exist.","Controls the behavior the 'Go to Type Definition'-command when multiple target locations exist.","Controls the behavior the 'Go to Declaration'-command when multiple target locations exist.","Controls the behavior the 'Go to Implementations'-command when multiple target locations exist.","Controls the behavior the 'Go to References'-command when multiple target locations exist.","Alternative command id that is being executed when the result of 'Go to Definition' is the current location.","Alternative command id that is being executed when the result of 'Go to Type Definition' is the current location.","Alternative command id that is being executed when the result of 'Go to Declaration' is the current location.","Alternative command id that is being executed when the result of 'Go to Implementation' is the current location.","Alternative command id that is being executed when the result of 'Go to Reference' is the current location.","Controls whether the hover is shown.","Controls the delay in milliseconds after which the hover is shown.","Controls whether the hover should remain visible when mouse is moved over it.","Prefer showing hovers above the line, if there's space.","Enables the code action lightbulb in the editor.","Enables the inlay hints in the editor.","Controls font size of inlay hints in the editor. A default of 90% of `#editor.fontSize#` is used when the configured value is less than `5` or greater than the editor font size.","Controls font family of inlay hints in the editor. When set to empty, the `#editor.fontFamily#` is used.",`Controls the line height. 
 - Use 0 to automatically compute the line height from the font size.
 - Values between 0 and 8 will be used as a multiplier with the font size.
 - Values greater than or equal to 8 will be used as effective values.`,"Controls whether the minimap is shown.","The minimap has the same size as the editor contents (and might scroll).","The minimap will stretch or shrink as necessary to fill the height of the editor (no scrolling).","The minimap will shrink as necessary to never be larger than the editor (no scrolling).","Controls the size of the minimap.","Controls the side where to render the minimap.","Controls when the minimap slider is shown.","Scale of content drawn in the minimap: 1, 2 or 3.","Render the actual characters on a line as opposed to color blocks.","Limit the width of the minimap to render at most a certain number of columns.","Controls the amount of space between the top edge of the editor and the first line.","Controls the amount of space between the bottom edge of the editor and the last line.","Enables a pop-up that shows parameter documentation and type information as you type.","Controls whether the parameter hints menu cycles or closes when reaching the end of the list.","Enable quick suggestions inside strings.","Enable quick suggestions inside comments.","Enable quick suggestions outside of strings and comments.","Controls whether suggestions should automatically show up while typing.","Line numbers are not rendered.","Line numbers are rendered as absolute number.","Line numbers are rendered as distance in lines to cursor position.","Line numbers are rendered every 10 lines.","Controls the display of line numbers.","Number of monospace characters at which this editor ruler will render.","Color of this editor ruler.","Render vertical rulers after a certain number of monospace characters. Use multiple values for multiple rulers. No rulers are drawn if array is empty.","The vertical scrollbar will be visible only when necessary.","The vertical scrollbar will always be visible.","The vertical scrollbar will always be hidden.","Controls the visibility of the vertical scrollbar.","The horizontal scrollbar will be visible only when necessary.","The horizontal scrollbar will always be visible.","The horizontal scrollbar will always be hidden.","Controls the visibility of the horizontal scrollbar.","The width of the vertical scrollbar.","The height of the horizontal scrollbar.","Controls whether clicks scroll by page or jump to click position.","Controls whether to automatically show inline suggestions in the editor.","Controls whether bracket pair colorization is enabled or not. Use 'workbench.colorCustomizations' to override the bracket highlight colors.","Enables bracket pair guides.","Enables bracket pair guides only for the active bracket pair.","Disables bracket pair guides.","Controls whether bracket pair guides are enabled or not.","Enables horizontal guides as addition to vertical bracket pair guides.","Enables horizontal guides only for the active bracket pair.","Disables horizontal bracket pair guides.","Controls whether horizontal bracket pair guides are enabled or not.","Controls whether bracket pair guides are enabled or not.","Controls whether the editor should render indent guides.","Controls whether the editor should highlight the active indent guide.","Insert suggestion without overwriting text right of the cursor.","Insert suggestion and overwrite text right of the cursor.","Controls whether words are overwritten when accepting completions. Note that this depends on extensions opting into this feature.","Controls whether filtering and sorting suggestions accounts for small typos.","Controls whether sorting favors words that appear close to the cursor.","Controls whether remembered suggestion selections are shared between multiple workspaces and windows (needs `#editor.suggestSelection#`).","Controls whether an active snippet prevents quick suggestions.","Controls whether to show or hide icons in suggestions.","Controls the visibility of the status bar at the bottom of the suggest widget.","Controls whether to preview the suggestion outcome in the editor.","Controls whether suggest details show inline with the label or only in the details widget","This setting is deprecated. The suggest widget can now be resized.","This setting is deprecated, please use separate settings like 'editor.suggest.showKeywords' or 'editor.suggest.showSnippets' instead.","When enabled IntelliSense shows `method`-suggestions.","When enabled IntelliSense shows `function`-suggestions.","When enabled IntelliSense shows `constructor`-suggestions.","When enabled IntelliSense shows `deprecated`-suggestions.","When enabled IntelliSense shows `field`-suggestions.","When enabled IntelliSense shows `variable`-suggestions.","When enabled IntelliSense shows `class`-suggestions.","When enabled IntelliSense shows `struct`-suggestions.","When enabled IntelliSense shows `interface`-suggestions.","When enabled IntelliSense shows `module`-suggestions.","When enabled IntelliSense shows `property`-suggestions.","When enabled IntelliSense shows `event`-suggestions.","When enabled IntelliSense shows `operator`-suggestions.","When enabled IntelliSense shows `unit`-suggestions.","When enabled IntelliSense shows `value`-suggestions.","When enabled IntelliSense shows `constant`-suggestions.","When enabled IntelliSense shows `enum`-suggestions.","When enabled IntelliSense shows `enumMember`-suggestions.","When enabled IntelliSense shows `keyword`-suggestions.","When enabled IntelliSense shows `text`-suggestions.","When enabled IntelliSense shows `color`-suggestions.","When enabled IntelliSense shows `file`-suggestions.","When enabled IntelliSense shows `reference`-suggestions.","When enabled IntelliSense shows `customcolor`-suggestions.","When enabled IntelliSense shows `folder`-suggestions.","When enabled IntelliSense shows `typeParameter`-suggestions.","When enabled IntelliSense shows `snippet`-suggestions.","When enabled IntelliSense shows `user`-suggestions.","When enabled IntelliSense shows `issues`-suggestions.","Whether leading and trailing whitespace should always be selected.","Controls whether suggestions should be accepted on commit characters. For example, in JavaScript, the semi-colon (`;`) can be a commit character that accepts a suggestion and types that character.","Only accept a suggestion with `Enter` when it makes a textual change.","Controls whether suggestions should be accepted on `Enter`, in addition to `Tab`. Helps to avoid ambiguity between inserting new lines or accepting suggestions.","Controls the number of lines in the editor that can be read out by a screen reader at once. When we detect a screen reader we automatically set the default to be 500. Warning: this has a performance implication for numbers larger than the default.","Editor content","Use language configurations to determine when to autoclose brackets.","Autoclose brackets only when the cursor is to the left of whitespace.","Controls whether the editor should automatically close brackets after the user adds an opening bracket.","Remove adjacent closing quotes or brackets only if they were automatically inserted.","Controls whether the editor should remove adjacent closing quotes or brackets when deleting.","Type over closing quotes or brackets only if they were automatically inserted.","Controls whether the editor should type over closing quotes or brackets.","Use language configurations to determine when to autoclose quotes.","Autoclose quotes only when the cursor is to the left of whitespace.","Controls whether the editor should automatically close quotes after the user adds an opening quote.","The editor will not insert indentation automatically.","The editor will keep the current line's indentation.","The editor will keep the current line's indentation and honor language defined brackets.","The editor will keep the current line's indentation, honor language defined brackets and invoke special onEnterRules defined by languages.","The editor will keep the current line's indentation, honor language defined brackets, invoke special onEnterRules defined by languages, and honor indentationRules defined by languages.","Controls whether the editor should automatically adjust the indentation when users type, paste, move or indent lines.","Use language configurations to determine when to automatically surround selections.","Surround with quotes but not brackets.","Surround with brackets but not quotes.","Controls whether the editor should automatically surround selections when typing quotes or brackets.","Emulate selection behavior of tab characters when using spaces for indentation. Selection will stick to tab stops.","Controls whether the editor shows CodeLens.","Controls the font family for CodeLens.","Controls the font size in pixels for CodeLens. When set to `0`, the 90% of `#editor.fontSize#` is used.","Controls whether the editor should render the inline color decorators and color picker.","Enable that the selection with the mouse and keys is doing column selection.","Controls whether syntax highlighting should be copied into the clipboard.","Control the cursor animation style.","Controls whether the smooth caret animation should be enabled.","Controls the cursor style.","Controls the minimal number of visible leading and trailing lines surrounding the cursor. Known as 'scrollOff' or 'scrollOffset' in some other editors.","`cursorSurroundingLines` is enforced only when triggered via the keyboard or API.","`cursorSurroundingLines` is enforced always.","Controls when `cursorSurroundingLines` should be enforced.","Controls the width of the cursor when `#editor.cursorStyle#` is set to `line`.","Controls whether the editor should allow moving selections via drag and drop.","Scrolling speed multiplier when pressing `Alt`.","Controls whether the editor has code folding enabled.","Use a language-specific folding strategy if available, else the indentation-based one.","Use the indentation-based folding strategy.","Controls the strategy for computing folding ranges.","Controls whether the editor should highlight folded ranges.","Controls whether the editor automatically collapses import ranges.","Controls whether clicking on the empty content after a folded line will unfold the line.","Controls the font family.","Controls whether the editor should automatically format the pasted content. A formatter must be available and the formatter should be able to format a range in a document.","Controls whether the editor should automatically format the line after typing.","Controls whether the editor should render the vertical glyph margin. Glyph margin is mostly used for debugging.","Controls whether the cursor should be hidden in the overview ruler.","Controls the letter spacing in pixels.","Controls whether the editor has linked editing enabled. Depending on the language, related symbols, e.g. HTML tags, are updated while editing.","Controls whether the editor should detect links and make them clickable.","Highlight matching brackets.","A multiplier to be used on the `deltaX` and `deltaY` of mouse wheel scroll events.","Zoom the font of the editor when using mouse wheel and holding `Ctrl`.","Merge multiple cursors when they are overlapping.","Maps to `Control` on Windows and Linux and to `Command` on macOS.","Maps to `Alt` on Windows and Linux and to `Option` on macOS.","The modifier to be used to add multiple cursors with the mouse. The Go to Definition and Open Link mouse gestures will adapt such that they do not conflict with the multicursor modifier. [Read more](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier).","Each cursor pastes a single line of the text.","Each cursor pastes the full text.","Controls pasting when the line count of the pasted text matches the cursor count.","Controls whether the editor should highlight semantic symbol occurrences.","Controls whether a border should be drawn around the overview ruler.","Focus the tree when opening peek","Focus the editor when opening peek","Controls whether to focus the inline editor or the tree in the peek widget.","Controls whether the Go to Definition mouse gesture always opens the peek widget.","Controls the delay in milliseconds after which quick suggestions will show up.","Controls whether the editor auto renames on type.","Deprecated, use `editor.linkedEditing` instead.","Controls whether the editor should render control characters.","Render last line number when the file ends with a newline.","Highlights both the gutter and the current line.","Controls how the editor should render the current line highlight.","Controls if the editor should render the current line highlight only when the editor is focused.","Render whitespace characters except for single spaces between words.","Render whitespace characters only on selected text.","Render only trailing whitespace characters.","Controls how the editor should render whitespace characters.","Controls whether selections should have rounded corners.","Controls the number of extra characters beyond which the editor will scroll horizontally.","Controls whether the editor will scroll beyond the last line.","Scroll only along the predominant axis when scrolling both vertically and horizontally at the same time. Prevents horizontal drift when scrolling vertically on a trackpad.","Controls whether the Linux primary clipboard should be supported.","Controls whether the editor should highlight matches similar to the selection.","Always show the folding controls.","Only show the folding controls when the mouse is over the gutter.","Controls when the folding controls on the gutter are shown.","Controls fading out of unused code.","Controls strikethrough deprecated variables.","Show snippet suggestions on top of other suggestions.","Show snippet suggestions below other suggestions.","Show snippets suggestions with other suggestions.","Do not show snippet suggestions.","Controls whether snippets are shown with other suggestions and how they are sorted.","Controls whether the editor will scroll using an animation.","Font size for the suggest widget. When set to `0`, the value of `#editor.fontSize#` is used.","Line height for the suggest widget. When set to `0`, the value of `#editor.lineHeight#` is used. The minimum value is 8.","Controls whether suggestions should automatically show up when typing trigger characters.","Always select the first suggestion.","Select recent suggestions unless further typing selects one, e.g. `console.| -> console.log` because `log` has been completed recently.","Select suggestions based on previous prefixes that have completed those suggestions, e.g. `co -> console` and `con -> const`.","Controls how suggestions are pre-selected when showing the suggest list.","Tab complete will insert the best matching suggestion when pressing tab.","Disable tab completions.","Tab complete snippets when their prefix match. Works best when 'quickSuggestions' aren't enabled.","Enables tab completions.","Unusual line terminators are automatically removed.","Unusual line terminators are ignored.","Unusual line terminators prompt to be removed.","Remove unusual line terminators that might cause problems.","Inserting and deleting whitespace follows tab stops.","Characters that will be used as word separators when doing word related navigations or operations.","Lines will never wrap.","Lines will wrap at the viewport width.","Lines will wrap at `#editor.wordWrapColumn#`.","Lines will wrap at the minimum of viewport and `#editor.wordWrapColumn#`.","Controls how lines should wrap.","Controls the wrapping column of the editor when `#editor.wordWrap#` is `wordWrapColumn` or `bounded`.","No indentation. Wrapped lines begin at column 1.","Wrapped lines get the same indentation as the parent.","Wrapped lines get +1 indentation toward the parent.","Wrapped lines get +2 indentation toward the parent.","Controls the indentation of wrapped lines.","Assumes that all characters are of the same width. This is a fast algorithm that works correctly for monospace fonts and certain scripts (like Latin characters) where glyphs are of equal width.","Delegates wrapping points computation to the browser. This is a slow algorithm, that might cause freezes for large files, but it works correctly in all cases.","Controls the algorithm that computes wrapping points."],"vs/editor/common/editorContextKeys":["Whether the editor text has focus (cursor is blinking)","Whether the editor or an editor widget has focus (e.g. focus is in the find widget)","Whether an editor or a rich text input has focus (cursor is blinking)","Whether the editor is read only","Whether the context is a diff editor","Whether `editor.columnSelection` is enabled","Whether the editor has text selected","Whether the editor has multiple selections","Whether `Tab` will move focus out of the editor","Whether the editor hover is visible","Whether the editor is part of a larger editor (e.g. notebooks)","The language identifier of the editor","Whether the editor has a completion item provider","Whether the editor has a code actions provider","Whether the editor has a code lens provider","Whether the editor has a definition provider","Whether the editor has a declaration provider","Whether the editor has an implementation provider","Whether the editor has a type definition provider","Whether the editor has a hover provider","Whether the editor has a document highlight provider","Whether the editor has a document symbol provider","Whether the editor has a reference provider","Whether the editor has a rename provider","Whether the editor has a signature help provider","Whether the editor has an inline hints provider","Whether the editor has a document formatting provider","Whether the editor has a document selection formatting provider","Whether the editor has multiple document formatting providers","Whether the editor has multiple document selection formatting providers"],"vs/editor/common/model/editStack":["Typing"],"vs/editor/common/modes/modesRegistry":["Plain Text"],"vs/editor/common/standaloneStrings":["No selection","Line {0}, Column {1} ({2} selected)","Line {0}, Column {1}","{0} selections ({1} characters selected)","{0} selections","Now changing the setting `accessibilitySupport` to 'on'.","Now opening the Editor Accessibility documentation page."," in a read-only pane of a diff editor."," in a pane of a diff editor."," in a read-only code editor"," in a code editor","To configure the editor to be optimized for usage with a Screen Reader press Command+E now.","To configure the editor to be optimized for usage with a Screen Reader press Control+E now.","The editor is configured to be optimized for usage with a Screen Reader.","The editor is configured to never be optimized for usage with a Screen Reader, which is not the case at this time.","Pressing Tab in the current editor will move focus to the next focusable element. Toggle this behavior by pressing {0}.","Pressing Tab in the current editor will move focus to the next focusable element. The command {0} is currently not triggerable by a keybinding.","Pressing Tab in the current editor will insert the tab character. Toggle this behavior by pressing {0}.","Pressing Tab in the current editor will insert the tab character. The command {0} is currently not triggerable by a keybinding.","Press Command+H now to open a browser window with more information related to editor accessibility.","Press Control+H now to open a browser window with more information related to editor accessibility.","You can dismiss this tooltip and return to the editor by pressing Escape or Shift+Escape.","Show Accessibility Help","Developer: Inspect Tokens","Go to Line/Column...","Show all Quick Access Providers","Command Palette","Show And Run Commands","Go to Symbol...","Go to Symbol by Category...","Editor content","Press Alt+F1 for Accessibility Options.","Toggle High Contrast Theme","Made {0} edits in {1} files"],"vs/editor/common/view/editorColorRegistry":["Background color for the highlight of line at the cursor position.","Background color for the border around the line at the cursor position.","Background color of highlighted ranges, like by quick open and find features. The color must not be opaque so as not to hide underlying decorations.","Background color of the border around highlighted ranges.","Background color of highlighted symbol, like for go to definition or go next/previous symbol. The color must not be opaque so as not to hide underlying decorations.","Background color of the border around highlighted symbols.","Color of the editor cursor.","The background color of the editor cursor. Allows customizing the color of a character overlapped by a block cursor.","Color of whitespace characters in the editor.","Color of the editor indentation guides.","Color of the active editor indentation guides.","Color of editor line numbers.","Color of editor active line number","Id is deprecated. Use 'editorLineNumber.activeForeground' instead.","Color of editor active line number","Color of the editor rulers.","Foreground color of editor CodeLens","Background color behind matching brackets","Color for matching brackets boxes","Color of the overview ruler border.","Background color of the editor overview ruler. Only used when the minimap is enabled and placed on the right side of the editor.","Background color of the editor gutter. The gutter contains the glyph margins and the line numbers.","Border color of unnecessary (unused) source code in the editor.",`Opacity of unnecessary (unused) source code in the editor. For example, "#000000c0" will render the code with 75% opacity. For high contrast themes, use the  'editorUnnecessaryCode.border' theme color to underline unnecessary code instead of fading it out.`,"Border color of ghost text in the editor.","Foreground color of the ghost text in the editor.","Overview ruler marker color for range highlights. The color must not be opaque so as not to hide underlying decorations.","Overview ruler marker color for errors.","Overview ruler marker color for warnings.","Overview ruler marker color for infos.","Foreground color of brackets (1). Requires enabling bracket pair colorization.","Foreground color of brackets (2). Requires enabling bracket pair colorization.","Foreground color of brackets (3). Requires enabling bracket pair colorization.","Foreground color of brackets (4). Requires enabling bracket pair colorization.","Foreground color of brackets (5). Requires enabling bracket pair colorization.","Foreground color of brackets (6). Requires enabling bracket pair colorization.","Foreground color of unexpected brackets.","Background color of inactive bracket pair guides (1). Requires enabling bracket pair guides.","Background color of inactive bracket pair guides (2). Requires enabling bracket pair guides.","Background color of inactive bracket pair guides (3). Requires enabling bracket pair guides.","Background color of inactive bracket pair guides (4). Requires enabling bracket pair guides.","Background color of inactive bracket pair guides (5). Requires enabling bracket pair guides.","Background color of inactive bracket pair guides (6). Requires enabling bracket pair guides.","Background color of active bracket pair guides (1). Requires enabling bracket pair guides.","Background color of active bracket pair guides (2). Requires enabling bracket pair guides.","Background color of active bracket pair guides (3). Requires enabling bracket pair guides.","Background color of active bracket pair guides (4). Requires enabling bracket pair guides.","Background color of active bracket pair guides (5). Requires enabling bracket pair guides.","Background color of active bracket pair guides (6). Requires enabling bracket pair guides."],"vs/editor/contrib/anchorSelect/anchorSelect":["Selection Anchor","Anchor set at {0}:{1}","Set Selection Anchor","Go to Selection Anchor","Select from Anchor to Cursor","Cancel Selection Anchor"],"vs/editor/contrib/bracketMatching/bracketMatching":["Overview ruler marker color for matching brackets.","Go to Bracket","Select to Bracket","Go to &&Bracket"],"vs/editor/contrib/caretOperations/caretOperations":["Move Selected Text Left","Move Selected Text Right"],"vs/editor/contrib/caretOperations/transpose":["Transpose Letters"],"vs/editor/contrib/clipboard/clipboard":["Cu&&t","Cut","Cut","Cut","&&Copy","Copy","Copy","Copy","Copy As","Copy As","&&Paste","Paste","Paste","Paste","Copy With Syntax Highlighting"],"vs/editor/contrib/codeAction/codeActionCommands":["Kind of the code action to run.","Controls when the returned actions are applied.","Always apply the first returned code action.","Apply the first returned code action if it is the only one.","Do not apply the returned code actions.","Controls if only preferred code actions should be returned.","An unknown error occurred while applying the code action","Quick Fix...","No code actions available","No preferred code actions for '{0}' available","No code actions for '{0}' available","No preferred code actions available","No code actions available","Refactor...","No preferred refactorings for '{0}' available","No refactorings for '{0}' available","No preferred refactorings available","No refactorings available","Source Action...","No preferred source actions for '{0}' available","No source actions for '{0}' available","No preferred source actions available","No source actions available","Organize Imports","No organize imports action available","Fix All","No fix all action available","Auto Fix...","No auto fixes available"],"vs/editor/contrib/codeAction/lightBulbWidget":["Show Code Actions. Preferred Quick Fix Available ({0})","Show Code Actions ({0})","Show Code Actions"],"vs/editor/contrib/codelens/codelensController":["Show CodeLens Commands For Current Line"],"vs/editor/contrib/colorPicker/colorPickerWidget":["Click to toggle color options (rgb/hsl/hex)"],"vs/editor/contrib/comment/comment":["Toggle Line Comment","&&Toggle Line Comment","Add Line Comment","Remove Line Comment","Toggle Block Comment","Toggle &&Block Comment"],"vs/editor/contrib/contextmenu/contextmenu":["Show Editor Context Menu"],"vs/editor/contrib/cursorUndo/cursorUndo":["Cursor Undo","Cursor Redo"],"vs/editor/contrib/find/findController":["Find","&&Find","Find With Selection","Find Next","Find Previous","Find Next Selection","Find Previous Selection","Replace","&&Replace"],"vs/editor/contrib/find/findWidget":["Icon for 'Find in Selection' in the editor find widget.","Icon to indicate that the editor find widget is collapsed.","Icon to indicate that the editor find widget is expanded.","Icon for 'Replace' in the editor find widget.","Icon for 'Replace All' in the editor find widget.","Icon for 'Find Previous' in the editor find widget.","Icon for 'Find Next' in the editor find widget.","Find","Find","Previous Match","Next Match","Find in Selection","Close","Replace","Replace","Replace","Replace All","Toggle Replace","Only the first {0} results are highlighted, but all find operations work on the entire text.","{0} of {1}","No results","{0} found","{0} found for '{1}'","{0} found for '{1}', at {2}","{0} found for '{1}'","Ctrl+Enter now inserts line break instead of replacing all. You can modify the keybinding for editor.action.replaceAll to override this behavior."],"vs/editor/contrib/folding/folding":["Unfold","Unfold Recursively","Fold","Toggle Fold","Fold Recursively","Fold All Block Comments","Fold All Regions","Unfold All Regions","Fold All Regions Except Selected","Unfold All Regions Except Selected","Fold All","Unfold All","Go to Parent Fold","Go to Previous Folding Range","Go to Next Folding Range","Fold Level {0}","Background color behind folded ranges. The color must not be opaque so as not to hide underlying decorations.","Color of the folding control in the editor gutter."],"vs/editor/contrib/folding/foldingDecorations":["Icon for expanded ranges in the editor glyph margin.","Icon for collapsed ranges in the editor glyph margin."],"vs/editor/contrib/fontZoom/fontZoom":["Editor Font Zoom In","Editor Font Zoom Out","Editor Font Zoom Reset"],"vs/editor/contrib/format/format":["Made 1 formatting edit on line {0}","Made {0} formatting edits on line {1}","Made 1 formatting edit between lines {0} and {1}","Made {0} formatting edits between lines {1} and {2}"],"vs/editor/contrib/format/formatActions":["Format Document","Format Selection"],"vs/editor/contrib/gotoError/gotoError":["Go to Next Problem (Error, Warning, Info)","Icon for goto next marker.","Go to Previous Problem (Error, Warning, Info)","Icon for goto previous marker.","Go to Next Problem in Files (Error, Warning, Info)","Next &&Problem","Go to Previous Problem in Files (Error, Warning, Info)","Previous &&Problem"],"vs/editor/contrib/gotoError/gotoErrorWidget":["Error","Warning","Info","Hint","{0} at {1}. ","{0} of {1} problems","{0} of {1} problem","Editor marker navigation widget error color.","Editor marker navigation widget error heading background.","Editor marker navigation widget warning color.","Editor marker navigation widget warning heading background.","Editor marker navigation widget info color.","Editor marker navigation widget info heading background.","Editor marker navigation widget background."],"vs/editor/contrib/gotoSymbol/goToCommands":["Peek","Definitions","No definition found for '{0}'","No definition found","Go to Definition","Open Definition to the Side","Peek Definition","Declarations","No declaration found for '{0}'","No declaration found","Go to Declaration","No declaration found for '{0}'","No declaration found","Peek Declaration","Type Definitions","No type definition found for '{0}'","No type definition found","Go to Type Definition","Peek Type Definition","Implementations","No implementation found for '{0}'","No implementation found","Go to Implementations","Peek Implementations","No references found for '{0}'","No references found","Go to References","References","Peek References","References","Go to Any Symbol","Locations","No results for '{0}'","References","Go to &&Definition","Go to &&Declaration","Go to &&Type Definition","Go to &&Implementations","Go to &&References"],"vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition":["Click to show {0} definitions."],"vs/editor/contrib/gotoSymbol/peek/referencesController":["Whether reference peek is visible, like 'Peek References' or 'Peek Definition'","Loading...","{0} ({1})"],"vs/editor/contrib/gotoSymbol/peek/referencesTree":["{0} references","{0} reference","References"],"vs/editor/contrib/gotoSymbol/peek/referencesWidget":["no preview available","No results","References"],"vs/editor/contrib/gotoSymbol/referencesModel":["symbol in {0} on line {1} at column {2}","symbol in {0} on line {1} at column {2}, {3}","1 symbol in {0}, full path {1}","{0} symbols in {1}, full path {2}","No results found","Found 1 symbol in {0}","Found {0} symbols in {1}","Found {0} symbols in {1} files"],"vs/editor/contrib/gotoSymbol/symbolNavigation":["Whether there are symbol locations that can be navigated via keyboard-only.","Symbol {0} of {1}, {2} for next","Symbol {0} of {1}"],"vs/editor/contrib/hover/hover":["Show Hover","Show Definition Preview Hover"],"vs/editor/contrib/hover/markdownHoverParticipant":["Loading...","Tokenization is skipped for long lines for performance reasons. This can be configured via `editor.maxTokenizationLineLength`."],"vs/editor/contrib/hover/markerHoverParticipant":["View Problem","No quick fixes available","Checking for quick fixes...","No quick fixes available","Quick Fix..."],"vs/editor/contrib/inPlaceReplace/inPlaceReplace":["Replace with Previous Value","Replace with Next Value"],"vs/editor/contrib/indentation/indentation":["Convert Indentation to Spaces","Convert Indentation to Tabs","Configured Tab Size","Select Tab Size for Current File","Indent Using Tabs","Indent Using Spaces","Detect Indentation from Content","Reindent Lines","Reindent Selected Lines"],"vs/editor/contrib/inlineCompletions/ghostTextController":["Whether an inline suggestion is visible","Whether the inline suggestion starts with whitespace","Whether the inline suggestion starts with whitespace that is less than what would be inserted by tab","Show Next Inline Suggestion","Show Previous Inline Suggestion","Trigger Inline Suggestion"],"vs/editor/contrib/inlineCompletions/inlineCompletionsHoverParticipant":["Next","Previous","Accept","Suggestion:"],"vs/editor/contrib/linesOperations/linesOperations":["Copy Line Up","&&Copy Line Up","Copy Line Down","Co&&py Line Down","Duplicate Selection","&&Duplicate Selection","Move Line Up","Mo&&ve Line Up","Move Line Down","Move &&Line Down","Sort Lines Ascending","Sort Lines Descending","Delete Duplicate Lines","Trim Trailing Whitespace","Delete Line","Indent Line","Outdent Line","Insert Line Above","Insert Line Below","Delete All Left","Delete All Right","Join Lines","Transpose characters around the cursor","Transform to Uppercase","Transform to Lowercase","Transform to Title Case","Transform to Snake Case"],"vs/editor/contrib/linkedEditing/linkedEditing":["Start Linked Editing","Background color when the editor auto renames on type."],"vs/editor/contrib/links/links":["Execute command","Follow link","cmd + click","ctrl + click","option + click","alt + click","Execute command {0}","Failed to open this link because it is not well-formed: {0}","Failed to open this link because its target is missing.","Open Link"],"vs/editor/contrib/message/messageController":["Whether the editor is currently showing an inline message","Cannot edit in read-only editor"],"vs/editor/contrib/multicursor/multicursor":["Cursor added: {0}","Cursors added: {0}","Add Cursor Above","&&Add Cursor Above","Add Cursor Below","A&&dd Cursor Below","Add Cursors to Line Ends","Add C&&ursors to Line Ends","Add Cursors To Bottom","Add Cursors To Top","Add Selection To Next Find Match","Add &&Next Occurrence","Add Selection To Previous Find Match","Add P&&revious Occurrence","Move Last Selection To Next Find Match","Move Last Selection To Previous Find Match","Select All Occurrences of Find Match","Select All &&Occurrences","Change All Occurrences"],"vs/editor/contrib/parameterHints/parameterHints":["Trigger Parameter Hints"],"vs/editor/contrib/parameterHints/parameterHintsWidget":["Icon for show next parameter hint.","Icon for show previous parameter hint.","{0}, hint","Foreground color of the active item in the parameter hint."],"vs/editor/contrib/peekView/peekView":["Whether the current code editor is embedded inside peek","Close","Background color of the peek view title area.","Color of the peek view title.","Color of the peek view title info.","Color of the peek view borders and arrow.","Background color of the peek view result list.","Foreground color for line nodes in the peek view result list.","Foreground color for file nodes in the peek view result list.","Background color of the selected entry in the peek view result list.","Foreground color of the selected entry in the peek view result list.","Background color of the peek view editor.","Background color of the gutter in the peek view editor.","Match highlight color in the peek view result list.","Match highlight color in the peek view editor.","Match highlight border in the peek view editor."],"vs/editor/contrib/quickAccess/gotoLineQuickAccess":["Open a text editor first to go to a line.","Go to line {0} and character {1}.","Go to line {0}.","Current Line: {0}, Character: {1}. Type a line number between 1 and {2} to navigate to.","Current Line: {0}, Character: {1}. Type a line number to navigate to."],"vs/editor/contrib/quickAccess/gotoSymbolQuickAccess":["To go to a symbol, first open a text editor with symbol information.","The active text editor does not provide symbol information.","No matching editor symbols","No editor symbols","Open to the Side","Open to the Bottom","symbols ({0})","properties ({0})","methods ({0})","functions ({0})","constructors ({0})","variables ({0})","classes ({0})","structs ({0})","events ({0})","operators ({0})","interfaces ({0})","namespaces ({0})","packages ({0})","type parameters ({0})","modules ({0})","properties ({0})","enumerations ({0})","enumeration members ({0})","strings ({0})","files ({0})","arrays ({0})","numbers ({0})","booleans ({0})","objects ({0})","keys ({0})","fields ({0})","constants ({0})"],"vs/editor/contrib/rename/rename":["No result.","An unknown error occurred while resolving rename location","Renaming '{0}'","Renaming {0}","Successfully renamed '{0}' to '{1}'. Summary: {2}","Rename failed to apply edits","Rename failed to compute edits","Rename Symbol","Enable/disable the ability to preview changes before renaming"],"vs/editor/contrib/rename/renameInputField":["Whether the rename input widget is visible","Rename input. Type new name and press Enter to commit.","{0} to Rename, {1} to Preview"],"vs/editor/contrib/smartSelect/smartSelect":["Expand Selection","&&Expand Selection","Shrink Selection","&&Shrink Selection"],"vs/editor/contrib/snippet/snippetController2":["Whether the editor in current in snippet mode","Whether there is a next tab stop when in snippet mode","Whether there is a previous tab stop when in snippet mode"],"vs/editor/contrib/snippet/snippetVariables":["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sun","Mon","Tue","Wed","Thu","Fri","Sat","January","February","March","April","May","June","July","August","September","October","November","December","Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],"vs/editor/contrib/suggest/suggest":["Whether suggestion are visible","Whether suggestion details are visible","Whether there are multiple suggestions to pick from","Whether inserting the current suggestion yields in a change or has everything already been typed","Whether suggestions are inserted when pressing Enter","Whether the current suggestion has insert and replace behaviour","Whether the default behaviour is to insert or replace","Whether the current suggestion supports to resolve further details"],"vs/editor/contrib/suggest/suggestController":["Accepting '{0}' made {1} additional edits","Trigger Suggest","Insert","Insert","Replace","Replace","Insert","show less","show more","Reset Suggest Widget Size"],"vs/editor/contrib/suggest/suggestWidget":["Background color of the suggest widget.","Border color of the suggest widget.","Foreground color of the suggest widget.","Foreground color of the selected entry in the suggest widget.","Icon foreground color of the selected entry in the suggest widget.","Background color of the selected entry in the suggest widget.","Color of the match highlights in the suggest widget.","Color of the match highlights in the suggest widget when an item is focused.","Loading...","No suggestions.","{0}, docs: {1}","Suggest"],"vs/editor/contrib/suggest/suggestWidgetDetails":["Close","Loading..."],"vs/editor/contrib/suggest/suggestWidgetRenderer":["Icon for more information in the suggest widget.","Read More"],"vs/editor/contrib/suggest/suggestWidgetStatus":["{0} ({1})"],"vs/editor/contrib/symbolIcons/symbolIcons":["The foreground color for array symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for boolean symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for class symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for color symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for constant symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for constructor symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for enumerator symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for enumerator member symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for event symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for field symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for file symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for folder symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for function symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for interface symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for key symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for keyword symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for method symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for module symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for namespace symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for null symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for number symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for object symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for operator symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for package symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for property symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for reference symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for snippet symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for string symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for struct symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for text symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for type parameter symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for unit symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for variable symbols. These symbols appear in the outline, breadcrumb, and suggest widget."],"vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode":["Toggle Tab Key Moves Focus","Pressing Tab will now move focus to the next focusable element","Pressing Tab will now insert the tab character"],"vs/editor/contrib/tokenization/tokenization":["Developer: Force Retokenize"],"vs/editor/contrib/unusualLineTerminators/unusualLineTerminators":["Unusual Line Terminators","Detected unusual line terminators","The file '{0}' contains one or more unusual line terminator characters, like Line Separator (LS) or Paragraph Separator (PS).\n\nIt is recommended to remove them from the file. This can be configured via `editor.unusualLineTerminators`.","Remove Unusual Line Terminators","Ignore"],"vs/editor/contrib/wordHighlighter/wordHighlighter":["Background color of a symbol during read-access, like reading a variable. The color must not be opaque so as not to hide underlying decorations.","Background color of a symbol during write-access, like writing to a variable. The color must not be opaque so as not to hide underlying decorations.","Border color of a symbol during read-access, like reading a variable.","Border color of a symbol during write-access, like writing to a variable.","Overview ruler marker color for symbol highlights. The color must not be opaque so as not to hide underlying decorations.","Overview ruler marker color for write-access symbol highlights. The color must not be opaque so as not to hide underlying decorations.","Go to Next Symbol Highlight","Go to Previous Symbol Highlight","Trigger Symbol Highlight"],"vs/editor/contrib/wordOperations/wordOperations":["Delete Word"],"vs/platform/actions/browser/menuEntryActionViewItem":["{0} ({1})","{0} ({1})"],"vs/platform/configuration/common/configurationRegistry":["Default Language Configuration Overrides","Configure editor settings to be overridden for a language.","This setting does not support per-language configuration.","Cannot register an empty property","Cannot register '{0}'. This matches property pattern '\\\\[.*\\\\]$' for describing language specific editor settings. Use 'configurationDefaults' contribution.","Cannot register '{0}'. This property is already registered."],"vs/platform/contextkey/browser/contextKeyService":["A command that returns information about context keys"],"vs/platform/contextkey/common/contextkeys":["Whether the operating system is Windows"],"vs/platform/keybinding/common/abstractKeybindingService":["({0}) was pressed. Waiting for second key of chord...","The key combination ({0}, {1}) is not a command."],"vs/platform/list/browser/listService":["Workbench","Maps to `Control` on Windows and Linux and to `Command` on macOS.","Maps to `Alt` on Windows and Linux and to `Option` on macOS.","The modifier to be used to add an item in trees and lists to a multi-selection with the mouse (for example in the explorer, open editors and scm view). The 'Open to Side' mouse gestures - if supported - will adapt such that they do not conflict with the multiselect modifier.","Controls how to open items in trees and lists using the mouse (if supported). Note that some trees and lists might choose to ignore this setting if it is not applicable.","Controls whether lists and trees support horizontal scrolling in the workbench. Warning: turning on this setting has a performance implication.","Controls tree indentation in pixels.","Controls whether the tree should render indent guides.","Controls whether lists and trees have smooth scrolling.","A multiplier to be used on the `deltaX` and `deltaY` of mouse wheel scroll events.","Scrolling speed multiplier when pressing `Alt`.","Simple keyboard navigation focuses elements which match the keyboard input. Matching is done only on prefixes.","Highlight keyboard navigation highlights elements which match the keyboard input. Further up and down navigation will traverse only the highlighted elements.","Filter keyboard navigation will filter out and hide all the elements which do not match the keyboard input.","Controls the keyboard navigation style for lists and trees in the workbench. Can be simple, highlight and filter.","Controls whether keyboard navigation in lists and trees is automatically triggered simply by typing. If set to `false`, keyboard navigation is only triggered when executing the `list.toggleKeyboardNavigation` command, for which you can assign a keyboard shortcut.","Controls how tree folders are expanded when clicking the folder names. Note that some trees and lists might choose to ignore this setting if it is not applicable."],"vs/platform/markers/common/markers":["Error","Warning","Info"],"vs/platform/quickinput/browser/commandsQuickAccess":["{0}, {1}","recently used","other commands","Command '{0}' resulted in an error ({1})"],"vs/platform/quickinput/browser/helpQuickAccess":["global commands","editor commands","{0}, {1}"],"vs/platform/theme/common/colorRegistry":["Overall foreground color. This color is only used if not overridden by a component.","Overall foreground color for error messages. This color is only used if not overridden by a component.","The default color for icons in the workbench.","Overall border color for focused elements. This color is only used if not overridden by a component.","An extra border around elements to separate them from others for greater contrast.","An extra border around active elements to separate them from others for greater contrast.","Foreground color for links in text.","Foreground color for links in text when clicked on and on mouse hover.","Background color for code blocks in text.","Shadow color of widgets such as find/replace inside the editor.","Input box background.","Input box foreground.","Input box border.","Border color of activated options in input fields.","Background color of activated options in input fields.","Foreground color of activated options in input fields.","Input validation background color for information severity.","Input validation foreground color for information severity.","Input validation border color for information severity.","Input validation background color for warning severity.","Input validation foreground color for warning severity.","Input validation border color for warning severity.","Input validation background color for error severity.","Input validation foreground color for error severity.","Input validation border color for error severity.","Dropdown background.","Dropdown foreground.","Button foreground color.","Button background color.","Button background color when hovering.","Badge background color. Badges are small information labels, e.g. for search results count.","Badge foreground color. Badges are small information labels, e.g. for search results count.","Scrollbar shadow to indicate that the view is scrolled.","Scrollbar slider background color.","Scrollbar slider background color when hovering.","Scrollbar slider background color when clicked on.","Background color of the progress bar that can show for long running operations.","Background color of error text in the editor. The color must not be opaque so as not to hide underlying decorations.","Foreground color of error squigglies in the editor.","Border color of error boxes in the editor.","Background color of warning text in the editor. The color must not be opaque so as not to hide underlying decorations.","Foreground color of warning squigglies in the editor.","Border color of warning boxes in the editor.","Background color of info text in the editor. The color must not be opaque so as not to hide underlying decorations.","Foreground color of info squigglies in the editor.","Border color of info boxes in the editor.","Foreground color of hint squigglies in the editor.","Border color of hint boxes in the editor.","Editor background color.","Editor default foreground color.","Background color of editor widgets, such as find/replace.","Foreground color of editor widgets, such as find/replace.","Border color of editor widgets. The color is only used if the widget chooses to have a border and if the color is not overridden by a widget.","Border color of the resize bar of editor widgets. The color is only used if the widget chooses to have a resize border and if the color is not overridden by a widget.","Quick picker background color. The quick picker widget is the container for pickers like the command palette.","Quick picker foreground color. The quick picker widget is the container for pickers like the command palette.","Quick picker title background color. The quick picker widget is the container for pickers like the command palette.","Quick picker color for grouping labels.","Quick picker color for grouping borders.","Keybinding label background color. The keybinding label is used to represent a keyboard shortcut.","Keybinding label foreground color. The keybinding label is used to represent a keyboard shortcut.","Keybinding label border color. The keybinding label is used to represent a keyboard shortcut.","Keybinding label border bottom color. The keybinding label is used to represent a keyboard shortcut.","Color of the editor selection.","Color of the selected text for high contrast.","Color of the selection in an inactive editor. The color must not be opaque so as not to hide underlying decorations.","Color for regions with the same content as the selection. The color must not be opaque so as not to hide underlying decorations.","Border color for regions with the same content as the selection.","Color of the current search match.","Color of the other search matches. The color must not be opaque so as not to hide underlying decorations.","Color of the range limiting the search. The color must not be opaque so as not to hide underlying decorations.","Border color of the current search match.","Border color of the other search matches.","Border color of the range limiting the search. The color must not be opaque so as not to hide underlying decorations.","Highlight below the word for which a hover is shown. The color must not be opaque so as not to hide underlying decorations.","Background color of the editor hover.","Foreground color of the editor hover.","Border color of the editor hover.","Background color of the editor hover status bar.","Color of active links.","Foreground color of inline hints","Background color of inline hints","Foreground color of inline hints for types","Background color of inline hints for types","Foreground color of inline hints for parameters","Background color of inline hints for parameters","The color used for the lightbulb actions icon.","The color used for the lightbulb auto fix actions icon.","Background color for text that got inserted. The color must not be opaque so as not to hide underlying decorations.","Background color for text that got removed. The color must not be opaque so as not to hide underlying decorations.","Outline color for the text that got inserted.","Outline color for text that got removed.","Border color between the two text editors.","Color of the diff editor's diagonal fill. The diagonal fill is used in side-by-side diff views.","List/Tree background color for the focused item when the list/tree is active. An active list/tree has keyboard focus, an inactive does not.","List/Tree foreground color for the focused item when the list/tree is active. An active list/tree has keyboard focus, an inactive does not.","List/Tree outline color for the focused item when the list/tree is active. An active list/tree has keyboard focus, an inactive does not.","List/Tree background color for the selected item when the list/tree is active. An active list/tree has keyboard focus, an inactive does not.","List/Tree foreground color for the selected item when the list/tree is active. An active list/tree has keyboard focus, an inactive does not.","List/Tree icon foreground color for the selected item when the list/tree is active. An active list/tree has keyboard focus, an inactive does not.","List/Tree background color for the selected item when the list/tree is inactive. An active list/tree has keyboard focus, an inactive does not.","List/Tree foreground color for the selected item when the list/tree is inactive. An active list/tree has keyboard focus, an inactive does not.","List/Tree icon foreground color for the selected item when the list/tree is inactive. An active list/tree has keyboard focus, an inactive does not.","List/Tree background color for the focused item when the list/tree is inactive. An active list/tree has keyboard focus, an inactive does not.","List/Tree outline color for the focused item when the list/tree is inactive. An active list/tree has keyboard focus, an inactive does not.","List/Tree background when hovering over items using the mouse.","List/Tree foreground when hovering over items using the mouse.","List/Tree drag and drop background when moving items around using the mouse.","List/Tree foreground color of the match highlights when searching inside the list/tree.","List/Tree foreground color of the match highlights on actively focused items when searching inside the list/tree.","Background color of the type filter widget in lists and trees.","Outline color of the type filter widget in lists and trees.","Outline color of the type filter widget in lists and trees, when there are no matches.","Tree stroke color for the indentation guides.","Tree stroke color for the indentation guides.","Please use quickInputList.focusBackground instead","Quick picker foreground color for the focused item.","Quick picker icon foreground color for the focused item.","Quick picker background color for the focused item.","Border color of menus.","Foreground color of menu items.","Background color of menu items.","Foreground color of the selected menu item in menus.","Background color of the selected menu item in menus.","Border color of the selected menu item in menus.","Color of a separator menu item in menus.","Toolbar background when hovering over actions using the mouse","Highlight background color of a snippet tabstop.","Highlight border color of a snippet tabstop.","Highlight background color of the final tabstop of a snippet.","Highlight border color of the final tabstop of a snippet.","Overview ruler marker color for find matches. The color must not be opaque so as not to hide underlying decorations.","Overview ruler marker color for selection highlights. The color must not be opaque so as not to hide underlying decorations.","Minimap marker color for find matches.","Minimap marker color for repeating editor selections.","Minimap marker color for the editor selection.","Minimap marker color for errors.","Minimap marker color for warnings.","Minimap background color.",'Opacity of foreground elements rendered in the minimap. For example, "#000000c0" will render the elements with 75% opacity.',"Minimap slider background color.","Minimap slider background color when hovering.","Minimap slider background color when clicked on.","The color used for the problems error icon.","The color used for the problems warning icon.","The color used for the problems info icon."],"vs/platform/theme/common/iconRegistry":["The id of the font to use. If not set, the font that is defined first is used.","The font character associated with the icon definition.","Icon for the close action in widgets."],"vs/platform/undoRedo/common/undoRedoService":["The following files have been closed and modified on disk: {0}.","The following files have been modified in an incompatible way: {0}.","Could not undo '{0}' across all files. {1}","Could not undo '{0}' across all files. {1}","Could not undo '{0}' across all files because changes were made to {1}","Could not undo '{0}' across all files because there is already an undo or redo operation running on {1}","Could not undo '{0}' across all files because an undo or redo operation occurred in the meantime","Would you like to undo '{0}' across all files?","Undo in {0} Files","Undo this File","Cancel","Could not undo '{0}' because there is already an undo or redo operation running.","Would you like to undo '{0}'?","Yes","Cancel","Could not redo '{0}' across all files. {1}","Could not redo '{0}' across all files. {1}","Could not redo '{0}' across all files because changes were made to {1}","Could not redo '{0}' across all files because there is already an undo or redo operation running on {1}","Could not redo '{0}' across all files because an undo or redo operation occurred in the meantime","Could not redo '{0}' because there is already an undo or redo operation running."]});

//# sourceMappingURL=./thirds/monaco-editor/min-maps/vs/editor/editor.main.nls.js.map