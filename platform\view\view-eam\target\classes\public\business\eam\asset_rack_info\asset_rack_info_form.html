<!--
/**
 * 机柜 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-08-27 23:10:13
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
	<title th:text="${lang.translate('机柜')}">机柜</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="display:none">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->


        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-5683-fieldset">
            <legend  th:text="${lang.translate('基本信息')}">基本信息</legend>
        </fieldset>

        <div class="layui-row form-row" id="random-5683-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('编码')}">编码</div><div class="layui-required">*</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="rackCode" id="rackCode" name="rackCode" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('编码') }" type="text" class="layui-input"    lay-verify="|required"  />
                        </div>
                    </div>

                                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('名称')}">名称</div><div class="layui-required">*</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="rackName" id="rackName" name="rackName" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('名称') }" type="text" class="layui-input"    lay-verify="|required"  />
                        </div>
                    </div>


                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('使用分类')}">使用分类</div><div class="layui-required">*</div></div>
                        <div class="layui-input-block ">
                            <div id="rackUsedType" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=ops_dc_rack_used_type'}" extraParam="{}"></div>
                        </div>
                    </div>


                <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >


                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('状态')}">状态</div><div class="layui-required">*</div></div>
                        <div class="layui-input-block ">
                            <div id="status" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=ops_dc_rack_status'}" extraParam="{}"></div>
                        </div>
                    </div>



                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('类型')}">类型</div><div class="layui-required">*</div></div>
                        <div class="layui-input-block ">
                            <div id="rackType" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=ops_dc_rack_type'}" extraParam="{}"></div>
                        </div>
                    </div>


                <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >


                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('到期日期')}">到期日期</div></div>
                        <div class="layui-input-block ">
                            <input input-type="date" lay-filter="expireDate" id="expireDate" name="expireDate"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择') +''+ lang.translate('到期日期') }" type="text" class="layui-input"    lay-verify=""   />
                        </div>
                    </div>


                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('环境')}">环境</div></div>
                        <div class="layui-input-block ">
                            <div id="environment" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=ops_dc_rack_environment'}" extraParam="{}"></div>
                        </div>
                    </div>


                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->


        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-4784-fieldset">
            <legend th:text="${lang.translate('属性信息')}">属性信息</legend>
        </fieldset>

        <div class="layui-row form-row" id="random-4784-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >


                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('U位数量')}">U位数量</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="uPostionNumber" id="uPostionNumber" name="uPostionNumber" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('U位数量') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="true" decimal="false" allow-negative="true" step="1.0"   scale="0" />
                        </div>
                    </div>



                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('容量')}">容量</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="rackCaptical" id="rackCaptical" name="rackCaptical" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('容量') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   min-value="0.0"  max-value="100.0"  scale="0" />
                        </div>
                    </div>



                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('合同电力')}">合同电力</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="contractPower" id="contractPower" name="contractPower" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('合同电力') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="true" decimal="false" allow-negative="true" step="1.0"   scale="0" />
                        </div>
                    </div>


                <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >


                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('PDU数量')}">PDU数量</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="pduNumber" id="pduNumber" name="pduNumber" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('PDU数量') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="true" decimal="false" allow-negative="true" step="1.0"   scale="0" />
                        </div>
                    </div>



                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('跳线数')}">跳线数</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="jumperNumber" id="jumperNumber" name="jumperNumber" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('跳线数') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="true" decimal="false" allow-negative="true" step="1.0"   scale="0" />
                        </div>
                    </div>


                <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('标签1')}">标签1</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="rackLabel1" id="rackLabel1" name="rackLabel1" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('标签1') }" type="text" class="layui-input"  />
                        </div>
                    </div>

                                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('标签2')}">标签2</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="rackLabel2" id="rackLabel2" name="rackLabel2" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('标签2') }" type="text" class="layui-input"  />
                        </div>
                    </div>

                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->



        <div class="layui-row form-row" id="random-8886-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >


                    <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="rackNotes" id="rackNotes" name="rackNotes" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('备注') }" class="layui-textarea" style="height: 30px" ></textarea>
                    </div>
                </div>


                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 80px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_asset_rack_info:create','eam_asset_rack_info:update','eam_asset_rack_info:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var VALIDATE_CONFIG={"rackCode":{"labelInForm":"编码","inputType":"text_input","required":true},"rackName":{"labelInForm":"名称","inputType":"text_input","required":true},"rackUsedType":{"labelInForm":"使用分类","inputType":"select_box","required":true},"expireDate":{"date":true,"labelInForm":"到期日期","inputType":"date_input"},"rackType":{"labelInForm":"类型","inputType":"select_box","required":true},"status":{"labelInForm":"状态","inputType":"select_box","required":true}};
    var AUTH_PREFIX="eam_asset_rack_info";


</script>



<script th:src="'/business/eam/asset_rack_info/asset_rack_info_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset_rack_info/asset_rack_info_form.js?'+${cacheKey}"></script>

</body>
</html>
