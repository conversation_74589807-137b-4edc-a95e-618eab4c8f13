var m=(v,y,i)=>new Promise((p,c)=>{var a=t=>{try{r(i.next(t))}catch(e){c(e)}},f=t=>{try{r(i.throw(t))}catch(e){c(e)}},r=t=>t.done?p(t.value):Promise.resolve(t.value).then(a,f);r((i=i.apply(v,y)).next())});import{M as I}from"./EditorWorker-54a88558.js";import"./editorWorker-43a98755.js";import{d as V,l as P,aB as g,aC as A,S as D,aD as k,r as s,c as M,e as o,w as n,ay as F,aE as J,o as w,q as L,f as _,u as d,E as x,a5 as E,j as R}from"./index-bb2cbf17.js";import{g as j}from"./storage-2b59f4d0.js";import"./querySelectorAll-a1c431d2.js";import{g as b}from"./plugin-3ef0fcec.js";import{i as z}from"./icon-f36697ff.js";import"./chartEditStore-55fbe93c.js";const G={class:"go-edit"},H=V({__name:"index",setup(v){const{ChevronBackOutlineIcon:y,DownloadIcon:i,AnalyticsIcon:p}=z.ionicons5,c=window.showOpenFilePicker,a=P("");window.$message.warning("请不要刷新此窗口！");function f(){return m(this,null,function*(){const e=yield j();F(`编辑-${e.editCanvasConfig.projectName}`),a.value=k(e)})}setTimeout(f);function r(){b({message:"导入数据将覆盖内容，此操作不可撤回，是否继续？",isMaskClosable:!0,transformOrigin:"center",onPositiveCallback:()=>m(this,null,function*(){try{const u=yield(yield c())[0].getFile(),l=new FileReader;l.readAsText(u),l.onloadend=()=>{a.value=(l.result||"").toString()},window.$message.success("导入成功！")}catch(e){window.$message.error("导入失败，请检查文件是否损坏！"),console.log(e)}})})}window.opener.addEventListener(g.CHART,e=>{window.$message.success("正在进行更新..."),A(D.GO_CHART_STORAGE_LIST,[e.detail]),a.value=k(e.detail)}),document.addEventListener("keydown",function(e){e.keyCode==83&&(navigator.platform.match("Mac")?e.metaKey:e.ctrlKey)&&(e.preventDefault(),t())});function t(){return m(this,null,function*(){if(!window.opener)return window.$message.error("源窗口已关闭，视图同步失败！");b({message:"是否覆盖源视图内容? 此操作不可撤！",isMaskClosable:!0,transformOrigin:"center",onPositiveCallback:()=>{try{const e=J(a.value);delete e.id,window.opener.dispatchEvent(new CustomEvent(g.JSON,{detail:e})),window.$message.success("正在同步内容...")}catch(e){window.$message.error("内容格式有误"),console.log(e)}}})})}return window.onbeforeunload=()=>{window.opener&&window.opener.dispatchEvent(new CustomEvent(g.CLOSE))},(e,u)=>{const l=s("n-text"),C=s("n-icon"),S=s("n-button"),O=s("n-tag"),h=s("n-space"),N=s("n-layout-header"),T=s("n-layout-content"),$=s("n-layout");return w(),M("div",G,[o($,null,{default:n(()=>[o(N,{class:"go-edit-header go-px-5 go-flex-items-center",bordered:""},{default:n(()=>[L("div",null,[o(l,{class:"go-edit-title go-mr-4"},{default:n(()=>[_("页面在线编辑器")]),_:1}),d(c)?(w(),x(S,{key:0,class:"go-mr-3",size:"medium",onClick:r},{icon:n(()=>[o(C,null,{default:n(()=>[o(d(i))]),_:1})]),default:n(()=>[_(" 导入 ")]),_:1})):E("",!0)]),o(h,null,{default:n(()=>[o(O,{bordered:!1,type:"warning"},{default:n(()=>[_(" 「Ctrl + S 更新视图」 ")]),_:1}),d(c)?(w(),x(S,{key:0,class:"go-mr-3",size:"medium",onClick:t},{icon:n(()=>[o(C,null,{default:n(()=>[o(d(p))]),_:1})]),default:n(()=>[_(" 保存 ")]),_:1})):E("",!0)]),_:1})]),_:1}),o(T,null,{default:n(()=>[o(d(I),{modelValue:a.value,"onUpdate:modelValue":u[0]||(u[0]=B=>a.value=B),language:"json",editorOptions:{lineNumbers:"on",minimap:{enabled:!0}}},null,8,["modelValue"])]),_:1})]),_:1})])}}});const ne=R(H,[["__scopeId","data-v-9c28ebc7"]]);export{ne as default};
