<!--
/**
 * 资产 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-11-13 18:13:53
 */
 -->
<!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <title th:text="${lang.translate('批量导入')}">批量导入</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <div id="data-form" lay-filter="data-form" class="layui-form model-form" style="display:none">


        <!--开始：group 循环-->


        <div class="layui-row form-row" id="default-content">

            <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column" >
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('导入说明')}">导入说明</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <textarea   readonly lay-filter="importInfo" id="importInfo" name="importInfo"   class="layui-textarea" style="resize:none;height: 80px" ></textarea>
                    </div>
                </div>
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('模版文件')}">模版文件</div></div>
                    <div class="layui-upload layui-input-block layui-input-block-c1">
                        <input input-type="upload" id="tpl-attach"  name="attach" lay-filter="attach" style="display: none">
                        <button type="button" class="layui-btn" id="tpl-attach-button" th:text="${lang.translate('模版文件下载')}">模版文件下载</button>
                    </div>
                </div>


                <div style="height:20px;"></div>
                <!-- upload : 附件 ,  attach  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1">
                        <div th:text="${lang.translate('选择文件')}">选择文件</div>
                    </div>
                    <div class="layui-upload layui-input-block layui-input-block-c1">
                        <input type="file" id="attach"  name="attach" lay-filter="attach"  >
                    </div>
                </div>


                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 50px"></div>
    </div>

    <div id="loading"></div>
    <div style="width:100%;text-align:center">
        <button type="button" class="layui-btn" id="attach-button" th:text="${lang.translate('上传提交')}">上传提交</button>
    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var VALIDATE_CONFIG={};
    var AUTH_PREFIX="common_user_batch_import";


</script>


<script th:src="'/business/common/user_import/data_import_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/common/user_import/data_import_list.js?'+${cacheKey}"></script>

</body>
</html>
