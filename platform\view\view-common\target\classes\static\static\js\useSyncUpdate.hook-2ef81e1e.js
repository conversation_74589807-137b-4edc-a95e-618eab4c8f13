var F=Object.defineProperty,B=Object.defineProperties;var V=Object.getOwnPropertyDescriptors;var O=Object.getOwnPropertySymbols;var k=Object.prototype.hasOwnProperty,G=Object.prototype.propertyIsEnumerable;var w=(e,t,o)=>t in e?F(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,l=(e,t)=>{for(var o in t||(t={}))k.call(t,o)&&w(e,o,t[o]);if(O)for(var o of O(t))G.call(t,o)&&w(e,o,t[o]);return e},u=(e,t)=>B(e,V(t));var h=(e,t,o)=>new Promise((s,r)=>{var d=n=>{try{E(o.next(n))}catch(i){r(i)}},f=n=>{try{E(o.throw(n))}catch(i){r(i)}},E=n=>n.done?s(n.value):Promise.resolve(n.value).then(d,f);E((o=o.apply(e,t)).next())});import{M as g,ck as S,aN as p,aO as T,aB as m,C as I,L as $,J as U,ap as D}from"./index-bb2cbf17.js";import{u as A,a as K,C as L,e as J,f as x}from"./chartEditStore-55fbe93c.js";import{u as Q,C as _}from"./chartLayoutStore-88198b25.js";import{f as W,d as b,e as q}from"./index-0ec04aee.js";import{g as z}from"./plugin-3ef0fcec.js";const X=e=>e,Y=(e,t)=>{try{if(t.id){const o="vnodeBeforeMount"in t.events,s="vnodeMounted"in t.events;return o&&(e.events.advancedEvents.vnodeBeforeMount=t==null?void 0:t.events.vnodeBeforeMount),s&&(e.events.advancedEvents.vnodeMounted=t==null?void 0:t.events.vnodeMounted),(o||s)&&(t.events={baseEvent:{[p.ON_CLICK]:void 0,[p.ON_DBL_CLICK]:void 0,[p.ON_MOUSE_ENTER]:void 0,[p.ON_MOUSE_LEAVE]:void 0},advancedEvents:{[T.VNODE_MOUNTED]:void 0,[T.VNODE_BEFORE_MOUNT]:void 0},interactEvents:[]}),e}}catch(o){return e}},C=(e,t,o=!1)=>{if(Y(e,t),o)return S(e,t);const s=t.option;if(!s)return S(e,t);if(t.option=void 0,s)return u(l({},S(e,t)),{option:s})},Z=()=>{const e=A(),t=K(),o=Q();return{updateComponent:(r,d=!1,f=!1)=>h(void 0,null,function*(){d&&(e.componentList=[],t.clearBackStack(),t.clearForwardStack()),r.editCanvasConfig=X(r.editCanvasConfig),r.componentList.forEach(n=>h(void 0,null,function*(){const i=a=>{window.$vue.component(a.chartConfig.chartKey)||(window.$vue.component(a.chartConfig.chartKey,W(a.chartConfig)),window.$vue.component(a.chartConfig.conKey,b(a.chartConfig)))};n.isGroup?n.groupList.forEach(a=>{i(a)}):i(n)}));const E=(n,i)=>h(void 0,null,function*(){let a=yield q(n.chartConfig);n.chartConfig.redirectComponent&&(n.chartConfig.dataset&&(a.option.dataset=n.chartConfig.dataset),a.chartConfig.title=n.chartConfig.title,a.chartConfig.chartFrame=n.chartConfig.chartFrame),i?i(f?C(a,u(l({},n),{id:g()})):C(a,n)):f?e.addComponentList(C(a,u(l({},n),{id:g()})),!1,!0):e.addComponentList(C(a,n),!1,!0)});for(const n in r)if(n===L.COMPONENT_LIST){let i=0;const a=r[n].length;for(const v of r[n]){let N=parseInt((parseFloat(`${++i/a}`)*100).toString());if(o.setItemUnHandle(_.PERCENTAGE,N),v.isGroup){let c=new J;f?c=C(c,u(l({},v),{id:g()})):c=C(c,v);const M=[];for(const P of v.groupList)yield E(P,R=>{M.push(R)});c.groupList=M,e.addComponentList(c,!1,!0)}else yield E(v);N===100&&(t.clearBackStack(),t.clearForwardStack())}}else(n===L.EDIT_CANVAS_CONFIG||n===L.REQUEST_GLOBAL_CONFIG)&&C(e[n],r[n],!0);o.setItemUnHandle(_.PERCENTAGE,0)})}},{updateComponent:j}=Z(),y=A(),rt=()=>{z({message:"是否覆盖源视图内容，此操作不可撤回?",isMaskClosable:!0,transformOrigin:"center",onPositiveCallback:()=>{window.$message.success("正在同步编辑器..."),dispatchEvent(new CustomEvent(m.CHART,{detail:y.getStorageInfo()}))}})},H=()=>{dispatchEvent(new CustomEvent(m.CHART_TO_PREVIEW,{detail:y.getStorageInfo()}))},tt=()=>{const e=d=>{window.$message.success("正在进行更新..."),j(d.detail,!0)},t=()=>{y.setEditCanvas(x.IS_CODE_EDIT,!1)},o=()=>{addEventListener("blur",H),addEventListener(m.JSON,e),addEventListener(m.CLOSE,D(t,1e3))},s=()=>{removeEventListener("blur",H),removeEventListener(m.JSON,e)};return(d,f)=>{f==U.CHART_HOME_NAME&&s(),d==U.CHART_HOME_NAME&&o()}},dt=()=>{const e=I();$(()=>e.name,tt(),{immediate:!0})};export{dt as a,rt as s,Z as u};
