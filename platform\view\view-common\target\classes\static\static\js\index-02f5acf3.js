import{d as D,b8 as N,m as H,l as L,a0 as m,aw as M,r,o as s,c,e as o,w as a,u as f,F as h,s as g,E as O,q as u,f as R,t as $,an as v,ao as q,aa as F,a8 as P,A as j,B as U,j as G}from"./index-bb2cbf17.js";import{u as J,E as K}from"./chartEditStore-55fbe93c.js";import{i as Q}from"./icon-f36697ff.js";import{l as W}from"./index-56351f34.js";import"./plugin-3ef0fcec.js";const X=n=>(j("data-v-4ce90fea"),n=n(),U(),n),Y={class:"go-chart-theme-color"},Z=X(()=>u("span",null,"自定义颜色",-1)),ee={class:"go-flex-items-center"},oe=D({__name:"index",setup(n){N(e=>({d133daf6:w.value}));const x=W(()=>F(()=>import("./index-6e48eddf.js"),["static/js/index-6e48eddf.js","static/js/index-bb2cbf17.js","static/css/index-a8adda2b.css","static/js/noData-9e194391.js","static/js/plugin-3ef0fcec.js","static/js/icon-f36697ff.js","static/js/chartEditStore-55fbe93c.js","static/js/index-56351f34.js","static/css/index-83eadabc.css","static/css/index-db178c17.css"])),{SquareIcon:te,AddIcon:S}=Q.ionicons5,l=J(),E=H(),d=L(!1),b=m(()=>M(l.getEditCanvasConfig.chartCustomThemeColorInfo)),w=m(()=>E.getAppTheme),I=m(()=>l.getEditCanvasConfig.chartThemeColor),k=()=>{d.value=!0},T=e=>`linear-gradient(to right, ${e.color[0]} 0%, ${e.color[5]} 100%)`,y=e=>P(e).splice(0,6),A=e=>{l.setEditCanvasConfig(K.CHART_THEME_COLOR,e)};return(e,p)=>{const B=r("n-icon"),z=r("n-text"),C=r("n-card"),V=r("n-ellipsis");return s(),c("div",Y,[o(C,{class:"card-box",size:"small",hoverable:"",embedded:"",onClick:k},{default:a(()=>[o(z,{class:"go-flex-items-center"},{default:a(()=>[Z,o(B,{size:"16"},{default:a(()=>[o(f(S))]),_:1})]),_:1})]),_:1}),(s(!0),c(h,null,g(b.value,(t,i)=>(s(),O(C,{key:i,class:q(["card-box",{selected:i===I.value}]),size:"small",hoverable:"",embedded:"",onClick:_=>A(i)},{default:a(()=>[u("div",ee,[o(V,{style:{"text-align":"left",width:"60px"}},{default:a(()=>[R($(t.name),1)]),_:2},1024),(s(!0),c(h,null,g(y(t.color),_=>(s(),c("span",{class:"theme-color-item",key:_,style:v({backgroundColor:_})},null,4))),128))]),u("div",{class:"theme-bottom",style:v({backgroundImage:T(t)})},null,4)]),_:2},1032,["class","onClick"]))),128)),o(f(x),{modelShow:d.value,"onUpdate:modelShow":p[0]||(p[0]=t=>d.value=t)},null,8,["modelShow"])])}}});const le=G(oe,[["__scopeId","data-v-4ce90fea"]]);export{le as default};
