<!--
/**
 * 财务分类 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2021-09-10 13:01:39
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <title th:text="${lang.translate('分类属性')}">分类属性</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/assets/css/admin.css"/>
    <link rel="stylesheet" href="/assets/css/foxnic-web.css">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" th:href="@{/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css}" rel="stylesheet"/>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">

</head>

<body style="overflow-y: hidden">


<form id="data-form" lay-filter="data-form" class="layui-form model-form" >


        <div class="layui-form-item"  >
            <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('主键')}">主键</div></div>
            <div class="layui-input-block layui-input-block-c1">
                <input style="background-color:#e6e6e6" disabled="disabled" lay-filter="id" id="id" name="id" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('主键') }" type="text" class="layui-input"  />
            </div>
        </div>


    <div class="layui-form-item" >
        <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('节点路径')}">节点路径</div></div>
        <div class="layui-input-block layui-input-block-c1">
            <input  style="background-color:#e6e6e6" disabled="disabled" type="text"  lay-filter="hierarchyName" id="hierarchyName" name="hierarchyName" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('节点路径名称') }" class="layui-input"   ></input>
        </div>
    </div>



    <div class="layui-form-item" >
        <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('编码')}">编码</div></div>
        <div class="layui-input-block layui-input-block-c1">
            <input lay-filter="categoryCode" id="categoryCode" name="categoryCode" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('编码') }" type="text" class="layui-input"  />
        </div>
    </div>


    <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('名称')}">名称</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="categoryName" id="categoryName" name="categoryName" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('名称') }" type="text" class="layui-input"  />
                    </div>
                </div>


    <!-- number_input : 使用期限 ,  serviceLife  -->
    <div class="layui-form-item" >
        <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('使用期限')}">使用期限</div><div class="layui-required">*</div></div>
        <div class="layui-input-block layui-input-block-c1">
            <input lay-filter="serviceLife" id="serviceLife" name="serviceLife" th:placeholder="${ lang.translate('请输入'+'使用期限') }" type="text" class="layui-input"    lay-verify="|required"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />
        </div>
    </div>

                                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('备注') }" type="text" class="layui-input"  />
                    </div>
                </div>




        <div class="layui-form-item model-form-footer">

            <button   class="layui-btn"   id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存')}">保存</button>
        </div>
</form>

<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var VALIDATE_CONFIG={"serviceLife":{"labelInForm":"使用期限","inputType":"number_input","required":true},"categoryCode":{"labelInForm":"编码","inputType":"text_input","required":true}};
    var AUTH_PREFIX="eam_category_finance";
</script>

<script type="text/javascript" src="/module/global.js"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js"></script>
<script src="/business/eam/category_finance/category_finance_ext.js"></script>
<script src="/business/eam/category_finance/category_finance_form.js"></script>



</body>
</html>
