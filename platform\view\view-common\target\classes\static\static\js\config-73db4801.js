var l=Object.defineProperty,c=Object.defineProperties;var f=Object.getOwnPropertyDescriptors;var n=Object.getOwnPropertySymbols;var C=Object.prototype.hasOwnProperty,b=Object.prototype.propertyIsEnumerable;var i=(a,t,o)=>t in a?l(a,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):a[t]=o,r=(a,t)=>{for(var o in t||(t={}))C.call(t,o)&&i(a,o,t[o]);if(n)for(var o of n(t))b.call(t,o)&&i(a,o,t[o]);return a},p=(a,t)=>c(a,f(t));var e=(a,t,o)=>(i(a,typeof t!="symbol"?t+"":t,o),o);import{aM as T,a8 as m,cy as u}from"./index-bb2cbf17.js";import{d as E}from"./chartEditStore-55fbe93c.js";import{z as s,A as g,B as A}from"./index-0ec04aee.js";import"./plugin-3ef0fcec.js";import"./icon-f36697ff.js";import"./SettingItem-7fe1cfec.js";/* empty css                                                                      */import"./SettingItemBox-500aaf18.js";import"./CollapseItem.vue_vue_type_script_setup_true_lang-9b6d8958.js";import"./index.esm.min-f1367f57.js";import"./http-36f53bd1.js";import"./lodash-d17632fd.js";import"./fileTypeEnum-21359a08.js";const d={[u]:A.DATA,tabLabel:"选项1",tabType:"segment",dataset:[{label:"选项1",value:"1"},{label:"选项2",value:"2"},{label:"选项3",value:"3"}]};class B extends E{constructor(){super(...arguments);e(this,"key",s.key);e(this,"attr",p(r({},T),{w:460,h:32,zIndex:-1}));e(this,"chartConfig",m(s));e(this,"interactActions",g);e(this,"option",m(d))}}export{B as default,d as option};
