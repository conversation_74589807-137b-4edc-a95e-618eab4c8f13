var u=Object.defineProperty,p=Object.defineProperties;var d=Object.getOwnPropertyDescriptors;var m=Object.getOwnPropertySymbols;var c=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable;var n=(e,t,o)=>t in e?u(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,i=(e,t)=>{for(var o in t||(t={}))c.call(t,o)&&n(e,o,t[o]);if(m)for(var o of m(t))f.call(t,o)&&n(e,o,t[o]);return e},r=(e,t)=>p(e,d(t));var a=(e,t,o)=>(n(e,typeof t!="symbol"?t+"":t,o),o);import{aM as y,a8 as l}from"./index-bb2cbf17.js";import{d as k}from"./chartEditStore-55fbe93c.js";import{K as s}from"./index-0ec04aee.js";import"./plugin-3ef0fcec.js";import"./icon-f36697ff.js";import"./SettingItem-7fe1cfec.js";/* empty css                                                                      */import"./SettingItemBox-500aaf18.js";import"./CollapseItem.vue_vue_type_script_setup_true_lang-9b6d8958.js";import"./index.esm.min-f1367f57.js";import"./http-36f53bd1.js";import"./lodash-d17632fd.js";import"./fileTypeEnum-21359a08.js";const S=[{title:"产品名称",key:"productName"},{title:"产品销量（万）",key:"totalSum"},{title:"销售额（万）",key:"totalAmount"}],g=[{key:0,productName:"产品A1",totalSum:10,totalAmount:10},{key:1,productName:"产品B1",totalSum:10,totalAmount:10},{key:2,productName:"产品C1",totalSum:10,totalAmount:10},{key:3,productName:"产品D1",totalSum:10,totalAmount:10},{key:4,productName:"产品A2",totalSum:10,totalAmount:10},{key:5,productName:"产品D2",totalSum:10,totalAmount:10},{key:6,productName:"产品A3",totalSum:10,totalAmount:10}],A={dimensions:S,source:g},{dimensions:C,source:b}=A,N={dataset:{dimensions:C,source:b},pagination:{page:1,pageSize:5},align:"center",style:{border:"on",singleColumn:"off",singleLine:"off",bottomBordered:"on",striped:"on",fontSize:16,borderWidth:0,borderColor:"black",borderStyle:"solid"},inputShow:"none"};class W extends k{constructor(){super(...arguments);a(this,"key",s.key);a(this,"attr",r(i({},y),{w:600,h:300,zIndex:-1}));a(this,"chartConfig",l(s));a(this,"option",l(N))}}export{W as default,N as option};
