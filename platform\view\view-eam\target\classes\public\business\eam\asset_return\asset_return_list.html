<!--
/**
 * 资产退库 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2021-08-18 11:57:36
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <title th:text="${lang.translate('资产退库')}">资产退库</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" th:href="@{/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css}" rel="stylesheet"/>
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon"> <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body">

        <div class="search-bar">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 业务日期 , businessDate ,typeName=date_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div"><span th:text="${lang.translate('业务日期')}" class="search-label">业务日期</span><span class="search-colon">:</span></div>
                            <input type="text" id="businessDate"  style="width: 140px"  lay-verify="date" th:placeholder="${lang.translate('请选择')}" autocomplete="off" class="layui-input search-input search-date-input" readonly>
                    </div>
                    <!-- 制单人 , originatorId ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div"><span th:text="${lang.translate('制单人')}" class="search-label">制单人</span><span class="search-colon">:</span></div>
                        <input id="originatorId" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>
                    <!-- 退库说明 , content ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div"><span th:text="${lang.translate('退库说明')}" class="search-label">退库说明</span><span class="search-colon">:</span></div>
                        <input id="content" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 办理状态 , status ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div"><span th:text="${lang.translate('办理状态')}" class="search-label">办理状态</span><span class="search-colon">:</span></div>
                        <input id="status" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索')}">搜索</span></button>
                <button id="search-button-advance" class="layui-btn layui-btn-primary icon-btn search-button-advance"><i class="layui-icon">&#xe671;</i><span th:text="${lang.translate('更多')}">更多</span></button>
            </div>
        </div>

        <div style="margin-top: 44px">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>

<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${perm.checkAuth('eam_asset_return:create')}" id="add-button" class="layui-btn icon-btn layui-btn-sm" lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建')}">新建</span></button>
            <button th:if="${perm.checkAuth('eam_asset_return:delete-by-ids')}" id="delete-button" class="layui-btn icon-btn layui-btn-danger layui-btn-sm" lay-event="batch-del"><i class="layui-icon">&#xe67e;</i><span th:text="${lang.translate('删除')}">删除</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">
    <a th:if="${perm.checkAuth('eam_asset_return:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit" th:text="${lang.translate('修改')}">修改</a>
    <a th:if="${perm.checkAuth('eam_asset_return:delete')}" class="layui-btn layui-btn-xs" lay-event="del" th:text="${lang.translate('删除')}">删除</a>
</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var AUTH_PREFIX="eam_asset_return";
</script>

<script th:src="'/business/eam/asset_return/asset_return_list.js?'+${cacheKey}"></script>

</body>
</html>
