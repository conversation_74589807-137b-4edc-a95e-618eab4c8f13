var G=Object.defineProperty;var h=Object.getOwnPropertySymbols;var b=Object.prototype.hasOwnProperty,L=Object.prototype.propertyIsEnumerable;var C=(a,t,o)=>t in a?G(a,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):a[t]=o,l=(a,t)=>{for(var o in t||(t={}))b.call(t,o)&&C(a,o,t[o]);if(h)for(var o of h(t))L.call(t,o)&&C(a,o,t[o]);return a};import{d as B,C as D,a0 as H,r as E,o as _,E as f,w as g,c as k,F as N,s as P,G as j,q as F,t as V,X as I,N as $,b0 as q,b1 as T,S as d,aC as S,O as J,aD as M,b2 as W,j as X}from"./index-bb2cbf17.js";import{i as z}from"./icon-f36697ff.js";import{u as K}from"./chartEditStore-55fbe93c.js";import{s as Q}from"./useSyncUpdate.hook-2ef81e1e.js";import{l as U}from"./lodash-d17632fd.js";import"./plugin-3ef0fcec.js";import"./chartLayoutStore-88198b25.js";import"./index-0ec04aee.js";import"./SettingItem-7fe1cfec.js";/* empty css                                                                      */import"./SettingItemBox-500aaf18.js";import"./CollapseItem.vue_vue_type_script_setup_true_lang-9b6d8958.js";import"./index.esm.min-f1367f57.js";import"./http-36f53bd1.js";import"./fileTypeEnum-21359a08.js";const Y=B({__name:"index",setup(a){const{BrowsersOutlineIcon:t,SendIcon:o,AnalyticsIcon:x}=z.ionicons5,m=K(),v=D(),A=()=>{const s=$(q.CHART_PREVIEW_NAME,"href");if(!s)return;const{id:r}=v.params,n=typeof r=="string"?r:r[0],c=m.getStorageInfo();console.log("storageInfo",c);const e=T(d.GO_CHART_STORAGE_LIST)||[];if(e!=null&&e.length){const i=e.findIndex(p=>p.id===n);i!==-1?(e.splice(i,1,l({id:n},c)),S(d.GO_CHART_STORAGE_LIST,e)):(e.push(l({id:n},c)),S(d.GO_CHART_STORAGE_LIST,e))}else S(d.GO_CHART_STORAGE_LIST,[l({id:n},c)]);J(s,[n],void 0,!0)},O=()=>{const{id:s}=v.params,r=typeof s=="string"?s:s[0];console.log("previewId:",r);const n=m.getStorageInfo(),c=T(d.GO_CHART_STORAGE_LIST)||[];var e=[l({id:r},n)];console.log("storageInfo2",e),console.log("sessionStorageInfo",c);var i=M(e);console.log(i);var p={id:"",jsonData:"[]"};p.id=r,p.jsonData=i,W.post("/service-common/sys-screen/update",p).then(function(u){var w=u.data;if(w.success)try{window.$message.success("保存成功")}catch(Z){}else window.$message.error("保存失败")}).catch(u=>{console.log(u)})},y=[{select:!0,title:"同步内容",type:"primary",icon:I(x),event:Q},{select:!0,title:"预览",icon:I(t),event:A},{select:!0,title:"保存",icon:I(o),event:O}],R=H(()=>{if(m.getEditCanvas.isCodeEdit)return y;const s=U.cloneDeep(y);return s.shift(),s});return(s,r)=>{const n=E("n-button"),c=E("n-space");return _(),f(c,{class:"go-mt-0"},{default:g(()=>[(_(!0),k(N,null,P(R.value,e=>(_(),f(n,{key:e.title,type:e.type,ghost:"",onClick:e.event},{icon:g(()=>[(_(),f(j(e.icon)))]),default:g(()=>[F("span",null,V(e.title),1)]),_:2},1032,["type","onClick"]))),128))]),_:1})}}});const ge=X(Y,[["__scopeId","data-v-66a5b3ab"]]);export{ge as default};
