function getFileId(){var b,a=location.search;return-1!=a.indexOf("?")?(b=a.substr(1),strs=b.split("="),strs[1]):""}function playVideo(){$("#playerbox").html("<video id='kiftplayer' class='video-js col-md-12' controls preload='auto' height='500'><source src='resourceController/getResource/"+f.fileId+"' type='video/mp4'></video>");var a=videojs("kiftplayer",{preload:"auto"});a.ready(function(){this.play()})}function reMainPage(){null!=tReq&&tReq.abort(),null!=tTimer&&window.clearTimeout(tTimer),window.opener=null,window.open("","_self"),window.close()}function doTranscode(){tReq=$.ajax({url:"resourceController/getVideoTranscodeStatus.ajax",type:"POST",dataType:"text",data:{fileId:f.fileId},success:function(a){"FIN"==a?playVideo():"ERROR"==a?(alert("错误：请求失败，请刷新重试。"),reMainPage()):($("#transcodeProgress").text(a),tTimer=setTimeout("doTranscode()",500))},error:function(){alert("错误：请求失败，请刷新重试。"),reMainPage()}})}function showCloseBtn(){var a=$(window).width();450>a?$("#closeBtn").addClass("hidden"):$("#closeBtn").removeClass("hidden")}function ping(){$.ajax({url:"homeController/ping.ajax",type:"POST",dataType:"text",data:{},success:function(a){"pong"!=a&&window.clearInterval(pingInt)},error:function(){window.clearInterval(pingInt)}})}var tReq,tTimer,pingInt;$(function(){window.onresize=function(){showCloseBtn()},pingInt=setInterval("ping()",6e4);var fileId=getFileId();$.ajax({url:"homeController/playVideo.ajax",type:"POST",dataType:"text",data:{fileId:fileId},success:function(result){if("ERROR"!=result){f=eval("("+result+")"),$("#vname").text(f.fileName),$("#vcreator").text(f.fileCreator),$("#vcdate").text(f.fileCreationDate);var fileSizeToInt=parseInt(f.fileSize);0==fileSizeToInt?$("#vsize").text("<1 MB"):1e3>fileSizeToInt?$("#vsize").text(fileSizeToInt+" MB"):1024e3>fileSizeToInt?$("#vsize").text((fileSizeToInt/1024).toFixed(2)+" GB"):$("#vsize").text((fileSizeToInt/1048576).toFixed(2)+" TB"),"N"==f.needEncode?playVideo():($("#playerMassage").html("<h2>播放器正在努力解码中...</h2><h3>已完成：<span id='transcodeProgress'>0</span>%</h3><p class='text-muted'>提示：该视频需解码后播放，请耐心等待！</p>"),doTranscode())}else alert("错误：无法定位要预览的文件或该操作未被授权。"),reMainPage()},error:function(){alert("错误：请求失败，请刷新重试。"),reMainPage()}})});