/* empty css                                                                      */import{d as y,r as l,o as d,E as A,w as r,e as n,u as e,c as x,F as I,s as V,G as w,f as _,X as m,a5 as H,j}from"./index-bb2cbf17.js";import"./chartEditStore-55fbe93c.js";import{S as v}from"./SettingItemBox-500aaf18.js";import{i as q}from"./icon-f36697ff.js";import{S as D}from"./StylesSetting-d5a665a6.js";import{u as U}from"./useTargetData.hook-a16b3b4d.js";import"./plugin-3ef0fcec.js";import"./SettingItem-7fe1cfec.js";import"./CollapseItem.vue_vue_type_script_setup_true_lang-9b6d8958.js";import"./logo-aa8b8747.js";const $=y({__name:"NameSetting",props:{chartConfig:{type:Object,required:!0}},setup(t){const a=t;let i="";const s=()=>{i=a.chartConfig.title},u=()=>{a.chartConfig.title.length||(window.$message.warning("请输入至少一个字符!"),a.chartConfig.title=i)};return(h,g)=>{const c=l("n-input");return d(),A(e(v),{name:"名称",alone:!0},{default:r(()=>[n(c,{type:"text",maxlength:"12",minlength:"1",placeholder:"请输入图表名称",size:"small",clearable:"","show-count":"",value:t.chartConfig.title,"onUpdate:value":g[0]||(g[0]=o=>t.chartConfig.title=o),onFocus:s,onBlur:u},null,8,["value"])]),_:1})}}}),E=y({__name:"PositionSetting",props:{canvasConfig:{type:Object,required:!0},chartAttr:{type:Object,required:!0}},setup(t){const{AlignHorizontalLeftIcon:a,AlignVerticalCenterIcon:i,AlignVerticalTopIcon:s,AlignHorizontalCenterIcon:u,AlignHorizontalRightIcon:h,AlignVerticalBottomIcon:g}=q.carbon,c=[{key:"AlignHorizontalLeftIcon",lable:"局左",icon:m(a)},{key:"AlignVerticalCenterIcon",lable:"X轴居中",icon:m(i)},{key:"AlignHorizontalRightIcon",lable:"局右",icon:m(h)},{key:"AlignVerticalTopIcon",lable:"顶部",icon:m(s)},{key:"AlignHorizontalCenterIcon",lable:"Y轴居中",icon:m(u)},{key:"AlignVerticalBottomIcon",lable:"底部",icon:m(g)}],o=t,z=C=>{switch(C){case c[0].key:o.chartAttr.x=0;break;case c[1].key:o.chartAttr.y=(o.canvasConfig.height-o.chartAttr.h)/2;break;case c[2].key:o.chartAttr.x=o.canvasConfig.width-o.chartAttr.w;break;case c[3].key:o.chartAttr.y=0;break;case c[4].key:o.chartAttr.x=(o.canvasConfig.width-o.chartAttr.w)/2;break;case c[5].key:o.chartAttr.y=o.canvasConfig.height-o.chartAttr.h;break}};return(C,f)=>{const S=l("n-divider"),G=l("n-button"),B=l("n-space"),b=l("n-text"),k=l("n-input-number");return d(),x(I,null,[n(S,{style:{margin:"10px 0"}}),n(B,{size:8,justify:"space-between",style:{"margin-top":"10px"}},{default:r(()=>[(d(),x(I,null,V(c,p=>n(G,{secondary:"",key:p.key,onClick:O=>z(p.key)},{icon:r(()=>[(d(),A(w(p.icon)))]),_:2},1032,["onClick"])),64))]),_:1}),n(e(v),{name:"位置"},{default:r(()=>[n(k,{value:t.chartAttr.y,"onUpdate:value":f[0]||(f[0]=p=>t.chartAttr.y=p),min:0,size:"small",placeholder:"px"},{prefix:r(()=>[n(b,{depth:"3"},{default:r(()=>[_("上")]),_:1})]),_:1},8,["value"]),n(k,{value:t.chartAttr.x,"onUpdate:value":f[1]||(f[1]=p=>t.chartAttr.x=p),min:0,size:"small",placeholder:"px"},{prefix:r(()=>[n(b,{depth:"3"},{default:r(()=>[_("左")]),_:1})]),_:1},8,["value"])]),_:1})],64)}}}),F=y({__name:"SizeSetting",props:{chartAttr:{type:Object,required:!0},isGroup:{type:Boolean,required:!1}},setup(t){return(a,i)=>{const s=l("n-text"),u=l("n-input-number");return d(),A(e(v),{name:"尺寸"},{default:r(()=>[n(u,{value:t.chartAttr.w,"onUpdate:value":i[0]||(i[0]=h=>t.chartAttr.w=h),min:50,disabled:t.isGroup,size:"small",placeholder:"px"},{prefix:r(()=>[n(s,{depth:"3"},{default:r(()=>[_("宽度")]),_:1})]),_:1},8,["value","disabled"]),n(u,{value:t.chartAttr.h,"onUpdate:value":i[1]||(i[1]=h=>t.chartAttr.h=h),min:50,disabled:t.isGroup,size:"small",placeholder:"px"},{prefix:r(()=>[n(s,{depth:"3"},{default:r(()=>[_("高度")]),_:1})]),_:1},8,["value","disabled"])]),_:1})}}}),L={key:0,class:"go-chart-configurations-setting"},N=y({__name:"index",setup(t){const{targetData:a,chartEditStore:i}=U();return(s,u)=>e(a)?(d(),x("div",L,[n(e($),{chartConfig:e(a).chartConfig},null,8,["chartConfig"]),n(e(F),{isGroup:e(a).isGroup,chartAttr:e(a).attr},null,8,["isGroup","chartAttr"]),n(e(E),{chartAttr:e(a).attr,canvasConfig:e(i).getEditCanvasConfig},null,8,["chartAttr","canvasConfig"]),n(e(D),{isGroup:e(a).isGroup,chartStyles:e(a).styles},null,8,["isGroup","chartStyles"]),(d(),A(w(e(a).chartConfig.conKey),{optionData:e(a).option},null,8,["optionData"]))])):H("",!0)}});const tt=j(N,[["__scopeId","data-v-246c8460"]]);export{tt as default};
