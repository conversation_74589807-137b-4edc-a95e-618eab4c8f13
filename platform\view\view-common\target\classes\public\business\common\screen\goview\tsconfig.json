{
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    "moduleResolution": "node",
    "strict": true,
    "jsx": "preserve",
    "baseUrl": ".",
    "sourceMap": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "lib": ["es6", "ESNext", "dom"],
    "types": ["vite/client"],
    "paths": {
      "@/*": ["src/*"],
      "/#/*": ["types/*"]
    },
    "noImplicitAny": true, //不允许使用any
    // "strictNullChecks": true, //不允许使用null
    "noImplicitThis": true //不允许往this上面挂属性
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "types/**/*"],
  "exclude": ["node_modules", "dist", "**/*.js"]
}
