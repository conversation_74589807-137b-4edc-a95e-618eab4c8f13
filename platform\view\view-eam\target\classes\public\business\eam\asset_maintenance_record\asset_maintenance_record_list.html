<!--
/**
 * 维保更新记录 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-10-29 13:55:08
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <title th:text="${lang.translate('维保更新记录')}">维保更新记录</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 维保更新 , maintenanceUpdateId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 资产 , assetId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 备注 , maintenanceNotes ,typeName=text_input, isHideInSearch=true -->
                    <!-- 备注[状态] , sMaintenanceNotes ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 维保商 , maintainerId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('维保商')}" class="search-label maintainerId-label">维保商</span><span class="search-colon">:</span></div>
                        <div id="maintainerId" th:data="${'/service-eam/eam-maintainer/query-list'}" style="width:150px" extraParam="{}"></div>
                    </div>
                    <!-- 维保厂商[状态] , sMaintainerId ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 维保厂商名称 , maintainerName ,typeName=text_input, isHideInSearch=true -->
                    <!-- 维保厂商名称[状态] , sMaintainerName ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 维保状态[状态] , sMaintenanceStatus ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 维保方式[状态] , sMaintenanceMethod ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 建议维保方式[状态] , sSuggestMaintenanceMethod ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 联系人 , contacts ,typeName=text_input, isHideInSearch=true -->
                    <!-- 联系人[状态] , sContacts ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 联系方式 , contactInformation ,typeName=text_input, isHideInSearch=true -->
                    <!-- 联系方式[状态] , sContactInformation ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 负责人 , director ,typeName=text_input, isHideInSearch=true -->
                    <!-- 负责人[状态] , sDirector ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 维保开始时间 , maintenanceStartDate ,typeName=date_input, isHideInSearch=true -->
                    <!-- 维保开始时间[状态] , sMaintenanceStartDate ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 维保到期时间 , maintenanceEndDate ,typeName=date_input, isHideInSearch=true -->
                    <!-- 维保到期时间[状态] , sMaintenanceEndDate ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 资产名称 , assetAssetName ,typeName=text_input, isHideInSearch=true -->
                    <!-- 资产编码 , assetAssetCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 资产型号 , assetAssetModel ,typeName=text_input, isHideInSearch=true -->
                    <!-- 资产状态 , assetAssetStatus ,typeName=text_input, isHideInSearch=true -->
                    <!-- 资产分类 , assetAssetCatalog ,typeName=text_input, isHideInSearch=true -->
                    <!-- 维保状态 , maintenanceStatus ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('维保状态')}" class="search-label maintenanceStatus-label">维保状态</span><span class="search-colon">:</span></div>
                        <div id="maintenanceStatus" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_maintenance_status'}" style="width:150px" extraParam="{}"></div>
                    </div>
                    <!-- 维保方式 , maintenanceMethod ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('维保方式')}" class="search-label maintenanceMethod-label">维保方式</span><span class="search-colon">:</span></div>
                        <div id="maintenanceMethod" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_maintenance_method'}" style="width:150px" extraParam="{}"></div>
                    </div>
                    <!-- 建议维保方式 , suggestMaintenanceMethod ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('建议维保方式')}" class="search-label suggestMaintenanceMethod-label">建议维保方式</span><span class="search-colon">:</span></div>
                        <div id="suggestMaintenanceMethod" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_suggest_maintenance_method'}" style="width:150px" extraParam="{}"></div>
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索')}">搜索</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 42px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${perm.checkAuth('eam_asset_maintenance_record:create')}" id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建')}">新建</span></button>
        <button th:if="${perm.checkAuth('eam_asset_maintenance_record:delete-by-ids')}" id="delete-button" class="layui-btn icon-btn layui-btn-danger layui-btn-sm batch-delete-button " lay-event="batch-del"><i class="layui-icon">&#xe67e;</i><span th:text="${lang.translate('删除')}">删除</span></button>
        <button id="select-asset"  class="layui-btn icon-btn layui-btn-sm " lay-event="tool-select-asset"><span th:text="${lang.translate('选择资产')}">选择资产</span></button>
        <button id="import-asset"  class="layui-btn icon-btn layui-btn-sm " lay-event="tool-import-asset"><span th:text="${lang.translate('导入资产')}">导入资产</span></button>
        <button id="batch-modify"  class="layui-btn icon-btn layui-btn-sm " lay-event="tool-batch-modify"><span th:text="${lang.translate('批量修改')}">批量修改</span></button>
        <button id="data-fill"  class="layui-btn icon-btn layui-btn-sm " lay-event="tool-data-fill"><span th:text="${lang.translate('数据填充')}">数据填充</span></button>
        <button id="finish"  class="layui-btn icon-btn layui-btn-sm " lay-event="tool-finish"><span th:text="${lang.translate('完成操作')}">完成操作</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${perm.checkAnyAuth('eam_asset_maintenance_record:update','eam_asset_maintenance_record:save')}" class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit"data-id="{{d.id}}"><span th:text="${lang.translate('修改')}">修改</span></button>




</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var RADIO_SMAINTENANCENOTES_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_SMAINTAINERID_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_SMAINTAINERNAME_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_SMAINTENANCESTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_SMAINTENANCEMETHOD_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_SSUGGESTMAINTENANCEMETHOD_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_SCONTACTS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_SCONTACTINFORMATION_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_SDIRECTOR_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_SMAINTENANCESTARTDATE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_SMAINTENANCEENDDATE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var AUTH_PREFIX="eam_asset_maintenance_record";

    // maintenanceUpdateId
    var MAINTENANCE_UPDATE_ID = [[${maintenanceUpdateId}]] ;
    // maintenanceUpdateData
    var MAINTENANCE_UPDATE_DATA = [[${maintenanceUpdateData}]] ;

</script>

<script th:src="'/business/eam/asset_maintenance_record/asset_maintenance_record_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset_maintenance_record/asset_maintenance_record_list.js?'+${cacheKey}"></script>

</body>
</html>
