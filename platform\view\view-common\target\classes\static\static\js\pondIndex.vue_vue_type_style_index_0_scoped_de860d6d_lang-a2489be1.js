import{d as z,l as L,p as ke,L as xe,r as o,o as u,E as k,w as n,q as d,c as D,F as M,s as A,t as I,e as t,f,Z as N,cS as b,u as e,a5 as j,b8 as De,m as Ue,a0 as Ee,bd as C,Q as W,R as Y,j as Q,aJ as F,cU as Te,aK as $,aH as m,cV as Se}from"./index-bb2cbf17.js";import{S as H}from"./SettingItem-7fe1cfec.js";import{S as V}from"./SettingItemBox-500aaf18.js";import{i as K}from"./icon-f36697ff.js";import"./chartEditStore-55fbe93c.js";/* empty css                                                                      */import{u as X}from"./useTargetData.hook-a16b3b4d.js";import{s as Z,a as Re}from"./index-819682d7.js";import{M as J}from"./EditorWorker-54a88558.js";import"./editorWorker-43a98755.js";const qe=d("thead",null,[d("tr",null,[d("th"),d("th",null,"Key"),d("th",null,"Value"),d("th",null,"操作"),d("th",null,"结果")])],-1),Ce={style:{width:"80px"}},P=z({__name:"index",props:{target:{type:Object,required:!0,default:()=>{}},editDisabled:{type:Boolean,required:!1,default:!1}},emits:["update"],setup(h,{emit:q}){const S=q,p=h;L(!1);const R={key:"",value:"",error:!1},s=ke({content:[]}),r=()=>{let _=0;if(s.content.forEach(l=>{l.key!==""&&l.value==""||l.key===""&&l.value!==""?l.error=!0:(_++,l.error=!1)}),_==s.content.length){const l={};s.content.forEach(i=>{i.key&&(l[i.key]=i.value)}),S("update",l)}},c=_=>{s.content.splice(_+1,0,{key:"",value:"",error:!1})},y=_=>{s.content.length!==1&&s.content.splice(_,1),r()};return xe(()=>p.target,_=>{s.content=[];for(const l in _)s.content.push({key:l,value:_[l],error:!1});s.content.length||(s.content=[JSON.parse(JSON.stringify(R))])},{immediate:!0,deep:!0}),(_,l)=>{const i=o("n-input"),x=o("n-button"),g=o("n-table");return u(),k(g,{class:"go-request-header-table-box","single-line":!1,size:"small"},{default:n(()=>[qe,d("tbody",null,[(u(!0),D(M,null,A(s.content,(U,E)=>(u(),D("tr",{key:E},[d("td",null,I(E+1),1),d("td",null,[t(i,{value:U.key,"onUpdate:value":T=>U.key=T,disabled:h.editDisabled,type:"text",size:"small",onBlur:r},null,8,["value","onUpdate:value","disabled"])]),d("td",null,[t(i,{value:U.value,"onUpdate:value":T=>U.value=T,disabled:h.editDisabled,type:"text",size:"small",onBlur:r},null,8,["value","onUpdate:value","disabled"])]),d("td",null,[d("div",Ce,[t(x,{class:"go-ml-2",type:"primary",size:"small",ghost:"",disabled:h.editDisabled,onClick:T=>c(E)},{default:n(()=>[f("+")]),_:2},1032,["disabled","onClick"]),t(x,{class:"go-ml-2",type:"warning",size:"small",ghost:"",disabled:E===0&&h.editDisabled,onClick:T=>y(E)},{default:n(()=>[f(" - ")]),_:2},1032,["disabled","onClick"])])]),d("td",null,[U.error?(u(),k(x,{key:0,class:"go-ml-2",text:"",type:"error"},{default:n(()=>[f(" 格式错误 ")]),_:1})):(u(),k(x,{key:1,class:"go-ml-2",text:"",type:"primary"},{default:n(()=>[f(" 格式通过 ")]),_:1}))])]))),128))])]),_:1})}}});const Oe={class:"go-mt-3"},Be=z({__name:"index",props:{editDisabled:{type:Boolean,default:!0}},setup(h){const{chartEditStore:q}=X(),{requestParams:S}=N(q.getRequestGlobalConfig),p=L(b.HEADER),R=[b.HEADER],s=r=>{p.value===b.HEADER&&(S.value[p.value]=r)};return(r,c)=>{const y=o("n-tab"),_=o("n-tabs");return u(),D("div",null,[t(_,{type:"line",animated:"",value:p.value,"onUpdate:value":c[0]||(c[0]=l=>p.value=l)},{default:n(()=>[(u(),D(M,null,A(R,l=>t(y,{key:l,name:l,tab:l},{default:n(()=>[f(I(l),1)]),_:2},1032,["name","tab"])),64))]),_:1},8,["value"]),d("div",Oe,[p.value===e(b).HEADER?(u(),k(e(P),{key:0,editDisabled:h.editDisabled,target:e(S)[p.value],onUpdate:s},null,8,["editDisabled","target"])):j("",!0)])])}}}),$e=z({__name:"index",setup(h){De(x=>({"17e515f4":i.value}));const{PencilIcon:q,ChevronDownOutlineIcon:S,ChevronUpOutlineIcon:p}=K.ionicons5,{chartEditStore:R}=X(),{requestOriginUrl:s,requestInterval:r,requestIntervalUnit:c}=N(R.getRequestGlobalConfig),y=L(!0),_=Ue(),l=L(!1),i=Ee(()=>_.getAppTheme);return(x,g)=>{const U=o("n-tag"),E=o("n-input"),T=o("n-input-number"),w=o("n-select"),G=o("n-input-group"),O=o("n-icon"),a=o("n-button"),ye=o("n-collapse-transition"),he=o("n-tooltip"),be=o("n-card");return u(),k(be,{class:"n-card-shallow"},{default:n(()=>[t(U,{type:"info",bordered:!1,style:{"border-radius":"5px"}},{default:n(()=>[f(" 全局公共配置 ")]),_:1}),t(e(V),{name:"服务",itemRightStyle:{gridTemplateColumns:"5fr 2fr 1fr"}},{default:n(()=>[t(e(H),{name:"前置 URL"},{default:n(()=>[t(E,{value:e(s),"onUpdate:value":g[0]||(g[0]=B=>C(s)?s.value=B:null),valueModifiers:{trim:!0},disabled:y.value,placeholder:"例：http://127.0.0.1/"},null,8,["value","disabled"])]),_:1}),t(e(H),{name:"更新间隔，为 0 只会初始化"},{default:n(()=>[t(G,null,{default:n(()=>[t(T,{class:"select-time-number",value:e(r),"onUpdate:value":g[1]||(g[1]=B=>C(r)?r.value=B:null),valueModifiers:{trim:!0},min:"0","show-button":!1,disabled:y.value,placeholder:"请输入数字"},null,8,["value","disabled"]),t(w,{class:"select-time-options",value:e(c),"onUpdate:value":g[2]||(g[2]=B=>C(c)?c.value=B:null),options:e(Z),disabled:y.value},null,8,["value","options","disabled"])]),_:1})]),_:1}),W(t(a,{type:"primary",ghost:"",onClick:g[3]||(g[3]=B=>y.value=!1)},{icon:n(()=>[t(O,null,{default:n(()=>[t(e(q))]),_:1})]),default:n(()=>[f(" 编辑配置 ")]),_:1},512),[[Y,y.value]])]),_:1}),t(ye,{show:l.value},{default:n(()=>[t(e(Be),{editDisabled:y.value},null,8,["editDisabled"])]),_:1},8,["show"]),l.value?(u(),D("div",{key:0,class:"go-flex-center go-mt-3 down",onClick:g[4]||(g[4]=B=>l.value=!1)},[t(O,{size:"32"},{default:n(()=>[t(e(p))]),_:1})])):(u(),D("div",{key:1,class:"go-flex-center go-mt-3 down",onClick:g[5]||(g[5]=B=>l.value=!0)},[t(he,{trigger:"hover",placement:"top","keep-alive-on-hover":!1},{trigger:n(()=>[t(O,{size:"32"},{default:n(()=>[t(e(S))]),_:1})]),default:n(()=>[f(" 展开 ")]),_:1})]))]),_:1})}}});const mt=Q($e,[["__scopeId","data-v-1a5167a8"]]),Me={style:{width:"600px"}},we={class:"go-mt-3"},Ge={key:0},Ie={key:1},Le=z({__name:"index",props:{targetDataRequest:Object},setup(h){const q=h,{requestHttpType:S,requestContentType:p,requestSQLContent:R,requestParams:s,requestParamsBodyType:r}=N(q.targetDataRequest),c=L(b.PARAMS),y=l=>{c.value!==b.BODY&&(s.value[c.value]=l)},_=l=>{c.value===b.BODY&&(r.value===$.FORM_DATA||r.value===$.X_WWW_FORM_URLENCODED)&&(s.value[b.BODY][r.value]=l)};return(l,i)=>{const x=o("n-tab"),g=o("n-tabs"),U=o("n-radio"),E=o("n-space"),T=o("n-radio-group"),w=o("n-text"),G=o("n-card"),O=o("n-tag");return u(),k(E,{vertical:""},{default:n(()=>[d("div",Me,[t(g,{value:e(p),"onUpdate:value":i[0]||(i[0]=a=>C(p)?p.value=a:null),type:"segment",size:"small"},{default:n(()=>[t(x,{name:e(F).DEFAULT,tab:"普通请求"},null,8,["name"]),t(x,{name:e(F).SQL,tab:"SQL 请求"},null,8,["name"])]),_:1},8,["value"])]),W(d("div",null,[t(g,{type:"line",animated:"",value:c.value,"onUpdate:value":i[1]||(i[1]=a=>c.value=a)},{default:n(()=>[(u(!0),D(M,null,A(e(b),a=>(u(),k(x,{key:a,name:a,tab:a},{default:n(()=>[f(I(a),1)]),_:2},1032,["name","tab"]))),128))]),_:1},8,["value"]),d("div",we,[c.value!==e(b).BODY?(u(),D("div",Ge,[t(e(P),{target:e(s)[c.value],onUpdate:y},null,8,["target"])])):(u(),D("div",Ie,[t(T,{value:e(r),"onUpdate:value":i[2]||(i[2]=a=>C(r)?r.value=a:null),name:"radiogroup"},{default:n(()=>[t(E,null,{default:n(()=>[(u(!0),D(M,null,A(e(Te),a=>(u(),k(U,{key:a,value:a},{default:n(()=>[f(I(a),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["value"]),e(r)===e($).NONE?(u(),k(G,{key:0,class:"go-mt-3 go-pb-3"},{default:n(()=>[t(w,{depth:"3"},{default:n(()=>[f("该接口没有 Body 体")]),_:1})]),_:1})):e(r)===e($).FORM_DATA||e(r)===e($).X_WWW_FORM_URLENCODED?(u(),k(e(P),{key:1,class:"go-mt-3",target:e(s)[e(b).BODY][e(r)],onUpdate:_},null,8,["target"])):e(r)===e($).JSON?(u(),k(e(J),{key:2,modelValue:e(s)[e(b).BODY][e(r)],"onUpdate:modelValue":i[3]||(i[3]=a=>e(s)[e(b).BODY][e(r)]=a),width:"600px",height:"200px",language:"json"},null,8,["modelValue"])):e(r)===e($).XML?(u(),k(e(J),{key:3,modelValue:e(s)[e(b).BODY][e(r)],"onUpdate:modelValue":i[4]||(i[4]=a=>e(s)[e(b).BODY][e(r)]=a),width:"600px",height:"200px",language:"html"},null,8,["modelValue"])):j("",!0)]))])],512),[[Y,e(p)===e(F).DEFAULT]]),W(d("div",null,[e(S)===e(m).GET?(u(),k(w,{key:0},{default:n(()=>[f("SQL 类型不支持 Get 请求，请使用其它方式")]),_:1})):(u(),D(M,{key:1},[t(O,{type:"warning"},{default:n(()=>[f("需要后台提供专门处理 sql 的接口")]),_:1}),t(e(V),{name:"键名"},{default:n(()=>[t(O,{type:"primary",bordered:!1,style:{width:"40px","font-size":"16px"}},{default:n(()=>[f(" sql ")]),_:1})]),_:1}),t(e(V),{name:"键值"},{default:n(()=>[t(e(J),{modelValue:e(R).sql,"onUpdate:modelValue":i[5]||(i[5]=a=>e(R).sql=a),width:"600px",height:"200px",language:"sql"},null,8,["modelValue"])]),_:1})],64))],512),[[Y,e(p)===e(F).SQL]])]),_:1})}}});const Ae=Q(Le,[["__scopeId","data-v-00740feb"]]),Ne=["12a","1a","2a","3a","4a","5a","6a","7a","8a","9a","10a","11a","12p","1p","2p","3p","4p","5p","6p","7p","8p","9p","10p","11p"],Ve=["Saturday","Friday","Thursday","Wednesday","Tuesday","Monday","Sunday"],ze=[[0,0,"@integer(0, 10)"],[1,0,"@integer(0, 10)"],[2,0,"-"],[3,0,"-"],[4,0,"-"],[5,0,"-"],[6,0,"-"],[7,0,"-"],[8,0,"-"],[9,0,"-"],[10,0,"-"],[11,0,"@integer(0, 10)"],[12,0,"@integer(0, 10)"],[13,0,"@integer(0, 10)"],[14,0,"@integer(0, 10)"],[15,0,"@integer(0, 10)"],[16,0,"@integer(0, 10)"],[17,0,"@integer(0, 10)"],[18,0,"@integer(0, 10)"],[19,0,"@integer(0, 10)"],[20,0,"@integer(0, 10)"],[21,0,"@integer(0, 10)"],[22,0,"@integer(0, 10)"],[23,0,"@integer(0, 10)"],[0,1,7],[1,1,"-"],[2,1,"-"],[3,1,"-"],[4,1,"-"],[5,1,"-"],[6,1,"-"],[7,1,"-"],[8,1,"-"],[9,1,"-"],[10,1,"@integer(0, 10)"],[11,1,"@integer(0, 10)"],[12,1,"@integer(0, 10)"],[13,1,"@integer(0, 10)"],[14,1,"@integer(0, 10)"],[15,1,"@integer(0, 10)"],[16,1,"@integer(0, 10)"],[17,1,"@integer(0, 10)"],[18,1,"@integer(0, 10)"],[19,1,"@integer(0, 10)"],[20,1,"@integer(0, 10)"],[21,1,"@integer(0, 10)"],[22,1,"@integer(0, 10)"],[23,1,"@integer(0, 10)"],[0,2,1],[1,2,1],[2,2,"-"],[3,2,"-"],[4,2,"-"],[5,2,"-"],[6,2,"-"],[7,2,"-"],[8,2,"-"],[9,2,"-"],[10,2,"@integer(0, 10)"],[11,2,"@integer(0, 10)"],[12,2,"@integer(0, 10)"],[13,2,"@integer(0, 10)"],[14,2,"@integer(0, 10)"],[15,2,"@integer(0, 10)"],[16,2,"@integer(0, 10)"],[17,2,"@integer(0, 10)"],[18,2,"@integer(0, 10)"],[19,2,"@integer(0, 10)"],[20,2,"@integer(0, 10)"],[21,2,"@integer(0, 10)"],[22,2,"@integer(0, 10)"],[23,2,"@integer(0, 10)"],[0,3,7],[1,3,3],[2,3,"-"],[3,3,"-"],[4,3,"-"],[5,3,"-"],[6,3,"-"],[7,3,"-"],[8,3,1],[9,3,"-"],[10,3,"@integer(0, 10)"],[11,3,"@integer(0, 10)"],[12,3,"@integer(0, 10)"],[13,3,"@integer(0, 10)"],[14,3,"@integer(0, 10)"],[15,3,"@integer(0, 10)"],[16,3,"@integer(0, 10)"],[17,3,"@integer(0, 10)"],[18,3,"@integer(0, 10)"],[19,3,"@integer(0, 10)"],[20,3,"@integer(0, 10)"],[21,3,"@integer(0, 10)"],[22,3,"@integer(0, 10)"],[23,3,"@integer(0, 10)"],[0,4,"@integer(0, 10)"],[1,4,"@integer(0, 10)"],[2,4,"-"],[3,4,"-"],[4,4,"-"],[5,4,"@integer(0, 10)"],[6,4,"-"],[7,4,"-"],[8,4,"-"],[9,4,"@integer(0, 10)"],[10,4,"@integer(0, 10)"],[11,4,"@integer(0, 10)"],[12,4,"@integer(0, 10)"],[13,4,"@integer(0, 10)"],[14,4,"@integer(0, 10)"],[15,4,"@integer(0, 10)"],[16,4,"@integer(0, 10)"],[17,4,"@integer(0, 10)"],[18,4,"@integer(0, 10)"],[19,4,"@integer(0, 10)"],[20,4,"@integer(0, 10)"],[21,4,"@integer(0, 10)"],[22,4,"@integer(0, 10)"],[23,4,"-"],[0,5,"@integer(0, 10)"],[1,5,"@integer(0, 10)"],[2,5,"-"],[3,5,"@integer(0, 10)"],[4,5,"-"],[5,5,"-"],[6,5,"-"],[7,5,"-"],[8,5,"@integer(0, 10)"],[9,5,"-"],[10,5,"@integer(0, 10)"],[11,5,"@integer(0, 10)"],[12,5,"@integer(0, 10)"],[13,5,"@integer(0, 10)"],[14,5,"@integer(0, 10)"],[15,5,"@integer(0, 10)"],[16,5,"@integer(0, 10)"],[17,5,"@integer(0, 10)"],[18,5,"-"],[19,5,"@integer(0, 10)"],[20,5,"@integer(0, 10)"],[21,5,"@integer(0, 10)"],[22,5,"@integer(0, 10)"],[23,5,"-"],[0,6,"@integer(0, 10)"],[1,6,"-"],[2,6,"-"],[3,6,"-"],[4,6,"-"],[5,6,"-"],[6,6,"-"],[7,6,"-"],[8,6,"-"],[9,6,"-"],[10,6,"@integer(0, 10)"],[11,6,"-"],[12,6,"@integer(0, 10)"],[13,6,"@integer(0, 10)"],[14,6,"@integer(0, 10)"],[15,6,"@integer(0, 10)"],[16,6,"-"],[17,6,"-"],[18,6,"-"],[19,6,"-"],[20,6,"@integer(0, 10)"],[21,6,"@integer(0, 10)"],[22,6,"@integer(0, 10)"],[23,6,"@integer(0, 10)"]],Fe={xAxis:Ne,yAxis:Ve,seriesData:ze},He=[{dimensions:["data1"],source:[[10,"@integer(0, 100)"],[8.07,"@integer(0, 100)"],[13,"@integer(0, 100)"],[9.05,"@integer(0, 100)"],[11,"@integer(0, 100)"],[14,"@integer(0, 100)"],[13.4,"@integer(0, 100)"],[10,"@integer(0, 100)"],[14,"@integer(0, 100)"],[12.5,"@integer(0, 100)"],[9.15,"@integer(0, 100)"],[11.5,"@integer(0, 100)"],[3.03,"@integer(0, 100)"],[12.2,"@integer(0, 100)"],[2.02,"@integer(0, 100)"],[1.05,"@integer(0, 100)"],[4.05,"@integer(0, 100)"],[6.03,"@integer(0, 100)"],[12,"@integer(0, 100)"],[12,"@integer(0, 100)"],[7.08,"@integer(0, 100)"],[5.02,"@integer(0, 100)"]]},{dimensions:["data2"],source:[[10,"@integer(0, 70)"],[8.07,"@integer(0, 70)"],[13,"@integer(0, 70)"],[9.05,"@integer(0, 70)"],[11,"@integer(0, 70)"],[14,"@integer(0, 70)"],[13.4,"@integer(0, 70)"],[10,"@integer(0, 70)"],[14,"@integer(0, 70)"],[12.5,"@integer(0, 70)"],[9.15,"@integer(0, 70)"],[11.5,"@integer(0, 70)"],[3.03,"@integer(0, 70)"],[12.2,"@integer(0, 70)"],[2.02,"@integer(0, 70)"],[1.05,"@integer(0, 70)"],[4.05,"@integer(0, 70)"],[6.03,"@integer(0, 70)"],[12,"@integer(0, 70)"],[12,"@integer(0, 70)"],[7.08,"@integer(0, 70)"],[5.02,"@integer(0, 70)"]]}],Je={"markers|50":[{name:"某某地市",value:"@integer(2, 20)",position:["@float(115, 117, 1, 6)","@float(38, 40, 1, 6)"]}]},We=[{name:"@name",value:"@integer(0, 1000)",children:[{name:"@name",value:"@integer(0, 500)"},{name:"@name",value:"@integer(0, 500)"}]},{name:"@name",value:"@integer(0, 1000)",children:[{name:"@name",value:"@integer(0, 00)"},{name:"@name",value:"@integer(0, 500)"}]},{name:"@name",value:"@integer(0, 1000)",children:[{name:"@name",value:"@integer(0, 1000)"}]},{name:"@name",value:"@integer(0, 1000)",children:[{name:"@name",value:"@integer(0, 1000)"}]}],Ye=[{name:"a"},{name:"b"},{name:"a1"},{name:"a2"},{name:"b1"},{name:"b2"}],Pe=[{source:"a",target:"a1",value:"@integer(0, 10)"},{source:"a",target:"a2",value:"@integer(0, 10)"},{source:"b",target:"b1",value:"@integer(0, 10)"},{source:"a",target:"b1",value:"@integer(0, 10)"},{source:"b1",target:"a1",value:"@integer(0, 10)"},{source:"b1",target:"b2",value:"@integer(0, 10)"}],je=[{depth:0,itemStyle:{color:"#decbe4"},lineStyle:{color:"source",opacity:.9}},{depth:1,itemStyle:{color:"#b3cde3"},lineStyle:{color:"source",opacity:.6}},{depth:2,itemStyle:{color:"#ccebc5"},lineStyle:{color:"source",opacity:.6}}],Qe={label:Ye,links:Pe,levels:je},Xe=[{id:"0",name:"Myriel",symbolSize:"@integer(0, 50)",x:-266.82776,y:299.6904,value:"@integer(0, 50)",category:3},{id:"1",name:"Napoleon",symbolSize:"@integer(0, 50)",x:-418.08344,y:446.8853,value:"@integer(0, 50)",category:5},{id:"2",name:"MlleBaptistine",symbolSize:"@integer(0, 50)",x:-212.76357,y:245.29176,value:"@integer(0, 50)",category:1},{id:"3",name:"MmeMagloire",symbolSize:"@integer(0, 50)",x:-242.82404,y:235.26283,value:"@integer(0, 50)",category:1},{id:"4",name:"CountessDeLo",symbolSize:"@integer(0, 50)",x:-379.30386,y:429.06424,value:"@integer(0, 50)",category:0}],Ke=[{source:"1",target:"@integer(2, 4)"},{source:"2",target:"@integer(3, 4)"},{source:"3",target:"@integer(0, 2)"},{source:"3",target:"@integer(0, 1)"},{source:"4",target:"@integer(0, 3)"}],Ze=[{name:"A"},{name:"B"},{name:"C"},{name:"D"},{name:"E"},{name:"F"},{name:"G"},{name:"H"},{name:"I"}],et={nodes:Xe,links:Ke,categories:Ze},v={fetchMockSingleData:{code:0,status:200,msg:"请求成功",data:{dimensions:["product","dataOne"],"source|20":[{product:"@name","dataOne|0-900":3}]}},fetchCapsule:{code:0,status:200,msg:"请求成功",data:{dimensions:["name","value"],"source|2-5":[{"name|+1":["厦门","福州","北京","上海","新疆","郑州","湖南","内蒙古"],"value|0-40":20}]}},fetchMockData:{code:0,status:200,msg:"请求成功",data:{dimensions:["product","dataOne","dataTwo","dataThree"],"source|20":[{product:"@name","dataOne|100-900":3,"dataTwo|100-900":3,"dataThree|100-900":3}]}},fetchRankList:{code:0,status:200,msg:"请求成功","data|50":[{name:"@name","value|100-900":5}]},fetchScrollBoard:{code:0,status:200,msg:"请求成功",data:[["行1列1","行1列2","1"],["行2列1","行2列2","2"],["行3列1","行3列2","3"],["行4列1","行4列2","4"],["行5列1","行5列2","5"],["行6列1","行6列2","6"],["行7列1","行7列2","行7列3"],["行8列1","行8列2","行8列3"],["行9列1","行9列2","行9列3"],["行10列1","行10列2","行10列3"]]},fetchNumberFloat:{code:0,status:200,msg:"请求成功",data:"@float(0, 0.99, 1, 4)"},fetchNumberInt:{code:0,status:200,msg:"请求成功",data:"@integer(0, 100)"},fetchText:{code:0,status:200,msg:"请求成功",data:"@paragraph(1, 10)"},fetchImage:h=>({code:0,status:200,msg:"请求成功",data:`https://robohash.org/${h}`}),fetchRadar:{code:0,status:200,msg:"请求成功",data:{radarIndicator:[{name:"@name",max:1e4},{name:"@name",max:1e4},{name:"@name",max:1e4},{name:"@name",max:1e4},{name:"@name",max:1e4},{name:"@name",max:1e4}],seriesData:[{value:["@integer(0, 10000)","@integer(0, 10000)","@integer(0, 10000)","@integer(0, 10000)","@integer(0, 10000)","@integer(0, 10000)"],name:"data1"},{value:["@integer(0, 10000)","@integer(0, 10000)","@integer(0, 10000)","@integer(0, 10000)","@integer(0, 10000)","@integer(0, 10000)"],name:"data2"}]}},fetchHeatmap:{code:0,status:200,msg:"请求成功",data:Fe},fetchScatterBasic:{code:0,status:200,msg:"请求成功",data:He},fetchMap:{code:0,status:200,msg:"请求成功",data:Je},fetchWordCloud:{code:0,status:200,msg:"请求成功",data:[{name:"@name",value:8e3,textStyle:{color:"#78fbb2"},emphasis:{textStyle:{color:"red"}}},{name:"@name",value:"@integer(10, 8000)"},{name:"@name",value:"@integer(10, 8000)"},{name:"@name",value:"@integer(10, 8000)"},{name:"@name",value:"@integer(10, 8000)"},{name:"@name",value:"@integer(10, 8000)"},{name:"@name",value:"@integer(10, 8000)"},{name:"@name",value:"@integer(10, 8000)"},{name:"@name",value:"@integer(10, 8000)"},{name:"@name",value:"@integer(10, 8000)"},{name:"@name",value:"@integer(10, 8000)"},{name:"@name",value:"@integer(10, 8000)"},{name:"@name",value:"@integer(10, 8000)"},{name:"@name",value:"@integer(10, 8000)"},{name:"@name",value:"@integer(10, 8000)"},{name:"@name",value:"@integer(10, 8000)"},{name:"@name",value:"@integer(10, 8000)"},{name:"@name",value:"@integer(10, 8000)"},{name:"@name",value:"@integer(10, 8000)"},{name:"@name",value:"@integer(10, 8000)"}]},fetchTreemap:{code:0,status:200,msg:"请求成功",data:We},threeEarth01Data:{code:0,status:200,msg:"请求成功",data:[{startArray:{name:"@name",N:"@integer(10, 100)",E:"@integer(10, 100)"},"endArray|10":[{name:"@name",N:"@integer(10, 100)",E:"@integer(10, 100)"}]}]},fetchSankey:{code:0,status:200,msg:"请求成功",data:Qe},graphData:{code:0,status:200,msg:"请求成功",data:et}},ee="/mock/chartData",te="/mock/chartSingleData",ne="/mock/number/float",ae="/mock/number/int",re="/mock/text",le="/mock/image",oe="/mock/rankList",se="/mock/scrollBoard",ie="/mock/radarData",ue="/mock/heatMapData",de="/mock/scatterBasic",ce="/mock/map",ge="/mock/capsule",me="/mock/wordCloud",pe="/mock/treemap",_e="/mock/threeEarth01Data",ve="/mock/sankey",fe="/mock/graphData";m.GET,m.GET,m.GET,m.GET,m.GET,m.GET,m.GET,m.GET,m.GET,m.GET,m.GET,m.GET,m.GET,m.GET,m.GET,m.GET,m.GET,m.GET;const tt={class:"go-pl-0"},nt=z({__name:"index",props:{targetDataRequest:Object},setup(h){const q=h,{HelpOutlineIcon:S}=K.ionicons5,{chartEditStore:p}=X(),{requestOriginUrl:R}=N(p.getRequestGlobalConfig),{requestInterval:s,requestIntervalUnit:r,requestHttpType:c,requestUrl:y}=N(q.targetDataRequest),_=[{value:`【图表】${ee}`},{value:`【单数据图表】${te}`},{value:`【文本】${re}`},{value:`【0~100 整数】${ae}`},{value:`【0~1小数】${ne}`},{value:`【图片地址】${le}`},{value:`【排名列表】${oe}`},{value:`【滚动表格】${se}`},{value:`【雷达】${ie}`},{value:`【热力图】${ue}`},{value:`【基础散点图】${de}`},{value:`【地图数据】${ce}`},{value:`【胶囊柱图】${ge}`},{value:`【词云】${me}`},{value:`【树图】${pe}`},{value:`【三维地球】${_e}`},{value:`【桑基图】${ve}`},{value:`【关系图】${fe}`}];return(l,i)=>{const x=o("n-divider"),g=o("n-icon"),U=o("n-text"),E=o("n-tooltip"),T=o("n-select"),w=o("n-input"),G=o("n-input-group"),O=o("n-input-number");return u(),D(M,null,[t(x,{class:"go-my-3","title-placement":"left"}),t(e(V),{itemRightStyle:{gridTemplateColumns:"6fr 2fr"},style:{"padding-right":"25px"}},{name:n(()=>[f(" 地址 "),e(Se)()?(u(),k(E,{key:0,trigger:"hover"},{trigger:n(()=>[t(g,{size:"21",depth:3},{default:n(()=>[t(e(S))]),_:1})]),default:n(()=>[d("ul",tt,[f(" 开发环境使用 mock 数据，请输入 "),(u(),D(M,null,A(_,a=>d("li",{key:a.value},[t(U,{type:"info"},{default:n(()=>[f(I(a.value),1)]),_:2},1024)])),64))])]),_:1})):j("",!0)]),default:n(()=>[t(e(H),{name:"请求方式 & URL 地址"},{default:n(()=>[t(G,null,{default:n(()=>[t(T,{class:"select-type-options",value:e(c),"onUpdate:value":i[0]||(i[0]=a=>C(c)?c.value=a:null),options:e(Re)},null,8,["value","options"]),t(w,{value:e(y),"onUpdate:value":i[1]||(i[1]=a=>C(y)?y.value=a:null),valueModifiers:{trim:!0},min:1,placeholder:"请输入地址（去除前置URL）"},{prefix:n(()=>[t(U,null,{default:n(()=>[f(I(e(R)),1)]),_:1}),t(x,{vertical:""})]),_:1},8,["value"])]),_:1})]),_:1}),t(e(H),{name:"更新间隔，为 0 只会初始化"},{default:n(()=>[t(G,null,{default:n(()=>[t(O,{value:e(s),"onUpdate:value":i[2]||(i[2]=a=>C(s)?s.value=a:null),valueModifiers:{trim:!0},class:"select-time-number",min:"0","show-button":!1,placeholder:"默认使用全局数据"},null,8,["value"]),t(T,{class:"select-time-options",value:e(r),"onUpdate:value":i[3]||(i[3]=a=>C(r)?r.value=a:null),options:e(Z)},null,8,["value","options"])]),_:1})]),_:1})]),_:1}),t(e(V),{name:"选择方式",class:"go-mt-0"},{default:n(()=>[t(e(Ae),{targetDataRequest:h.targetDataRequest},null,8,["targetDataRequest"])]),_:1})],64)}}});const pt=Q(nt,[["__scopeId","data-v-ccb89b17"]]);export{mt as R,pt as a};
