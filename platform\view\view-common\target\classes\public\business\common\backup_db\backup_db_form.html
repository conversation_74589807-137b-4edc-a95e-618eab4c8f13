<!--
/**
 * 备份记录 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2023-01-27 17:48:40
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
	<title th:text="${lang.translate('备份记录')}">备份记录</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="display:none">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-5354-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column" >

                <!-- text_input : 名称 ,  dbName -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('名称')}">名称</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="dbName" id="dbName" name="dbName" th:placeholder="${ lang.translate('请输入'+'名称') }" type="text" class="layui-input"  />
                    </div>
                </div>

                <!-- radio_box : 状态 ,  status  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('状态')}">状态</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input input-type="radio" type="radio" name="status" lay-filter="status" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.common.BackupStatusEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==-1)}">
                    </div>
                </div>

                <!-- text_input : 备份文件 ,  fileRoute -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('备份文件')}">备份文件</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="fileRoute" id="fileRoute" name="fileRoute" th:placeholder="${ lang.translate('请输入'+'备份文件') }" type="text" class="layui-input"  />
                    </div>
                </div>

                <!-- text_area : 内容 ,  content  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('内容')}">内容</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <textarea lay-filter="content" id="content" name="content" th:placeholder="${ lang.translate('请输入'+'内容') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>

                <!-- date_input : 开始时间 ,  startTime  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('开始时间')}">开始时间</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input input-type="date" lay-filter="startTime" id="startTime" name="startTime"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择'+'开始时间') }" type="text" class="layui-input"   />
                    </div>
                </div>

                <!-- date_input : 结束时间 ,  endTime  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('结束时间')}">结束时间</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input input-type="date" lay-filter="endTime" id="endTime" name="endTime"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择'+'结束时间') }" type="text" class="layui-input"   />
                    </div>
                </div>

                <!-- date_input : 失效日期 ,  invalidTime  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('失效日期')}">失效日期</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input input-type="date" lay-filter="invalidTime" id="invalidTime" name="invalidTime"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择'+'失效日期') }" type="text" class="layui-input"   />
                    </div>
                </div>

                <!-- text_area : 备注 ,  notes  -->
                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <textarea lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入'+'备注') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 250px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('sys_backup_db:create','sys_backup_db:update','sys_backup_db:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var RADIO_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.BackupStatusEnum')}]];
    var VALIDATE_CONFIG={};
    var AUTH_PREFIX="sys_backup_db";


</script>



<script th:src="'/business/common/backup_db/backup_db_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/common/backup_db/backup_db_form.js?'+${cacheKey}"></script>

</body>
</html>
